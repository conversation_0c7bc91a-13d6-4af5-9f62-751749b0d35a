{"response": "```yaml\n- kind: Question\n  id: aa6067_WICCCollectPinDTMF_DM\n  displayName: aa6067_WICCCollectPinDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa6067_nm1_01.wav\\\">Sorry Let s try THAT again  Please enter your *4-digit* PIN now</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.aa6067_WICCCollectPinDTMF_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.childCareInfoVariables.incorrectPinCounter = 1,\n            \"Please enter your PIN again\",\n            true,\n            \"Please enter your PIN\"\n        )}\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/aa6067_ni1_01.wav\\\">Sorry, we didn t get an entry  For security purposes we need the PIN that goes along with this card  Please enter the PIN now or press * to start over</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.childCareInfoVariables.collectPin\n  value: aa6067_WICCCollectPinDTMF_DM.returnvalue\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.dnisInfo.callType = \"WICC\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aa6067_out_01.wav\\\">One Moment</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa6069_WICCValidatePin_DB_DA\n\n  elseActions:\n    - kind: SendActivity\n      id: sendActivity_REPLACE_THIS\n      activity:\n        speak:\n          - \"<audio src=\\\"AUDIO_LOCATION/aa6067_out_01.wav\\\">One Moment</audio>\"\n    - kind: GotoAction\n      id: goto_REPLACE_THIS\n      actionId: aa6070_CCApplyPayment_DB_DA\n```", "timestamp": 1749544046.5688825, "content_hash": "653bdf43e67ddcde88db5a65c45f603a"}