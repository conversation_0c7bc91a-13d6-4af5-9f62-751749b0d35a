{"response": "```yaml\n- kind: Question\n  id: st0630_RefillOrChangePlan_DM\n  displayName: st0630_RefillOrChangePlan_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"Say Yes or press 1, or say No or press 2\"\n      - \"Say Yes or press 1, or say No or press 2\"\n\n  alwaysPrompt: true\n  variable: Global.st0630_RefillOrChangePlan_DM_reco\n  prompt:\n    speak:\n      - \"{CustomAudio: com.nuance.att.application.audio.st0630_RefillOrChangePlan_DM_initial}\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: refill\n          displayName: refill\n        - id: change_plan\n          displayName: change_plan\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"{CustomAudio: com.nuance.att.application.audio.st0630_RefillOrChangePlan_DM_noinput1}\"\n        - \"{CustomAudio: com.nuance.att.application.audio.st0630_RefillOrChangePlan_DM_noinput2}\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_st0630_RefillOrChangePlan_DM\n  value: =Text(Global.st0630_RefillOrChangePlan_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_st0630_RefillOrChangePlan_DM = \"refill\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: refillAccount\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: st0635_DataRefill_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_st0630_RefillOrChangePlan_DM = \"change_plan\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.intent\n              value: changePlan\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: st0620_DataChangePlan_SD\n```", "timestamp": **********.029949, "content_hash": "5a1f92d5250b4760beb543923f182ec7"}