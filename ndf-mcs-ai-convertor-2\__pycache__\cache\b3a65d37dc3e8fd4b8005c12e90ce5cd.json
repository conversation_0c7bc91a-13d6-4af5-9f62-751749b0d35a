{"response": "```yaml\n- kind: Question\n  id: ma2215_AskIfHavePhoneNumber_DM\n  displayName: ma2215_AskIfHavePhoneNumber_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I missed that</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ma2215_ni1_01.wav\\\">Did you get your wireless number when you started your activation earlier? Just say Yes or No</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">Sorry</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ma2215_ni2_01.wav\\\">If youve already received your new wireless number, say Yes or press 1 If not, say No or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ma2215_AskIfHavePhoneNumber_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ma2215_ini_01.wav\\\">Do you have your new wireless number yet?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ma2215_ni1_01.wav\\\">Did you get your wireless number when you started your activation earlier? Just say Yes or No</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ma2215_ni2_01.wav\\\">If youve already received your new wireless number, say Yes or press 1 If not, say No or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_commandgrammar\n  value: |\n    {Switch(\n      true,\n      Global.callType = \"611\",\n      \"GlobalCommands.grxml?SWI_vars.allow=mainmenu&SWI_vars.operator_cnf_mode=NEVER\",\n      \"GlobalCommands.grxml?SWI_vars.operator_cnf_mode=NEVER\"\n    )}\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ma2215_AskIfHavePhoneNumber_DM\n  value: =Text(Global.ma2215_AskIfHavePhoneNumber_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.callType = \"611\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_commandgrammar\n          value: \"GlobalCommands.grxml?SWI_vars.allow=mainmenu\"\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.collection_commandgrammar\n      value: \"GlobalCommands.grxml\"\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma2215_AskIfHavePhoneNumber_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.numMINAttempts\n          value: 0\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ma2220_ContinueWirelessNumber_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.fromma2210 = true, true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/ma2215_out_02.wav\\\">No problem Let me check where we left off </audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\"></audio>\"\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Or(Global.activationStatus = \"verifiedIMEI\", Global.activationStatus = \"activatedCSI\", Global.activationStatus = \"activatedEricsson\"), true, false)\n                  actions:\n                    - kind: BeginDialog\n                      id: begin_REPLACE_THIS\n                      dialog: topic.M03_MainMenu.dvxml#ma2320_ActivateSIM_SD\n              elseActions:\n                - kind: BeginDialog\n                  id: begin_REPLACE_THIS\n                  dialog: topic.M03_MainMenu.dvxml#ma2315_AskHavePackage_DM\n      elseActions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/ma2215_out_01.wav\\\">Alright, lets look up your SIM number instead</audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\"></audio>\"\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.accountType = \"shared\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: ma2123_AskMobilePhone_DM\n          elseActions:\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.M03_MainMenu.dvxml#ma2315_AskHavePackage_DM\n```", "timestamp": **********.468807, "content_hash": "b3a65d37dc3e8fd4b8005c12e90ce5cd"}