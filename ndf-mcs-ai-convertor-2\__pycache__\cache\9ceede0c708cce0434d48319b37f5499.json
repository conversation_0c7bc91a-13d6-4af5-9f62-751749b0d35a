{"response": "```yaml\n- kind: Question\n  id: UW1120_ConfirmCardYN_DM\n  displayName: UW1120_ConfirmCardYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/UW1120_nm1_01.wav\\\">Do you want to use that card?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/UW1120_nm2_01.wav\\\">If you'd like to use this saved card, say 'yes' or press 1 Otherwise, to choose another card, say 'no' or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/UW1120_nm3_01.wav\\\">To pay with the card I have on file for you, press 1 To pay with a different card, press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.UW1120_ConfirmCardYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/UW1120_ini_01.wav\\\">The card I have for you is</audio>{Global.paymentOptionId}<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio><audio src=\\\"AUDIO_LOCATION/UW1120_ini_03.wav\\\">Is that the one you want to use?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/UW1120_nm1_01.wav\\\">Do you want to use that card?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/UW1120_nm2_01.wav\\\">If you'd like to use this saved card, say 'yes' or press 1 Otherwise, to choose another card, say 'no' or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/UW1120_nm3_01.wav\\\">To pay with the card I have on file for you, press 1 To pay with a different card, press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.paymentOptionId\n  value: GlobalVars.GetWalletInfo.walletItems[0].paymentOptionId\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_UW1120_ConfirmCardYN_DM\n  value: =Text(Global.UW1120_ConfirmCardYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_UW1120_ConfirmCardYN_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.paymentOptionId\n          value: GlobalVars.GetWalletInfo.walletItems[0].paymentOptionId\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.payingWithEWallet\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.PaymentTable.ACTIVATION_STATUS\n          value: '106'\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.savedCardPreference\n          value: GlobalVars.GetWalletInfo.walletItems[0].savedCardPreference\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.lastFourDigitsOfCard\n          value: GlobalVars.GetWalletInfo.walletItems[0].lastFourDigitsOfCard\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: UW1130_CheckHaveMethodSet_JDA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_UW1120_ConfirmCardYN_DM = \"false\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: UW1125_UseNewCard_PP\n```", "timestamp": 1749529924.9027483, "content_hash": "9ceede0c708cce0434d48319b37f5499"}