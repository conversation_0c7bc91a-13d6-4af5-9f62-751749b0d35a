{"response": "```yaml\n- kind: Question\n  id: ss0112_AskRecoveredDevice_DM\n  displayName: ss0112_AskRecoveredDevice_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ss0112_ni1_01.wav\\\">Have you recovered your phone? Please say Yes or No</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ss0112_ni2_01.wav\\\">If you've recoered your phone, press 1 Otherwise, press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ss0112_AskRecoveredDevice_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ss0112_ini_01.wav\\\">Are you calling becuse you have recovered your lost phone?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ss0112_ni1_01.wav\\\">Have you recovered your phone? Please say Yes or No</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ss0112_ni2_01.wav\\\">If you've recoered your phone, press 1 Otherwise, press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ss0112_AskRecoveredDevice_DM\n  value: =Text(Global.ss0112_AskRecoveredDevice_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ss0112_AskRecoveredDevice_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/ss0112_out_01.wav\\\">Okay, lets reactivate your account</audio>\"\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: recoveredDevice\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ss0114_LostStolenCall_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ss0112_AskRecoveredDevice_DM = \"false\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: ss0113_AskCallReasonLost_DM\n```", "timestamp": **********.075271, "content_hash": "3f37801b6705295c50d8f98f6bf3d252"}