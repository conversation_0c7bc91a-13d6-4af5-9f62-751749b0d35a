{"response": "```yaml\n- kind: Question\n  id: pf0220_AskWhichCategory_DM\n  displayName: pf0220_AskWhichCategory_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              \"com.nuance.att.application.audio.pf0220_AskWhichCategory_noinput_01\",\n              \"com.nuance.att.application.audio.pf0220_AskWhichCategory_noinput_01\"\n          )\n        }\n      - |\n        {Switch(\n              true,\n              \"com.nuance.att.application.audio.pf0220_AskWhichCategory_noinput_02\",\n              \"com.nuance.att.application.audio.pf0220_AskWhichCategory_noinput_02\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.pf0220_AskWhichCategory_DM_reco\n  prompt:\n    speak:\n      - \"com.nuance.att.application.audio.pf0220_AskWhichCategory_initial\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items: []\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"com.nuance.att.application.audio.pf0220_AskWhichCategory_noinput_01\"\n        - \"com.nuance.att.application.audio.pf0220_AskWhichCategory_noinput_02\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.returnCode\n  value: GetPackageList.returnCode\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.returnCode = 0, true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_dtmfgrammar1\n          value: =Text('pf0220_AskWhichCategory_DM_dtmf.jsp' + GetPackageList.packageListString)\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_grammar1\n          value: =Text('pf0220_AskWhichCategory_DM.jsp' + GetPackageList.packageListString)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.choseOtherPackages\n  value: false\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.packageCategoryCode\n  value: pf0220_AskWhichCategory_DM.returnvalue\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: pf0225_NumPackagesInCategory_JDA\n```\n\n**Notes on the conversion:**\n- **unrecognizedPrompt**: Since the nomatch/noinput prompts are custom audio class references, they are output as string references to the className, as per the pattern in your examples.\n- **prompt**: The initial prompt is also a custom audio class reference.\n- **entity**: No explicit options are defined in the XML, so the ClosedListEntity is left empty.\n- **SetVariable**: For session-mapping and if-conditions, variables are set as per the XML.\n- **ConditionGroup**: The if condition for `returnCode==0` is converted to PowerFx style.\n- **GotoAction**: The success action's next is mapped to a GotoAction.\n- **No explicit nomatch/noinput audiofiles**: Only className references are present, so those are used as string values.\n- **No command actions are included**: As per your instructions, only success and main flow are converted.\n\nIf you need the command actions (mainmenu/operator/repeat) as well, let me know!", "timestamp": 1749471722.140683, "content_hash": "e02fe4c37942c4cff187431d15b2c480"}