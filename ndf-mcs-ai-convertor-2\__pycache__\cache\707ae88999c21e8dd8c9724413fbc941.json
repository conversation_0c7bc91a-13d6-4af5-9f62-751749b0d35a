{"response": "```yaml\n- kind: Question\n  id: dc0115_AskChangePlan_DM\n  displayName: dc0115_AskChangePlan_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I missed that</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">Sorry</audio>\"\n      - \"{Switch(\n            true,\n            true,\n            \\\"Say Yes or press 1, or say No or press 2\\\"\n        )}\"\n      - \"{Switch(\n            true,\n            true,\n            \\\"Say Yes or press 1, or say No or press 2\\\"\n        )}\"\n\n  alwaysPrompt: true\n  variable: Global.dc0115_AskChangePlan_DM_reco\n  prompt:\n    speak:\n      - \"{Switch(\n            true,\n            true,\n            \\\"[AUDIO: com.nuance.att.application.audio.dc0115_AskChangePlan_DM_initial]\\\"\n        )}\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"{Switch(\n              true,\n              true,\n              \\\"[AUDIO: com.nuance.att.application.audio.dc0115_AskChangePlan_DM_noinput1]\\\"\n          )}\"\n        - \"{Switch(\n              true,\n              true,\n              \\\"[AUDIO: com.nuance.att.application.audio.dc0115_AskChangePlan_DM_noinput2]\\\"\n          )}\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_dc0115_AskChangePlan_DM\n  value: =Text(Global.dc0115_AskChangePlan_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_dc0115_AskChangePlan_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: changePlan\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: dc0120_DataChangePlan_SD\n\n  elseActions:\n    - kind: GotoAction\n      id: goto_REPLACE_THIS\n      actionId: getReturnLink()\n```\n\n---\n\n**Explanation of mapping:**\n\n- **kind**: Always \"Question\" as per instruction.\n- **id/displayName**: Set to \"dc0115_AskChangePlan_DM\" from `<dm-state id=\"...\">`.\n- **unrecognizedPrompt**: \n  - From `<nomatchprefixes>` and `<nomatchprompts>`, mapped audiofile text and custom audio class as Switch (since they're custom, used a placeholder).\n- **prompt**: \n  - From `<initialprompt>`, since it's a custom audio, used a Switch with the audio class as a placeholder.\n- **entity**: BooleanPrebuiltEntity (since success actions are \"true\"/\"false\").\n- **voiceInputSettings.inputTimeoutResponse**: \n  - From `<noinputprompts>`, custom audio class as Switch.\n- **SetVariable**: For the variable mapping as per instruction 11.\n- **ConditionGroup**: \n  - For \"true\" label, set intent and goto next.\n  - For \"false\" label, goto getReturnLink().\n- **AUDIO_LOCATION**: Used for audiofile src, for custom audio class, used `[AUDIO: ...]` as a placeholder since no .wav file is present.\n\nIf you want to replace `[AUDIO: ...]` with a different placeholder or want to omit it, let me know!", "timestamp": 1749470677.4444685, "content_hash": "707ae88999c21e8dd8c9724413fbc941"}