{"response": "```yaml\n- kind: Question\n  id: aa6045_CCConfirmProviderIdDTMF_DM\n  displayName: aa6045_CCConfirmProviderIdDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.dnisInfo.dtmfOnlyFlag = true,\n              \"Sorry, I still didn t get that\",\n              true,\n              \"\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.aa6045_CCConfirmProviderIdDTMF_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa6045_ini_01.wav\\\">Just to confirm, this payment is going to provider</audio> {Global.childCareInfoVariables.collectedProviderId} <audio src=\\\"AUDIO_LOCATION/aa6045_ini_03.wav\\\">If that s correct, press 1 If that s wrong, press 2</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.dnisInfo.dtmfOnlyFlag = true,\n                \"Sorry, I still didn t get that\",\n                true,\n                \"\"\n            )\n          }\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa6045_CCConfirmProviderIdDTMF_DM\n  value: =Text(Global.aa6045_CCConfirmProviderIdDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa6045_CCConfirmProviderIdDTMF_DM = \"yes\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.cameFrom\n          value: aa6045\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aa6045_out_01.wav\\\">Sure [audio icon] You ll be able to use your voice while making your payment</audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa6055_CCCollectAmountDTMF_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa6045_CCConfirmProviderIdDTMF_DM = \"no\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.cameFrom\n              value: aa6045\n\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/aa6045_out_02.wav\\\">Sure [audio icon] You ll be able to use your voice while making your payment</audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa6029_InitializeProviderId_JDA_DA\n```", "timestamp": **********.3718133, "content_hash": "8537e2521f8e026dac476f982f25aa45"}