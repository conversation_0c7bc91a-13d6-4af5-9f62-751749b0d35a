{"response": "```yaml\n- kind: Question\n  id: MW1225_CardAlreadyInWallet_DM\n  displayName: MW1225_CardAlreadyInWallet_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1225_nm1_01.wav\\\">This card is already saved  Would you like to make this your primary payment card?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1225_nm2_01.wav\\\">To make this your primary payment card say  yes  or press 1  or say  no  or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1225_nm2_01.wav\\\">To make this your primary payment card say  yes  or press 1  or say  no  or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.MW1225_CardAlreadyInWallet_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.tag = \"setup-autopay\",\n            \"This card is already saved in your account So to confirm you d like to use this as your primary autopay card?\",\n            Global.tag <> \"setup-autopay\",\n            \"This card is already saved in your account To confirm, you d like this to be your primary payment card?\"\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1225_nm1_01.wav\\\">This card is already saved  Would you like to make this your primary payment card?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1225_nm2_01.wav\\\">To make this your primary payment card say  yes  or press 1  or say  no  or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1225_nm2_01.wav\\\">To make this your primary payment card say  yes  or press 1  or say  no  or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.tag\n  value: GlobalVars.tag\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_MW1225_CardAlreadyInWallet_DM\n  value: =Text(Global.MW1225_CardAlreadyInWallet_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MW1225_CardAlreadyInWallet_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.fromManageCardNode\n          value: MW1225\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.tag = \"setup-autopay\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.autopay\n                  value: true\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: MW1600_UpdateSPMItem_DB_DA\n\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.defaultPaymentMethod\n              value: true\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: MW1600_UpdateSPMItem_DB_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_MW1225_CardAlreadyInWallet_DM = \"false\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.tryNewCard\n              value: true\n\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/MW1225_out_01.wav\\\">Ok, let s try another card</audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: MW1200_AskDebitOrCredit_DM\n```", "timestamp": 1749557049.3601577, "content_hash": "c26b57d268c1211196040b1267a3104d"}