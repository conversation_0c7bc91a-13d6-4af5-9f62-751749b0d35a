{"response": "```yaml\n- kind: Question\n  id: aa5030_PinChangeWrapUpDTMF_DM\n  displayName: aa5030_PinChangeWrapUpDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa5030_ini_01.wav\\\">All set You can start using your new PIN at any time If you re done, feel free to hang-up Or for the main menu, Press 1</audio>\"\n      - |\n        {Switch(\n              true,\n              Global.dnisInfo.dtmfOnlyFlag = true,\n              \"Sorry, I still didnt get that\",\n          \n              true,\n              \"Sorry, I still didnt get that Lets try it another way\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.aa5030_PinChangeWrapUpDTMF_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa5030_ini_01.wav\\\">All set You can start using your new PIN at any time If you re done, feel free to hang-up Or for the main menu, Press 1</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: main_menu\n          displayName: main_menu\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa5030_PinChangeWrapUpDTMF_DM\n  value: =Text(Global.aa5030_PinChangeWrapUpDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa5030_PinChangeWrapUpDTMF_DM = \"main_menu\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: handleMainMenu_CS\n```", "timestamp": 1749543665.438307, "content_hash": "5f20e3f372a1d2c4b68a8811118469c9"}