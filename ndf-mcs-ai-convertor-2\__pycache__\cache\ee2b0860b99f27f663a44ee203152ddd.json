{"response": "```yaml\n- kind: Question\n  id: AX1705_ExtensionTermsYN_DM\n  displayName: AX1705_ExtensionTermsYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AX1705_ini_01.wav\\\">By saying yes, you agree to THESE terms </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AX1705_ini_02.wav\\\">Do you agree to go ahead? </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AX1705_nm2_01.wav\\\">By saying yes, you agree to THESE terms </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AX1705_nm2_02.wav\\\">If you agree to go ahead, say yes or press 1 Otherwise, say no or press 2 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AX1705_nm2_01.wav\\\">By saying yes, you agree to THESE terms </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AX1705_nm2_02.wav\\\">If you agree to go ahead, say yes or press 1 Otherwise, say no or press 2 </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.AX1705_ExtensionTermsYN_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n\n            // Case 1: AX1705_operator_counter = 1\n            Global.AX1705_operator_counter = 1,\n            [\n                \"<audio src=\\\"AUDIO_LOCATION/AX1705_operator_01.wav\\\">I'm sorry, I can't transfer you right now But if you want a payment extension, I can do that right here</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/AX1705_operator_02.wav\\\">Do you agree to a *one-time* extension to restore your phone service for forty-eight hours? Please say 'yes' or 'no' </audio>\"\n            ],\n\n            // Case 2: AX1705_operator_counter = 2\n            Global.AX1705_operator_counter = 2,\n            [\n                \"<audio src=\\\"AUDIO_LOCATION/AX1705_operator_04.wav\\\">Let me try to help you here one more time </audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/AX1705_operator_05.wav\\\">I can give you a payment extension right here  </audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/AX1705_ini_01.wav\\\">By saying yes, you agree to THESE terms </audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/AX1705_ini_02.wav\\\">Do you agree to go ahead? </audio>\"\n            ],\n\n            // Default Case\n            [\n                \"<audio src=\\\"AUDIO_LOCATION/AX1705_ini_01.wav\\\">By saying yes, you agree to THESE terms </audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/AX1705_ini_02.wav\\\">Do you agree to go ahead? </audio>\"\n            ]\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/AX1705_ini_01.wav\\\">By saying yes, you agree to THESE terms </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AX1705_ini_02.wav\\\">Do you agree to go ahead? </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AX1705_nm2_01.wav\\\">By saying yes, you agree to THESE terms </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AX1705_nm2_02.wav\\\">If you agree to go ahead, say yes or press 1 Otherwise, say no or press 2 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AX1705_nm2_01.wav\\\">By saying yes, you agree to THESE terms </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AX1705_nm2_02.wav\\\">If you agree to go ahead, say yes or press 1 Otherwise, say no or press 2 </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.lang\n  value: language\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.audioMessageKey\n  value: care_configurable_extension_terms_audio\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.extensionEntryPoint\n  value: \"GlobalVars.extensionEntryPoint != undefined  ? GlobalVars.extensionEntryPoint : ''\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.care_nlu_enabled\n  value: \"GlobalVars.GetBCSParameters.care_nlu_enabled != undefined  ? GlobalVars.GetBCSParameters.care_nlu_enabled : true \"\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_AX1705_ExtensionTermsYN_DM\n  value: =Text(Global.AX1705_ExtensionTermsYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AX1705_ExtensionTermsYN_DM = \"yes\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/AX1705_out_03.wav\\\">Okay One moment while I work on that </audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_1000ms.wav\\\">test</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: AX1710_CreateUnhotlineCase_DB_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_AX1705_ExtensionTermsYN_DM = \"no\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/AX1705_out_04.wav\\\">No problem </audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_AX1705_ExtensionTermsYN_DM = \"make-payment\", true, false)\n              actions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/AX1705_out_01.wav\\\">Great</audio>\"\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.callType\n                  value: make_pmt\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.tag\n                  value: make-payment\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.acceptPayByPhone\n                  value: true\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.paymentsEntryPoint\n                  value: careSuspended\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.cti_Intent\n                  value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: getReturnLink()\n```", "timestamp": 1749527409.9756992, "content_hash": "ee2b0860b99f27f663a44ee203152ddd"}