{"response": "```yaml\n- kind: Question\n  id: ai0120_AskIMEIVisible_DM\n  displayName: ai0120_AskIMEIVisible_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ai0120_ni1_01.wav\\\">Do you see a sticker with a number labeled as the IMEI ?  Say Yes or No </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ai0120_ni2_01.wav\\\">If you see a sticker with an IMEI number on the device package, say Yes or press 1 If not, say No or press 2 </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ai0120_AskIMEIVisible_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ai0120_ini_01.wav\\\">Next, on the device package, do you see a sticker with a number labeled as the IMEI?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ai0120_ni1_01.wav\\\">Do you see a sticker with a number labeled as the IMEI ?  Say Yes or No </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ai0120_ni2_01.wav\\\">If you see a sticker with an IMEI number on the device package, say Yes or press 1 If not, say No or press 2 </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ai0120_AskIMEIVisible_DM\n  value: =Text(Global.ai0120_AskIMEIVisible_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ai0120_AskIMEIVisible_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/ai0120_out_01.wav\\\">Okay</audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_100ms.wav\\\"></audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ai0115_AskIMEI_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ai0120_AskIMEIVisible_DM = \"false\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.foundIMEI\n              value: false\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: ai0210_IsAccountType_JDA_DA\n```", "timestamp": **********.6139562, "content_hash": "ab71520219026374bf83be5133802290"}