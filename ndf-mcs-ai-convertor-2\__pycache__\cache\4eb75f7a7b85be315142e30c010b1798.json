{"response": "```yaml\n- kind: Question\n  id: AU1100_GetSecurityCode_DM\n  displayName: AU1100_GetSecurityCode_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"{Switch(\n            true,\n            Global.accountPinToggleOn = true,\n            \\\"Please say or enter your  6-to-15-digit Metro account PIN one digit at a time You can also say 'I don't' know it' \\\",\n            Global.accountPinToggleOn <> true,\n            \\\"Please say or enter your  8-digit Metro account PIN one digit at a time You can also say 'I don't' know it\\\"\n        )}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"{Switch(\n            true,\n            Global.accountPinToggleOn = true,\n            \\\"We are looking for the account PIN you set up when you first got your phone  Please enter your 6-to-15-digit account PIN or say 'I don't' know it' \\\",\n            Global.accountPinToggleOn <> true,\n            \\\"We are looking for the account PIN you set up when you first got your phone Please enter your 8-digit account PIN or say 'I don't' know it' \\\"\n        )}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"{Switch(\n            true,\n            Global.accountPinToggleOn = true,\n            \\\"We are looking for the account PIN you set up when you first got your phone  Please enter your 6-to-15-digit account PIN or say 'I don't' know it' \\\",\n            Global.accountPinToggleOn <> true,\n            \\\"We are looking for the account PIN you set up when you first got your phone Please enter your 8-digit account PIN or say 'I don't' know it' \\\"\n        )}\"\n\n  alwaysPrompt: true\n  variable: Global.AU1100_GetSecurityCode_DM_reco\n  prompt:\n    speak:\n      - \"{Switch(\n            true,\n            Global.accountPinToggleOn = true,\n            \\\"Now, I need your 6-to-15-digit account PIN\\\",\n            Global.accountPinToggleOn <> true,\n            \\\"Now, I need your 8-digit account PIN\\\"\n        )}\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: dont_know\n          displayName: dont_know\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"{Switch(\n              true,\n              Global.accountPinToggleOn = true,\n              \\\"Please say or enter your  6-to-15-digit Metro account PIN one digit at a time You can also say 'I don't' know it' \\\",\n              Global.accountPinToggleOn <> true,\n              \\\"Please say or enter your  8-digit Metro account PIN one digit at a time You can also say 'I don't' know it\\\"\n          )}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"{Switch(\n              true,\n              Global.accountPinToggleOn = true,\n              \\\"We are looking for the account PIN you set up when you first got your phone  Please enter your 6-to-15-digit account PIN or say 'I don't' know it' \\\",\n              Global.accountPinToggleOn <> true,\n              \\\"We are looking for the account PIN you set up when you first got your phone Please enter your 8-digit account PIN or say 'I don't' know it' \\\"\n          )}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"{Switch(\n              true,\n              Global.accountPinToggleOn = true,\n              \\\"We are looking for the account PIN you set up when you first got your phone  Please enter your 6-to-15-digit account PIN or say 'I don't' know it' \\\",\n              Global.accountPinToggleOn <> true,\n              \\\"We are looking for the account PIN you set up when you first got your phone Please enter your 8-digit account PIN or say 'I don't' know it' \\\"\n          )}\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.reentry\n  value: \"GlobalVars.AU1100reentry != undefined ? GlobalVars.AU1100reentry : false \"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.lastPinTry\n  value: GlobalVars.lastPinTry\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.accountPinToggleOn\n  value: GlobalVars.GetBCSParameters.accountPinToggleOn\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.playTransitionalPINprompt\n  value: false\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.AU1100reentry\n  value: true\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_AU1100_GetSecurityCode_DM\n  value: =Text(Global.AU1100_GetSecurityCode_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AU1100_GetSecurityCode_DM = \"dont_know\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/AU1100_out_01.wav\\\">No problem</audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.dontKnowPIN\n          value: true\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: AU1340_GoToTransfer_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_AU1100_GetSecurityCode_DM = \"default\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.pinAttempts\n              value: pinAttempts + 1\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.pin\n              value: AU1100_GetSecurityCode_DM.returnvalue\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.verificationType\n              value: pin\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.verificationValue\n              value: AU1100_GetSecurityCode_DM.returnvalue\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.securityCode\n              value: AU1100_GetSecurityCode_DM.returnvalue\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.cti_PIN\n              value: AU1100_GetSecurityCode_DM.returnvalue\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: AU1110_Authenticate_DB_DA\n```", "timestamp": 1749527609.8601496, "content_hash": "4eb75f7a7b85be315142e30c010b1798"}