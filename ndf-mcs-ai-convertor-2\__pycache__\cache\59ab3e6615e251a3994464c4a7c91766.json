{"response": "```yaml\n- kind: Question\n  id: aa1052_CollectLostStolenLastFourDTMF_DM\n  displayName: aa1052_CollectLostStolenLastFourDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - aa1052_CollectLostStolenLastFourDTMF_DM_initial\n\n  alwaysPrompt: true\n  variable: Global.aa1052_CollectLostStolenLastFourDTMF_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            (Global.dnisInfo.terminologyEbtCard <> null && Global.dnisInfo.terminologyEbtCard <> \"\"),\n            [\n                \"<audio src=\\\"AUDIO_LOCATION/aa1052_ini_01.wav\\\">If you know your</audio>\",\n                \"{Global.dnisInfo.terminologyEbtCard}\",\n                \"<audio src=\\\"AUDIO_LOCATION/aa1052_ini_03.wav\\\">card number, please enter the last four digits now short pause Otherwise,  press 1</audio>\"\n            ],\n            [\n                \"<audio src=\\\"AUDIO_LOCATION/aa1052_ini_01.wav\\\">If you know your</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/aa1052_ini_03.wav\\\">card number, please enter the last four digits now short pause Otherwise,  press 1</audio>\"\n            ]\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: default\n          displayName: default\n        - id: card_number_not_known\n          displayName: card_number_not_known\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/aa1052_ni1_01.wav\\\">Sorry</audio>\"\n        - aa1052_CollectLostStolenLastFourDTMF_DM_initial\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa1052_CollectLostStolenLastFourDTMF_DM\n  value: =Text(Global.aa1052_CollectLostStolenLastFourDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa1052_CollectLostStolenLastFourDTMF_DM = \"default\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.cardInfoVariables.collectedCardNumLastFour\n          value: aa1052_CollectLostStolenLastFourDTMF_DM.returnvalue\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aa1052_out_01.wav\\\">Thank you</audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa1084_CheckCardsForMatch_DB_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa1052_CollectLostStolenLastFourDTMF_DM = \"card_number_not_known\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa1054_LostStolenNeedAdditionalInfo_PP\n```", "timestamp": 1749458514.5733275, "content_hash": "59ab3e6615e251a3994464c4a7c91766"}