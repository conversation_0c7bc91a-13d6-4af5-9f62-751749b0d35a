{"response": "```yaml\n- kind: Question\n  id: BC1120_UseOtherCardYN_DM\n  displayName: BC1120_UseOtherCardYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1120_nm1_01.wav\\\">If I got your date wrong, say 'try again' Otherwise, would you like to try with a different card?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1120_nm2_01.wav\\\">This card seems to be expired If you have a different card you can use, say 'yes' or press 1 Otherwise, say 'no' or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"{Switch(\n            true,\n            Global.callType = \\\"activate\\\",\n            \\\"I can't complete your activation without a payment If you have a different card you can use, press 1 Otherwise, press 2\\\",\n            true,\n            \\\"I can't complete your payment without an active card If you have a different card you can use, press 1 Otherwise, press 2\\\"\n        )}\"\n\n  alwaysPrompt: true\n  variable: Global.BC1120_UseOtherCardYN_DM_reco\n  prompt:\n    speak:\n      - |\n        <audio src=\"AUDIO_LOCATION/BC1120_ini_01.wav\">I got </audio>\n        {Global.bankCardDate}\n        <audio src=\"AUDIO_LOCATION/silence_250ms.wav\">test</audio>\n        {Switch(\n            true,\n            Global.callType = \"activate\",\n            \"That would mean your credit card is expired If I got it wrong, say 'try again' Otherwise, I wont be able to complete your activation without a payment Would you like to use a different card?\",\n            true,\n            \"That would mean your card is expired If I got it wrong, say 'try again' Otherwise, I wont be able to complete your payment Would you like to use a different card?\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: yes\n          displayName: yes\n        - id: no\n          displayName: no\n        - id: try_again\n          displayName: try_again\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1120_nm1_01.wav\\\">If I got your date wrong, say 'try again' Otherwise, would you like to try with a different card?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1120_nm2_01.wav\\\">This card seems to be expired If you have a different card you can use, say 'yes' or press 1 Otherwise, say 'no' or press 2</audio>\"\n        - \"{Switch(\n              true,\n              Global.callType = \\\"activate\\\",\n              \\\"I can't complete your activation without a payment If you have a different card you can use, press 1 Otherwise, press 2\\\",\n              true,\n              \\\"I can't complete your payment without an active card If you have a different card you can use, press 1 Otherwise, press 2\\\"\n          )}\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.saidOperator\n  value: GlobalVars.saidOperator\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.bankCardDate\n  value: GlobalVars.bankCardDate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.callType\n  value: GlobalVars.callType\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_BC1120_UseOtherCardYN_DM\n  value: =Text(Global.BC1120_UseOtherCardYN_DM_reco)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.saidOperator\n  value: false\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_BC1120_UseOtherCardYN_DM = \"yes\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.tryOtherCardReason\n          value: card_expired\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.preferredPaymentMethod\n          value: undefined\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.PaymentTable.CARD_TYPE\n          value: not_set\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.disconfirmedDetails\n          value: false\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/BC1120_out_01.wav\\\">Alright</audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: getReturnLink()\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_BC1120_UseOtherCardYN_DM = \"no\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: BC1125_GoTo_ErrorHandling_SD\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_BC1120_UseOtherCardYN_DM = \"try_again\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.fromBC1120\n                  value: true\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: BC1105_GetExpirationDate_DM\n```", "timestamp": 1749527790.85743, "content_hash": "3bf3f07e24f0ef7559c551b5da35a1ba"}