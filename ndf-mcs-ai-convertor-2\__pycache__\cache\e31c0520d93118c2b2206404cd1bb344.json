{"response": "```yaml\n- kind: Question\n  id: aa2506_FrozenPinWrapUp_DM\n  displayName: aa2506_FrozenPinWrapUp_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"\"\n\n  alwaysPrompt: true\n  variable: Global.aa2506_FrozenPinWrapUp_DM_reco\n  prompt:\n    speak:\n      - \"To hear that again press 1 If you re done, you can end the call by hanging up Or wait for more options\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa2506_FrozenPinWrapUp_DM\n  value: =Text(Global.aa2506_FrozenPinWrapUp_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa2506_FrozenPinWrapUp_DM = \"repeat\", true, false)\n      actions: []\n```", "timestamp": **********.2851863, "content_hash": "e31c0520d93118c2b2206404cd1bb344"}