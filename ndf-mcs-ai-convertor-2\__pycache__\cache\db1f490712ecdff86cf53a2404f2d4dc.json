{"response": "```yaml\n- kind: Question\n  id: RC0115_CustomerOrCarrier_DM\n  displayName: RC0115_CustomerOrCarrier_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.RC0115_CustomerOrCarrier_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/RC0115_ini_01.wav\\\">As part of this call, and subsequent interactions from this call, we may collect information about you to improve service To learn more, go to metro by T dash Mobile dot com slash privacy If you are a customer calling from Metro or another telephone service provider, please stay on the line and we'll get you to an representative who can help If you are a rural landline carrier, please press one </audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: spanish\n          displayName: spanish\n        - id: carrier\n          displayName: carrier\n        - id: customer\n          displayName: customer\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/RC0115_ini_01.wav\\\">As part of this call, and subsequent interactions from this call, we may collect information about you to improve service To learn more, go to metro by T dash Mobile dot com slash privacy If you are a customer calling from Metro or another telephone service provider, please stay on the line and we'll get you to an representative who can help If you are a rural landline carrier, please press one </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_RC0115_CustomerOrCarrier_DM\n  value: =Text(Global.RC0115_CustomerOrCarrier_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_RC0115_CustomerOrCarrier_DM = \"spanish\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.language\n          value: es-US\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.TransferTag\n          value: RCC_Customer_Spanish\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: RC0125_MetricsRCCCustomer_JDA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_RC0115_CustomerOrCarrier_DM = \"carrier\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.TransferTag\n              value: RCC_Carrier\n\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/RC0115_out_01.wav\\\">Putting you through One moment</audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: RC0120_MetricsRCCCarrier_JDA\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_RC0115_CustomerOrCarrier_DM = \"customer\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: RC0125_MetricsRCCCustomer_JDA\n```", "timestamp": 1749529237.0172508, "content_hash": "db1f490712ecdff86cf53a2404f2d4dc"}