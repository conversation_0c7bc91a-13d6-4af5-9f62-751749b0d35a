{"response": "```yaml\n- kind: Question\n  id: TS1315_HotspotTipsWrapMenu_DM\n  displayName: TS1315_HotspotTipsWrapMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TS1315_ini_01.wav\\\">Say repeat that, or I have already tried If you are all set, you can just hang up </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TS1315_nm2_01.wav\\\">To hear those tips again, say repeat or press 1 Or say I have already tried or press 2 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TS1315_nm2_01.wav\\\">To hear those tips again, say repeat or press 1 Or say I have already tried or press 2 </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.TS1315_HotspotTipsWrapMenu_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/TS1315_ini_01.wav\\\">Say repeat that, or I have already tried If you are all set, you can just hang up </audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: repeat\n          displayName: repeat\n        - id: tried\n          displayName: tried\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/TS1315_ini_01.wav\\\">Say repeat that, or I have already tried If you are all set, you can just hang up </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/TS1315_nm2_01.wav\\\">To hear those tips again, say repeat or press 1 Or say I have already tried or press 2 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/TS1315_nm2_01.wav\\\">To hear those tips again, say repeat or press 1 Or say I have already tried or press 2 </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_TS1315_HotspotTipsWrapMenu_DM\n  value: =Text(Global.TS1315_HotspotTipsWrapMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TS1315_HotspotTipsWrapMenu_DM = \"repeat\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TS1310_PlayHotspotTips_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_TS1315_HotspotTipsWrapMenu_DM = \"tried\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/TS1315_out_01.wav\\\">Okay, our tech support team will take it from here</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: TS1010_TechSupportTransfer_SD\n```", "timestamp": 1749529852.7502697, "content_hash": "b63b9f5b3cb47a4a9611d2ad6c274803"}