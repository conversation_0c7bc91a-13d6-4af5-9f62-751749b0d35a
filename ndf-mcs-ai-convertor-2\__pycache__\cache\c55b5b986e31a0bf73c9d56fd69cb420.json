{"response": "```yaml\n- kind: Question\n  id: ca0160_PortingDisambig_DM\n  displayName: ca0160_PortingDisambig_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ca0160_ni1_01.wav\\\">To port a number IN to A T and T prepaid say port in or press 1 To port a number our from A T and T prepaid press 2 To unlock a device for transfer of service say unlock or press 3</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ca0160_ni2_01.wav\\\">To port a number in to an A T and T prepaid, press 1 To port a number out from your A T and T prepaid, press 2 To unlock a device for transfer of service, press 3</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ca0160_PortingDisambig_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ca0160_ini_01.wav\\\">Which of the following do you need help with If you are porting a number in to A T and T prepaid, say porting in  If you are porting a number out from your A T and T prepaid, say porting out  To unlock a device for transfer of service say unlock</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: porting_in\n          displayName: porting_in\n        - id: porting_out\n          displayName: porting_out\n        - id: unlock\n          displayName: unlock\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ca0160_ni1_01.wav\\\">To port a number IN to A T and T prepaid say port in or press 1 To port a number our from A T and T prepaid press 2 To unlock a device for transfer of service say unlock or press 3</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ca0160_ni2_01.wav\\\">To port a number in to an A T and T prepaid, press 1 To port a number out from your A T and T prepaid, press 2 To unlock a device for transfer of service, press 3</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ca0160_PortingDisambig_DM\n  value: =Text(Global.ca0160_PortingDisambig_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ca0160_PortingDisambig_DM = \"porting_in\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/ca0160_out_02.wav\\\">I will need to connect you to someone who can help with that</audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.nextCallerIntent\n          value: port_in\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: port_in\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.lastTransferState\n          value: ca0160_PortingDisambig_DM\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferReason\n          value: AGENT_REQUEST\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M09_MainMenu_Dialog\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ca0160_PortingDisambig_DM = \"porting_out\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.intent\n              value: port_out\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: ca0166_PortSubMenu_DM\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_ca0160_PortingDisambig_DM = \"unlock\", true, false)\n              actions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/ca0160_out_01.wav\\\">Alright, unlock</audio>\"\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.intent\n                  value: unlock\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: ca0165_UnlockInstructions_DM\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_ca0160_PortingDisambig_DM = \"repeat\", true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: ca0160_PortingDisambig_DM\n```", "timestamp": 1749470116.3849385, "content_hash": "c55b5b986e31a0bf73c9d56fd69cb420"}