{"response": "```yaml\n- kind: Question\n  id: AQ1120_ColorQuestion_DM\n  displayName: AQ1120_ColorQuestion_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/AQ1120_nm1_01.wav\\\">I'm sorry, that's not the answer I have Please try again If you want, you can also spell it</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AQ1120_nm2_01.wav\\\">That doesn't match my records either Please try one more time</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.AQ1120_ColorQuestion_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.callType = \"dsgExtension\" || Global.callType = \"switch_lines\",\n            [\n                \"The security question is your favorite color\",\n                \"test\",\n                \"If you know the answer, say it now Or say 'wait a minute'\"\n            ],\n            Global.callType <> \"dsgExtension\" && Global.callType <> \"switch_lines\",\n            [\n                \"I can also use the answer to your security question - your favorite color\",\n                \"test\",\n                \"If you know the answer, say it now Or say 'wait a minute'\"\n            ],\n            [\n                \"If you know the answer, say it now Or say 'wait a minute'\"\n            ]\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: wait\n          displayName: wait\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/AQ1120_ni1_01.wav\\\">Sorry please say the answer to your security question now You can also spell it or say 'wait a minute'</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AQ1120_ni2_01.wav\\\">Please say the answer to your security question now You can also spell it or say 'wait a minute'</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.saidOperator\n  value: \"GlobalVars.saidOperatorAQ1120?GlobalVars.saidOperatorAQ1120:false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.securityInfo\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.callType\n  value: GlobalVars.callType\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.securityQuestionCode\n  value: \"GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.securityQuestionCode:''\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.securityAnswer\n  value: \"GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.securityAnswer:''\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_maxnoinputs\n  value: \"GlobalVars.smsReceived?1:2\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_maxnomatches\n  value: \"GlobalVars.smsReceived?1:2\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.smsReceived\n  value: GlobalVars.smsReceived\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.securityInfo\n  value: \"securityAnswer + ';' + securityQuestionCode + ';' + language\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.saidOperatorAQ1120\n  value: false\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_AQ1120_ColorQuestion_DM\n  value: =Text(Global.AQ1120_ColorQuestion_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AQ1120_ColorQuestion_DM = \"wait\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: AQ1130_GetAnswerWaitSBI_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_AQ1120_ColorQuestion_DM = \"default\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.verificationType\n              value: question\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.verificationValue\n              value: AQ1120_ColorQuestion_DM.returnvalue\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: AQ1134_Authenticate_DB_DA\n```", "timestamp": 1749527717.8405883, "content_hash": "68f1839d66469ed70a0605b8a8f28e7c"}