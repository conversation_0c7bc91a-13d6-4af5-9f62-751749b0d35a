{"response": "```yaml\n- kind: Question\n  id: AC1510_ConfirmSecurityCodeYN_DM\n  displayName: AC1510_ConfirmSecurityCodeYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AC1510_nm1_01.wav\\\">Please say  yes  or  no  The security code you wanted was</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AC1510_nm1_03.wav\\\">right?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AC1510_nm2_02.wav\\\">Please say  yes  or press one or say  no  or press two  The security code you wanted was</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AC1510_nm2_05.wav\\\">right?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AC1510_nm3_01.wav\\\">If the security code you wanted was</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AC1510_nm3_04.wav\\\"> say  yes  or press one  Otherwise, say  no  or press two</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.AC1510_ConfirmSecurityCodeYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/AC1510_ini_01.wav\\\">That was</audio>\"\n      - \"{Global.securityCode}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AC1510_ini_04.wav\\\">right?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/AC1510_ni1_01.wav\\\">Please say  yes  or  no   The security code you wanted was</audio>\"\n        - \"{Global.securityCode}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AC1510_ni1_04.wav\\\">right?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AC1510_ni2_01.wav\\\">Please say  yes  or  no   The security code you wanted was</audio>\"\n        - \"{Global.securityCode}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AC1510_ni2_04.wav\\\">right?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AC1510_ni3_01.wav\\\">If the security code you wanted was</audio>\"\n        - \"{Global.securityCode}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AC1510_ni3_04.wav\\\"> say  yes  or press one  Otherwise, say  no  or press two</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.securityCode\n  value: GlobalVars.securityPinCode\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_AC1510_ConfirmSecurityCodeYN_DM\n  value: =Text(Global.AC1510_ConfirmSecurityCodeYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AC1510_ConfirmSecurityCodeYN_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/AC1510_out_01.wav\\\">Got it</audio>\"\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.Activation_Common.dvxml#AC1515_AcctDetsSMS_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(typeof(GlobalVars.getNewSC) = \"undefined\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.getNewSC\n              value: 0\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.GlobalVars.getNewSC < 2, true, false)\n              actions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/AC1510_out_02.wav\\\">Sorry about that Lets try that again</audio>\"\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.getNewSC\n                  value: GlobalVars.getNewSC + 1\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: AC1500_CreateSecurityCode_DM\n\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.activationResult\n              value: transfer\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n```", "timestamp": 1749527406.5430326, "content_hash": "c97df34f3ffea08e82e61a47446fe402"}