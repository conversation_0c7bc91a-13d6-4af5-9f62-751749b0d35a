{"response": "```yaml\n- kind: Question\n  id: sl0130_AskAlreadyReplaced_DM\n  displayName: sl0130_AskAlreadyReplaced_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/sl0130_ni1_01.wav\\\">If Youve already gotten a new device and want to transfer your account information, say Yes If not, say No</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/sl0130_ni2_01.wav\\\">If you want to move your account information to a new device, say Yes or press 1 If you havent yet gotten a new device, say No or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.sl0130_AskAlreadyReplaced_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.accountType = \"dataOnly\" || Global.accountType = \"whp\",\n            \"Have you already replaced your device?\",\n            \"Have you already replaced your phone?\"\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/sl0130_ni1_01.wav\\\">If Youve already gotten a new device and want to transfer your account information, say Yes If not, say No</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/sl0130_ni2_01.wav\\\">If you want to move your account information to a new device, say Yes or press 1 If you havent yet gotten a new device, say No or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_sl0130_AskAlreadyReplaced_DM\n  value: =Text(Global.sl0130_AskAlreadyReplaced_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_sl0130_AskAlreadyReplaced_DM = \"true\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.accountType = \"dataOnly\" || Global.accountType = \"whp\", true, false)\n              actions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/sl0130_out_01.wav\\\">Ill need to connect you to someone who can help with your replacement device</audio>\"\n          elseActions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/sl0130_out_02.wav\\\">Ill need to connect you to someone who can help with your replacement phone</audio>\"\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferReason\n          value: BUSINESS_RULE\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferPrompt\n          value: short\n\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M09_MainMenu_Dialog\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_sl0130_AskAlreadyReplaced_DM = \"false\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: sl0135_GetDeviceInfo_DB_DA\n```", "timestamp": 1749471963.3967912, "content_hash": "c01eb6be76ad9d964a4df7072b3d59ed"}