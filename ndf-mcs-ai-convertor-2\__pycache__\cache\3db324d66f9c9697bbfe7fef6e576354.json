{"response": "```yaml\n- kind: Question\n  id: DA1315_OfferDataOptions_DM\n  displayName: DA1315_OfferDataOptions_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DA1315_ini_01.wav\\\">Which would you like? A one-time top-up, or a monthly add-on? </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DA1315_ini_02.wav\\\">Which would you like? A one-time top-up, or a different plan? </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DA1315_ini_03.wav\\\">Which would you like? A monthly add-on, or a different plan? </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DA1315_ini_04.wav\\\">Which would you like? A one-time top-up a monthly add-on or  a different plan? </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DA1315_nm2_01.wav\\\">Please say one-time top-up or press 1, or monthly add-on or press 2 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DA1315_nm2_02.wav\\\">Please say one-time top-up or press 1, or a different plan or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DA1315_nm2_03.wav\\\">Please say 'monthly add-on or press 1, or a different plan or press 2 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DA1315_nm2_04.wav\\\">Please say one-time top-up or press 1, a monthly add-on or press 2, or  a different plan - 3</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DA1315_nm2_01.wav\\\">Please say one-time top-up or press 1, or monthly add-on or press 2 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DA1315_nm2_02.wav\\\">Please say one-time top-up or press 1, or a different plan or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DA1315_nm2_03.wav\\\">Please say 'monthly add-on or press 1, or a different plan or press 2 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DA1315_nm2_04.wav\\\">Please say one-time top-up or press 1, a monthly add-on or press 2, or  a different plan - 3</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.DA1315_OfferDataOptions_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.topupEligibility = \"ELIGIBLE\" && Global.eligibleForDataAddons = true && Global.isDataUnlimited = true,\n            \"Which would you like? A one-time top-up, or a monthly add-on? \",\n\n            Global.topupEligibility = \"ELIGIBLE\" && Global.eligibleForDataAddons = false && Global.isDataUnlimited = false,\n            \"Which would you like? A one-time top-up, or a different plan? \",\n\n            Global.topupEligibility = \"NOT-ELIGIBLE\" && Global.eligibleForDataAddons = true && Global.isDataUnlimited = false,\n            \"Which would you like? A monthly add-on, or a different plan? \",\n\n            Global.topupEligibility = \"ELIGIBLE\" && Global.eligibleForDataAddons = true && Global.isDataUnlimited = false,\n            \"Which would you like? A one-time top-up a monthly add-on or  a different plan? \",\n\n            \"Which would you like?\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: buy-data_topup\n          displayName: buy-data_topup\n        - id: add-feature\n          displayName: add-feature\n        - id: change-plan\n          displayName: change-plan\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/DA1315_ini_01.wav\\\">Which would you like? A one-time top-up, or a monthly add-on? </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DA1315_ini_02.wav\\\">Which would you like? A one-time top-up, or a different plan? </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DA1315_ini_03.wav\\\">Which would you like? A monthly add-on, or a different plan? </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DA1315_ini_04.wav\\\">Which would you like? A one-time top-up a monthly add-on or  a different plan? </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DA1315_nm2_01.wav\\\">Please say one-time top-up or press 1, or monthly add-on or press 2 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DA1315_nm2_02.wav\\\">Please say one-time top-up or press 1, or a different plan or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DA1315_nm2_03.wav\\\">Please say 'monthly add-on or press 1, or a different plan or press 2 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DA1315_nm2_04.wav\\\">Please say one-time top-up or press 1, a monthly add-on or press 2, or  a different plan - 3</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DA1315_nm2_01.wav\\\">Please say one-time top-up or press 1, or monthly add-on or press 2 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DA1315_nm2_02.wav\\\">Please say one-time top-up or press 1, or a different plan or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DA1315_nm2_03.wav\\\">Please say 'monthly add-on or press 1, or a different plan or press 2 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DA1315_nm2_04.wav\\\">Please say one-time top-up or press 1, a monthly add-on or press 2, or  a different plan - 3</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.topupEligibility\n  value: GlobalVars.GetAccountDetails.topupEligibility\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.eligibleForDataAddons\n  value: GlobalVars.eligibleForDataAddons\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.isDataUnlimited\n  value: \"GlobalVars.GetAccountDetails.isDataUnlimited != undefined ? GlobalVars.GetAccountDetails.isDataUnlimited  : false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_dtmfgrammar1\n  value: DA1315_OfferDataOptions_DM_dtmf.jsp\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_grammar1\n  value: DA1315_OfferDataOptions_DM.jsp\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_DA1315_OfferDataOptions_DM\n  value: =Text(Global.DA1315_OfferDataOptions_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_DA1315_OfferDataOptions_DM = \"buy-data_topup\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.tag\n          value: buy-data_topup\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: dataTopup\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: DA1205_RouteToIntent_JDA_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_DA1315_OfferDataOptions_DM = \"add-feature\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.tag\n              value: add-feature_active\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.callType\n              value: add_feature\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.cti_Intent\n              value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: DA1205_RouteToIntent_JDA_DA\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_DA1315_OfferDataOptions_DM = \"change-plan\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.tag\n                  value: change-plan\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.callType\n                  value: change_plan\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.cti_Intent\n                  value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: DA1205_RouteToIntent_JDA_DA\n```", "timestamp": 1749558308.3880467, "content_hash": "3db324d66f9c9697bbfe7fef6e576354"}