{"response": "```yaml\n- kind: Question\n  id: AS1010_OfferAcctSMS_DM\n  displayName: AS1010_OfferAcctSMS_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AS1010_nm1_01.wav\\\">Should I text you the link to finish filling out your account details? Say 'yes' or press 1, or 'no' or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AS1010_nm2_01.wav\\\">That's OK, let's move on</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.AS1010_OfferAcctSMS_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/AS1010_ini_01.wav\\\">Can I text you a link, when your phone's activated? </audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/AS1010_ni1_01.wav\\\">Should I text you the link to finish filling out your account details? Say 'yes' or press 1, or 'no' or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AS1010_ni2_01.wav\\\">That's OK, let's move on </audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_AS1010_OfferAcctSMS_DM\n  value: =Text(Global.AS1010_OfferAcctSMS_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AS1010_OfferAcctSMS_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/AS1010_out_01.wav\\\">Great</audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.acceptedAcctDetsSMS\n          value: true\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: getReturnLink()\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_AS1010_OfferAcctSMS_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/AS1010_out_02.wav\\\">No problem Don't forget to to go to metrobyt-mobilecom, and log in under My Account </audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n```", "timestamp": **********.1292222, "content_hash": "94c170e09987482d43e93085cc5d0761"}