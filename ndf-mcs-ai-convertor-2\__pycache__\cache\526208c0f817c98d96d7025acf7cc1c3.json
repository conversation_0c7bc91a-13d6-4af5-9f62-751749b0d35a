{"response": "```yaml\n- kind: Question\n  id: MW1300_GetExpirationDate_DM\n  displayName: MW1300_GetExpirationDate_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1300_nm1_01.wav\\\">What's the expiration date?  or say 'use a different card' </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1300_nm2_01.wav\\\">Please enter the four-digit expiration date  To use a different card press 9</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1300_nm2_01.wav\\\">Please enter the four-digit expiration date  To use a different card press 9</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.MW1300_GetExpirationDate_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.mw1300Help <> true && Global.expDateInPast = true,\n            \"Sorry, that expiration date is in the past  What s the expiration date again?  You can also say  use a different card \",\n            Global.mw1300Help <> true && Global.manageCardTask = \\\"updateExp\\\" && Global.expDateInPast <> true,\n            \"Ok, what s the new expiration date?\",\n            Global.mw1300Help <> true && Global.manageCardTask <> \\\"updateExp\\\" && Global.expDateInPast <> true,\n            \"And the expiration date?\",\n            \"What is the expiration date?\"\n        )}\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1300_nm1_01.wav\\\">What's the expiration date?  or say 'use a different card' </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1300_nm2_01.wav\\\">Please enter the four-digit expiration date  To use a different card press 9</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1300_nm2_01.wav\\\">Please enter the four-digit expiration date  To use a different card press 9</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.expDateInPast\n  value: GlobalVars.expDateInPast\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.manageCardTask\n  value: GlobalVars.manageCardTask\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.mw1300Help\n  value: GlobalVars.MW1300Help\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: GlobalVars.MW1300Help\n  value: false\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_MW1300_GetExpirationDate_DM\n  value: =Text(Global.MW1300_GetExpirationDate_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MW1300_GetExpirationDate_DM = \"use-different_card\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: GlobalVars.tryNewCard\n          value: true\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: GlobalVars.expDateInPast\n          value: false\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: MW1200_AskDebitOrCredit_DM\n\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: GlobalVars.bankCardDate\n      value: MW1300_GetExpirationDate_DM.returnvalue\n\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: GlobalVars.bankCardDateIsValid\n      value: MW1300_GetExpirationDate_DM.nbestresults[0].interpretation.isValid\n\n    - kind: GotoAction\n      id: goto_REPLACE_THIS\n      actionId: MW1310_CheckDateFuture_JDA\n```", "timestamp": **********.446789, "content_hash": "526208c0f817c98d96d7025acf7cc1c3"}