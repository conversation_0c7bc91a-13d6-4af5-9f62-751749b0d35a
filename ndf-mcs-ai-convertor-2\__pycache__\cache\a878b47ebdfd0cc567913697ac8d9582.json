{"response": "```yaml\n- kind: Question\n  id: UA1125_ExistingCustomerInfoYN_DM\n  displayName: UA1125_ExistingCustomerInfoYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/UA1125_nm1_01.wav\\\">Would you like to hear that again?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/UA1125_nm2_01.wav\\\">To hear that information again, say 'repeat' or press 1If you are done, you can simply hang up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/UA1125_nm2_01.wav\\\">To hear that information again, say 'repeat' or press 1If you are done, you can simply hang up</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.UA1125_ExistingCustomerInfoYN_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.existingCustomerInfoType = \"lostPhone\",\n            \"<audio src=\\\"AUDIO_LOCATION/UA1125_ini_01.wav\\\">You can fill out an insurance claim, and order a replacement phone online or you can get help at one of our stores or authorized dealers For a map of our locations near you, visit metrobyt-mobilecom</audio>\",\n            Global.existingCustomerInfoType = \"forgotMDN\",\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/UA1125_ini_02.wav\\\">To find out your phone number, you can hang up here and dial #-6-8-6-# from your Metro  phone Hit  Send  and your phone number will appear on your screen That s #-6-8-6-#</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_1000ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/UA1125_ini_04.wav\\\">You can also manage your account and make payments by dialing 611 directly from your Metro phone, with the myMetro app, or at metrobyt-mobilecom</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/UA1125_ini_05.wav\\\">If your phone is lost or damaged, you can fill out a claim and order a replacement online, or at one of our stores or authorized dealers For a map of our locations near you, visit metrobyt-mobilecom</audio>\"\n            ],\n            !(Global.existingCustomerInfoType = \"forgotMDN\"),\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/UA1125_ini_03.wav\\\">If you forgot your number, you can hang up here and dial #-6-8-6-# from your Metro  phone Hit  Send  and your phone number will appear on your screen That s #-6-8-6-#</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_1000ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/UA1125_ini_04.wav\\\">You can also manage your account and make payments by dialing 611 directly from your Metro phone, with the myMetro app, or at metrobyt-mobilecom</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/UA1125_ini_05.wav\\\">If your phone is lost or damaged, you can fill out a claim and order a replacement online, or at one of our stores or authorized dealers For a map of our locations near you, visit metrobyt-mobilecom</audio>\"\n            ],\n            \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio><audio src=\\\"AUDIO_LOCATION/UA1125_ini_06.wav\\\">Would you like to hear that again?</audio>\"\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/UA1125_nm1_01.wav\\\">Would you like to hear that again?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/UA1125_nm2_01.wav\\\">To hear that information again, say 'repeat' or press 1If you are done, you can simply hang up</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/UA1125_nm2_01.wav\\\">To hear that information again, say 'repeat' or press 1If you are done, you can simply hang up</audio>\"\n\n    defaultValueMissingAction: Esc", "timestamp": 1749529983.3893828, "content_hash": "a878b47ebdfd0cc567913697ac8d9582"}