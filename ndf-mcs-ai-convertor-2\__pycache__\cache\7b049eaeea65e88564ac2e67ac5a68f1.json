{"response": "```yaml\n- kind: Question\n  id: IU1048_AskExistingCustomer_DM\n  displayName: IU1048_AskExistingCustomer_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/IU1048_nm1_01.wav\\\">Do you currently have an existing Metro by Tmobile account?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/IU1048_nm2_01.wav\\\">If you're currently a Metro by Tmobile customer press 1 If you don't have an account press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/IU1048_nm2_01.wav\\\">If you're currently a Metro by Tmobile customer press 1 If you don't have an account press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.IU1048_AskExistingCustomer_DM_reco\n  prompt:\n    speak:\n      - \"{Switch(         true,         Global.IU1048saidOperator <> true,         \\\"I don't recognize the phone number you're calling from Are you currently an existing Metro by Tmobile customer?\\\"     )}\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/IU1048_nm1_01.wav\\\">Do you currently have an existing Metro by Tmobile account?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/IU1048_nm2_01.wav\\\">If you're currently a Metro by Tmobile customer press 1 If you don't have an account press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/IU1048_nm2_01.wav\\\">If you're currently a Metro by Tmobile customer press 1 If you don't have an account press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.requestOperatorUnidentified\n  value: GlobalVars.requestOperatorUnidentified\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.IU1048saidOperator\n  value: GlobalVars.IU1048saidOperator\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_IU1048_AskExistingCustomer_DM\n  value: =Text(Global.IU1048_AskExistingCustomer_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_IU1048_AskExistingCustomer_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.requestOperatorUnidentified\n          value: 0\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: IU1005_GetMDN_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_IU1048_AskExistingCustomer_DM = \"false\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.skipBroadcastMessage\n              value: true\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: IU1050_NewCustomerMenu_DM\n```", "timestamp": 1749530285.096207, "content_hash": "7b049eaeea65e88564ac2e67ac5a68f1"}