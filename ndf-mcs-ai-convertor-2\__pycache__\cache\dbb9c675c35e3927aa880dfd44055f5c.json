{"response": "```yaml\n- kind: Question\n  id: st0109_TechSupportPreMenu_DM\n  displayName: st0109_TechSupportPreMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/st0109_ni1_01.wav\\\">For assistance activating or changing a device or sim say Activate or press 1 For changes to your wireless number, lines on account, or ports say Account Changes or press 2 For technical support options say continue or press 3</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/st0109_ni2_01.wav\\\">For assistance activating or changing a device or sim press 1 For changes to your wireless number, lines on account, or ports press 2 For technical support options press 3</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.st0109_TechSupportPreMenu_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/st0109_ini_01.wav\\\">For assistance activating or changing a device or sim say Activate For changes to your wireless number, lines on account, or ports say Account Changes For technical support options say Continue?</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: activate\n          displayName: activate\n        - id: account_changes\n          displayName: account_changes\n        - id: continue\n          displayName: continue\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/st0109_ni1_01.wav\\\">For assistance activating or changing a device or sim say Activate or press 1 For changes to your wireless number, lines on account, or ports say Account Changes or press 2 For technical support options say continue or press 3</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/st0109_ni2_01.wav\\\">For assistance activating or changing a device or sim press 1 For changes to your wireless number, lines on account, or ports press 2 For technical support options press 3</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_st0109_TechSupportPreMenu_DM\n  value: =Text(Global.st0109_TechSupportPreMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_st0109_TechSupportPreMenu_DM = \"activate\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: st0150_CallActivate_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_st0109_TechSupportPreMenu_DM = \"account_changes\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.intent\n              value: accountChanges\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: st0125_CallAccountChanges_SD\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_st0109_TechSupportPreMenu_DM = \"continue\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: st0110_AskTechSupport_DM\n```", "timestamp": **********.7247875, "content_hash": "dbb9c675c35e3927aa880dfd44055f5c"}