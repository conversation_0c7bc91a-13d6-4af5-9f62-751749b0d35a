{"response": "```yaml\n- kind: Question\n  id: EP1110_ChooseNewPlanShort_DM\n  displayName: EP1110_ChooseNewPlanShort_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/EP1110_nm1_01.wav\\\">Say </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/EP1110_nm2_01.wav\\\">Say </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/EP1110_nm2_01.wav\\\">Say </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/EP1110_nm3_01.wav\\\"> Or say more information </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.EP1110_ChooseNewPlanShort_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.newPlanShortEntryFrom = \"EP1201_CheckAmbiguity_DS\",\n            \" There are a few plans that match that option Please say the full name of the plan you d like I ll read out your choices again  you can say \",\n            Global.newPlanShortEntryFrom = \"EP1215_DisambiguatePlan_SD_return\",\n            \" Let me read you the available plans again, in case one of them works for you If you do hear one that you want, repeat the full name after me Here they are \",\n            Global.choseInvalidPlan = true,\n            \" Let me tell you about the plans we DO have, and when you hear the one you want, just say it back to me So, tell me which plan you want \",\n            \" I ll tell you what plans are available  when you hear the one you want, just say it back to me \"\n        )}\n        {ratePlanSOCs}\n        <audio src=\"AUDIO_LOCATION/silence_500ms.wav\">test</audio>\n        <audio src=\"AUDIO_LOCATION/EP1110_ini_02.wav\"> So, which plan would you like? </audio>\n        {Switch(\n            true,\n            Global.choseInvalidPlan = true,\n            \"<audio src=\\\"AUDIO_LOCATION/EP1110_rin_04.wav\\\">If none of those are what you wanted, you can say different plan  </audio>\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: more_info\n          displayName: more_info\n        - id: different_plan\n          displayName: different_plan\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/EP1110_nm1_01.wav\\\">Say </audio>\"\n        - \"{ratePlanSOCs}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/EP1110_nm2_01.wav\\\">Say </audio>\"\n        - \"{ratePlanSOCs}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/EP1110_nm2_01.wav\\\">Say </audio>\"\n        - \"{ratePlanSOCs}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/EP1110_nm3_01.wav\\\"> Or say more information </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.ratePlanSOCs\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.cheapPlan\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.expensivePlan\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.choseInvalidPlan\n  value: GlobalVars.choseInvalidPlan\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.planGrammarURL\n  value: GlobalVars.RatePlanGrammarURL\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.planGrammarDtmfURL\n  value: GlobalVars.RatePlanDTMFGrammarURL\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.RatePlanUnambiguousGrammarURL\n  value: GlobalVars.RatePlanUnambiguousGrammarURL\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.ratePlanSOCs\n  value: getAllRatePlans(GlobalVars.ratePlans)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.cheapPlan\n  value: Math.round(getLowestRatePlanPrice(GlobalVars.ratePlans))\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.expensivePlan\n  value: Math.round(getHighestRatePlanPrice(GlobalVars.ratePlans))\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_EP1110_ChooseNewPlanShort_DM\n  value: =Text(Global.EP1110_ChooseNewPlanShort_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_EP1110_ChooseNewPlanShort_DM = \"more_info\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: EP1115_ChooseNewPlanLong_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_EP1110_ChooseNewPlanShort_DM = \"different_plan\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.ActivationTable.ACTIVATION_STATUS\n              value: '134'\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: EP1020_CallTransfer_SD\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_EP1110_ChooseNewPlanShort_DM = \"default\", true, false)\n              actions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.EP1110_ChooseNewPlanShort_DM.nbestresults <> undefined, true, false)\n                      actions:\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.GlobalVars.nbestresults\n                          value: EP1110_ChooseNewPlanShort_DM.nbestresults\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.GlobalVars.ratePlanSelectionType\n                          value: EP1110_ChooseNewPlanShort_DM.nbestresults[0].interpretation.selectionType\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.selectedPlan\n                  value: EP1110_ChooseNewPlanShort_DM.returnvalue\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.choseInvalidPlan\n                  value: undefined\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: EP1201_CheckAmbiguity_JDA\n```", "timestamp": 1749527855.2241483, "content_hash": "b34609b22daad4b134f8c61b65a86b79"}