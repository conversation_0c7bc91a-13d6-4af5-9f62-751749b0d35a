{"response": "```yaml\n- kind: Question\n  id: AQ1105_NamesQuestion_DM\n  displayName: AQ1105_NamesQuestion_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/AQ1105_nm1_01.wav\\\">I'm sorry, that's not the answer I have Please try again If you want, you can also spell it</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AQ1105_nm2_01.wav\\\">That doesn't match my records either Please try one more time</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.AQ1105_NamesQuestion_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n\n            // Q13: childhood nickname\n            Global.securityQuestionCode = \"Q13\" && (Global.callType = \"dsgExtension\" || Global.callType = \"switch_lines\"),\n            \"The security question is your childhood nickname\",\n\n            Global.securityQuestionCode = \"Q13\" && (Global.callType <> \"dsgExtension\" && Global.callType <> \"switch_lines\"),\n            \"I can also use the answer to your security question - your childhood nickname\",\n\n            // Q10: first pet\n            Global.securityQuestionCode = \"Q10\" && (Global.callType = \"dsgExtension\" || Global.callType = \"switch_lines\"),\n            \"The security question is the name of your first pet\",\n\n            Global.securityQuestionCode = \"Q10\" && (Global.callType <> \"dsgExtension\" && Global.callType <> \"switch_lines\"),\n            \"I can also use the answer to your security question - the name of your first pet\",\n\n            // SQ3: pet's name\n            Global.securityQuestionCode = \"SQ3\" && (Global.callType = \"dsgExtension\" || Global.callType = \"switch_lines\"),\n            \"The security question is your pet's name\",\n\n            Global.securityQuestionCode = \"SQ3\" && (Global.callType <> \"dsgExtension\" && Global.callType <> \"switch_lines\"),\n            \"I can also use the answer to your security question - your pet's name\",\n\n            // SQ5: school's mascot\n            Global.securityQuestionCode = \"SQ5\" && (Global.callType = \"dsgExtension\" || Global.callType = \"switch_lines\"),\n            \"The security question is the name of your school's mascot\",\n\n            Global.securityQuestionCode = \"SQ5\" && (Global.callType <> \"dsgExtension\" && Global.callType <> \"switch_lines\"),\n            \"I can also use the answer to your security question - the name of your school's mascot\",\n\n            // Q14: sibling's middle name\n            Global.securityQuestionCode = \"Q14\" && (Global.callType = \"dsgExtension\" || Global.callType = \"switch_lines\"),\n            \"The security question is your sibling's middle name\",\n\n            Global.securityQuestionCode = \"Q14\" && (Global.callType <> \"dsgExtension\" && Global.callType <> \"switch_lines\"),\n            \"I can also use the answer to your security question - your sibling's middle name\",\n\n            // SQ2: mother's maiden name\n            Global.securityQuestionCode = \"SQ2\" && (Global.callType = \"dsgExtension\" || Global.callType = \"switch_lines\"),\n            \"The security question is your mother's maiden name\",\n\n            Global.securityQuestionCode = \"SQ2\" && (Global.callType <> \"dsgExtension\" && Global.callType <> \"switch_lines\"),\n            \"I can also use the answer to your security question - your mother's maiden name\",\n\n            // Default: silence and ask for answer\n            \"test\",\n            \"If you know the answer, say it now Or say 'wait a minute'\"\n        )}\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/AQ1105_ni1_01.wav\\\">Sorry please say the answer to your security question now You can also spell it or say 'wait a minute'</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AQ1105_ni2_01.wav\\\">Please say the answer to your security question now You can also spell it or say 'wait a minute'</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.saidOperator\n  value: \"GlobalVars.saidOperatorAQ1105?GlobalVars.saidOperatorAQ1105:false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.securityInfo\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.callType\n  value: GlobalVars.callType\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.securityQuestionCode\n  value: \"GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.securityQuestionCode:''\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.securityAnswer\n  value: \"GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.securityAnswer:''\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_maxnoinputs\n  value: \"GlobalVars.smsReceived?1:2\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_maxnomatches\n  value: \"GlobalVars.smsReceived?1:2\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.securityInfo\n  value: securityAnswer + ';' + securityQuestionCode + ';' + language\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.saidOperatorAQ1105\n  value: false\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AQ1105_NamesQuestion_DM = \"wait\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: AQ1130_GetAnswerWaitSBI_DM\n\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.GlobalVars.verificationType\n      value: question\n\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.GlobalVars.verificationValue\n      value: AQ1105_NamesQuestion_DM.returnvalue\n\n    - kind: GotoAction\n      id: goto_REPLACE_THIS\n      actionId: AQ1134_Authenticate_DB_DA\n```", "timestamp": 1749527533.7331083, "content_hash": "e56a790a552b25e16cf7541c96ebd405"}