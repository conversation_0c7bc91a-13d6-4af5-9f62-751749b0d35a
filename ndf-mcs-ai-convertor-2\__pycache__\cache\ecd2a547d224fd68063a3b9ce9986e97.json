{"response": "```yaml\n- kind: Question\n  id: RP1510_PayNowYN_DM\n  displayName: RP1510_PayNowYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/RP1510_ini_01.wav\\\">Would you like to pay that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/RP1510_ini_02.wav\\\"> right now? </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/RP1510_nm2_01.wav\\\">You have a payment of </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/RP1510_nm2_02.wav\\\">due right now Would you like to pay it now over the phone? Say 'yes' or press 1 or 'No' or press 2 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.RP1510_PayNowYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/RP1510_ini_01.wav\\\">Would you like to pay that </audio>{Global.dueImmediatelyAmount}<audio src=\\\"AUDIO_LOCATION/RP1510_ini_02.wav\\\"> right now? </audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/RP1510_ini_01.wav\\\">Would you like to pay that </audio>{Global.dueImmediatelyAmount}<audio src=\\\"AUDIO_LOCATION/RP1510_ini_02.wav\\\"> right now? </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/RP1510_nm2_01.wav\\\">You have a payment of </audio>{Global.dueImmediatelyAmount}<audio src=\\\"AUDIO_LOCATION/RP1510_nm2_02.wav\\\">due right now Would you like to pay it now over the phone? Say 'yes' or press 1 or 'No' or press 2 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/RP1510_nm2_01.wav\\\">You have a payment of </audio>{Global.dueImmediatelyAmount}<audio src=\\\"AUDIO_LOCATION/RP1510_nm2_02.wav\\\">due right now Would you like to pay it now over the phone? Say 'yes' or press 1 or 'No' or press 2 </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_RP1510_PayNowYN_DM\n  value: =Text(Global.RP1510_PayNowYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_RP1510_PayNowYN_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: make_pmt\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.acceptPayByPhone\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.paymentAmount\n          value: dueImmediatelyAmount\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.paymentsEntryPoint\n          value: carePlan\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: RP1515_MakePayment_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_RP1510_PayNowYN_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/RP1510_out_01.wav\\\">Okay, just don't forget to do it today to keep your service active!  </audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: RP1520_CheckNextStep_JDA_DA\n```", "timestamp": 1749558489.080649, "content_hash": "ecd2a547d224fd68063a3b9ce9986e97"}