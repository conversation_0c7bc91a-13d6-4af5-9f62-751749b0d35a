{"response": "```yaml\n- kind: Question\n  id: pp0128_AskCurrentOrChange_DM\n  displayName: pp0128_AskCurrentOrChange_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"{CustomAudio: com.nuance.att.application.audio.pp0128_AskCurrentOrChange_DM_noinput_01}\"\n      - \"{CustomAudio: com.nuance.att.application.audio.pp0128_AskCurrentOrChange_DM_noinput_02}\"\n\n  alwaysPrompt: true\n  variable: Global.pp0128_AskCurrentOrChange_DM_reco\n  prompt:\n    speak:\n      - \"{CustomAudio: com.nuance.att.application.audio.pp0128_AskCurrentOrChange_DM_initial}\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: current_summary\n          displayName: current_summary\n        - id: make_changes\n          displayName: make_changes\n        - id: packages\n          displayName: packages\n        - id: other_options\n          displayName: other_options\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"{CustomAudio: com.nuance.att.application.audio.pp0128_AskCurrentOrChange_DM_noinput_01}\"\n        - \"{CustomAudio: com.nuance.att.application.audio.pp0128_AskCurrentOrChange_DM_noinput_02}\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_pp0128_AskCurrentOrChange_DM\n  value: =Text(Global.pp0128_AskCurrentOrChange_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pp0128_AskCurrentOrChange_DM = \"current_summary\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: pp0129_NewOrOldDescription_JDA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_pp0128_AskCurrentOrChange_DM = \"make_changes\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.fromPlansAndPackages\n              value: true\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.selectedChangePlan\n              value: true\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.selectedChangePackages\n              value: false\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.P01_PlansAndPackages2.dvxml#pp0310_PlayPlanPackageIntroIOM_SD\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_pp0128_AskCurrentOrChange_DM = \"packages\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.fromPlansAndPackages\n                  value: true\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.selectedChangePackages\n                  value: true\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.selectedChangePlan\n                  value: false\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.numberOfAutoRenewFeatures > 0 || Global.numRenewEligible > 0, true, false)\n                      actions:\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: pp0123_AutoRenewDisambig_DM\n                  elseActions:\n                    - kind: BeginDialog\n                      id: begin_REPLACE_THIS\n                      dialog: topic.P01_PlansAndPackages2.dvxml\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_pp0128_AskCurrentOrChange_DM = \"other_options\", true, false)\n                  actions:\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/pp0128_out_01.wav\\\">Alright, other options</audio>\"\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.fromPlansAndPackages\n                      value: true\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: pp0126_FollowUpMenu_DM\n```", "timestamp": 1749471653.389329, "content_hash": "bb541d467a209e40de08a2848ae3e443"}