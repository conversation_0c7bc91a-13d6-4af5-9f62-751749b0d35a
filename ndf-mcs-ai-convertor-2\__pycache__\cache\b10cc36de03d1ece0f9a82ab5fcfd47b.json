{"response": "```yaml\n- kind: Question\n  id: SE2156_PlayUnblockInfo_DM\n  displayName: SE2156_PlayUnblockInfo_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SE2156_nm1_01.wav\\\">Say Repeat that'  or If you like to receive a text with this information, say, 'send me a text'  You can also say main menu  If you're finished, feel free to hang up </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SE2156_nm2_01.wav\\\">Please say Repeat that or press 1  'send me a text' or press 2 Main menu or press 3   If you're all set, feel free to hang up </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SE2156_nm2_01.wav\\\">Please say Repeat that or press 1  'send me a text' or press 2 Main menu or press 3   If you're all set, feel free to hang up </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.SE2156_PlayUnblockInfo_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            (Global.visited_SE2156_operator = false || Global.saidRepeatSE2156 = true) && Global.tag = \"cancel-service_transfer\",\n            \"<audio src=\\\"AUDIO_LOCATION/SE2156_ini_01.wav\\\">To keep your account secure, your current settings prevent the transfer of your number to a different carrier  However, you can quickly change these settings online  Sign into your account , go to My Account and select Profile From there select, Privacy and Notification where you can adjust your account security settings as needed</audio><audio src=\\\"AUDIO_LOCATION/SE2156_ini_03.wav\\\">To hear that again say, Repeat that To receive a text with this information, say, 'send me a text'  To do something else, say 'main menu</audio>\",\n            (Global.visited_SE2156_operator = false || Global.saidRepeatSE2156 = true) && Global.tag = \"unlock-sim\",\n            \"<audio src=\\\"AUDIO_LOCATION/SE2156_ini_04.wav\\\">To keep your account secure, your current settings prevent a SIM change  However, you can quickly change these settings online  Sign into your account , go to My Account and select Profile From there select, Privacy and Notification where you can adjust your account security settings as needed</audio><audio src=\\\"AUDIO_LOCATION/SE2156_ini_03.wav\\\">To hear that again say, Repeat that To receive a text with this information, say, 'send me a text'  To do something else, say 'main menu</audio>\",\n            (Global.visited_SE2156_operator = false || Global.saidRepeatSE2156 = true) && !(Global.tag = \"cancel-service_transfer\" || Global.tag = \"unlock-sim\"),\n            \"<audio src=\\\"AUDIO_LOCATION/SE2156_ini_02.wav\\\">To keep your account secure, your current settings prevent the transfer of your number to a different carrier and prevent a SIM chagne   However, you can quickly change these settings online  Sign into your account , go to My Account and select Profile From there select, Privacy and Notification where you can adjust your account security settings as needed</audio><audio src=\\\"AUDIO_LOCATION/SE2156_ini_03.wav\\\">To hear that again say, Repeat that To receive a text with this information, say, 'send me a text'  To do something else, say 'main menu</audio>\",\n            \"<audio src=\\\"AUDIO_LOCATION/SE2156_ini_03.wav\\\">To hear that again say, Repeat that To receive a text with this information, say, 'send me a text'  To do something else, say 'main menu</audio>\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: send_text\n          displayName: send_text\n        - id: repeat\n          displayName: repeat\n        - id: main-menu\n          displayName: main-menu\n        - id: operator\n          displayName: operator\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/SE2156_nm1_01.wav\\\">Say Repeat that'  or If you like to receive a text with this information, say, 'send me a text'  You can also say main menu  If you're finished, feel free to hang up </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SE2156_nm2_01.wav\\\">Please say Repeat that or press 1  'send me a text' or press 2 Main menu or press 3   If you're all set, feel free to hang up </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SE2156_nm2_01.wav\\\">Please say Repeat that or press 1  'send me a text' or press 2 Main menu or press 3   If you're all set, feel free to hang up </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.tag\n  value: GlobalVars.tag\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.saidOperatorSE2156\n  value: GlobalVars.saidOperatorSE2156\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.saidRepeatSE2156\n  value: GlobalVars.saidRepeatSE2156\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.operatorReqCount_SE2156\n  value: GlobalVars.operatorReqCount_SE2156 != undefined ? GlobalVars.operatorReqCount_SE2156 : 0\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_SE2156_PlayUnblockInfo_DM\n  value: =Text(Global.SE2156_PlayUnblockInfo_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_SE2156_PlayUnblockInfo_DM = \"send_text\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/SE2156_out_01.wav\\\">Ok, will do </audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.campaignId\n          value: GlobalVars.GetBCSParameters.unblock_info_campaign_id\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: SE2180_SendUnblockingText_DB_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_SE2156_PlayUnblockInfo_DM = \"repeat\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.saidRepeatSE2156\n              value: true\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: SE2156_PlayUnblockInfo_DM\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_SE2156_PlayUnblockInfo_DM = \"main-menu\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: getReturnLink()\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_SE2156_PlayUnblockInfo_DM = \"operator\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.operatorReqCount_SE2156\n                  value: GlobalVars.operatorReqCount_SE2156+1\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.saidRepeatSE2156\n                  value: false\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.callerSaidOperator\n                  value: true\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.visited_SE2156_operator\n                  value: true\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.saidOperatorSE2156\n                  value: true\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.GlobalVars.operatorReqCount_SE2156 = 1, true, false)\n                      actions:\n                        - kind: SendActivity\n                          id: sendActivity_REPLACE_THIS\n                          activity:\n                            speak:\n                              - \"<audio src=\\\"AUDIO_LOCATION/SE2156_Operator_01.wav\\\">To keep your account secure, only you can make adjustments to your account restrictions online  Customer care is unable to adjust your account security preferences   You can quickly change these settings on metry by t dash moblecom  Sign in and navigate to  My Account and select Profile From there select, Privacy and Notification and then choose 'account protection' to adjust your account security settings as needed   To hear that again say, Repeat that To receive a text with this information, say, 'send me a text'  To do something else, say 'main menu </audio>\"\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: SE2156_PlayUnblockInfo_DM\n                  elseActions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: SE2175_CallTransfer_SD\n```", "timestamp": **********.7256222, "content_hash": "b10cc36de03d1ece0f9a82ab5fcfd47b"}