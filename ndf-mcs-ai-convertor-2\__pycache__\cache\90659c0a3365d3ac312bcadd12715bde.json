{"response": "```yaml\n- kind: Question\n  id: su0200_AskUnsuspendDevice_DM\n  displayName: su0200_AskUnsuspendDevice_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/su0200_ni1_01.wav\\\">Would you like to unsuspend your account now? Pleasr say Yes or No</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/su0200_ni2_01.wav\\\">If youd like to unsuspend your account, press 1 Otherwise, press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.su0200_AskUnsuspendDevice_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.intent <> \"suspendedAccount\",\n            \"It looks like your account has been suspended Would you like to unsuspend your account now?\",\n            \"Would you like to unsuspend your account now?\"\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/su0200_ni1_01.wav\\\">Would you like to unsuspend your account now? Pleasr say Yes or No</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/su0200_ni2_01.wav\\\">If youd like to unsuspend your account, press 1 Otherwise, press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_su0200_AskUnsuspendDevice_DM\n  value: =Text(Global.su0200_AskUnsuspendDevice_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_su0200_AskUnsuspendDevice_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/su0200_out_01.wav\\\">Alright, unsuspend</audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: recoveredDevice\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: su0205_UnsuspendDevice_DB_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_su0200_AskUnsuspendDevice_DM = \"false\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.intent = \"lostDevice\" || Global.intent = \"stolenDevice\", true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: su0203_AskLostStolen_DM\n              elseActions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: su0201_AskCallReasonByRequest_DM\n```", "timestamp": 1749472274.5034604, "content_hash": "90659c0a3365d3ac312bcadd12715bde"}