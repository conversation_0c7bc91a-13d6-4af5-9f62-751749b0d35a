{"response": "```yaml\n- kind: Question\n  id: br0220_AutoRefillMenu_DM\n  displayName: br0220_AutoRefillMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/br0220_AutoRefillMenu_DM_noinput_01.wav\\\">[custom audio: br0220_AutoRefillMenu_DM_noinput_01]</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/br0220_AutoRefillMenu_DM_noinput_02.wav\\\">[custom audio: br0220_AutoRefillMenu_DM_noinput_02]</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.br0220_AutoRefillMenu_DM_reco\n  prompt:\n    speak:\n      - \"[custom audio: br0220_AutoRefillMenu_DM_initial]\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: repeat\n          displayName: repeat\n        - id: change\n          displayName: change\n        - id: cancel\n          displayName: cancel\n        - id: update_exp_date\n          displayName: update_exp_date\n        - id: payment\n          displayName: payment\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/br0220_AutoRefillMenu_DM_noinput_01.wav\\\">[custom audio: br0220_AutoRefillMenu_DM_noinput_01]</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/br0220_AutoRefillMenu_DM_noinput_02.wav\\\">[custom audio: br0220_AutoRefillMenu_DM_noinput_02]</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_br0220_AutoRefillMenu_DM\n  value: =Text(Global.br0220_AutoRefillMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_br0220_AutoRefillMenu_DM = \"repeat\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: br0220_AutoRefillMenu_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_br0220_AutoRefillMenu_DM = \"change\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.autoRefillPromoQualified = true || Global.autoRefillPromoQualified = \"true\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.receivingAPCreditBeforeMigration\n                      value: true\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.autoRefillPromoQualified = false || Global.autoRefillPromoQualified = \"false\", true, false)\n                      actions:\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.receivingAPCreditBeforeMigration\n                          value: false\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.autoRefillChange\n              value: true\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: br0220_AutoRefillMenu_DM_change_JDA\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_br0220_AutoRefillMenu_DM = \"cancel\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.autoRefillChange\n                  value: false\n\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/br0220_out_02.wav\\\">Okay</audio>\"\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: br0225_AutoRefillRemove_DB_DA\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_br0220_AutoRefillMenu_DM = \"update_exp_date\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.autoRefillUpdate\n                      value: true\n\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/br0220_out_03.wav\\\">Okay</audio>\"\n\n                    - kind: BeginDialog\n                      id: begin_REPLACE_THIS\n                      dialog: topic.B03_AutoRefill_03_Dialog\n\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.Var_br0220_AutoRefillMenu_DM = \"payment\", true, false)\n                      actions:\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.lastTransferState\n                          value: br0220_AutoRefillMenu_DM\n\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.transferReason\n                          value: BUSINESS_RULE\n\n                        - kind: BeginDialog\n                          id: begin_REPLACE_THIS\n                          dialog: topic.M09_MainMenu_Dialog\n```", "timestamp": 1749469928.723205, "content_hash": "e43e66abf87d08546a14d42d7919c23f"}