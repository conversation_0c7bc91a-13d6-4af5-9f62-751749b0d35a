{"response": "```yaml\n- kind: Question\n  id: st0112_PostPaidMenu_DM\n  displayName: st0112_PostPaidMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/st0112_ni1_01.wav\\\">For assistance with DIRECTV, U-Verse, Internet or Home Phone servces, say home services or press 1 For help with wireless services other than prepaid, say wireless services or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/st0112_ni2_01.wav\\\">For assistance with DIRECTV, U-Verse, Internet or Home Phone services press 1 Otherwise, for wireless services other than prepaid, press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.st0112_PostPaidMenu_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/st0112_ini_01.wav\\\">For assistance with DIRECTV, U-verse, Internet or Home Phone services, home services, for wireless services other than prepaid, say wireless services</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: home_services\n          displayName: home_services\n\n        - id: wireless_services\n          displayName: wireless_services\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/st0112_ni1_01.wav\\\">For assistance with DIRECTV, U-Verse, Internet or Home Phone servces, say home services or press 1 For help with wireless services other than prepaid, say wireless services or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/st0112_ni2_01.wav\\\">For assistance with DIRECTV, U-Verse, Internet or Home Phone services press 1 Otherwise, for wireless services other than prepaid, press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_st0112_PostPaidMenu_DM\n  value: =Text(Global.st0112_PostPaidMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_st0112_PostPaidMenu_DM = \"home_services\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/st0112_out_01.wav\\\">Youll need to call 800 3 3 1 0 5 0 0 from a non A T and T Prepaid device for further assistance with home services</audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\"></audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/st0112_out_02.wav\\\">Again that is 800 3 3 1 0 5 0 0</audio>\"\n\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M09_MainMenu_Dialog\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_st0112_PostPaidMenu_DM = \"wireless_services\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/st0112_out_01.wav\\\">Youll need to call 800 3 3 1 0 5 0 0 from a non A T and T Prepaid device for further assistance with home services</audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\"></audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/st0112_out_02.wav\\\">Again that is 800 3 3 1 0 5 0 0</audio>\"\n\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.M09_MainMenu_Dialog\n```", "timestamp": 1749472091.0010767, "content_hash": "e20e7f54dd38500ccb8e78dba7d11ccf"}