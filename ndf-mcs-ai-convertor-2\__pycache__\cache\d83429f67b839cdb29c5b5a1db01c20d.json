{"response": "```yaml\n- kind: Question\n  id: aa2022_BalanceAdjustmentWrapUpDTMF_DM\n  displayName: aa2022_BalanceAdjustmentWrapUpDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa2022_nm1_01.wav\\\">Sorry</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.aa2022_BalanceAdjustmentWrapUpDTMF_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa2022_ini_01.wav\\\">To hear that again, press 1 To discuss this with a representative, press 2 To go the main menu, press 3 And if you re done feel free to hang-up</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: repeat\n          displayName: repeat\n        - id: main_menu\n          displayName: main_menu\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa2022_BalanceAdjustmentWrapUpDTMF_DM\n  value: =Text(Global.aa2022_BalanceAdjustmentWrapUpDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa2022_BalanceAdjustmentWrapUpDTMF_DM = \"repeat\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.balanceInquiriesVariables.skip2007\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.balanceInquiriesVariables.playedAdjustmentCount\n          value: false\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.balanceInquiriesVariables.adjustmentCountCounter\n          value: 1\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa2005_Balance_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa2022_BalanceAdjustmentWrapUpDTMF_DM = \"main_menu\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: handleMainMenu_CS\n```", "timestamp": 1749543479.50039, "content_hash": "d83429f67b839cdb29c5b5a1db01c20d"}