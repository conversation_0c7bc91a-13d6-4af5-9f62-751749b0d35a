{"response": "```yaml\n- kind: Question\n  id: RP1415_ListRatePlans_DM\n  displayName: RP1415_ListRatePlans_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/RP1415_nm1_01.wav\\\">You can say 'repeat that', 'none of those', or 'main menu'  </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/RP1415_nm2_02.wav\\\">You can say 'repeat that', 'none of those', or 'main menu'</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/RP1415_nm2_02.wav\\\">You can say 'repeat that', 'none of those', or 'main menu'</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.RP1415_ListRatePlans_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.FirstTimeRP1415 = true,\n            [\n                \"To make sure all the features on your new plan will work on your phone, you can check out metrobyt-mobilecom\",\n                \"test\",\n                \"These prices include all taxes and regulatory fees You'll get a chance to hear further plan details once you've made a selection\",\n                \"Please choose the one you're interested \",\n                \"for \"\n            ],\n            Global.FirstTimeRP1415 = false && Global.declineChangeCounter = 1,\n            [\n                \"Ok, please make another selection  or say 'dont change my plan'\",\n                \"test\",\n                \"for \"\n            ],\n            Global.reentry && Global.choseInvalidPlan = true && Global.noneOfThoseCount = 0 && !((Global.FirstTimeRP1415 = true) || (Global.FirstTimeRP1415 = false && Global.declineChangeCounter = 1)),\n            [\n                \"Here are the plans we DO have \"\n            ],\n            Global.reentry && Global.choseInvalidPlan <> true && Global.noneOfThoseCount = 0 && !((Global.FirstTimeRP1415 = true) || (Global.FirstTimeRP1415 = false && Global.declineChangeCounter = 1)),\n            [\n                \"Ok, so choose the plan you may be interested in \"\n            ],\n            Global.reentry && Global.noneOfThoseCount = 0 && !((Global.FirstTimeRP1415 = true) || (Global.FirstTimeRP1415 = false && Global.declineChangeCounter = 1)),\n            [\n                \"test\"\n            ],\n            true,\n            [\n                \"{availablePlansList}\",  # Custom dynamic prompt\n                \"test\",\n                \"You can say 'repeat that', 'none of those', or 'main menu'  \"\n            ]\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: dont_change\n          displayName: dont_change\n        - id: none_of_those\n          displayName: none_of_those\n        - id: main_menu\n          displayName: main_menu\n        - id: start_over\n          displayName: start_over\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/RP1415_nm1_01.wav\\\">You can say 'repeat that', 'none of those', or 'main menu'  </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/RP1415_nm2_02.wav\\\">You can say 'repeat that', 'none of those', or 'main menu'</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/RP1415_nm2_02.wav\\\">You can say 'repeat that', 'none of those', or 'main menu'</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.FirstTimeRP1415\n  value: GlobalVars.FirstTimeRP1415\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.choseInvalidPlan\n  value: GlobalVars.choseInvalidPlan\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.declineChangeCounter\n  value: GlobalVars.declineChangeCounter\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.revisit\n  value: \"GlobalVars.revisit == undefined ? false : GlobalVars.revisit\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.plansList\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.subPlansList\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.finalList\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.planType\n  value: Both\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.reentry\n  value: \"GlobalVars.RP1415reentry == undefined ? false : GlobalVars.RP1415reentry\"\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_RP1415_ListRatePlans_DM\n  value: =Text(Global.RP1415_ListRatePlans_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_RP1415_ListRatePlans_DM = \"dont_change\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: GlobalVars.FirstTimeRP1415\n          value: false\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: GlobalVars.RP1415reentry\n          value: true\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/RP1415_out_01.wav\\\">No problem</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: RP1335_GoToAnythingElse_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_RP1415_ListRatePlans_DM = \"none_of_those\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.noneOfThoseCount\n              value: noneOfThoseCount + 1\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: GlobalVars.revisit\n              value: true\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.noneOfThoseCount < 2, true, false)\n                  actions:\n                    - kind: ConditionGroup\n                      id: conditionGroup_REPLACE_THIS\n                      conditions:\n                        - id: conditionItem_REPLACE_THIS\n                          condition: =If(Global.ratePlans.length <= 6, true, false)\n                          actions:\n                            - kind: BeginDialog\n                              id: begin_REPLACE_THIS\n                              dialog: topic.RatePlan_Main.dvxml#RP1280_GoToCallTransfer_SD\n                      elseActions:\n                        - kind: ConditionGroup\n                          id: conditionGroup_REPLACE_THIS\n                          conditions:\n                            - id: conditionItem_REPLACE_THIS\n                              condition: =If(Global.ratePlans.length = 7, true, false)\n                              actions:\n                                - kind: SendActivity\n                                  id: sendActivity_REPLACE_THIS\n                                  activity:\n                                    speak:\n                                      - \"<audio src=\\\"AUDIO_LOCATION/RP1415_out_02.wav\\\">Ok I have one more plan For </audio>\"\n                                - kind: GotoAction\n                                  id: goto_REPLACE_THIS\n                                  actionId: RP1415_ListRatePlans_DM\n                          elseActions:\n                            - kind: ConditionGroup\n                              id: conditionGroup_REPLACE_THIS\n                              conditions:\n                                - id: conditionItem_REPLACE_THIS\n                                  condition: =If(Global.ratePlans.length > 7, true, false)\n                                  actions:\n                                    - kind: SendActivity\n                                      id: sendActivity_REPLACE_THIS\n                                      activity:\n                                        speak:\n                                          - \"<audio src=\\\"AUDIO_LOCATION/RP1415_out_03.wav\\\">Ok, here are the last ones For </audio>\"\n                                    - kind: GotoAction\n                                      id: goto_REPLACE_THIS\n                                      actionId: RP1415_ListRatePlans_DM\n                  elseActions:\n                    - kind: BeginDialog\n                      id: begin_REPLACE_THIS\n                      dialog: topic.RatePlan_Main.dvxml#RP1280_GoToCallTransfer_SD\n\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_RP1415_ListRatePlans_DM = \"main_menu\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_RP1415_ListRatePlans_DM = \"start_over\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: GlobalVars.FirstTimeRP1415\n              value: true\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: GlobalVars.RP1415reentry\n              value: false\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: GlobalVars.revisit\n              value: false\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: RP1415_ListRatePlans_DM\n\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_RP1415_ListRatePlans_DM = \"default\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.RP1415_ListRatePlans_DM.nbestresults <> undefined, true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: GlobalVars.ratePlanSelectionType\n                      value: RP1415_ListRatePlans_DM.nbestresults[0].interpretation.selectionType\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: GlobalVars.nbestresults\n                      value: RP1415_ListRatePlans_DM.nbestresults\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: GlobalVars.selectedPlan\n              value: RP1415_ListRatePlans_DM.returnvalue\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.ratePlanSelectionType <> undefined && Global.ratePlanSelectionType = \"dtmfOption\", true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: RP1416_DescribePlanAskChange_DM\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.RatePlan_GetPlan.dvxml#RP1050_CheckDisambigNeeded_DS\n```", "timestamp": 1749529229.437061, "content_hash": "3b6b32f0e177c69c116464b08801d182"}