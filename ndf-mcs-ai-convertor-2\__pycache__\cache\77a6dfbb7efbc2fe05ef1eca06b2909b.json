{"response": "```yaml\n- kind: Question\n  id: PE1220_OfferStoreOrExtension_DM\n  displayName: PE1220_OfferStoreOrExtension_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/PE1220_nm1_01.wav\\\">Please say 'find a store' or 'get an extension'</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/PE1220_nm2_01.wav\\\">Please say 'find a store' or press 1, or say 'get an extension' or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/PE1220_nm3_01.wav\\\">Please say 'find a store' or press 1, or say 'get an extension' or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.PE1220_OfferStoreOrExtension_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/PE1220_ini_01.wav\\\">You can pay cash in a store, or we can give you a payment extension so you keep your service until tomorrow Say 'find a store' or 'get an extension'</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: inquire-store_location\n          displayName: inquire-store_location\n        - id: request-extension_pe\n          displayName: request-extension_pe\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/PE1220_ini_01.wav\\\">You can pay cash in a store, or we can give you a payment extension so you keep your service until tomorrow Say 'find a store' or 'get an extension'</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/PE1220_ni2_01.wav\\\">Please say 'find a store' or press 1, or say 'get an extension' or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/PE1220_ni3_01.wav\\\">Please say 'find a store' or press 1, or say 'get an extension' or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_PE1220_OfferStoreOrExtension_DM\n  value: =Text(Global.PE1220_OfferStoreOrExtension_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_PE1220_OfferStoreOrExtension_DM = \"inquire-store_location\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.storeLocatorReason\n          value: payment\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: PE1230_FindStore_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_PE1220_OfferStoreOrExtension_DM = \"request-extension_pe\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.callType\n              value: extension\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.cti_Intent\n              value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.extensionEntryPoint\n              value: failed_payment\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/PE1220_out_01.wav\\\">Okay, let me do that for you</audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n```", "timestamp": 1749528967.167569, "content_hash": "77a6dfbb7efbc2fe05ef1eca06b2909b"}