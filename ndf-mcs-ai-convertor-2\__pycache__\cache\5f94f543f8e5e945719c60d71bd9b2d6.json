{"response": "```yaml\n- kind: Question\n  id: BB1305_WrapMenu_DM\n  displayName: BB1305_WrapMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"{Switch(\n            true,\n            Global.tag = 'payment-help' || Global.tag = 'vague-billing',\n            \\\"Say  repeat ,  pay now ,  main menu , or  back to payment options  If you re done, you can hang up\\\",\n            true,\n            \\\"Say  repeat ,  pay now ,  main menu , or  payment options  If you re done, you can hang up\\\"\n        )}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BB1305_nm2_01.wav\\\">Say  repeat  or press 1,  pay now  or press 2,  main menu  - 3, or  payment options  - 4</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BB1305_nm2_01.wav\\\">Say  repeat  or press 1,  pay now  or press 2,  main menu  - 3, or  payment options  - 4</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.BB1305_WrapMenu_DM_reco\n  prompt:\n    speak:\n      - \"{Switch(\n            true,\n            Global.tag = 'payment-help' || Global.tag = 'vague-billing',\n            \\\"Say  repeat ,  pay now ,  main menu , or  back to payment options  If you re done, you can hang up\\\",\n            true,\n            \\\"Say  repeat ,  pay now ,  main menu , or  payment options  If you re done, you can hang up\\\"\n        )}\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: repeat\n          displayName: repeat\n        - id: make-payment\n          displayName: make-payment\n        - id: main_menu\n          displayName: main_menu\n        - id: more-options\n          displayName: more-options\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"{Switch(\n              true,\n              Global.tag = 'payment-help' || Global.tag = 'vague-billing',\n              \\\"Say  repeat ,  pay now ,  main menu , or  back to payment options  If you re done, you can hang up\\\",\n              true,\n              \\\"Say  repeat ,  pay now ,  main menu , or  payment options  If you re done, you can hang up\\\"\n          )}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BB1305_nm2_01.wav\\\">Say  repeat  or press 1,  pay now  or press 2,  main menu  - 3, or  payment options  - 4</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BB1305_nm2_01.wav\\\">Say  repeat  or press 1,  pay now  or press 2,  main menu  - 3, or  payment options  - 4</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.tag\n  value: GlobalVars.tag\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_BB1305_WrapMenu_DM\n  value: =Text(Global.BB1305_WrapMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_BB1305_WrapMenu_DM = \"repeat\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: BB1310_CheckOrigin_JDA_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_BB1305_WrapMenu_DM = \"make-payment\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.callType\n              value: make_pmt\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.acceptPayByPhone\n              value: true\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.cti_Intent\n              value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: BB1315_MakePayment_SD\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_BB1305_WrapMenu_DM = \"main_menu\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.callType\n                  value: mainmenu\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: getReturnLink()\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_BB1305_WrapMenu_DM = \"more-options\", true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: BB1320_CheckOrigin_JDA_DA\n```", "timestamp": 1749558113.8925662, "content_hash": "5f94f543f8e5e945719c60d71bd9b2d6"}