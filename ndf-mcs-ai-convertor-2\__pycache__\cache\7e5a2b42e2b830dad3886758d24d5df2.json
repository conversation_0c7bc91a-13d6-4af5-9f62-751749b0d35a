{"response": "```yaml\n- kind: Question\n  id: pf0230_AskWhichPackage_DM\n  displayName: pf0230_AskWhichPackage_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/pf0230_AskWhichPackage_noinput_01.wav\\\">[custom audio: com.nuance.att.application.audio.pf0230_AskWhichPackage_noinput_01]</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/pf0230_AskWhichPackage_noinput_02.wav\\\">[custom audio: com.nuance.att.application.audio.pf0230_AskWhichPackage_noinput_02]</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.pf0230_AskWhichPackage_DM_reco\n  prompt:\n    speak:\n      - \"[custom audio: com.nuance.att.application.audio.pf0230_AskWhichPackage_initial]\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: other\n          displayName: other\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/pf0230_AskWhichPackage_noinput_01.wav\\\">[custom audio: com.nuance.att.application.audio.pf0230_AskWhichPackage_noinput_01]</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/pf0230_AskWhichPackage_noinput_02.wav\\\">[custom audio: com.nuance.att.application.audio.pf0230_AskWhichPackage_noinput_02]</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.returnCode\n  value: GetPackageOptionList.returnCode\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.returnCode = 0, true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_dtmfgrammar1\n          value: =Text('pf0230_AskWhichPackage_DM_dtmf.jsp' + GetPackageOptionList.packageOptionString)\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_grammar1\n          value: =Text('pf0230_AskWhichPackage_DM.jsp' + GetPackageOptionList.packageOptionString)\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_pf0230_AskWhichPackage_DM\n  value: =Text(Global.pf0230_AskWhichPackage_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pf0230_AskWhichPackage_DM = \"other\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.fromPlansAndPackages = true && Global.pbNumPlans > 0, true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: getReturnLink()\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.choseOtherPackages\n              value: true\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: pf0220_AskWhichCategory_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_pf0230_AskWhichPackage_DM = \"default\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.choseOtherPackages\n              value: false\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.packageCode\n              value: pf0230_AskWhichPackage_DM.returnvalue\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: pf0230_AskWhichPackage_DM_JDA_DA\n```", "timestamp": 1749471777.6440425, "content_hash": "7e5a2b42e2b830dad3886758d24d5df2"}