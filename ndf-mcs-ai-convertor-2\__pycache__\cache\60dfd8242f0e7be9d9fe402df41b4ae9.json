{"response": "```yaml\n- kind: Question\n  id: cl0110_AskSaveLanguage_DM\n  displayName: cl0110_AskSaveLanguage_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.accountLanguage = \"spanish\",\n              \"To change your account language to English , say Yes To leave it set to Spanish, say No\",\n              true,\n              \"To change your account language to Spanish , say Yes To leave it set to English, say No\"\n          )\n        }\n      - |\n        {Switch(\n              true,\n              Global.accountLanguage = \"spanish\",\n              \"If yuod like us to communicate with you in English from now on, say Yes or press 1  To continue using Spanish, say No or press 2\",\n              true,\n              \"If yuod like us to communicate with you in Spanish from now on, say Yes or press 1  To continue using English, say No or press 2\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.cl0110_AskSaveLanguage_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.accountLanguage = \"spanish\",\n              \"Would you like me to change your account language setting to English?\",\n              true,\n              \"Your account language is already set to English Would you like me to change that to Spanish?\"\n          )\n        }\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.accountLanguage = \"spanish\",\n                \"To change your account language to English , say Yes To leave it set to Spanish, say No\",\n                true,\n                \"To change your account language to Spanish , say Yes To leave it set to English, say No\"\n            )\n          }\n        - |\n          {Switch(\n                true,\n                Global.accountLanguage = \"spanish\",\n                \"If yuod like us to communicate with you in English from now on, say Yes or press 1  To continue using Spanish, say No or press 2\",\n                true,\n                \"If yuod like us to communicate with you in Spanish from now on, say Yes or press 1  To continue using English, say No or press 2\"\n            )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_cl0110_AskSaveLanguage_DM\n  value: =Text(Global.cl0110_AskSaveLanguage_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.accountLanguage = \"undefined\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.accountLanguage\n          value: systemLanguage\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: |\n            =If(Global.Var_cl0110_AskSaveLanguage_DM = \"true\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: |\n                    =If(Global.accountLanguage = \"english\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.ivrLanguage\n                      value: spanish\n              elseActions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.ivrLanguage\n                  value: english\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: cl0115_LanguageNeedsLogin_JDA_DA\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.Var_cl0110_AskSaveLanguage_DM = \"false\", true, false)\n              actions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: |\n                        =If(Global.accountLanguage = \"english\", true, false)\n                      actions:\n                        - kind: SendActivity\n                          id: sendActivity_REPLACE_THIS\n                          activity:\n                            speak:\n                              - \"<audio src=\\\"AUDIO_LOCATION/cl0110_out_01.wav\\\">No problem, well keep your account set to English</audio>\"\n                  elseActions:\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/cl0110_out_02.wav\\\">No problem, well keep your account set to Spanish</audio>\"\n\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\"></audio>\"\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: cl0135_ChangeLanguageWrapMenu_DM\n```", "timestamp": **********.7727947, "content_hash": "60dfd8242f0e7be9d9fe402df41b4ae9"}