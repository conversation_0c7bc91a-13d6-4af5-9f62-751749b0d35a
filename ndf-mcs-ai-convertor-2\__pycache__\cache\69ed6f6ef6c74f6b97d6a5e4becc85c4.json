{"response": "```yaml\n- kind: Question\n  id: IH2110_AskWantSMSYN_DM\n  displayName: IH2110_AskWantSMSYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.IH2110_AskWantSMSYN_DM_reco\n  prompt:\n    speak:\n      - \"{Global.broadcastMessageKey} <audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_IH2110_AskWantSMSYN_DM\n  value: =Text(Global.IH2110_AskWantSMSYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_IH2110_AskWantSMSYN_DM = \"yes\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.broadcastMessageKey\n          value: CARE_SMS\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: IH2115_SendConfigurabeSMS_DB_DA\n```", "timestamp": 1749528527.3445246, "content_hash": "69ed6f6ef6c74f6b97d6a5e4becc85c4"}