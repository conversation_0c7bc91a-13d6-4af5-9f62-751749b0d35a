{"response": "```yaml\n- kind: Question\n  id: DH1235_DeviceCarrierYN_DM\n  displayName: DH1235_DeviceCarrierYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DH1235_ini_01.wav\\\">Is that phone from another wireless carrier?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DH1235_ini_02.wav\\\">Is the phone you want to switch to locked by another wireless carrier? You can also say more information </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DH1235_nm2_01.wav\\\">If the device you would like to use on the new line is from another wireless carrier, say yes or press 1 Otherwise, say no or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DH1235_nm2_02.wav\\\">If the device you would like to switch to might be locked by another wireless carrier, say yes or press 1 Otherwise, say no or press 2, or more information or press star </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DH1235_nm2_01.wav\\\">If the device you would like to use on the new line is from another wireless carrier, say yes or press 1 Otherwise, say no or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DH1235_nm2_02.wav\\\">If the device you would like to switch to might be locked by another wireless carrier, say yes or press 1 Otherwise, say no or press 2, or more information or press star </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.DH1235_DeviceCarrierYN_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.enteringFrom <> \"DH1235_DeviceCarrierYN_DM\" && Global.callType = \"add_line\",\n            \"Is that phone from another wireless carrier?\",\n            Global.enteringFrom <> \"DH1235_DeviceCarrierYN_DM\" && Global.callType <> \"add_line\",\n            \"Is the phone you want to switch to locked by another wireless carrier? You can also say more information \",\n            \"Is that phone from another wireless carrier?\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: yes\n          displayName: yes\n        - id: no\n          displayName: no\n        - id: dont_know\n          displayName: dont_know\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/DH1235_ini_01.wav\\\">Is that phone from another wireless carrier?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DH1235_ini_02.wav\\\">Is the phone you want to switch to locked by another wireless carrier? You can also say more information </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DH1235_nm2_01.wav\\\">If the device you would like to use on the new line is from another wireless carrier, say yes or press 1 Otherwise, say no or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DH1235_nm2_02.wav\\\">If the device you would like to switch to might be locked by another wireless carrier, say yes or press 1 Otherwise, say no or press 2, or more information or press star </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DH1235_nm2_01.wav\\\">If the device you would like to use on the new line is from another wireless carrier, say yes or press 1 Otherwise, say no or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DH1235_nm2_02.wav\\\">If the device you would like to switch to might be locked by another wireless carrier, say yes or press 1 Otherwise, say no or press 2, or more information or press star </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.callType\n  value: GlobalVars.callType\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.isSwitchPhone\n  value: \"GlobalVars.callType == 'switch_phone' ? true : false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.stateVar\n  value: \"\"\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_DH1235_DeviceCarrierYN_DM\n  value: =Text(Global.DH1235_DeviceCarrierYN_DM_reco)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.enteringFrom\n  value: \"\"\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_DH1235_DeviceCarrierYN_DM = \"yes\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: DH1238_BYODDevice_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_DH1235_DeviceCarrierYN_DM = \"no\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.callType = \"add_line\", true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: DH1240_OtherDeviceHandling_PP\n\n              elseActions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/DH1235_out_01.wav\\\">Okay</audio>\"\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.esnChangeVars.eventTypeGMT\n                  value: getEventTime()\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.esnChangeVars.status\n                  value: incomplete\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.esnChangeVars.eventType\n                  value: esn_change\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.ActivationTable.ACTIVATION_TYPE\n                  value: 11\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.ActivationTable.ACTIVATION_STARTED\n                  value: getGMTTime()\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.ActivationTable.ACTIVATION_STATUS\n                  value: 130\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: DH1301_CheckContext_JDA\n\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_DH1235_DeviceCarrierYN_DM = \"dont_know\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/DH1235_out_02.wav\\\">Alright, then please check with your old wireless carrier, and ask them to unlock the phone for you </audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: DH1238_BYODDevice_DM\n```", "timestamp": 1749528287.355072, "content_hash": "7a2b4a225467e78bece600b5ebd335b7"}