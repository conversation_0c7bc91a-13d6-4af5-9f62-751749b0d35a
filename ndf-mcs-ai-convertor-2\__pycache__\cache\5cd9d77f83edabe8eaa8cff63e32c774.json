{"response": "```yaml\n- kind: Question\n  id: BC1205_GetVerificationCode_DM\n  displayName: BC1205_GetVerificationCode_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1205_nm1_01.wav\\\">Please enter the verification code, including any zeros Or say 'more info'</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1205_nm2_01.wav\\\">Please enter the four-digit verification code on the front of your card You can also say 'more info' or press star</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1205_nm2_02.wav\\\">Please enter the three-digit verification code on the back of your card You can also say 'more info' or press star </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1205_nm3_01.wav\\\">Please enter the verification code on your card For more information, press star</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.BC1205_GetVerificationCode_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n\n            // Case: fromBC1110 = true && voiceOrDtmf = \"voice\"\n            Global.fromBC1110 = true && Global.voiceOrDtmf = \"voice\",\n            [\n                \"okay\",\n                \"{Global.bankCardDateFirst2Digits}\",\n                \"{Global.bankCardDateLast2Digits}\",\n                \"test\"\n            ],\n\n            // Case: providedCVV <> true && cardTypeAmex = true\n            Global.providedCVV <> true && Global.cardTypeAmex = true,\n            \"Now, the four-digit verification code\",\n\n            // Case: providedCVV <> true && cardTypeAmex <> true\n            Global.providedCVV <> true && Global.cardTypeAmex <> true,\n            \"Now, the three digit verification code on the back of the card\",\n\n            // Default\n            [\n                \"test\",\n                \"You can also say 'more info'\"\n            ]\n        )}\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1205_nm1_01.wav\\\">Please enter the verification code, including any zeros Or say 'more info'</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1205_nm2_01.wav\\\">Please enter the four-digit verification code on the front of your card You can also say 'more info' or press star</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1205_nm2_02.wav\\\">Please enter the three-digit verification code on the back of your card You can also say 'more info' or press star </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1205_nm3_01.wav\\\">Please enter the verification code on your card For more information, press star</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.saidOperator\n  value: \"GlobalVars.saidOperatorBC1205?GlobalVars.saidOperatorBC1205:false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.preferredPaymentMethod\n  value: GlobalVars.preferredPaymentMethod\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.failedChecksum\n  value: GlobalVars.failedChecksum\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.disconfirmedDetails\n  value: GlobalVars.disconfirmedDetails\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.correctAllDetails\n  value: GlobalVars.correctAllDetails\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.bankCardDate\n  value: GlobalVars.bankCardDate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.bankCardDateFirst2Digits\n  value: \"(bankCardDate!=null && bankCardDate !=undefined)?bankCardDate.substring(0, 2):bankCardDate\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.bankCardDateLast2Digits\n  value: \"(bankCardDate!=null && bankCardDate !=undefined)?bankCardDate.substring(2, 4):bankCardDate\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.fromBC1110\n  value: GlobalVars.fromBC1110\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.providedCVV\n  value: GlobalVars.providedCVV\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.cardTypeAmex\n  value: \"GlobalVars.cardTypeAmex ? GlobalVars.cardTypeAmex : false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.implicitConfirmReject\n  value: GlobalVars.implicitConfirmReject\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.voiceOrDtmf\n  value: application.lastresult$.inputmode\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.fromBC1110\n  value: undefined\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.FromBC1205\n  value: true\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.saidOperatorBC1205\n  value: false\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.implicitConfirmReject\n  value: false\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_BC1205_GetVerificationCode_DM = \"thats_not_right\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.implicitConfirmReject\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.bankCardDate\n          value: \"\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: BC1105_GetExpirationDate_DM\n\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.GlobalVars.providedCVV\n      value: true\n\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.GlobalVars.bankCardCVV\n      value: BC1205_GetVerificationCode_DM.returnvalue\n\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.GlobalVars.VerificationCode_voiceOrDtmf\n      value: application.lastresult$.inputmode\n\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.GlobalVars.disconfirmedDetails = true && Global.GlobalVars.correctAllDetails = false, true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: BC1401_CheckContext_JDA\n\n      elseActions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: BC1301_CheckContext_JDA\n```", "timestamp": **********.4676437, "content_hash": "5cd9d77f83edabe8eaa8cff63e32c774"}