{"response": "```yaml\n- kind: Question\n  id: FQ1010_BillingPayments_DM\n  displayName: FQ1010_BillingPayments_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/FQ1010_nm1_01.wav\\\">Which would you like help with please say  'Statements', 'Ways to Pay', 'Cost of Changing Plans', 'Cost of Adding Features' or 'it's none of those'</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/FQ1010_nm2_01.wav\\\">For help understanding your statement and due date, say 'statement' or press one To hear about payment methods, say 'Ways to Pay', or press two To find out how changing plans will affect your charges, say 'Cost of Changing Plans' or press three For how adding features will affect your charges, say 'Cost of Adding Features' or press four  If none of those topics will help you, say 'none of those' or press five  Or say 'go back' or press star to go back and choose a different category</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/FQ1010_nm2_01.wav\\\">For help understanding your statement and due date, say 'statement' or press one To hear about payment methods, say 'Ways to Pay', or press two To find out how changing plans will affect your charges, say 'Cost of Changing Plans' or press three For how adding features will affect your charges, say 'Cost of Adding Features' or press four  If none of those topics will help you, say 'none of those' or press five  Or say 'go back' or press star to go back and choose a different category</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.FQ1010_BillingPayments_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/FQ1010_ini_01.wav\\\">Say 'Statements', 'Ways to Pay', 'Cost of Changing Plans' or 'Cost of Adding Features'</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_3000ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/FQ1010_ini_03.wav\\\">Or say  it s none of those  or, to go back and choose a different category, say  go back </audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: billing_info\n          displayName: billing_info\n        - id: ways_to_pay\n          displayName: ways_to_pay\n        - id: cost_of_changing_plans\n          displayName: cost_of_changing_plans\n        - id: cost_of_adding_features\n          displayName: cost_of_adding_features\n        - id: none_of_those\n          displayName: none_of_those\n        - id: go_back\n          displayName: go_back\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/FQ1010_nm1_01.wav\\\">Which would you like help with please say  'Statements', 'Ways to Pay', 'Cost of Changing Plans', 'Cost of Adding Features' or 'it's none of those'</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/FQ1010_nm2_01.wav\\\">For help understanding your statement and due date, say 'statement' or press one To hear about payment methods, say 'Ways to Pay', or press two To find out how changing plans will affect your charges, say 'Cost of Changing Plans' or press three For how adding features will affect your charges, say 'Cost of Adding Features' or press four  If none of those topics will help you, say 'none of those' or press five  Or say 'go back' or press star to go back and choose a different category</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/FQ1010_nm2_01.wav\\\">For help understanding your statement and due date, say 'statement' or press one To hear about payment methods, say 'Ways to Pay', or press two To find out how changing plans will affect your charges, say 'Cost of Changing Plans' or press three For how adding features will affect your charges, say 'Cost of Adding Features' or press four  If none of those topics will help you, say 'none of those' or press five  Or say 'go back' or press star to go back and choose a different category</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_FQ1010_BillingPayments_DM\n  value: =Text(Global.FQ1010_BillingPayments_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_FQ1010_BillingPayments_DM = \"billing_info\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.faq\n          value: billing_info\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: FQ1025_ReadAnswers_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_FQ1010_BillingPayments_DM = \"ways_to_pay\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.faq\n              value: ways_to_pay\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: FQ1025_ReadAnswers_PP\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_FQ1010_BillingPayments_DM = \"cost_of_changing_plans\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.faq\n                  value: cost_of_changing_plans\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: FQ1025_ReadAnswers_PP\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_FQ1010_BillingPayments_DM = \"cost_of_adding_features\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.faq\n                      value: cost_of_adding_features\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: FQ1025_ReadAnswers_PP\n\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.Var_FQ1010_BillingPayments_DM = \"none_of_those\", true, false)\n                      actions:\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: FQ1035_GoToCallTransfer_SD\n\n                  elseActions:\n                    - kind: ConditionGroup\n                      id: conditionGroup_REPLACE_THIS\n                      conditions:\n                        - id: conditionItem_REPLACE_THIS\n                          condition: =If(Global.Var_FQ1010_BillingPayments_DM = \"go_back\", true, false)\n                          actions:\n                            - kind: GotoAction\n                              id: goto_REPLACE_THIS\n                              actionId: FQ1005_NarrowDownFAQTopic_DM\n```", "timestamp": 1749528410.8563373, "content_hash": "d0942e03c51101b8b34d2851c83188b6"}