{"response": "```yaml\n- kind: Question\n  id: ss0113_AskCallReasonLost_DM\n  displayName: ss0113_AskCallReasonLost_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ss0113_ni1_01.wav\\\">If youre calling regarding your recovered device say recovered To manage your account say main menu</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ss0113_ni2_01.wav\\\">If youre calling regarding your recovered device say recovered or press 1 To manage your account say main menu or press star</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ss0113_AskCallReasonLost_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ss0113_ini_01.wav\\\">Just so you know you wont be able to fully manage or access this account while it is suspended You can say unsuspend or main menu</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: recovered\n          displayName: recovered\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ss0113_ni1_01.wav\\\">If youre calling regarding your recovered device say recovered To manage your account say main menu</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ss0113_ni2_01.wav\\\">If youre calling regarding your recovered device say recovered or press 1 To manage your account say main menu or press star</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ss0113_AskCallReasonLost_DM\n  value: =Text(Global.ss0113_AskCallReasonLost_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ss0113_AskCallReasonLost_DM = \"recovered\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: recoveredDevice\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ss0114_LostStolenCall_SD\n```", "timestamp": **********.6430387, "content_hash": "c1ab94462b3487ba562b7d4f189bb492"}