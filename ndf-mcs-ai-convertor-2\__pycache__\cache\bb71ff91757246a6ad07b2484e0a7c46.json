{"response": "```yaml\n- kind: Question\n  id: IU1050_NewCustomerMenu_DM\n  displayName: IU1050_NewCustomerMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/IU1050_nm1_01.wav\\\">If you already have a device that you need to activate, say 'activate my device'or press 1  If  you're interested in becoming a new Metro by T-Mobile customer and need to order a new device or equipment, say,'New Customer' or press 2You can also say, I'm calling about something else' or press 3</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/IU1050_nm2_01.wav\\\">If you're calling to activate a device press 1If you're interested in becoming a Metro byTmobile customer and are looking to purchase a device or equipment press 2  If you're calling for something else press 3</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/IU1050_nm2_01.wav\\\">If you're calling to activate a device press 1If you're interested in becoming a Metro byTmobile customer and are looking to purchase a device or equipment press 2  If you're calling for something else press 3</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.IU1050_NewCustomerMenu_DM_reco\n  prompt:\n    speak:\n      - \"{Switch(         true,         Global.IU1050saidOperator <> true,         \\\"If you already have a device that you need to activate, say 'activate my device'  Or, if you're interested in becoming a new Metro by T-Mobile customer and need to order a new device or equipment, say, 'New Customer'\\\"     )}\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: activate-phone_nc\n          displayName: activate-phone_nc\n        - id: purchase-phone_nc\n          displayName: purchase-phone_nc\n        - id: something-else_nc\n          displayName: something-else_nc\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/IU1050_nm1_01.wav\\\">If you already have a device that you need to activate, say 'activate my device'or press 1  If  you're interested in becoming a new Metro by T-Mobile customer and need to order a new device or equipment, say,'New Customer' or press 2You can also say, I'm calling about something else' or press 3</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/IU1050_nm2_01.wav\\\">If you're calling to activate a device press 1If you're interested in becoming a Metro byTmobile customer and are looking to purchase a device or equipment press 2  If you're calling for something else press 3</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/IU1050_nm2_01.wav\\\">If you're calling to activate a device press 1If you're interested in becoming a Metro byTmobile customer and are looking to purchase a device or equipment press 2  If you're calling for something else press 3</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.requestOperatorUnidentified\n  value: GlobalVars.requestOperatorUnidentified\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.IU1050saidOperator\n  value: GlobalVars.IU1050saidOperator\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_IU1050_NewCustomerMenu_DM\n  value: =Text(Global.IU1050_NewCustomerMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_IU1050_NewCustomerMenu_DM = \"activate-phone_nc\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.requestOperatorUnidentified\n          value: 0\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: IU1051_AskMetroDevice_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_IU1050_NewCustomerMenu_DM = \"purchase-phone_nc\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.callType\n              value: transfer\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.language = \"en-US\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.TransferTag\n                      value: New_Customer_Sales_en\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: IU1040_CallTransfer_SD\n              elseActions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.TransferTag\n                  value: New_Customer_Sales_Es\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.tag\n                  value: purchase-phone_nc\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: IU1040_CallTransfer_SD\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_IU1050_NewCustomerMenu_DM = \"something-else_nc\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: IU1040_CallTransfer_SD\n```", "timestamp": 1749530305.3991125, "content_hash": "bb71ff91757246a6ad07b2484e0a7c46"}