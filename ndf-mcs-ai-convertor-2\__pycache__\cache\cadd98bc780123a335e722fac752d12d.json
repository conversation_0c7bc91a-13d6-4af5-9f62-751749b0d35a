{"response": "```yaml\n- kind: Question\n  id: bc0415_AskBillingNumber_DM\n  displayName: bc0415_AskBillingNumber_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bc0415_ni1_01.wav\\\">Please say or enter the phone number thats associated with your bank account</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/bc0415_ni2_01.wav\\\">Please enter the phone number that s associated with your bank account</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.bc0415_AskBillingNumber_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bc0415_ini_01.wav\\\">First, what phone number does your bank have on file for you?</audio>\"\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/bc0415_ni1_01.wav\\\">Please say or enter the phone number thats associated with your bank account</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/bc0415_ni2_01.wav\\\">Please enter the phone number that s associated with your bank account</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_bc0415_AskBillingNumber_DM\n  value: =Text(Global.bc0415_AskBillingNumber_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_bc0415_AskBillingNumber_DM = \"dont_have\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/bc0415_out_01.wav\\\">Okay I will need to connect you to someone who can assist</audio>\"\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferPrompt\n          value: short\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferDestination\n          value: vestaRevenueAssurance\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferReason\n          value: BUSINESS_RULE\n\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M09_MainMenu.dvxml#ma9210_NeedTransferType_DS\n\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.bankPhoneNumber\n      value: bc0415_AskBillingNumber_DM.returnvalue\n\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.frombc0415\n      value: true\n\n    - kind: GotoAction\n      id: goto_REPLACE_THIS\n      actionId: bc0420_AskContactNumber_DM\n```", "timestamp": **********.80159, "content_hash": "cadd98bc780123a335e722fac752d12d"}