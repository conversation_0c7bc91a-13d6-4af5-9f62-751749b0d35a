{"response": "```yaml\n- kind: Question\n  id: MW1510_AskIfPrimary_DM\n  displayName: MW1510_AskIfPrimary_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1510_ini_01.wav\\\">Would you like this new card to be used as your new primary autopay card?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1510_nm2_01.wav\\\">If you d like to use this card as you primary autopay pay card say  yes  or press 1  or say  no  or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1510_nm2_01.wav\\\">If you d like to use this card as you primary autopay pay card say  yes  or press 1  or say  no  or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.MW1510_AskIfPrimary_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1510_ini_01.wav\\\">Would you like this new card to be used as your new primary autopay card?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1510_ini_01.wav\\\">Would you like this new card to be used as your new primary autopay card?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1510_nm2_01.wav\\\">If you d like to use this card as you primary autopay pay card say  yes  or press 1  or say  no  or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1510_nm2_01.wav\\\">If you d like to use this card as you primary autopay pay card say  yes  or press 1  or say  no  or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_MW1510_AskIfPrimary_DM\n  value: =Text(Global.MW1510_AskIfPrimary_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MW1510_AskIfPrimary_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.autopay\n          value: true\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: MW1602_CreateSPMItem_DB_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_MW1510_AskIfPrimary_DM = \"false\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: MW1602_CreateSPMItem_DB_DA\n```", "timestamp": 1749557093.256463, "content_hash": "588b8539393584d73109d0d65252cf89"}