{"response": "```yaml\n- kind: Question\n  id: cd0110_ReplaceSIM_DM\n  displayName: cd0110_ReplaceSIM_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I missed that</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/cd0110_ni1_01.wav\\\">Have you already tried moving your SIM card from your old device to your new device? Please say Yes or No You can also say Help Me Find It</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">Sorry</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/cd0110_ni2_01.wav\\\">In order to replace your device, you ll need to move your current SIM card to your new device If you ve already tried to move it, say Yes or press 1 If you ve *not* yet tried to move it, say No or press 2 Or if you need help *finding* your SIM card, press 3</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.cd0110_ReplaceSIM_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n            \"Your SIM card stores your account information, so in most cases you can change phones by taking that SIM card out and putting it into your new phone\",\n            Global.accountType = \"dataOnly\",\n            \"Your SIM card stores your account information, so in most cases you can change devices by taking that SIM card out and putting it into your new device\",\n            Global.accountType = \"whp\",\n            \"Your SIM card stores your account information, so in most cases you can change your Wireless Home Services device by taking that SIM card out and putting it into your new device\",\n            \"Have you tried that already?\"\n        )}\n\n  entity: \n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: yes\n          displayName: yes\n        - id: no\n          displayName: no\n        - id: help\n          displayName: help\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/cd0110_ni1_01.wav\\\">Have you already tried moving your SIM card from your old device to your new device? Please say Yes or No You can also say Help Me Find It</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/cd0110_ni2_01.wav\\\">In order to replace your device, you ll need to move your current SIM card to your new device If you ve already tried to move it, say Yes or press 1 If you ve *not* yet tried to move it, say No or press 2 Or if you need help *finding* your SIM card, press 3</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.cd0115lastDM\n  value: cd0110\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.cd0210lastDM\n  value: cd0110\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_cd0110_ReplaceSIM_DM\n  value: =Text(Global.cd0110_ReplaceSIM_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_cd0110_ReplaceSIM_DM = \"yes\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/cd0110_out_01.wav\\\">Alright</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: cd0210_AskSIMFits_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_cd0110_ReplaceSIM_DM = \"no\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/cd0110_out_02.wav\\\">Okay</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: cd0115_OldSIMWrap_DM\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_cd0110_ReplaceSIM_DM = \"help\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: cd0120_OldSIMHelp_PP\n```", "timestamp": 1749470365.7651217, "content_hash": "f602ac67209f0885af35a6e6c5258a02"}