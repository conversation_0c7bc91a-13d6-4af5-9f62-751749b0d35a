{"response": "```yaml\n- kind: Question\n  id: dp0110_MobileProtection_DM\n  displayName: dp0110_MobileProtection_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I missed that</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/dp0110_ni1_01.wav\\\">To get help with Mobile Protection and speak with an Asurion representative, say Continue or press 1 Or to go back to the Main Menu, say Main Menu or press star</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">Sorry</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/dp0110_ni2_01.wav\\\">To speak with an Asurion representative about Mobile Protection, press 1 Or to go back to the Main Menu, press star</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.dp0110_MobileProtection_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/dp0110_ini_01.wav\\\">Mobile Protection for A T and T PREPAID provides comprehensive device coverage if your device is lost, stolen or damaged as well as access to a US based ProTech expert when you need it</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\"></audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/dp0110_ini_02.wav\\\">To get help with Mobile Protection and speak with an Asurion representative, say Continue Or to go back to the Main Menu, say Main Menu</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/dp0110_ni1_01.wav\\\">To get help with Mobile Protection and speak with an Asurion representative, say Continue or press 1 Or to go back to the Main Menu, say Main Menu or press star</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/dp0110_ni2_01.wav\\\">To speak with an Asurion representative about Mobile Protection, press 1 Or to go back to the Main Menu, press star</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_dp0110_MobileProtection_DM\n  value: =Text(Global.dp0110_MobileProtection_DM_reco)\n\n- kind: SendActivity\n  id: sendActivity_REPLACE_THIS\n  activity:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/dp0110_out_01.wav\\\">One moment while we connect you with an Asurion representative In order to ensure quality customer service, your call may be recorded</audio>\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.transferReason\n  value: BUSINESS_RULE\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.transferPrompt\n  value: none\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.transferDestination\n  value: asurion\n\n- kind: BeginDialog\n  id: begin_REPLACE_THIS\n  dialog: topic.M09_MainMenu_Dialog\n```", "timestamp": 1749470737.4961288, "content_hash": "c9f4537061a47c5865dae6b7158d2830"}