{"response": "```yaml\n- kind: Question\n  id: LP1020_InsuranceInfoWait_DM\n  displayName: LP1020_InsuranceInfoWait_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.LP1020_InsuranceInfoWait_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/LP1020_ini_01.wav\\\">Okay I will give you a phone number and website where you can find help When youre ready to write this down, say ContinueWhen youre ready, say Continue, or press 1 You can say Continue, or press 1, at any time If youre ready to hear the the MetroGuard phone number and website, say Continue or press 1 If youre ready,  say Continue or press 1  When youre ready say Continue or press 1 Im having some trouble Lets move on</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: continue\n          displayName: continue\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/LP1020_ini_01.wav\\\">Okay I will give you a phone number and website where you can find help When youre ready to write this down, say ContinueWhen youre ready, say Continue, or press 1 You can say Continue, or press 1, at any time If youre ready to hear the the MetroGuard phone number and website, say Continue or press 1 If youre ready,  say Continue or press 1  When youre ready say Continue or press 1 Im having some trouble Lets move on</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_LP1020_InsuranceInfoWait_DM\n  value: =Text(Global.LP1020_InsuranceInfoWait_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_LP1020_InsuranceInfoWait_DM = \"continue\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: LP1025_PhoneInsuranceInfo_DM\n```", "timestamp": 1749528591.0991664, "content_hash": "73f1925a10b0fa29bf758b2ba2429e2a"}