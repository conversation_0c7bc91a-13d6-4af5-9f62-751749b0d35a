{"response": "```yaml\n- kind: Question\n  id: AI1040_RepeatNPIInformationYN_DM\n  displayName: AI1040_RepeatNPIInformationYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AI1040_nm1_01.wav\\\">Please say  yes  or  no   Would you like to hear that information again?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AI1040_nm2_01.wav\\\">Please say  yes  or press one, or  no  or press two  Do you want to hear that information again?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AI1040_nm3_01.wav\\\">To hear that information again, say  yes  or press one  To continue, say  no  or press two</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.AI1040_RepeatNPIInformationYN_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.npi_type = \"cellphone\",\n            [\n              \"You should be able to make calls out right away You will start receiving calls on that number within about two hours If you re still not getting incoming calls on that number after two hours, please visit a Metro store or authorized dealer for help\",\n              \"test\",\n              \"Now, would you like to hear that again?\"\n            ],\n            Global.npi_type = \"landline\",\n            [\n              \"You should be able to make calls out right away You will start receiving calls on that number in one to two weeks If you re still not getting incoming calls on that number after two weeks, please visit a Metro store or authorized dealer for help\",\n              \"test\",\n              \"Now, would you like to hear that again?\"\n            ],\n            [\n              \"test\",\n              \"Now, would you like to hear that again?\"\n            ]\n        )\n        }\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/AI1040_nm1_01.wav\\\">Please say  yes  or  no   Would you like to hear that information again?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AI1040_nm2_01.wav\\\">Please say  yes  or press one, or  no  or press two  Do you want to hear that information again?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AI1040_nm3_01.wav\\\">To hear that information again, say  yes  or press one  To continue, say  no  or press two</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.npi_type\n  value: NumberPortInVars.npi_type\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_AI1040_RepeatNPIInformationYN_DM\n  value: =Text(Global.AI1040_RepeatNPIInformationYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AI1040_RepeatNPIInformationYN_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/AI1040_out_01.wav\\\">Okay</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: AI1040_RepeatNPIInformationYN_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_AI1040_RepeatNPIInformationYN_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/AI1040_out_02.wav\\\">That s fine</audio>\"\n            # No GotoAction for \"return\" as per instructions (no _DS/_DM/_PP/_DA/_DB or _Dialog postfix)\n```", "timestamp": **********.528936, "content_hash": "4fea201271a8103aabd5133d2fbc7373"}