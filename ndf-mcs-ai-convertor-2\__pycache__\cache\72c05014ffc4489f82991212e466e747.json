{"response": "```yaml\n- kind: Question\n  id: nlu0100_NLU_Disambiguation_DM\n  displayName: nlu0100_NLU_Disambiguation_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.nlu0100_NLU_Disambiguation_DM_reco\n  prompt:\n    speak:\n      - \"Please clarify your request.\"\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"I'm sorry, I didn't get that. Could you please clarify?\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.aniMatch\n  value: GlobalVars.aniMatch\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.hasAccountDetails\n  value: \"GlobalVars.GetAccountDetails != undefined ? true : false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.unCoopMaxRequest\n  value: GlobalVars.unCoopMaxRequest\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.accountStatus\n  value: GlobalVars.GetAccountDetails.accountStatus\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.loggedIn\n  value: GlobalVars.loggedIn\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.isOnFamilyPlan\n  value: GlobalVars.GetAccountDetails.isOnFamilyPlan\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.disambigTag\n  value: .returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.confidencescoreDisambigDM\n  value: .confidencescore\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.ssmScore\n  value: (.confidencescore)*100\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.tag\n  value: disambigTag\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.dm_name\n  value: \"\"\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.dm_name = \"ND1435_CancelService_DM\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.GlobalVars.tag = \"true\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.tag\n                  value: cancel-service_transfer\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.GlobalVars.tag = \"false\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.disambiguationForm\n                  value: ND1436_CancelWhatService_DM\n                - kind: BeginDialog\n                  id: begin_REPLACE_THIS\n                  dialog: topic.NLUDisambiguation_Start_Dialog\n\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.dm_name = \"ND1440_ChangeAccount_DM\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.disambigTag = \"hear-current_features\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.fromDisamg\n                  value: true\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.disambigTag = \"change-lines\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.disambiguationForm\n                  value: ND1441_AddOrRemoveLines_DM\n                - kind: BeginDialog\n                  id: begin_REPLACE_THIS\n                  dialog: topic.NLUDisambiguation_Start_Dialog\n\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.dm_name = \"ND1436_CancelWhatService_DM\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.disambigTag = \"close\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: ND1437_CloseAccount_PP\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.disambigTag = \"something-else_cancelservice\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: ND1475_GoToTransfer_SD\n\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.dm_name = \"ND1420_VagueAutoFeature_DM\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.disambigTag = \"true\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.tag\n                  value: vague-autopay\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.disambigTag = \"false\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.tag\n                  value: vague-auto_feature_reprompt\n\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.dm_name = \"ND1455_PrepaidPinPreCheck_DM\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.disambigTag = \"true\", true, false)\n              actions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: |\n                        =If(Global.GlobalVars.GetBCSParameters.payments_enable_prepaid_methods = true, true, false)\n                      actions:\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.GlobalVars.tag\n                          value: make-payment\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.GlobalVars.payingWithPrepaid\n                          value: true\n                        - kind: SendActivity\n                          id: sendActivity_REPLACE_THIS\n                          activity:\n                            speak:\n                              - \"<audio src=\\\"AUDIO_LOCATION/ND1455_out_01.wav\\\">Ok, I'll take you to the payment flow</audio>\"\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: getReturnLink()\n                  elseActions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.tag\n                      value: inquire-prepaid_pin\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: getReturnLink()\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.disambigTag = \"false\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.disambiguationForm\n                  value: ND1456_PrepaidInfo_DM\n                - kind: BeginDialog\n                  id: begin_REPLACE_THIS\n                  dialog: topic.NLUDisambiguation_Start_Dialog\n\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.dm_name = \"ND1456_PrepaidInfo_DM\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.disambigTag = \"true\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: nlu0100_NLU_Disambiguation_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.dm_name = \"ND1460_ChangeSim_DM\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.disambigTag = \"true\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.tag\n                  value: change-sim_transfer\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.disambigTag = \"false\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.tag\n                  value: switch-phone\n\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.dm_name = \"ND1465_VagueBenefits_DM\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.disambigTag = \"true\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.tag\n                  value: inquire-acp\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.disambigTag = \"false\", true, false)\n              actions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/ND1465_out_01.wav\\\">Just so you know, you can view our current offers by going to metro dash t mobilecom forward slash deals And for information on the Lifeline program please visit assurancewirelesscom</audio>\"\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.tag\n                  value: undefined\n\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.dm_name = \"ND1470_VagueUnlock_DM\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.disambigTag = \"unlock-sim\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.tag\n                  value: unlock-sim\n\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.dm_name = \"ND1010_VagueForgotPinDisambig_DM\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.disambigTag = \"payment-pin\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.tag\n                  value: pay-prepaid_PIN_Precheck\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.disambigTag = \"something-else_forgotpin\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: ND1475_GoToTransfer_SD\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.disambigTag = \"forgot-account_pin\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.tag\n                  value: forgot-account_pin\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: getReturnLink()\n\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.dm_name = \"ND1305_VagueAgentDestination_DM\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_TransferReason\n          value: getctiTransferReason(GlobalVars.GetCTIParameters.ctiTransferReason, 'xferred_max_agent_requests')\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.callerSaidOperator\n          value: true\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.operatorReqCount\n          value: operatorReqCount+1\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.GlobalVars.tag = \"transfer-disambig_troubleshooting\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.TransferTag\n                  value: Technical_Support_English\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.TransferTag\n              value: Customer_Support_English\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: getReturnLink()\n\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.dm_name = \"ND1015_VaguePlanDisambig_DM\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.securityRequired\n          value: false\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.needMDN\n          value: false\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.GlobalVars.tag = \"add_line\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.callType\n                  value: add_line\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.GlobalVars.tag = \"cancel_line\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.callType\n                  value: cancel_line\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: getReturnLink()\n\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.dm_name = \"ND1020_VagueAddServiceDisambig_DM\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.disambigTag = \"something-else_addservice\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: ND1475_GoToTransfer_SD\n\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.dm_name = \"ND1120_VagueTransferNumberDisambig_DM\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.disambigTag = \"something-else_xfernum\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: ND1475_GoToTransfer_SD\n\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.dm_name = \"ND1310_VagueActivatePhone_DM\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.disambigTag = \"something-else_activatephone\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: ND1475_GoToTransfer_SD\n\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.dm_name = \"ND1450_VagueSim_DM\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.disambigTag = \"something-else_vaguesim\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: ND1475_GoToTransfer_SD\n\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.dm_name = \"ND1315_VagueReactivatePhoneActive_DM\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.disambigTag = \"something-else_reactivatephone\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: ND1475_GoToTransfer_SD\n\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.dm_name = \"ND1430_ChangePhone_DM\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.disambigTag = \"something-else_changephone\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: ND1475_GoToTransfer_SD\n\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.dm_name = \"ND1024_ConfirmExistingCustomer_DM\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.GlobalVars.tag = \"true\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.disambiguationForm\n                  value: ND1020_VagueAddServiceDisambig_DM\n                - kind: BeginDialog\n                  id: begin_REPLACE_THIS\n                  dialog: topic.NLUDisambiguation_Start_Dialog\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.GlobalVars.tag = \"false\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.callType\n                  value: transfer\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.TransferTag\n                  value: New_Customer_Sales_En\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: ND1030_GoToTransfer_SD\n\n  elseActions:\n    - kind: GotoAction\n      id: goto_REPLACE_THIS\n      actionId: getReturnLink()\n```", "timestamp": 1749528802.5915072, "content_hash": "72c05014ffc4489f82991212e466e747"}