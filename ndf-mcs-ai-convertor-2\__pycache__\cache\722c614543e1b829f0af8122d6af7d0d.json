{"response": "```yaml\n- kind: Question\n  id: PH1010_PaymentHelpMenu_DM\n  displayName: PH1010_PaymentHelpMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"{Switch(\n            true,\n            Global.tag = 'vague-billing',\n            \\\"Which would you like, say, 'Get an extension', 'hear balance breakdown', 'make a payment', 'reduce monthly payment' or 'change my due date'\\\",\n            Global.tag <> 'vague-billing',\n            \\\"Payment help menu  You can say 'get an extension', 'change due date', 'reduce my payment', 'ways to pay', 'question about my charges', 'manage payment cards' Or say, 'its something else' \\\"\n        )}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"{Switch(\n            true,\n            Global.tag = 'vague-billing',\n            \\\"Which would you like, say, 'Get an extension', 'hear balance breakdown', 'make a payment', 'reduce monthly payment' or 'change my due date'\\\",\n            Global.tag <> 'vague-billing',\n            \\\"To request an extension press 1, To change your due date' press 2, 'reduce  payment'  3, 'To hear about ways to pay' - 4, 'questions about a charge' - 5, 'manage payment cards' -6 , if it's something else' press 7 \\\"\n        )}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"{Switch(\n            true,\n            Global.tag = 'vague-billing',\n            \\\"Which would you like, say, 'Get an extension', 'hear balance breakdown', 'make a payment', 'reduce monthly payment' or 'change my due date'\\\",\n            Global.tag <> 'vague-billing',\n            \\\"To request an extension press 1, To change your due date' press 2, 'reduce  payment'  3, 'To hear about ways to pay' - 4, 'questions about a charge' - 5, 'manage payment cards' -6 , if it's something else' press 7 \\\"\n        )}\"\n\n  alwaysPrompt: true\n  variable: Global.PH1010_PaymentHelpMenu_DM_reco\n  prompt:\n    speak:\n      - \"{Switch(\n            true,\n            Global.tag = 'vague-billing',\n            \\\"Which would you like, say, 'Get an extension', 'hear balance breakdown', 'make a payment', 'reduce monthly payment' or 'change my due date'\\\",\n            Global.tag <> 'vague-billing',\n            \\\"Payment help menu  You can say 'get an extension', 'change due date', 'reduce my payment', 'ways to pay', 'question about my charges', 'manage payment cards' Or say, 'its something else' \\\"\n        )}\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: faq_payment_methods\n          displayName: faq_payment_methods\n        - id: manage_cards\n          displayName: manage_cards\n        - id: make-payment\n          displayName: make-payment\n        - id: request-extension\n          displayName: request-extension\n        - id: bcr\n          displayName: bcr\n        - id: reduce\n          displayName: reduce\n        - id: report-problem_payment_pm\n          displayName: report-problem_payment_pm\n        - id: inquire-billing\n          displayName: inquire-billing\n        - id: something-else_payhelp\n          displayName: something-else_payhelp\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"{Switch(\n              true,\n              Global.tag = 'vague-billing',\n              \\\"Which would you like, say, 'Get an extension', 'hear balance breakdown', 'make a payment', 'reduce monthly payment' or 'change my due date'\\\",\n              Global.tag <> 'vague-billing',\n              \\\"Payment help menu  You can say 'get an extension', 'change due date', 'reduce my payment', 'ways to pay', 'question about my charges', 'manage payment cards' Or say, 'its something else' \\\"\n          )}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"{Switch(\n              true,\n              Global.tag = 'vague-billing',\n              \\\"Which would you like, say, 'Get an extension', 'hear balance breakdown', 'make a payment', 'reduce monthly payment' or 'change my due date'\\\",\n              Global.tag <> 'vague-billing',\n              \\\"To request an extension press 1, To change your due date' press 2, 'reduce  payment'  3, 'To hear about ways to pay' - 4, 'questions about a charge' - 5, 'manage payment cards' -6 , if it's something else' press 7 \\\"\n          )}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"{Switch(\n              true,\n              Global.tag = 'vague-billing',\n              \\\"Which would you like, say, 'Get an extension', 'hear balance breakdown', 'make a payment', 'reduce monthly payment' or 'change my due date'\\\",\n              Global.tag <> 'vague-billing',\n              \\\"To request an extension press 1, To change your due date' press 2, 'reduce  payment'  3, 'To hear about ways to pay' - 4, 'questions about a charge' - 5, 'manage payment cards' -6 , if it's something else' press 7 \\\"\n          )}\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.allowedResponses\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.allowedResponsesDTMF\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.tag\n  value: GlobalVars.tag\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.lastTag\n  value: tag\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_PH1010_PaymentHelpMenu_DM\n  value: =Text(Global.PH1010_PaymentHelpMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_PH1010_PaymentHelpMenu_DM = \"faq_payment_methods\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.tag\n          value: faq-payment_methods\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: PH1035_Faq_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_PH1010_PaymentHelpMenu_DM = \"manage_cards\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.manageCardTask\n              value: MainMC\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: PH1045_ManageCards_SD\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_PH1010_PaymentHelpMenu_DM = \"make-payment\", true, false)\n              actions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/PH1010_out_01.wav\\\">Alright</audio>\"\n                      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.callType\n                  value: make_pmt\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.acceptPayByPhone\n                  value: true\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.cti_Intent\n                  value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: PH1025_MakePayment_SD\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_PH1010_PaymentHelpMenu_DM = \"request-extension\", true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: PH1101_CheckExtensionEligibility_JDA\n\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.Var_PH1010_PaymentHelpMenu_DM = \"bcr\", true, false)\n                      actions:\n                        - kind: SendActivity\n                          id: sendActivity_REPLACE_THIS\n                          activity:\n                            speak:\n                              - \"<audio src=\\\"AUDIO_LOCATION/PH1005_out_02.wav\\\">Okay</audio>\"\n                              - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.GlobalVars.callType\n                          value: billcyclereset\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.GlobalVars.tag\n                          value: change-payment_date\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.GlobalVars.cti_Intent\n                          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: PH1015_BillCycleReset_SD\n\n                  elseActions:\n                    - kind: ConditionGroup\n                      id: conditionGroup_REPLACE_THIS\n                      conditions:\n                        - id: conditionItem_REPLACE_THIS\n                          condition: =If(Global.Var_PH1010_PaymentHelpMenu_DM = \"reduce\", true, false)\n                          actions:\n                            - kind: GotoAction\n                              id: goto_REPLACE_THIS\n                              actionId: PH1205_CheckHaveAddOns_JDA\n\n                      elseActions:\n                        - kind: ConditionGroup\n                          id: conditionGroup_REPLACE_THIS\n                          conditions:\n                            - id: conditionItem_REPLACE_THIS\n                              condition: =If(Global.Var_PH1010_PaymentHelpMenu_DM = \"report-problem_payment_pm\", true, false)\n                              actions:\n                                - kind: SendActivity\n                                  id: sendActivity_REPLACE_THIS\n                                  activity:\n                                    speak:\n                                      - \"<audio src=\\\"AUDIO_LOCATION/PH1005_out_03.wav\\\">Sorry to hear that!</audio>\"\n                                      - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n                                - kind: GotoAction\n                                  id: goto_REPLACE_THIS\n                                  actionId: PH1405_PaymentIssuesTransition_PP\n\n                          elseActions:\n                            - kind: ConditionGroup\n                              id: conditionGroup_REPLACE_THIS\n                              conditions:\n                                - id: conditionItem_REPLACE_THIS\n                                  condition: =If(Global.Var_PH1010_PaymentHelpMenu_DM = \"inquire-billing\", true, false)\n                                  actions:\n                                    - kind: GotoAction\n                                      id: goto_REPLACE_THIS\n                                      actionId: PH1030_BalanceBreakdown_SD\n\n                              elseActions:\n                                - kind: ConditionGroup\n                                  id: conditionGroup_REPLACE_THIS\n                                  conditions:\n                                    - id: conditionItem_REPLACE_THIS\n                                      condition: =If(Global.Var_PH1010_PaymentHelpMenu_DM = \"something-else_payhelp\", true, false)\n                                      actions:\n                                        - kind: GotoAction\n                                          id: goto_REPLACE_THIS\n                                          actionId: PH1020_TransferPaymentHelp_SD\n```", "timestamp": 1749529038.2313426, "content_hash": "722c614543e1b829f0af8122d6af7d0d"}