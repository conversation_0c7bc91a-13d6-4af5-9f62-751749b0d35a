{"response": "```yaml\n- kind: Question\n  id: aa1097_UnfreezeCard_DM\n  displayName: aa1097_UnfreezeCard_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.aa1097_UnfreezeCard_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa1097_ini_01.wav\\\">Your card is frozen, and no transactions are currently allowed Would you like to unfreeze it now?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa1097_UnfreezeCard_DM\n  value: =Text(Global.aa1097_UnfreezeCard_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa1097_UnfreezeCard_DM = \"yes\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.nextStep\n          value: freeze_card\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.currentTask\n          value: freezeCard\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aa1097_out_01.wav\\\">Okay</audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: return\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa1097_UnfreezeCard_DM = \"no\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.nextStep\n              value: balance_inquiry\n\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/aa1097_out_02.wav\\\">Okay You can unfreeze your card at any time</audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: return\n```", "timestamp": 1749458594.1389782, "content_hash": "8de08044bec5983ef8f628ff572d1df3"}