{"response": "```yaml\n- kind: Question\n  id: AU1020_AutoPayMenu_DM\n  displayName: AU1020_AutoPayMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AU1020_nm1_01.wav\\\">You can say 'cancel autopay', or 'manage payment cards'</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AU1020_nm2_01.wav\\\">You can say 'cancel autopay' or press 1'manage payment cards' or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AU1020_nm2_01.wav\\\">You can say 'cancel autopay' or press 1'manage payment cards' or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.AU1020_AutoPayMenu_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/AU1020_ini_01.wav\\\">Alright, you can 'cancel autopay', or 'manage payment cards'  Which would you like?</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: cancel-autopay\n          displayName: cancel-autopay\n        - id: manage-cards\n          displayName: manage-cards\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/AU1020_nm1_01.wav\\\">You can say 'cancel autopay', or 'manage payment cards'</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AU1020_nm2_01.wav\\\">You can say 'cancel autopay' or press 1'manage payment cards' or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AU1020_nm2_01.wav\\\">You can say 'cancel autopay' or press 1'manage payment cards' or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_AU1020_AutoPayMenu_DM\n  value: =Text(Global.AU1020_AutoPayMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AU1020_AutoPayMenu_DM = \"cancel-autopay\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.tag\n          value: cancel-autopay\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: AU1302_CheckNextDueDate_JDA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_AU1020_AutoPayMenu_DM = \"manage-cards\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.manageCardTask\n              value: MainMC\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: AU1320_GoToManageCards_SD\n```", "timestamp": 1749527590.514975, "content_hash": "af7ad6df03519d5020e7760cc6e9a923"}