{"response": "```yaml\n- kind: Question\n  id: aa1550_CardReplacementWrapDTMF_DM\n  displayName: aa1550_CardReplacementWrapDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.validationCriteriaInfo.stateAllowCardIssue = true &&\n            Global.validateLostStolenCriteriaInfo.cardReplacementRange = \"3\" && Global.cardReplacementVariables.reorderCardReplacement = false,\n            [\n              \"If you never received a replacement card\",\n              \"press 1\"\n            ],\n\n            Global.validationCriteriaInfo.stateAllowCardIssue = true &&\n            (Global.validateLostStolenCriteriaInfo.cardReplacementRange = \"2\" || Global.validateLostStolenCriteriaInfo.cardReplacementRange = \"3\") && Global.cardReplacementVariables.reorderCardReplacement = false,\n            [\n              \"If you have received a replacement card which is now lost, stolen, or damaged, and you would like to order another replacement\",\n              {Switch(\n                true,\n                Global.validateLostStolenCriteriaInfo.cardReplacementRange = \"2\" && Global.cardReplacementVariables.reorderCardReplacement = false,\n                \"press 1\",\n                true,\n                \"press 2\"\n              )},\n              [\n                \"To hear that again\",\n                \"press 1\"\n              ]\n            ],\n\n            Global.validationCriteriaInfo.stateAllowCardIssue = true,\n            [\n              \"To hear that again\",\n              \"press 1\"\n            ],\n\n            ((Global.validateLostStolenCriteriaInfo.cardReplacementRange = \"3\" && Global.cardReplacementVariables.reorderCardReplacement = true) || (Global.validateLostStolenCriteriaInfo.cardReplacementRange <> \"3\" || Global.validationCriteriaInfo.stateAllowCardIssue = false)),\n            \"If you re done, feel free to hang up\"\n        )}\n\n      - |\n        {Switch(\n            true,\n            Global.dnisInfo.dtmfOnlyFlag = true,\n            [\n              \"Sorry, I still didn t get that\"\n            ]\n        )}\n\n  alwaysPrompt: true\n  variable: Global.aa1550_CardReplacementWrapDTMF_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.validationCriteriaInfo.stateAllowCardIssue = true &&\n            Global.validateLostStolenCriteriaInfo.cardReplacementRange = \"3\" && Global.cardReplacementVariables.reorderCardReplacement = false,\n            [\n              \"If you never received a replacement card\",\n              \"press 1\"\n            ],\n\n            Global.validationCriteriaInfo.stateAllowCardIssue = true &&\n            (Global.validateLostStolenCriteriaInfo.cardReplacementRange = \"2\" || Global.validateLostStolenCriteriaInfo.cardReplacementRange = \"3\") && Global.cardReplacementVariables.reorderCardReplacement = false,\n            [\n              \"If you have received a replacement card which is now lost, stolen, or damaged, and you would like to order another replacement\",\n              {Switch(\n                true,\n                Global.validateLostStolenCriteriaInfo.cardReplacementRange = \"2\" && Global.cardReplacementVariables.reorderCardReplacement = false,\n                \"press 1\",\n                true,\n                \"press 2\"\n              )},\n              [\n                \"To hear that again\",\n                \"press 1\"\n              ]\n            ],\n\n            Global.validationCriteriaInfo.stateAllowCardIssue = true,\n            [\n              \"To hear that again\",\n              \"press 1\"\n            ],\n\n            ((Global.validateLostStolenCriteriaInfo.cardReplacementRange = \"3\" && Global.cardReplacementVariables.reorderCardReplacement = true) || (Global.validateLostStolenCriteriaInfo.cardReplacementRange <> \"3\" || Global.validationCriteriaInfo.stateAllowCardIssue = false)),\n            \"If you re done, feel free to hang up\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: missing_card\n          displayName: missing_card\n        - id: reorder_card\n          displayName: reorder_card\n        - id: repeat_cancellation_info\n          displayName: repeat_cancellation_info\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n              true,\n              Global.validationCriteriaInfo.stateAllowCardIssue = true &&\n              Global.validateLostStolenCriteriaInfo.cardReplacementRange = \"3\" && Global.cardReplacementVariables.reorderCardReplacement = false,\n              [\n                \"If you never received a replacement card\",\n                \"press 1\"\n              ],\n\n              Global.validationCriteriaInfo.stateAllowCardIssue = true &&\n              (Global.validateLostStolenCriteriaInfo.cardReplacementRange = \"2\" || Global.validateLostStolenCriteriaInfo.cardReplacementRange = \"3\") && Global.cardReplacementVariables.reorderCardReplacement = false,\n              [\n                \"If you have received a replacement card which is now lost, stolen, or damaged, and you would like to order another replacement\",\n                {Switch(\n                  true,\n                  Global.validateLostStolenCriteriaInfo.cardReplacementRange = \"2\" && Global.cardReplacementVariables.reorderCardReplacement = false,\n                  \"press 1\",\n                  true,\n                  \"press 2\"\n                )},\n                [\n                  \"To hear that again\",\n                  \"press 1\"\n                ]\n              ],\n\n              Global.validationCriteriaInfo.stateAllowCardIssue = true,\n              [\n                \"To hear that again\",\n                \"press 1\"\n              ],\n\n              ((Global.validateLostStolenCriteriaInfo.cardReplacementRange = \"3\" && Global.cardReplacementVariables.reorderCardReplacement = true) || (Global.validateLostStolenCriteriaInfo.cardReplacementRange <> \"3\" || Global.validationCriteriaInfo.stateAllowCardIssue = false)),\n              \"If you re done, feel free to hang up\"\n          )}\n\n        - |\n          {Switch(\n              true,\n              Global.dnisInfo.dtmfOnlyFlag = true,\n              [\n                \"Sorry, I still didn t get anything\"\n              ]\n          )}\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa1550_CardReplacementWrapDTMF_DM\n  value: =Text(Global.aa1550_CardReplacementWrapDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa1550_CardReplacementWrapDTMF_DM = \"missing_card\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.transferAllowed\n          value: true\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aa1550_out_01.wav\\\">I ll transfer you to customer service</audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: transferHandler_CS\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa1550_CardReplacementWrapDTMF_DM = \"reorder_card\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.cardReplacementVariables.reorderCardReplacement\n              value: true\n\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.validateLostStolenCriteriaInfo.cardIssuableClient <> \"Y\", true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: aa1507_ReplacementWarningMsg_PP\n\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.validateLostStolenCriteriaInfo.cardholderMailingAddress = \"\", true, false)\n                      actions:\n                        - kind: SendActivity\n                          id: sendActivity_REPLACE_THIS\n                          activity:\n                            speak:\n                              - \"<audio src=\\\"AUDIO_LOCATION/aa1550_out_02.wav\\\">Unfortunately, there is no address on your account To update your records, I ll transfer you to customer service</audio>\"\n\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.globalVariables.transferAllowed\n                          value: true\n\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: transferHandler_CS\n\n                  elseActions:\n                    - kind: ConditionGroup\n                      id: conditionGroup_REPLACE_THIS\n                      conditions:\n                        - id: conditionItem_REPLACE_THIS\n                          condition: =If(Global.validateLostStolenCriteriaInfo.cardholderMailingAddress = null, true, false)\n                          actions:\n                            - kind: GotoAction\n                              id: goto_REPLACE_THIS\n                              actionId: aa1505_NoAddressFoundMsgOn_PP\n\n                    elseActions:\n                      - kind: GotoAction\n                        id: goto_REPLACE_THIS\n                        actionId: aa1510_ConfirmAddressDTMF_DM\n\n    elseActions:\n      - kind: ConditionGroup\n        id: conditionGroup_REPLACE_THIS\n        conditions:\n          - id: conditionItem_REPLACE_THIS\n            condition: =If(Global.Var_aa1550_CardReplacementWrapDTMF_DM = \"repeat_cancellation_info\", true, false)\n            actions:\n              - kind: GotoAction\n                id: goto_REPLACE_THIS\n                actionId: aa1545_CardCancelledSuccessMsg_PP\n```", "timestamp": 1749543738.7510476, "content_hash": "b03ab46ca2ae82efb2d5550d534e541c"}