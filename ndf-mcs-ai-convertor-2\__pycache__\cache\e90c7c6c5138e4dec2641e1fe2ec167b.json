{"response": "```yaml\n- kind: Question\n  id: aa2020_BalanceWrapUpDTMF_DM\n  displayName: aa2020_BalanceWrapUpDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa2020_nm1_01.wav\\\">Sorry</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.aa2020_BalanceWrapUpDTMF_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n\n            // Case: dnisInfo.callType = \"TX\" or \"WV\"\n            Global.dnisInfo.callType = \"TX\" || Global.dnisInfo.callType = \"WV\",\n            [\n                \"To hear that again, press 1\",\n                \"To end this call, hang up  Or, wait for more options\"\n            ],\n\n            // Default case\n            [\n                \"To hear that again, press 1 To go the main menu, press 2 And if you re done feel free to hang-up\"\n            ]\n        )\n        }\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: repeat_that\n          displayName: repeat_that\n\n        - id: main_menu\n          displayName: main_menu\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa2020_BalanceWrapUpDTMF_DM\n  value: =Text(Global.aa2020_BalanceWrapUpDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa2020_BalanceWrapUpDTMF_DM = \"repeat_that\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa2005_Balance_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa2020_BalanceWrapUpDTMF_DM = \"main_menu\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: handleMainMenu_CS\n```", "timestamp": 1749543416.5697072, "content_hash": "e90c7c6c5138e4dec2641e1fe2ec167b"}