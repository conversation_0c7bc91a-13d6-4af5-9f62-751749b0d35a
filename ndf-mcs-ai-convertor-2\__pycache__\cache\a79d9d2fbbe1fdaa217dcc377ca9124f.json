{"response": "```yaml\n- kind: Question\n  id: MW1215_UseOtherCardYN_DM\n  displayName: MW1215_UseOtherCardYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1215_nm1_01.wav\\\">I was unable to validate this card Would you like to try  a different card?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1215_nm2_01.wav\\\">I couldn t validate the card number you gave me If you have a different card you can use, say  yes  or press 1 Otherwise, say  no  or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1215_nm2_01.wav\\\">I couldn t validate the card number you gave me If you have a different card you can use, say  yes  or press 1 Otherwise, say  no  or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.MW1215_UseOtherCardYN_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.unsupportedCardIssuer = \"discover\",\n            \"It looks like you re using a Discover card, and we don t take those right now We *do* take Visa, MasterCard, American Express, Star, NYCE, Pulse, and Accel\",\n            Global.unsupportedCardIssuer <> \"discover\",\n            \"I couldn t validate the card number you gave me\",\n            [\n              \"test\",\n              \"Would you like to use a different card?\"\n            ]\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1215_nm1_01.wav\\\">I was unable to validate this card Would you like to try  a different card?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1215_nm2_01.wav\\\">I couldn t validate the card number you gave me If you have a different card you can use, say  yes  or press 1 Otherwise, say  no  or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1215_nm2_01.wav\\\">I couldn t validate the card number you gave me If you have a different card you can use, say  yes  or press 1 Otherwise, say  no  or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.unsupportedCardIssuer\n  value: GlobalVars.unsupportedCardIssuer\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_MW1215_UseOtherCardYN_DM\n  value: =Text(Global.MW1215_UseOtherCardYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MW1215_UseOtherCardYN_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.failedChecksum\n          value: false\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/MW1215_out_01.wav\\\">Alright</audio>\"\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.tryNewCard\n          value: true\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: MW1200_AskDebitOrCredit_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_MW1215_UseOtherCardYN_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/MW1215_out_02.wav\\\">ok</audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n```", "timestamp": 1749557038.6189063, "content_hash": "a79d9d2fbbe1fdaa217dcc377ca9124f"}