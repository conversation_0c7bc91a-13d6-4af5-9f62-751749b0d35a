{"response": "```yaml\n- kind: Question\n  id: SH1305_OfferExtensionYN_DM\n  displayName: SH1305_OfferExtensionYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - |\n        {Switch(\n              true,\n              Global.callType = \"change_plan\",\n              \"<audio src=\\\"AUDIO_LOCATION/SH1305_ini_02.wav\\\">I can also check if you can get an extension on your payment Would you like to try *that*? </audio>\",\n              true,\n              \"<audio src=\\\"AUDIO_LOCATION/SH1305_ini_03.wav\\\">We can also give you a onetime extension on your payment Would you like to do that</audio>\"\n          )\n        }\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SH1305_nm2_01.wav\\\">Would you like to check if you can get an extension? Say 'yes' or press 1, or 'no' or press 2 If you're done, you can simply hang up </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SH1305_nm2_01.wav\\\">Would you like to check if you can get an extension? Say 'yes' or press 1, or 'no' or press 2 If you're done, you can simply hang up </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.SH1305_OfferExtensionYN_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.SH1305_operator_counter = 1,\n              \"<audio src=\\\"AUDIO_LOCATION/SH1305_operator_01.wav\\\">Our agents wont be able to give you other options But I can give you an extension right here Would you like to do that</audio>\",\n              true,\n              \"<audio src=\\\"AUDIO_LOCATION/SH1305_ini_02.wav\\\">I can also check if you can get an extension on your payment Would you like to try *that*? </audio>\"\n          )\n        }\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.SH1305_operator_counter = 1,\n                \"<audio src=\\\"AUDIO_LOCATION/SH1305_operator_01.wav\\\">Our agents wont be able to give you other options But I can give you an extension right here Would you like to do that</audio>\",\n                true,\n                \"<audio src=\\\"AUDIO_LOCATION/SH1305_ini_02.wav\\\">I can also check if you can get an extension on your payment Would you like to try *that*? </audio>\"\n            )\n          }\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SH1305_nm2_01.wav\\\">Would you like to check if you can get an extension? Say 'yes' or press 1, or 'no' or press 2 If you're done, you can simply hang up </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SH1305_nm2_01.wav\\\">Would you like to check if you can get an extension? Say 'yes' or press 1, or 'no' or press 2 If you're done, you can simply hang up </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.callType\n  value: GlobalVars.callType\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_SH1305_OfferExtensionYN_DM\n  value: =Text(Global.SH1305_OfferExtensionYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_SH1305_OfferExtensionYN_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: extension\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: SH1310_ApplyExtension_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_SH1305_OfferExtensionYN_DM = \"false\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: SH1312_PayNowYN_DM\n```", "timestamp": 1749529678.9501715, "content_hash": "96193571ae474f4cdc09a6ff34911fff"}