import yaml
import os
import pandas as pd
from collections import defaultdict
import re

def extract_prompt_data(component, component_name="", parent_path=""):
    """
    Recursively extract prompt IDs and verbiage from a component.
    Returns a list of dictionaries with prompt information.
    """
    prompts = []
    
    def traverse_dict(data, path="", current_id=""):
        if isinstance(data, dict):
            # Look for ID field
            action_id = data.get('id', current_id)
            display_name = data.get('displayName', '')
            
            # Check if this is a prompt structure
            if 'prompt' in data:
                prompt_data = data['prompt']
                if isinstance(prompt_data, dict) and 'speak' in prompt_data:
                    speak_content = prompt_data['speak']
                    
                    # Handle speak as list or single item
                    if isinstance(speak_content, list):
                        for i, speak_item in enumerate(speak_content):
                            prompts.append({
                                'Component_Name': component_name,
                                'Prompt_ID': action_id or display_name or f"unknown_{len(prompts)}",
                                'Display_Name': display_name,
                                'Prompt_Verbiage': clean_prompt_text(speak_item),
                                'Path': f"{path}.prompt.speak[{i}]" if path else f"prompt.speak[{i}]",
                                'Prompt_Index': i
                            })
                    else:
                        prompts.append({
                            'Component_Name': component_name,
                            'Prompt_ID': action_id or display_name or f"unknown_{len(prompts)}",
                            'Display_Name': display_name,
                            'Prompt_Verbiage': clean_prompt_text(speak_content),
                            'Path': f"{path}.prompt.speak" if path else "prompt.speak",
                            'Prompt_Index': 0
                        })
            
            # Check for unrecognizedPrompt
            if 'unrecognizedPrompt' in data:
                unrecog_data = data['unrecognizedPrompt']
                if isinstance(unrecog_data, dict) and 'speak' in unrecog_data:
                    speak_content = unrecog_data['speak']
                    
                    if isinstance(speak_content, list):
                        for i, speak_item in enumerate(speak_content):
                            prompts.append({
                                'Component_Name': component_name,
                                'Prompt_ID': f"{action_id}_unrecognized" if action_id else f"unrecognized_{len(prompts)}",
                                'Display_Name': display_name,
                                'Prompt_Verbiage': clean_prompt_text(speak_item),
                                'Path': f"{path}.unrecognizedPrompt.speak[{i}]" if path else f"unrecognizedPrompt.speak[{i}]",
                                'Prompt_Index': i
                            })
                    else:
                        prompts.append({
                            'Component_Name': component_name,
                            'Prompt_ID': f"{action_id}_unrecognized" if action_id else f"unrecognized_{len(prompts)}",
                            'Display_Name': display_name,
                            'Prompt_Verbiage': clean_prompt_text(speak_content),
                            'Path': f"{path}.unrecognizedPrompt.speak" if path else "unrecognizedPrompt.speak",
                            'Prompt_Index': 0
                        })
            
            # Check for inputTimeoutResponse
            if 'inputTimeoutResponse' in data:
                timeout_data = data['inputTimeoutResponse']
                if isinstance(timeout_data, dict) and 'speak' in timeout_data:
                    speak_content = timeout_data['speak']
                    
                    if isinstance(speak_content, list):
                        for i, speak_item in enumerate(speak_content):
                            if speak_item:  # Skip empty items
                                prompts.append({
                                    'Component_Name': component_name,
                                    'Prompt_ID': f"{action_id}_timeout" if action_id else f"timeout_{len(prompts)}",
                                    'Display_Name': display_name,
                                    'Prompt_Verbiage': clean_prompt_text(speak_item),
                                    'Path': f"{path}.inputTimeoutResponse.speak[{i}]" if path else f"inputTimeoutResponse.speak[{i}]",
                                    'Prompt_Index': i
                                })
            
            # Recursively traverse nested structures
            for key, value in data.items():
                new_path = f"{path}.{key}" if path else key
                if key in ['actions', 'conditions']:
                    if isinstance(value, list):
                        for i, item in enumerate(value):
                            traverse_dict(item, f"{new_path}[{i}]", action_id)
                    else:
                        traverse_dict(value, new_path, action_id)
                elif isinstance(value, (dict, list)) and key not in ['prompt', 'unrecognizedPrompt', 'inputTimeoutResponse']:
                    traverse_dict(value, new_path, action_id)
        
        elif isinstance(data, list):
            for i, item in enumerate(data):
                traverse_dict(item, f"{path}[{i}]", current_id)
    
    traverse_dict(component, parent_path)
    return prompts

def clean_prompt_text(text):
    """
    Clean prompt text by removing audio tags and extracting readable content.
    """
    if not text:
        return ""
    
    # Remove audio tags and extract text content
    # Pattern: <audio src="...">text content</audio>
    audio_pattern = r'<audio[^>]*>(.*?)</audio>'
    matches = re.findall(audio_pattern, text, re.DOTALL)
    
    if matches:
        # Return the text content inside audio tags
        return matches[0].strip()
    else:
        # Return original text if no audio tags found
        return text.strip()

def analyze_yaml_file(file_path):
    """
    Analyze a YAML file and extract all prompt data.
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        
        all_prompts = []
        
        # Handle different YAML structures
        if 'components' in data:
            # Bot definition format
            for component in data['components']:
                component_name = component.get('displayName', 'Unknown')
                dialog = component.get('dialog', {})
                begin_dialog = dialog.get('beginDialog', {})
                
                prompts = extract_prompt_data(begin_dialog, component_name)
                all_prompts.extend(prompts)
        
        elif isinstance(data, list):
            # Topic format (list of components)
            for component in data:
                component_name = component.get('displayName', 'Unknown')
                dialog = component.get('dialog', {})
                begin_dialog = dialog.get('beginDialog', {})
                
                prompts = extract_prompt_data(begin_dialog, component_name)
                all_prompts.extend(prompts)
        
        else:
            # Single component or other format
            component_name = data.get('displayName', os.path.basename(file_path))
            prompts = extract_prompt_data(data, component_name)
            all_prompts.extend(prompts)
        
        return all_prompts
    
    except Exception as e:
        print(f"Error processing YAML file {file_path}: {e}")
        return []

def process_directory(directory_path):
    """
    Process all YAML files in a directory and extract prompt data.
    """
    all_prompts = []
    yaml_extensions = ['.yml', '.yaml']
    
    for root, dirs, files in os.walk(directory_path):
        for file in files:
            if any(file.lower().endswith(ext) for ext in yaml_extensions):
                file_path = os.path.join(root, file)
                print(f"Processing: {file_path}")
                
                prompts = analyze_yaml_file(file_path)
                
                # Add file information to each prompt
                for prompt in prompts:
                    prompt['Source_File'] = file_path
                    prompt['File_Name'] = file
                
                all_prompts.extend(prompts)
    
    return all_prompts

def extract_simple_prompts(prompts):
    """
    Extract only prompt ID and verbiage from the full prompt data.
    Returns a list of dictionaries with only 'Prompt_ID' and 'Prompt_Verbiage'.
    """
    simple_prompts = []
    for prompt in prompts:
        simple_prompts.append({
            'Prompt_ID': prompt.get('Prompt_ID', ''),
            'Prompt_Verbiage': prompt.get('Prompt_Verbiage', '')
        })
    return simple_prompts

def generate_simple_excel_output(prompts, output_path):
    """
    Generate Excel output with only prompt ID and verbiage.
    """
    try:
        if not prompts:
            print("No prompts found to export.")
            return

        # Extract only prompt ID and verbiage
        simple_prompts = extract_simple_prompts(prompts)

        # Create DataFrame
        df = pd.DataFrame(simple_prompts)

        # Sort by prompt ID
        df = df.sort_values(['Prompt_ID'])

        # Save to Excel
        df.to_excel(output_path, index=False, engine='openpyxl')

        print(f"\nSimple Excel file generated: {output_path}")
        print(f"Total prompts extracted: {len(simple_prompts)}")

    except Exception as e:
        print(f"Error generating simple Excel output: {e}")

def generate_excel_output(prompts, output_path):
    """
    Generate Excel output with prompt data.
    """
    try:
        if not prompts:
            print("No prompts found to export.")
            return

        # Create DataFrame
        df = pd.DataFrame(prompts)

        # Reorder columns for better readability
        column_order = [
            'Source_File', 'File_Name', 'Component_Name', 'Prompt_ID',
            'Display_Name', 'Prompt_Verbiage', 'Path', 'Prompt_Index'
        ]

        # Only include columns that exist
        existing_columns = [col for col in column_order if col in df.columns]
        df = df[existing_columns]

        # Sort by file name and prompt ID
        df = df.sort_values(['File_Name', 'Prompt_ID'])

        # Save to Excel
        df.to_excel(output_path, index=False, engine='openpyxl')

        print(f"\nExcel file generated: {output_path}")
        print(f"Total prompts extracted: {len(prompts)}")
        print(f"Unique components: {len(df['Component_Name'].unique())}")
        print(f"Files processed: {len(df['File_Name'].unique())}")

    except Exception as e:
        print(f"Error generating Excel output: {e}")

def main():
    """
    Main function to run the prompt extractor.
    """
    import argparse

    parser = argparse.ArgumentParser(description='Extract prompt IDs and verbiage from YAML files')
    parser.add_argument('input_path', help='Path to YAML file or directory containing YAML files')
    parser.add_argument('-o', '--output', default='prompt_extraction.xlsx',
                       help='Output Excel file path (default: prompt_extraction.xlsx)')
    parser.add_argument('-s', '--simple', action='store_true',
                       help='Generate simple output with only Prompt_ID and Prompt_Verbiage columns')

    args = parser.parse_args()

    input_path = args.input_path
    output_path = args.output
    simple_mode = args.simple

    if not os.path.exists(input_path):
        print(f"Error: Input path '{input_path}' does not exist.")
        return

    print(f"Starting prompt extraction from: {input_path}")
    print(f"Output will be saved to: {output_path}")
    if simple_mode:
        print("Mode: Simple (Prompt_ID and Prompt_Verbiage only)")
    else:
        print("Mode: Full (All details)")
    print("-" * 50)

    if os.path.isfile(input_path):
        # Single file
        prompts = analyze_yaml_file(input_path)
        for prompt in prompts:
            prompt['Source_File'] = input_path
            prompt['File_Name'] = os.path.basename(input_path)
    else:
        # Directory
        prompts = process_directory(input_path)

    if simple_mode:
        generate_simple_excel_output(prompts, output_path)
    else:
        generate_excel_output(prompts, output_path)

if __name__ == "__main__":
    main()
