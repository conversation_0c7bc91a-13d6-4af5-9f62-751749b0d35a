{"response": "```yaml\n- kind: Question\n  id: GS1010_GettingStarted_DM\n  displayName: GS1010_GettingStarted_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/GS1010_nm1_01.wav\\\">You can say tell me about Metro,open an account, or find a store To browse our phones and plans, visit metro by t dash mobile dot com </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/GS1010_nm2_01.wav\\\">You can say tell me about Metro or press 1 Open an account or press 2, or say find a store or press 3 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/GS1010_nm2_01.wav\\\">You can say tell me about Metro or press 1 Open an account or press 2, or say find a store or press 3 </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.GS1010_GettingStarted_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/GS1010_ini_01.wav\\\">Say tell me about Metro, buy a phone, open an account, or find a store</audio><audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio><audio src=\\\"AUDIO_LOCATION/GS1010_ini_02.wav\\\">If you are having trouble with an activation, say troubleshoot</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: buy_phone\n          displayName: buy_phone\n        - id: about_metro\n          displayName: about_metro\n        - id: open_acct\n          displayName: open_acct\n        - id: find_store\n          displayName: find_store\n        - id: main_menu\n          displayName: main_menu\n        - id: troubleshoot\n          displayName: troubleshoot\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/GS1010_nm1_01.wav\\\">You can say tell me about Metro,open an account, or find a store To browse our phones and plans, visit metro by t dash mobile dot com </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/GS1010_nm2_01.wav\\\">You can say tell me about Metro or press 1 Open an account or press 2, or say find a store or press 3 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/GS1010_nm2_01.wav\\\">You can say tell me about Metro or press 1 Open an account or press 2, or say find a store or press 3 </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_GS1010_GettingStarted_DM\n  value: =Text(Global.GS1010_GettingStarted_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_GS1010_GettingStarted_DM = \"buy_phone\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/GS1010_out_02.wav\\\">sure</audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: transfer\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.tag\n          value: purchase-phone\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: GS1040_GoToCallTransfer_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_GS1010_GettingStarted_DM = \"about_metro\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.securityRequired\n              value: false\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.needMDN\n              value: false\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.callType\n              value: about_metro\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.cti_Intent\n              value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: GS1015_CheckConfigSetting_JDA_DA\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_GS1010_GettingStarted_DM = \"open_acct\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.securityRequired\n                  value: false\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.needMDN\n                  value: false\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.callType\n                  value: activate_unknownDevice\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.cti_Intent\n                  value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: GS1015_CheckConfigSetting_JDA_DA\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_GS1010_GettingStarted_DM = \"find_store\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.securityRequired\n                      value: false\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.needMDN\n                      value: false\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.callType\n                      value: find_store\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.cti_Intent\n                      value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: GS1030_GoToStoreLocator_SD\n\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.Var_GS1010_GettingStarted_DM = \"main_menu\", true, false)\n                      actions:\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.GlobalVars.securityRequired\n                          value: false\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.GlobalVars.needMDN\n                          value: false\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.GlobalVars.callType\n                          value: undefined\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.GlobalVars.cti_Intent\n                          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: GS1065_GoToMainMenu_SD\n\n                  elseActions:\n                    - kind: ConditionGroup\n                      id: conditionGroup_REPLACE_THIS\n                      conditions:\n                        - id: conditionItem_REPLACE_THIS\n                          condition: =If(Global.Var_GS1010_GettingStarted_DM = \"troubleshoot\", true, false)\n                          actions:\n                            - kind: SendActivity\n                              id: sendActivity_REPLACE_THIS\n                              activity:\n                                speak:\n                                  - \"<audio src=\\\"AUDIO_LOCATION/GS1010_out_01.wav\\\">sure</audio>\"\n                            - kind: SetVariable\n                              id: setVariable_REPLACE_THIS\n                              variable: Global.GlobalVars.callType\n                              value: troubleshoot\n                            - kind: SetVariable\n                              id: setVariable_REPLACE_THIS\n                              variable: Global.GlobalVars.cti_Intent\n                              value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n                            - kind: GotoAction\n                              id: goto_REPLACE_THIS\n                              actionId: GS1040_GoToCallTransfer_SD\n```", "timestamp": 1749558306.998654, "content_hash": "439a02b7736c846c4d478763e0f9da2c"}