{"response": "```yaml\n- kind: Question\n  id: aa2588c_BBCollectPinDTMF_DM\n  displayName: aa2588c_BBCollectPinDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa2588c_nm1_01.wav\\\">Sorry Let's try THAT again  Please enter your 4-digit PIN now</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.aa2588c_BBCollectPinDTMF_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa2588c_ini_01.wav\\\">Please enter your 4-digit PIN</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/aa2588c_ni1_01.wav\\\">Sorry, we didn t get an entry  For security purposes we need the PIN that goes along with this card  Please enter the 4-digit PIN now or press * to start over</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa2588c_BBCollectPinDTMF_DM\n  value: =Text(Global.aa2588c_BBCollectPinDTMF_DM_reco)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.validationCriteriaVariables.collectPin\n  value: aa2588c_BBCollectPinDTMF_DM.returnvalue\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: aa2589_BBValidatePin_DB_DA\n```", "timestamp": 1749544350.1453974, "content_hash": "c55b395e0d04d2b3b8c8cafc3fd19ce7"}