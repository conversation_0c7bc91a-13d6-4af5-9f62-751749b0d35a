{"response": "```yaml\n- kind: Question\n  id: BC1420_BankCardCorrection_DM\n  displayName: BC1420_BankCardCorrection_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"{Switch(\n            true,\n            Global.payingWithEWallet = true,\n            \\\"To pay with a different card, say start over If you want to continue to pay with the card you picked, say continue\\\",\n            Global.payingWithEWallet <> true,\n            \\\"Which do you want to change say 'card number', 'expiration date', 'verification code' or 'zip code' You can also say 'start over', or 'they're all fine'\\\"\n        )}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"{Switch(\n            true,\n            Global.payingWithEWallet = true,\n            \\\"To start over with a different card, press 1 and I'll collect your payment information To authorize your payment from the card you picked, press 2\\\",\n            Global.payingWithEWallet <> true,\n            \\\"To change your card number, press 1 For the expiration date, press 2 For the verification code - 3 For the zip code - 4 To re-enter all of them - 5 If they're all good, press 6 \\\"\n        )}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"{Switch(\n            true,\n            Global.payingWithEWallet = true,\n            \\\"To start over with a different card, press 1 and I'll collect your payment information To authorize your payment from the card you picked, press 2\\\",\n            Global.payingWithEWallet <> true,\n            \\\"To change your card number, press 1 For the expiration date, press 2 For the verification code - 3 For the zip code - 4 To re-enter all of them - 5 If they're all good, press 6\\\"\n        )}\"\n\n  alwaysPrompt: true\n  variable: Global.BC1420_BankCardCorrection_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.FirstEntryBC1420 <> false,\n            \"alright\",\n            Global.payingWithEWallet = true && Global.tryOtherCardReason <> \"fail_authorization\",\n            \"To pay with a different card, say start over Or to continue with the one you picked, say continue\",\n            true,\n            [\n                \"Which would you like to change  The card number  The expiration date  The verification code   Or the zip code\",\n                \"test\",\n                {Switch(\n                    true,\n                    Global.cardStatus <> \"valid\" || Global.tryOtherCardReason = \"fail_authorization\",\n                    \"You can also say 'start over'\",\n                    !(Global.cardStatus <> \"valid\" || Global.tryOtherCardReason = \"fail_authorization\"),\n                    \"You can also say 'start over' or 'they're all fine'\"\n                )}\n            ]\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: cardnumber\n          displayName: cardnumber\n        - id: date\n          displayName: date\n        - id: cvv\n          displayName: cvv\n        - id: zipcode\n          displayName: zipcode\n        - id: startover\n          displayName: startover\n        - id: allfine\n          displayName: allfine\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"{Switch(\n            true,\n            Global.payingWithEWallet = true,\n            \\\"To pay with a different card, say start over If you want to continue to pay with the card you picked, say continue\\\",\n            Global.payingWithEWallet <> true,\n            \\\"Which do you want to change say 'card number', 'expiration date', 'verification code' or 'zip code' You can also say 'start over', or 'they're all fine'\\\"\n        )}\"\n        - \"{Switch(\n            true,\n            Global.payingWithEWallet = true,\n            \\\"To start over with a different card, press 1 and I'll collect your payment information To authorize your payment from the card you picked, press 2\\\",\n            Global.payingWithEWallet <> true,\n            \\\"To change your card number, press 1 For the expiration date, press 2 For the verification code - 3 For the zip code - 4 To re-enter all of them - 5 If they're all good, press 6 \\\"\n        )}\"\n        - \"{Switch(\n            true,\n            Global.payingWithEWallet = true,\n            \\\"To start over with a different card, press 1 and I'll collect your payment information To authorize your payment from the card you picked, press 2\\\",\n            Global.payingWithEWallet <> true,\n            \\\"To change your card number, press 1 For the expiration date, press 2 For the verification code - 3 For the zip code - 4 To re-enter all of them - 5 If they're all good, press 6\\\"\n        )}\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.saidOperator\n  value: GlobalVars.saidOperator\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.cardStatus\n  value: GlobalVars.ValidateCardOptions.cardStatus\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.tryOtherCardReason\n  value: GlobalVars.tryOtherCardReason\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.payingWithEWallet\n  value: (GlobalVars.payingWithEWallet)?GlobalVars.payingWithEWallet:false\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.gcallType\n  value: (GlobalVars.callType =='activate')?'active':'notActive'\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.FirstEntryBC1420\n  value: GlobalVars.FirstEntryBC1420 == false ? GlobalVars.FirstEntryBC1420 : true\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.eWalletAndCallType\n  value: payingWithEWallet + ',' + gcallType\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_BC1420_BankCardCorrection_DM\n  value: =Text(Global.BC1420_BankCardCorrection_DM_reco)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: GlobalVars.FirstEntryBC1420\n  value: false\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: GlobalVars.saidOperator\n  value: false\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_BC1420_BankCardCorrection_DM = \"cardnumber\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: GlobalVars.correctAllDetails\n          value: false\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: GlobalVars.bankCardNumber\n          value: undefined\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: BC1001_CheckContext_JDA_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_BC1420_BankCardCorrection_DM = \"date\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: GlobalVars.correctAllDetails\n              value: false\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: GlobalVars.bankCardDate\n              value: undefined\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: BC1101_CheckContext_JDA\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_BC1420_BankCardCorrection_DM = \"cvv\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: GlobalVars.correctAllDetails\n                  value: false\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: GlobalVars.bankCardCVV\n                  value: undefined\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: BC1201_CheckContext_JDA\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_BC1420_BankCardCorrection_DM = \"zipcode\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: GlobalVars.correctAllDetails\n                      value: false\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: GlobalVars.bankCardZip\n                      value: undefined\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: BC1301_CheckContext_JDA_DA\n\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.Var_BC1420_BankCardCorrection_DM = \"startover\", true, false)\n                      actions:\n                        - kind: ConditionGroup\n                          id: conditionGroup_REPLACE_THIS\n                          conditions:\n                            - id: conditionItem_REPLACE_THIS\n                              condition: =If(GlobalVars.payingWithEWallet = true, true, false)\n                              actions:\n                                - kind: SetVariable\n                                  id: setVariable_REPLACE_THIS\n                                  variable: GlobalVars.payingWithEWallet\n                                  value: false\n                                - kind: SetVariable\n                                  id: setVariable_REPLACE_THIS\n                                  variable: GlobalVars.tryOtherCardReason\n                                  value: disconfirm_wallet\n                          elseActions:\n                            - kind: SetVariable\n                              id: setVariable_REPLACE_THIS\n                              variable: GlobalVars.tryOtherCardReason\n                              value: startover\n                            - kind: SetVariable\n                              id: setVariable_REPLACE_THIS\n                              variable: GlobalVars.correctAllDetails\n                              value: true\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: GlobalVars.bankCardNumber\n                          value: undefined\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: GlobalVars.bankCardDate\n                          value: undefined\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: GlobalVars.bankCardCVV\n                          value: undefined\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: GlobalVars.bankCardZip\n                          value: undefined\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: GlobalVars.ValidateCardOptions.cardStatus\n                          value: undefined\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: GlobalVars.preferredPaymentMethod\n                          value: undefined\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: GlobalVars.authorizationFailureHandling\n                          value: undefined\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: getReturnLink()\n\n                  elseActions:\n                    - kind: ConditionGroup\n                      id: conditionGroup_REPLACE_THIS\n                      conditions:\n                        - id: conditionItem_REPLACE_THIS\n                          condition: =If(Global.Var_BC1420_BankCardCorrection_DM = \"allfine\", true, false)\n                          actions:\n                            - kind: SetVariable\n                              id: setVariable_REPLACE_THIS\n                              variable: GlobalVars.disconfirmedDetails\n                              value: false\n                            - kind: ConditionGroup\n                              id: conditionGroup_REPLACE_THIS\n                              conditions:\n                                - id: conditionItem_REPLACE_THIS\n                                  condition: =If(GlobalVars.ValidateCardOptions && GlobalVars.ValidateCardOptions.cardStatus = \"invalid\", true, false)\n                                  actions:\n                                    - kind: SetVariable\n                                      id: setVariable_REPLACE_THIS\n                                      variable: GlobalVars.bankCardNumber\n                                      value: undefined\n                                    - kind: SetVariable\n                                      id: setVariable_REPLACE_THIS\n                                      variable: GlobalVars.bankCardDate\n                                      value: undefined\n                                    - kind: SetVariable\n                                      id: setVariable_REPLACE_THIS\n                                      variable: GlobalVars.bankCardCVV\n                                      value: undefined\n                                    - kind: SetVariable\n                                      id: setVariable_REPLACE_THIS\n                                      variable: GlobalVars.bankCardZip\n                                      value: undefined\n                                    - kind: GotoAction\n                                      id: goto_REPLACE_THIS\n                                      actionId: BC1501_CheckContext_JDA\n                              elseActions:\n                                - kind: GotoAction\n                                  id: goto_REPLACE_THIS\n                                  actionId: getReturnLink()\n```", "timestamp": **********.8166466, "content_hash": "73d5b71a6fb5ea3ff2c28d4782ad7e6a"}