{"response": "```yaml\n- kind: Question\n  id: bm0415_RefillPINHelp_DM\n  displayName: bm0415_RefillPINHelp_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I missed that</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">Sorry</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.bm0415_RefillPINHelp_DM_reco\n  prompt:\n    speak:\n      - \"{CustomAudio: com.nuance.att.application.audio.bm0415_RefillPINHelp_initial}\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: ready\n          displayName: ready\n        - id: dont_have\n          displayName: dont_have\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_bm0415_RefillPINHelp_DM\n  value: =Text(Global.bm0415_RefillPINHelp_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_bm0415_RefillPINHelp_DM = \"ready\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.askRefillPINFromVar\n          value: bm0415_RefillPINHelp_DM\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/bm0415_out_01.wav\\\">Okay</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: bm0410_AskRefillPIN_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_bm0415_RefillPINHelp_DM = \"dont_have\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/bm0415_out_02.wav\\\">No problem</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: bm0420_AskCreditDebit_DM\n\n      elseActions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.refillPIN\n          value: bm0415_RefillPINHelp_DM.returnvalue\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.B02_AddMoney_05.dvxml\n```", "timestamp": 1749469799.8765597, "content_hash": "562835b65a6e4c395f705720348d66a3"}