{"response": "```yaml\n- kind: Question\n  id: sv0130_PlayVoicemailHelp_DM\n  displayName: sv0130_PlayVoicemailHelp_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/sv0130_ni1_01.wav\\\">To hear those instructions again, say Repeat or press 1 Or say Change Voicemail Password or press 2, or say Main Menu or press star If thats all you needed, you can hang up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/sv0130_nm2_01.wav\\\">If you want to hear the instructions about how to set up your voicemail again, press 1 If you want to change your voicemail password, press 2 If you need help with something else, press star or, if youre all done, go ahead and hang up</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.sv0130_PlayVoicemailHelp_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/sv0130_ini_01.wav\\\">To set up your voicemail, you will need to call the voicemail system To do that, just press and hold down the 1 key on your phone From there, follow the instructions to create your password and record your greeting Once your voicemail is set up, you can also press and hold the 1 key to listen to messages and manage your settings Say Repeat, or if thats all you needed, go ahead and hang up You can also say Change Voicemail Password or Main Menu</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: change\n          displayName: change\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/sv0130_ni1_01.wav\\\">To hear those instructions again, say Repeat or press 1 Or say Change Voicemail Password or press 2, or say Main Menu or press star If thats all you needed, you can hang up</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_sv0130_PlayVoicemailHelp_DM\n  value: =Text(Global.sv0130_PlayVoicemailHelp_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_sv0130_PlayVoicemailHelp_DM = \"change\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: changeVmailPassword\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: sv0135_VoicemailHelpReset_SD\n```", "timestamp": 1749472211.9050605, "content_hash": "eabb7a31cba803969e402432c3d78730"}