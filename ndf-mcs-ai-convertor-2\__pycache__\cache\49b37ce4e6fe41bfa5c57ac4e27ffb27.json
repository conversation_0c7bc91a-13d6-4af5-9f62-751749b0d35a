{"response": "```yaml\n- kind: Question\n  id: MP1030_SelectRatePlanShort_DM\n  displayName: MP1030_SelectRatePlanShort_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MP1030_nm1_01.wav\\\">Say</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MP1030_nm2_01.wav\\\">Say</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MP1030_nm2_01.wav\\\">Say</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MP1030_nm3_01.wav\\\">Or say  more information </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.MP1030_SelectRatePlanShort_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/MP1030_ini_01.wav\\\">Let s pick your monthly plan Here s what we have for your phone When you hear the one you want, say its name back to me</audio>\"\n      - \"{Global.availablePlansList}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MP1030_ini_02.wav\\\">To hear more about them, say more info If none of those are what you wanted, say different plan</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: more_info\n          displayName: more_info\n        - id: different_plan\n          displayName: different_plan\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/MP1030_nm1_01.wav\\\">Say</audio>\"\n        - \"{Global.availablePlansList}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MP1030_nm2_01.wav\\\">Say</audio>\"\n        - \"{Global.availablePlansList}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MP1030_nm2_01.wav\\\">Say</audio>\"\n        - \"{Global.availablePlansList}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MP1030_nm3_01.wav\\\">Or say  more information </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.disambigTooManyMatches\n  value: GlobalVars.disambigTooManyMatches\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.disambigNoMatches\n  value: GlobalVars.disambigNoMatches\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.availablePlansList\n  value: getAllRatePlans(GlobalVars.ratePlans)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.planType\n  value: none\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.planGrammarURL\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.planGrammarDtmfURL\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.allowedResponses\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.choseInvalidPlan\n  value: GlobalVars.choseInvalidPlan\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.planGrammarURL\n  value: GlobalVars.GetAvailableRatePlans.RatePlanGrammarURL\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.planGrammarDtmfURL\n  value: GlobalVars.GetAvailableRatePlans.RatePlanDTMFGrammarURL\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.onlyOneOrAmbiguous_MP1050\n  value: false\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.disambigTooManyMatches\n  value: false\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.disambigNoMatches\n  value: false\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_MP1030_SelectRatePlanShort_DM\n  value: =Text(Global.MP1030_SelectRatePlanShort_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MP1030_SelectRatePlanShort_DM = \"more_info\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: MP1040_SelectRatePlanMoreInfo_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_MP1030_SelectRatePlanShort_DM = \"different_plan\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.activationResult\n              value: transfer\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_MP1030_SelectRatePlanShort_DM = \"default\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.ratePlanSelectionType\n                  value: MP1030_SelectRatePlanShort_DM.nbestresults[0].interpretation.selectionType\n\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(MP1030_SelectRatePlanShort_DM.nbestresults <> undefined, true, false)\n                      actions:\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.GlobalVars.nbestresults\n                          value: MP1030_SelectRatePlanShort_DM.nbestresults\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.selectedPlan\n                  value: MP1030_SelectRatePlanShort_DM.returnvalue\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.choseInvalidPlan\n                  value: undefined\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: MP1042_CheckAmbiguity_JDA_DA\n```", "timestamp": 1749558491.9918284, "content_hash": "49b37ce4e6fe41bfa5c57ac4e27ffb27"}