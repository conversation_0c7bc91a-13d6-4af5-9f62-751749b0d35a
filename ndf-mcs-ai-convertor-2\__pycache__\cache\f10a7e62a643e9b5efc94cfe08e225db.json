{"response": "```yaml\n- kind: Question\n  id: ca0164_AccountSMSHelp_DM\n  displayName: ca0164_AccountSMSHelp_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ca0164_ni1_01.wav\\\">If theres anything else youd like to do, say Main Menu Otherwise, you can just hang up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ca0164_ni2_01.wav\\\">If theres anything else youd like to do, say Main Menu Otherwise, you can just hang up</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ca0164_AccountSMSHelp_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ca0164_ini_01.wav\\\">A text with your account number is on its way to the phone number associated with this account If theres anything else youd like to do, say Main Menu Otherwise, you can just hang up</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: mainmenu\n          displayName: mainmenu\n        - id: operator\n          displayName: operator\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ca0164_ni1_01.wav\\\">If theres anything else youd like to do, say Main Menu Otherwise, you can just hang up</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ca0164_ni2_01.wav\\\">If theres anything else youd like to do, say Main Menu Otherwise, you can just hang up</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ca0164_AccountSMSHelp_DM\n  value: =Text(Global.ca0164_AccountSMSHelp_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ca0164_AccountSMSHelp_DM = \"mainmenu\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferDestination\n          value: attPrepaid\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M01_MainMenu2_Dialog\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ca0164_AccountSMSHelp_DM = \"operator\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.lastTransferState\n          value: ca0164_AccountSMSHelp_DM\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferReason\n          value: AGENT_REQUEST\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M09_MainMenu_Dialog\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ca0164_AccountSMSHelp_DM = \"repeat\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ca0164_AccountSMSHelp_DM\n```", "timestamp": **********.6857777, "content_hash": "f10a7e62a643e9b5efc94cfe08e225db"}