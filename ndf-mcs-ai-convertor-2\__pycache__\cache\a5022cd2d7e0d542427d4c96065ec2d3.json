{"response": "```yaml\n- kind: Question\n  id: ma2412_MultiLineQuestion_DM\n  displayName: ma2412_MultiLineQuestion_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ma2412_ni1_01.wav\\\">If you'd like to sign-up for multi-line, say Yes or press 1 Otherwise, say No or press 2 You can also say Tell Me More or press 3</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ma2412_ni2_01.wav\\\">To sign-up for multi-line, press 1 Otherwise, press 2 For information on multi-line, press 3</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ma2412_MultiLineQuestion_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.ma2412_ReRecognize = false,\n            \"<audio src=\\\"AUDIO_LOCATION/ma2412_ini_01.wav\\\">Do you want to use this line to sign-up on a multi-line account? For details, say Tell Me More</audio>\",\n            \"Do you want to use this line to sign-up on a multi-line account? For details, say Tell Me More\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: yes\n          displayName: yes\n        - id: no\n          displayName: no\n        - id: continue\n          displayName: continue\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ma2412_ni1_01.wav\\\">If you'd like to sign-up for multi-line, say Yes or press 1 Otherwise, say No or press 2 You can also say Tell Me More or press 3</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ma2412_ni2_01.wav\\\">To sign-up for multi-line, press 1 Otherwise, press 2 For information on multi-line, press 3</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ma2412_MultiLineQuestion_DM\n  value: =Text(Global.ma2412_MultiLineQuestion_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma2412_MultiLineQuestion_DM = \"yes\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/ma2412_out_01.wav\\\">Okay</audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/ma2412_out_02.wav\\\">Go to a t t dot com slash multiline to learn how to setup or join a Multi-Line account Again, that's a t t dot com slash multiline Also, you should wait to add money until the multi-line account is setup  If that's all you needed, you can hang-up, or, to continue as an *individual* account, say Continue</audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.ma2412_ReRecognize\n          value: true\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ma2412_MultiLineQuestion_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ma2412_MultiLineQuestion_DM = \"no\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/ma2412_out_01.wav\\\">Okay</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: ma2415_ActivateRefill_SD\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_ma2412_MultiLineQuestion_DM = \"continue\", true, false)\n              actions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/ma2412_out_01.wav\\\">Okay</audio>\"\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: ma2415_ActivateRefill_SD\n```", "timestamp": 1749471403.7856905, "content_hash": "a5022cd2d7e0d542427d4c96065ec2d3"}