{"response": "```yaml\n- kind: Question\n  id: BC1310_GetBillingZipCode_DM\n  displayName: BC1310_GetBillingZipCode_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1310_nm1_01.wav\\\">Please enter the five-digit zip code where you receive the bills for this card Or say 'more info'</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1310_nm2_01.wav\\\">Using your keypad, enter the five-digit zip code where you receive the bills for this card For help finding it, press star</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1310_nm3_01.wav\\\">Please enter the zip code where your bank sends the bills and statements for this card, including any zeroes If you're not sure what it is, press star</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.BC1310_GetBillingZipCode_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n\n            Global.MDEinBC1305 = true,\n            \"Please enter the billing zip code for this card\",\n\n            Global.disconfirmedDetails = true && Global.correctAllDetails = false,\n            \"Please enter the correct one now\",\n\n            Global.callType <> \"activate\",\n            [\n                // If voiceOrDtmf = \"voice\", play number and silence\n                \"{Global.bankCardCVV}\", // number prompt, would be replaced with actual value in runtime\n                \"test\",\n                \"Last, what s the zip code where you get the bill for this card? It doesn t have to be the same as your Metro  account \"\n            ],\n\n            Global.useSameZipCode = \"disconfirmed\",\n            \"Ok, then enter the correct one now\",\n\n            true,\n            \"Ok, then go ahead and enter it now\"\n        )}\n\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_200ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1310_ini_03.wav\\\">Or say 'more info'</audio>\"\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1310_nm1_01.wav\\\">Please enter the five-digit zip code where you receive the bills for this card Or say 'more info'</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1310_nm2_01.wav\\\">Using your keypad, enter the five-digit zip code where you receive the bills for this card For help finding it, press star</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1310_nm3_01.wav\\\">Please enter the zip code where your bank sends the bills and statements for this card, including any zeroes If you're not sure what it is, press star</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.saidOperator\n  value: GlobalVars.saidOperatorBC1310?GlobalVars.saidOperatorBC1310:false\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.disconfirmedDetails\n  value: GlobalVars.disconfirmedDetails\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.correctAllDetails\n  value: GlobalVars.correctAllDetails\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.useSameZipCode\n  value: GlobalVars.useSameZipCode\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.fromBC1320\n  value: GlobalVars.fromBC1320\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.bankCardCVV\n  value: GlobalVars.bankCardCVV\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.voiceOrDtmf\n  value: GlobalVars.VerificationCode_voiceOrDtmf\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.MDEinBC1305\n  value: GlobalVars.MDEinBC1305\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.callType\n  value: GlobalVars.callType\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.fromBC1320\n  value: undefined\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.MDEinBC1305\n  value: undefined\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.saidOperatorBC1310\n  value: false\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.implicitConfirmReject\n  value: false\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_BC1310_GetBillingZipCode_DM = \"more_info\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: BC1320_MoreInfoZipWaitSBI_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_BC1310_GetBillingZipCode_DM = \"thats_not_right\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.implicitConfirmReject\n              value: true\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.bankCardCVV\n              value: undefined\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: BC1205_GetVerificationCode_DM\n\n      elseActions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.bankCardZip\n          value: BC1310_GetBillingZipCode_DM.returnvalue\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: BC1401_CheckContext_JDA_DA\n```", "timestamp": **********.564694, "content_hash": "3ec1513e8771c2506b3c9bf8d939da68"}