{"response": "```yaml\n- kind: Question\n  id: ma1350_SetupServiceDisambig_DM\n  displayName: ma1350_SetupServiceDisambig_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ma1350_ni1_01.wav\\\">Do you want to add a line to an existing account Just say Yes or No</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ma1350_ni2_01.wav\\\">If you want to add an additional line to your account, say Yes or press 1 If not, say No or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ma1350_SetupServiceDisambig_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ma1350_ini_01.wav\\\">Do you want to add a line to an existing account</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ma1350_ni1_01.wav\\\">Do you want to add a line to an existing account Just say Yes or No</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ma1350_ni2_01.wav\\\">If you want to add an additional line to your account, say Yes or press 1 If not, say No or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ma1350_SetupServiceDisambig_DM\n  value: =Text(Global.ma1350_SetupServiceDisambig_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma1350_SetupServiceDisambig_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: add_line\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ma1355_MultilineInfoWrap_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ma1350_SetupServiceDisambig_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/ma1350_out_01.wav\\\">Okay</audio>\"\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.callType\n              value: activations\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.offerChatAgent\n              value: false\n\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.M02_MainMenu.dvxml#ma2126_OtherActivationChannels_PP\n```", "timestamp": 1749471472.185429, "content_hash": "4019058388b726f95d1fb7c495002b3d"}