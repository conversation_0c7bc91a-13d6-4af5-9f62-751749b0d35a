{"response": "```yaml\n- kind: Question\n  id: MW1205_GetCardNumber_DM\n  displayName: MW1205_GetCardNumber_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1205_nm1_01.wav\\\">What s the card number?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1205_nm2_01.wav\\\">Please say or enter your card number</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1205_nm2_01.wav\\\">Please say or enter your card number</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.MW1205_GetCardNumber_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.failedChecksum = true,\n            \"Hmm, I had trouble with that card  Please tell me the card number once more\",\n            Global.failedChecksum <> true,\n            \"Ok, what s the card number?\"\n        )}\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1205_nm1_01.wav\\\">What s the card number?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1205_nm2_01.wav\\\">Please say or enter your card number</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1205_nm2_01.wav\\\">Please say or enter your card number</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.failedChecksum\n  value: GlobalVars.failedChecksum\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: GlobalVars.bankCardNumber\n  value: MW1205_GetCardNumber_DM.returnvalue\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(GlobalVars.bankCardNumber.substring(0,2) = \"34\" || GlobalVars.bankCardNumber.substring(0,2) = \"37\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: GlobalVars.cardTypeAmex\n          value: true\n\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: GlobalVars.cardTypeAmex\n      value: false\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(GlobalVars.bankCardNumber.substring(0,2) = \"65\" || GlobalVars.bankCardNumber.substring(0,2) = \"64\" || GlobalVars.bankCardNumber.substring(0,2) = \"62\" || GlobalVars.bankCardNumber.substring(0,4) = \"6011\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: GlobalVars.unsupportedCardIssuer\n          value: discover\n\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: GlobalVars.unsupportedCardIssuer\n      value: \"\"\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: MW1208_EvaluateCardEntered_JDA\n```", "timestamp": **********.6758635, "content_hash": "ea80bde92b1c532971a45ef641ee0627"}