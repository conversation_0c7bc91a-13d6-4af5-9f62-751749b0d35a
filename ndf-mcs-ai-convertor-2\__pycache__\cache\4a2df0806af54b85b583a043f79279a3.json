{"response": "```yaml\n- kind: Question\n  id: TS1005_AskSupportType_DM\n  displayName: TS1005_AskSupportType_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TS1005_ini_01.wav\\\">Which do you need help with? Say mobile data, hotspot, or something else</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TS1005_nm2_01.wav\\\">Please say mobile data or press 1, or hotspot or press 2 For all other help, say something else or press 3</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TS1005_nm2_01.wav\\\">Please say mobile data or press 1, or hotspot or press 2 For all other help, say something else or press 3</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.TS1005_AskSupportType_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/TS1005_ini_01.wav\\\">Which do you need help with? Say mobile data, hotspot, or something else</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: mobile\n          displayName: mobile\n        - id: hotspot\n          displayName: hotspot\n        - id: something-else_troubleshooting\n          displayName: something-else_troubleshooting\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/TS1005_ini_01.wav\\\">Which do you need help with? Say mobile data, hotspot, or something else</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/TS1005_nm2_01.wav\\\">Please say mobile data or press 1, or hotspot or press 2 For all other help, say something else or press 3</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/TS1005_nm2_01.wav\\\">Please say mobile data or press 1, or hotspot or press 2 For all other help, say something else or press 3</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_TS1005_AskSupportType_DM\n  value: =Text(Global.TS1005_AskSupportType_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TS1005_AskSupportType_DM = \"mobile\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.techSupportType\n          value: mobiledata\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TS1105_CheckHaveDataUsage_JDA_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_TS1005_AskSupportType_DM = \"hotspot\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.techSupportType\n              value: hotspot\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: TS1105_CheckHaveDataUsage_JDA_DA\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_TS1005_AskSupportType_DM = \"something-else_troubleshooting\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.techSupportType\n                  value: other\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: TS1010_TechSupportTransfer_SD\n```", "timestamp": 1749558613.086257, "content_hash": "4a2df0806af54b85b583a043f79279a3"}