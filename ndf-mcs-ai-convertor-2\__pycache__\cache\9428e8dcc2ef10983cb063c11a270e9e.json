{"response": "```yaml\n- kind: Question\n  id: aa6065_CCCollectPinDTMF_DM\n  displayName: aa6065_CCCollectPinDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa6065_nm1_01.wav\\\">Sorry, lets try that again  Please enter your 4 digit pin </audio>\"\n      - |\n        {Switch(\n              true,\n              Global.dnisInfo.dtmfOnlyFlag = true,\n              [\n                \"Sorry, lets try that again \",\n                \"Please enter your pin\"\n              ],\n              true,\n              \"Sorry, I still didnt get that Lets try it another way\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.aa6065_CCCollectPinDTMF_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.globalVariables.cameFrom = \"aa6056\" || Global.globalVariables.cameFrom = \"aa6055\",\n              \"Finally, to complete your payment, please enter your 4-digit PIN\",\n              Global.childCareInfoVariables.firstTime = true,\n              \"I m sorry, but the payment was not applied  The PIN entered does not match our records  Lets, try again  Please enter your 4-digit PIN now\",\n              true,\n              \"I m sorry, but the payment was not applied  That PIN entered does not match our records either  Lets, try again  Please enter your 4-digit PIN now pause If you would like to speak to an agent, press 0\"\n          )\n        }\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/aa6065_ni1_01.wav\\\">Sorry, lets try that again  Please enter your 4 digit pin </audio>\"\n        - |\n          {Switch(\n                true,\n                Global.dnisInfo.dtmfOnlyFlag = true,\n                [\n                  \"Sorry, I still didn t get that\",\n                  \"Please enter your pin\"\n                ],\n                true,\n                \"Sorry, I still didn t get anything Let s try it another way\"\n            )\n          }\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.childCareInfoVariables.collectPin\n  value: aa6065_CCCollectPinDTMF_DM.returnvalue\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.childCareInfoVariables.firstTime = true, true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.childCareInfoVariables.firstTime\n          value: false\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.dnisInfo.callType = \"WICC\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aa6065_out_01.wav\\\">Thank you I ll go ahead and try to apply that payment for you  One Moment</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa6069_WICCValidatePin_DB_DA\n\n  elseActions:\n    - kind: SendActivity\n      id: sendActivity_REPLACE_THIS\n      activity:\n        speak:\n          - \"<audio src=\\\"AUDIO_LOCATION/aa6065_out_01.wav\\\">Thank you I ll go ahead and try to apply that payment for you  One Moment</audio>\"\n    - kind: GotoAction\n      id: goto_REPLACE_THIS\n      actionId: aa6070_CCApplyPayment_DB_DA\n```", "timestamp": 1749543993.0648346, "content_hash": "9428e8dcc2ef10983cb063c11a270e9e"}