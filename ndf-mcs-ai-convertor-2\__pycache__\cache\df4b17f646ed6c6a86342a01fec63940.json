{"response": "```yaml\n- kind: Question\n  id: cp0225_AskTempPIN_DM\n  displayName: cp0225_AskTempPIN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/cp0225_ni1_01.wav\\\">Please say or enter your temporary PIN</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.cp0225_AskTempPIN_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.numTempPINAttempts = 0,\n            \"Say or enter your temporary PIN\",\n            \"Sorry, that PIN doesnt match Please say or enter the temporary PIN you received one more time\"\n        )}\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/cp0225_ni1_01.wav\\\">Please say or enter your temporary PIN</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.numTempPINAttempts\n  value: numTempPINAttempts + 1\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.tempPIN\n  value: cp0225_AskTempPIN_DM.returnvalue\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: cp0235_ValidateTempPIN_DB_DA\n```", "timestamp": 1749470494.155863, "content_hash": "df4b17f646ed6c6a86342a01fec63940"}