{"response": "```yaml\n- kind: Question\n  id: aa1056_ConfirmCardMissing_DM\n  displayName: aa1056_ConfirmCardMissing_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.aa1056_ConfirmCardMissing_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa1056_ini_01.wav\\\">If you d like to cancel your card, say,  Cancel My Card  If you re ready with your card, say,  I m Ready with my Card  Otherwise, please hang up and call us back later</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: cancel\n          displayName: cancel\n\n        - id: ready\n          displayName: ready\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa1056_ConfirmCardMissing_DM\n  value: =Text(Global.aa1056_ConfirmCardMissing_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa1056_ConfirmCardMissing_DM = \"cancel\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.transferAllowed\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.transferReason\n          value: lost\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aa1055_out_01.wav\\\">Okay</audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/aa1055_out_03.wav\\\">I ll transfer you to a customer service representative</audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa9805_ProcessTransfer_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa1056_ConfirmCardMissing_DM = \"ready\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/aa1056_out_04.wav\\\">Your card has not been cancelled</audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa1031_FullCardInputMode_JDA\n```", "timestamp": 1749458544.7378535, "content_hash": "95b98ae9fdde1b453ddd88fc2d411e25"}