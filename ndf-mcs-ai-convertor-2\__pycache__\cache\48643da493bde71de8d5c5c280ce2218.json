{"response": "```yaml\n- kind: Question\n  id: aa6535_TransactionHistoryWrapUpDTMF_DM\n  displayName: aa6535_TransactionHistoryWrapUpDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.dnisInfo.callType = \"WICC\",\n              \"To hear that again, Press 1 For the main menu, Press 2 And if you re done, feel free to hang up\",\n          \n              true,\n              \"To hear that again, Press 1 For other kinds of transactions, Press 2 To get a Statement, Press 3 To dispute a transaction, Press 4 For the main menu, Press 5 And if you re done, feel free to hang up\"\n          )\n        }\n      - |\n        {Switch(\n              true,\n              Global.dnisInfo.dtmfOnlyFlag = true,\n              \"Sorry, I still didn t get that\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.aa6535_TransactionHistoryWrapUpDTMF_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.dnisInfo.callType = \"WICC\",\n              \"To hear that again, Press 1 For the main menu, Press 2 And if you re done, feel free to hang up\",\n          \n              true,\n              \"To hear that again, Press 1 For other kinds of transactions, Press 2 To get a Statement, Press 3 To dispute a transaction, Press 4 For the main menu, Press 5 And if you re done, feel free to hang up\"\n          )\n        }\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: repeat\n          displayName: repeat\n        - id: main_menu\n          displayName: main_menu\n        - id: other\n          displayName: other\n        - id: statements\n          displayName: statements\n        - id: disputes\n          displayName: disputes\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa6535_TransactionHistoryWrapUpDTMF_DM\n  value: =Text(Global.aa6535_TransactionHistoryWrapUpDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa6535_TransactionHistoryWrapUpDTMF_DM = \"repeat\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aa6535_out_01.wav\\\">Again</audio>\"\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.returnCodeDetail = \"68\" || Global.returnCodeDetail = \"10\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: aa6520_NoTransactionsFound_PP\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.returnCodeDetail = \"11\", true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: aa6525_OneTransactionsFound_PP\n\n              elseActions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak: []\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: aa6527_MultipleTransactionsFound_PP\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa6535_TransactionHistoryWrapUpDTMF_DM = \"main_menu\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aa6536_out_06.wav\\\">Sure</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: handleMainMenu_CS\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa6535_TransactionHistoryWrapUpDTMF_DM = \"other\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aa6535_out_03.wav\\\">Okay</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa6510_TransactionHistoryMainMenuDTMF_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa6535_TransactionHistoryWrapUpDTMF_DM = \"statements\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.transferReason\n          value: Statements\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.transferAllowed\n          value: true\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa9805_ProcessTransfer_PP\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa6535_TransactionHistoryWrapUpDTMF_DM = \"disputes\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.transferReason\n          value: Disputes\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.transferAllowed\n          value: true\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa9805_ProcessTransfer_PP\n```", "timestamp": 1749543802.6021886, "content_hash": "48643da493bde71de8d5c5c280ce2218"}