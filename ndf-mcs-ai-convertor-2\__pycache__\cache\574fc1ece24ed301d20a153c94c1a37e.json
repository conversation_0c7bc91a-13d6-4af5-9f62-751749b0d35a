{"response": "```yaml\n- kind: Question\n  id: aa3020_WICCBalanceWrapUpDTMF_DM\n  displayName: aa3020_WICCBalanceWrapUpDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"\"\n\n  alwaysPrompt: true\n  variable: Global.aa3020_WICCBalanceWrapUpDTMF_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa3020_ini_01.wav\\\">To hear that again, Press 1 To make a payment, Press 2 For the main menu, Press 3 Or if you re done, feel free to hang up</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: make_payment\n          displayName: make_payment\n        - id: main_menu\n          displayName: main_menu\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa3020_WICCBalanceWrapUpDTMF_DM\n  value: =Text(Global.aa3020_WICCBalanceWrapUpDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa3020_WICCBalanceWrapUpDTMF_DM = \"make_payment\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.nextStep\n          value: make_payment_wicc\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: return\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa3020_WICCBalanceWrapUpDTMF_DM = \"main_menu\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: handleMainMenu_CS\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_aa3020_WICCBalanceWrapUpDTMF_DM = \"repeat\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: aa3010_WICCBalance1Child_PP\n```", "timestamp": 1749543356.0528405, "content_hash": "574fc1ece24ed301d20a153c94c1a37e"}