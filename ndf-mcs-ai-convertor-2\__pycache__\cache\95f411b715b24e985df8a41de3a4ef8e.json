{"response": "```yaml\n- kind: Question\n  id: PR1015_AskCustomerDealerorEmployee_DM\n  displayName: PR1015_AskCustomerDealerorEmployee_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/PR1015_nm1_01.wav\\\">Which are you Please say 'customer', 'dealer' or 'employee'</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/PR1015_nm2_01.wav\\\">If you are Metro by T-Mobile customer, say 'customer' or press 1 If you are a dealer, say 'dealer', or press 2 If you work as a Metro employee, say 'employee' or press 3</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/PR1015_nm2_01.wav\\\">If you are Metro by T-Mobile customer, say 'customer' or press 1 If you are a dealer, say 'dealer', or press 2 If you work as a Metro employee, say 'employee' or press 3</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.PR1015_AskCustomerDealerorEmployee_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/PR1015_ini_01.wav\\\">Now, tell me, are you a Metro by T-Mobile customer, dealer, or an employee? </audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: customer\n          displayName: customer\n        - id: dealer\n          displayName: dealer\n        - id: employee\n          displayName: employee\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/PR1015_nm1_01.wav\\\">Which are you Please say 'customer', 'dealer' or 'employee'</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/PR1015_nm2_01.wav\\\">If you are Metro by T-Mobile customer, say 'customer' or press 1 If you are a dealer, say 'dealer', or press 2 If you work as a Metro employee, say 'employee' or press 3</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/PR1015_nm2_01.wav\\\">If you are Metro by T-Mobile customer, say 'customer' or press 1 If you are a dealer, say 'dealer', or press 2 If you work as a Metro employee, say 'employee' or press 3</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_PR1015_AskCustomerDealerorEmployee_DM\n  value: =Text(Global.PR1015_AskCustomerDealerorEmployee_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_PR1015_AskCustomerDealerorEmployee_DM = \"customer\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: PR1030_PasswordResetDisambig_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_PR1015_AskCustomerDealerorEmployee_DM = \"dealer\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.language = \"en-US\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.TransferTag\n                      value: Password_Dealer_English\n            elseActions:\n              - kind: SetVariable\n                id: setVariable_REPLACE_THIS\n                variable: Global.TransferTag\n                value: Password_Dealer_Spanish\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: PR1025_GoToTransfer_SD\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_PR1015_AskCustomerDealerorEmployee_DM = \"employee\", true, false)\n              actions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.language = \"en-US\", true, false)\n                      actions:\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.TransferTag\n                          value: Password_Employee_English\n                elseActions:\n                  - kind: SetVariable\n                    id: setVariable_REPLACE_THIS\n                    variable: Global.TransferTag\n                    value: Password_Employee_Spanish\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: PR1025_GoToTransfer_SD\n```", "timestamp": 1749528993.9646137, "content_hash": "95f411b715b24e985df8a41de3a4ef8e"}