{"response": "```yaml\n- kind: Question\n  id: ai0215_AskIMEIGophoneFallback1_DM\n  displayName: ai0215_AskIMEIGophoneFallback1_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.ai0215_AskIMEIGophoneFallback1_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.foundIMEI = false,\n            \"That's OK Let's try something different  We'll use your new phone to look up your IMEI or serial number  To get started, please turn on your phone  If the battery isn't charged yet, you might have to first plug in the charger When you have the phone on, say I'm Ready Or say I Don't Have It \",\n\n            true,\n            \"Next, we'll use  your new phone to look up your IMEI or serial number  To get started, please turn on your phone  If the battery isn't charged yet, you might have to first plug in the charger When you have the phone on, say I'm Ready Or say I Don't Have It \"\n        )}\n      - \"<audio src=\\\"AUDIO_LOCATION/percolation_20s.wav\\\">20 seconds of hold music</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ai0215_ini_04.wav\\\">Remember, once your new phone is on, say I'm Ready Or say I Don't Have It</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/percolation_20s.wav\\\">20 seconds of hold music</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ai0215_ini_04.wav\\\">Remember, once your new phone is on, say I'm Ready Or say I Don't Have It</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/percolation_20s.wav\\\">20 seconds of hold music</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ai0215_ini_04.wav\\\">Remember, once your new phone is on, say I'm Ready Or say I Don't Have It</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/percolation_20s.wav\\\">20 seconds of hold music</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ai0215_ini_04.wav\\\">Remember, once your new phone is on, say I'm Ready Or say I Don't Have It</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/percolation_20s.wav\\\">20 seconds of hold music</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ai0215_ini_04.wav\\\">Remember, once your new phone is on, say I'm Ready Or say I Don't Have It</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/percolation_20s.wav\\\">20 seconds of hold music</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: im_ready\n          displayName: im_ready\n        - id: dont_have\n          displayName: dont_have\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ai0215_AskIMEIGophoneFallback1_DM\n  value: =Text(Global.ai0215_AskIMEIGophoneFallback1_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ai0215_AskIMEIGophoneFallback1_DM = \"im_ready\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ai0217_EnterCodeWait_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ai0215_AskIMEIGophoneFallback1_DM = \"dont_have\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/ai0215_out_01.wav\\\">Youll need your new phone to complete the activation When you have it, just call us back</audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\"></audio>\"\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.M09_MainMenu_Dialog\n```", "timestamp": 1749469551.7534688, "content_hash": "7341f3872098a60f3c992c233229fdc1"}