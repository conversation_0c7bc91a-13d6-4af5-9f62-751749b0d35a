{"response": "```yaml\n- kind: Question\n  id: an0130_PlayCheckStatusOnline_DM\n  displayName: an0130_PlayCheckStatusOnline_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/an0130_ni1_01.wav\\\">You'll need your wireless number and password to make the first payment on your account You'll also need to know how to do that Do you want me to repeat all that information? Just say Yes or No</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/an0130_ni2_01.wav\\\">To hear that information again, say Yes or press 1 If you're done, say No or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.an0130_PlayCheckStatusOnline_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.requestedRepeat = true || Global.requestedYes = true,\n            [\n              \"Sure, here it is again There's a delay in getting your account set up in our payment system, but I can give you your new wireless number now which you’ll need to make your first payment\",\n              \"\",\n              \"Your new wireless number is\",\n              \"{Global.subscriberNumber}\",\n              \"\",\n              \"Again, that's\",\n              \"{Global.subscriberNumber}\",\n              \"\",\n              \"And your initial password is set to the last four digits of your wireless number\",\n              \"\",\n              {Switch(\n                true,\n                Global.accountType = \"gophone\",\n                \"To finish your activation, you'll need to wait around 30 minutes, and then make the first payment on your account To do that, call 611 from your new phone, or go online to a t t dot com slash myprepaid, and use the wireless number and password I just gave you\",\n                \"To finish your activation, you'll need to wait around 30 minutes, and then make the first payment on your account To do that, call ************, or go online to a t t dot com slash myprepaid, and use the wireless number and password I just gave you\"\n              )},\n              \"\",\n              \"Would you like to hear that again?\"\n            ],\n            [\n              \"Sorry, there's a delay in getting your account set up in our payment system, but I can give you your new wireless number\",\n              \"\",\n              \"Your new wireless number is\",\n              \"{Global.subscriberNumber}\",\n              \"\",\n              \"Again, that's\",\n              \"{Global.subscriberNumber}\",\n              \"\",\n              \"And your initial password is set to the last four digits of your wireless number\",\n              \"\",\n              {Switch(\n                true,\n                Global.accountType = \"gophone\",\n                \"To finish your activation, you'll need to wait around 30 minutes, and then make the first payment on your account To do that, call 611 from your new phone, or go online to a t t dot com slash myprepaid, and use the wireless number and password I just gave you\",\n                \"To finish your activation, you'll need to wait around 30 minutes, and then make the first payment on your account To do that, call ************, or go online to a t t dot com slash myprepaid, and use the wireless number and password I just gave you\"\n              )},\n              \"\",\n              \"Would you like to hear that again?\"\n            ]\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/an0130_ni1_01.wav\\\">You'll need your wireless number and password to make the first payment on your account You'll also need to know how to do that Do you want me to repeat all that information? Just say Yes or No</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/an0130_ni2_01.wav\\\">To hear that information again, say Yes or press 1 If you're done, say No or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_an0130_PlayCheckStatusOnline_DM\n  value: =Text(Global.an0130_PlayCheckStatusOnline_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_an0130_PlayCheckStatusOnline_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.requestedYes\n          value: true\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: an0130_PlayCheckStatusOnline_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_an0130_PlayCheckStatusOnline_DM = \"false\", true, false)\n          actions:\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.M09_MainMenu_Dialog\n```", "timestamp": 1749469677.9873776, "content_hash": "a021f77f179fe812c022d808085e48b9"}