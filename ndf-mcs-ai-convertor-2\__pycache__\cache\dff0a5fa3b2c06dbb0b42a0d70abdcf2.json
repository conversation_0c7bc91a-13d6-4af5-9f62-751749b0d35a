{"response": "```yaml\n- kind: Question\n  id: NP0055_GetOSPAccountNum_DM\n  displayName: NP0055_GetOSPAccountNum_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/NP0055_nm1_01.wav\\\">You can either say or enter the account number you had with your old service provider pause If you don t know it, say  I don t know it </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/NP0055_nm2_01.wav\\\">The account number from your old telephone company can usually be found on a recent bill Please enter that account number now, using your telephone keypad pause Or if you don t know what it is, say  I don t know it </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/NP0055_nm2_01.wav\\\">The account number from your old telephone company can usually be found on a recent bill Please enter that account number now, using your telephone keypad pause Or if you don t know what it is, say  I don t know it </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.NP0055_GetOSPAccountNum_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/NP0055_ini_01.wav\\\">One digit at a time, say or enter the account number you had with your old provider</audio>\"\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/NP0055_nm1_01.wav\\\">You can either say or enter the account number you had with your old service provider pause If you don t know it, say  I don t know it </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/NP0055_nm2_01.wav\\\">The account number from your old telephone company can usually be found on a recent bill Please enter that account number now, using your telephone keypad pause Or if you don t know what it is, say  I don t know it </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/NP0055_nm2_01.wav\\\">The account number from your old telephone company can usually be found on a recent bill Please enter that account number now, using your telephone keypad pause Or if you don t know what it is, say  I don t know it </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.NumberPortInVars.accountNumber\n  value: NP0055_GetOSPAccountNum_DM.returnvalue\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: NP0060_GetOSPSecurityPIN_DM\n```", "timestamp": **********.284602, "content_hash": "dff0a5fa3b2c06dbb0b42a0d70abdcf2"}