{"response": "```yaml\n- kind: Question\n  id: SH1009_AskPayNow_DM\n  displayName: SH1009_AskPayNow_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SH1009_ini_01.wav\\\">Would you like to make a payment now?</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.SH1009_AskPayNow_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/SH1009_ini_01.wav\\\">Would you like to make a payment now?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/SH1009_ini_01.wav\\\">Would you like to make a payment now?</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_commandgrammar\n  value: SH1009_AskPayNow_DM.grxml\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_dtmfcommandgrammar\n  value: SH1009_AskPayNow_DM_dtmf.grxml\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_commandgrammar\n  value: collection_commandgrammar + '?SWI_vars.disallow=repeat^operator'\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_dtmfcommandgrammar\n  value: collection_dtmfcommandgrammar + '?SWI_vars.disallow=repeat^operator'\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_SH1009_AskPayNow_DM\n  value: =Text(Global.SH1009_AskPayNow_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_SH1009_AskPayNow_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: make_pmt\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: SH1010_MakePayment_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_SH1009_AskPayNow_DM = \"false\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: SH1008_CheckNLUMenuConfig_JDA_DA\n```", "timestamp": 1749558561.5300472, "content_hash": "a01af1e25d0e91db1e0bc7217d11f4cc"}