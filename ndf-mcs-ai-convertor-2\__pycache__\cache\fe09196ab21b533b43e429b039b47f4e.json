{"response": "```yaml\n- kind: Question\n  id: AQ1115_SchoolQuestion_DM\n  displayName: AQ1115_SchoolQuestion_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/AQ1115_nm1_01.wav\\\">I'm sorry, that's not the answer I have Please try again If you want, you can also spell it</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AQ1115_nm2_01.wav\\\">That doesn't match my records either Please try one more time</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.AQ1115_SchoolQuestion_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.securityQuestionCode = \"Q11\" && (Global.callType = \"dsgExtension\" || Global.callType = \"switch_lines\"),\n            \"The security question is your first elementary school test If you know the answer, say it now Or say 'wait a minute'\",\n\n            Global.securityQuestionCode = \"Q11\" && (Global.callType <> \"dsgExtension\" && Global.callType <> \"switch_lines\"),\n            \"I can also use the answer to your security question - your first elementary school test If you know the answer, say it now Or say 'wait a minute'\",\n\n            Global.securityQuestionCode = \"SQ4\" && (Global.callType = \"dsgExtension\" || Global.callType = \"switch_lines\"),\n            \"The security question is your first school test If you know the answer, say it now Or say 'wait a minute'\",\n\n            Global.securityQuestionCode = \"SQ4\" && (Global.callType <> \"dsgExtension\" && Global.callType <> \"switch_lines\"),\n            \"I can also use the answer to your security question - your first school test If you know the answer, say it now Or say 'wait a minute'\",\n\n            \"test If you know the answer, say it now Or say 'wait a minute'\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: wait\n          displayName: wait\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/AQ1115_ni1_01.wav\\\">Sorry please say the answer to your security question now You can also spell it or say 'wait a minute'</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AQ1115_ni2_01.wav\\\">Please say the answer to your security question now You can also spell it or say 'wait a minute'</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.saidOperator\n  value: \"GlobalVars.saidOperatorAQ1115?GlobalVars.saidOperatorAQ1115:false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.securityInfo\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.callType\n  value: \"GlobalVars.callType\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.securityQuestionCode\n  value: \"GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.securityQuestionCode:''\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.securityAnswer\n  value: \"GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.securityAnswer:''\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_maxnoinputs\n  value: \"GlobalVars.smsReceived?1:2\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_maxnomatches\n  value: \"GlobalVars.smsReceived?1:2\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.smsReceived\n  value: \"GlobalVars.smsReceived\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.securityInfo\n  value: securityAnswer + ';' + securityQuestionCode + ';' + language\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: GlobalVars.saidOperatorAQ1115\n  value: false\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_AQ1115_SchoolQuestion_DM\n  value: =Text(Global.AQ1115_SchoolQuestion_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AQ1115_SchoolQuestion_DM = \"wait\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: AQ1130_GetAnswerWaitSBI_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_AQ1115_SchoolQuestion_DM = \"default\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: GlobalVars.verificationType\n              value: question\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: GlobalVars.verificationValue\n              value: AQ1115_SchoolQuestion_DM.returnvalue\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: AQ1134_Authenticate_DB_DA\n```", "timestamp": 1749527654.8448548, "content_hash": "fe09196ab21b533b43e429b039b47f4e"}