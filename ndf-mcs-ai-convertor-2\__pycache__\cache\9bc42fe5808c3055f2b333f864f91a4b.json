{"response": "```yaml\n- kind: Question\n  id: cp0310_AskNewPIN_DM\n  displayName: cp0310_AskNewPIN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/cp0310_nm1_01.wav\\\">Please say or enter your new PIN one more time Make sure that s 4 digit long Or if you need some time, say Hold On</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/cp0310_nm2_01.wav\\\">Please enter a PIN that s 4 digit long Or if you need some time, say Hold On</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.cp0310_AskNewPIN_DM_reco\n  prompt:\n    speak:\n      - \"{CustomAudio: com.nuance.att.application.audio.cp0310_AskNewPIN_DM_initial}\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: hold_on\n          displayName: hold_on\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"{CustomAudio: com.nuance.att.application.audio.cp0310_AskNewPIN_DM_noinput1}\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_dtmfgrammar1\n  value: \"'cp0310_AskNewPIN_DM_dtmf.grxml?SWI_vars.disallow='+last4Number+'^'+passcode\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_grammar1\n  value: \"'cp0310_AskNewPIN_DM.grxml?SWI_vars.disallow='+last4Number+'^'+passcode\"\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_cp0310_AskNewPIN_DM\n  value: =Text(Global.cp0310_AskNewPIN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_cp0310_AskNewPIN_DM = \"hold_on\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: cp0315_NewPINWait_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_cp0310_AskNewPIN_DM = \"default\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.numNewPINAttempts\n              value: numNewPINAttempts + 1\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.newPassCode\n              value: cp0310_AskNewPIN_DM.returnvalue\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: cp0320_SetNewPIN_DB_DA\n```\n\n---\n\n**Explanation of the YAML conversion:**\n\n- **kind:** Always \"Question\" as per your instruction.\n- **id/displayName:** Set to the dm-state id: `cp0310_AskNewPIN_DM`.\n- **unrecognizedPrompt:** Both nomatchprompts are converted to `<audio>` tags as per your pattern.\n- **prompt:** Since the initial prompt is a custom audio class, it's represented as `{CustomAudio: ...}` for clarity.\n- **entity:** Since there are two labels (\"hold_on\" and \"default\"), a ClosedListEntity is created with both.\n- **voiceInputSettings:** Standard timeouts, and the noinput prompt is a custom audio class.\n- **SetVariable:** For both session-mapping keys at the top.\n- **SetVariable:** For the Var_cp0310_AskNewPIN_DM variable, as per your pattern for multi-label success.\n- **ConditionGroup:** For each label, a condition is created to check the value of the variable, and the appropriate actions are taken (GotoAction or SetVariable as needed).\n- **GotoAction:** For each \"next\" in the success actions.\n\nIf you need the confirmation configuration or command actions (mainmenu/operator) converted as well, let me know!", "timestamp": 1749470615.4295092, "content_hash": "9bc42fe5808c3055f2b333f864f91a4b"}