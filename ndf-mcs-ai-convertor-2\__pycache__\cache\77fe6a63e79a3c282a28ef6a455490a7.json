{"response": "```yaml\n- kind: Question\n  id: bf0110_OfferRefill_DM\n  displayName: bf0110_OfferRefill_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bf0110_ni1_01.wav\\\">Do you want to make a payment now? Just say Yes or No</audio>\"\n      - |\n        {Switch(\n              true,\n              Global.intent = \"changePlan\",\n              \"<audio src=\\\"AUDIO_LOCATION/bf0110_ni2_01.wav\\\">To continue with your plan change, you'll need to make a payment To do that now, say Yes or press 1 To cancel, say No or press 2</audio>\",\n              Global.intent = \"addPackages\",\n              \"<audio src=\\\"AUDIO_LOCATION/bf0110_ni2_02.wav\\\">To continue buying that add-on, you'll need to make a payment To do that now, say Yes or press 1 To cancel, say No or press 2</audio>\",\n              Global.intent = \"changeNumber\",\n              \"<audio src=\\\"AUDIO_LOCATION/bf0110_ni2_03.wav\\\">To continue with your number change, you'll need to make a payment To do that now, say Yes or press 1 To cancel, say No or press 2</audio>\",\n              Global.intent = \"autoRefill\",\n              \"<audio src=\\\"AUDIO_LOCATION/bf0110_ni2_04.wav\\\">To continue setting up Auto Pay, you'll need to make a payment To do that now, say Yes or press 1 To cancel, say No or press 2</audio>\"\n          )\n          }\n\n  alwaysPrompt: true\n  variable: Global.bf0110_OfferRefill_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            // numRefillAttempts == 0\n            Global.numRefillAttempts = 0 && Global.offerRefillReason = \"account\",\n            \"<audio src=\\\"AUDIO_LOCATION/bf0110_ini_01.wav\\\">Your account needs to be active</audio>\",\n            Global.numRefillAttempts = 0 && Global.offerRefillReason <> \"account\" && Global.balanceAmount > 0,\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/bf0110_ini_02.wav\\\">Your balance is only</audio>\",\n              \"{Global.balanceAmount} USD\",\n            ],\n            Global.numRefillAttempts = 0 && Global.offerRefillReason <> \"account\" && Global.balanceAmount = 0,\n            \"<audio src=\\\"AUDIO_LOCATION/bf0110_ini_03.wav\\\">Your account has a zero balance</audio>\",\n            Global.numRefillAttempts = 0 && Global.offerRefillReason <> \"account\",\n            \"<audio src=\\\"AUDIO_LOCATION/bf0110_ini_04.wav\\\">so you'll need to make a payment</audio>\",\n            Global.numRefillAttempts <> 0,\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/bf0110_ini_05.wav\\\">Your balance is still only</audio>\",\n              \"{Global.balanceAmount} USD\",\n              \"<audio src=\\\"AUDIO_LOCATION/bf0110_ini_06.wav\\\">so you'll need to make another payment</audio>\"\n            ],\n            Global.intent = \"changePlan\",\n            \"<audio src=\\\"AUDIO_LOCATION/bf0110_ini_07.wav\\\">to change plans</audio>\",\n            Global.intent = \"addPackages\",\n            \"<audio src=\\\"AUDIO_LOCATION/bf0110_ini_08.wav\\\">to buy your add-on</audio>\",\n            Global.intent = \"changeNumber\",\n            \"<audio src=\\\"AUDIO_LOCATION/bf0110_ini_09.wav\\\">to change your wireless number</audio>\",\n            Global.intent = \"autoRefill\" && Global.autoRefillChange = true,\n            \"<audio src=\\\"AUDIO_LOCATION/bf0110_ini_12.wav\\\">to make changes to Auto Pay</audio>\",\n            Global.intent = \"autoRefill\" && Global.autoRefillChange <> true,\n            \"<audio src=\\\"AUDIO_LOCATION/bf0110_ini_10.wav\\\">to enroll in AutoPay</audio>\",\n            true,\n            \"<audio src=\\\"AUDIO_LOCATION/bf0110_ini_11.wav\\\">Would you like to make that payment now?</audio>\"\n        )\n        }\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/bf0110_ni1_01.wav\\\">Do you want to make a payment now? Just say Yes or No</audio>\"\n        - |\n          {Switch(\n                true,\n                Global.intent = \"changePlan\",\n                \"<audio src=\\\"AUDIO_LOCATION/bf0110_ni2_01.wav\\\">To continue with your plan change, you'll need to make a payment To do that now, say Yes or press 1 To cancel, say No or press 2</audio>\",\n                Global.intent = \"addPackages\",\n                \"<audio src=\\\"AUDIO_LOCATION/bf0110_ni2_02.wav\\\">To continue buying that add-on, you'll need to make a payment To do that now, say Yes or press 1 To cancel, say No or press 2</audio>\",\n                Global.intent = \"changeNumber\",\n                \"<audio src=\\\"AUDIO_LOCATION/bf0110_ni2_03.wav\\\">To continue with your number change, you'll need to make a payment To do that now, say Yes or press 1 To cancel, say No or press 2</audio>\",\n                Global.intent = \"autoRefill\",\n                \"<audio src=\\\"AUDIO_LOCATION/bf0110_ni2_04.wav\\\">To continue setting up Auto Pay, you'll need to make a payment To do that now, say Yes or press 1 To cancel, say No or press 2</audio>\"\n            )\n            }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.Var_bf0110_OfferRefill_DM\n  value: =Text(Global.bf0110_OfferRefill_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_bf0110_OfferRefill_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.playBalanceReminder\n          value: false\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.midTaskRefill\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.numRefillAttempts\n          value: numRefillAttempts + 1\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: bf0115_MidTaskRefill_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_bf0110_OfferRefill_DM = \"false\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.intent = \"changePlan\", true, false)\n                  actions:\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/bf0110_out_01.wav\\\">Ok, Ill cancel your plan change</audio>\"\n\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.intent = \"addPackages\", true, false)\n                  actions:\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/bf0110_out_02.wav\\\">Okay, I'll cancel your request</audio>\"\n\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.intent = \"changeNumber\", true, false)\n                  actions:\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/bf0110_out_03.wav\\\">Ok, Ill cancel your wireless number change</audio>\"\n\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.intent = \"autoRefill\" && Global.autoRefillChange = true, true, false)\n                  actions:\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/bf0110_out_05.wav\\\">Ok, Ill cancel your Auto Pay change</audio>\"\n\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.intent = \"autoRefill\" && Global.autoRefillChange <> true, true, false)\n                  actions:\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/bf0110_out_04.wav\\\">Ok, Ill cancel your AutoPay enrollment</audio>\"\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.numRefillAttempts\n              value: 0\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n```", "timestamp": 1749470121.2338123, "content_hash": "77fe6a63e79a3c282a28ef6a455490a7"}