{"response": "```yaml\n- kind: Question\n  id: ai0230_AskIMEIDataOnlyFallback_DM\n  displayName: ai0230_AskIMEIDataOnlyFallback_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ai0230_ni1_01.wav\\\">The IMEI number is sometimes printed on a sticker underneath the battery, so please take the battery out now and check If you see it there, say or enter it now If not, say I Can't Find It</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ai0230_ni2_01.wav\\\">Take the battery out from your device to check whether there's a sticker underneath with the IMEI number printed on it If so, enter that number on the keypad If you don't see it, or you can't get to it, press 1</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ai0230_AskIMEIDataOnlyFallback_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n\n            // Case 1: foundIMEI = false\n            Global.foundIMEI = false,\n            [\n                \"That's OK Let's try something different \",\n                \"\",\n                {Switch(\n                    true,\n                    Global.callerDeviceType = \"hotspot\",\n                    \"Take the battery out of your device to check whether the IMEI number is printed underneath If so, read it to me now Or say I Can't Find It \",\n                    [\n                        \"There are a couple different ways to find the IMEI number on your tablet First, check the back of your tablet to see if you see it on a sticker, or printed on the case itself If you don't see it there, go ahead and turn on your device\",\n                        \"5 seconds of hold music\",\n                        \"If you are activating an iPad, open the Settings app, then choose General, and then About Or for an Android, go to Applications, then Settings, then About Tablet, then Status\",\n                        \"5 seconds of hold music\",\n                        \"Either way, scroll down until you see the letters IMEI Once you have that number, just read it to me Or say I Can't Find It\"\n                    ]\n                )}\n            ],\n\n            // Case 2: foundIMEI = true\n            Global.foundIMEI = true,\n            [\n                \"Now I need the IMEI number \",\n                \"\",\n                {Switch(\n                    true,\n                    Global.callerDeviceType = \"hotspot\",\n                    \"Take the battery out of your device to check whether the IMEI number is printed underneath If so, read it to me now Or say I Can't Find It \",\n                    [\n                        \"There are a couple different ways to find the IMEI number on your tablet First, check the back of your tablet to see if you see it on a sticker, or printed on the case itself If you don't see it there, go ahead and turn on your device\",\n                        \"5 seconds of hold music\",\n                        \"If you are activating an iPad, open the Settings app, then choose General, and then About Or for an Android, go to Applications, then Settings, then About Tablet, then Status\",\n                        \"5 seconds of hold music\",\n                        \"Either way, scroll down until you see the letters IMEI Once you have that number, just read it to me Or say I Can't Find It\"\n                    ]\n                )}\n            ],\n\n            // Default Case\n            [\"Invalid state.\"]\n        )\n        }\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: cant_find\n          displayName: cant_find\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ai0230_ni1_01.wav\\\">The IMEI number is sometimes printed on a sticker underneath the battery, so please take the battery out now and check If you see it there, say or enter it now If not, say I Can't Find It</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ai0230_ni2_01.wav\\\">Take the battery out from your device to check whether there's a sticker underneath with the IMEI number printed on it If so, enter that number on the keypad If you don't see it, or you can't get to it, press 1</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ai0230_AskIMEIDataOnlyFallback_DM\n  value: =Text(Global.ai0230_AskIMEIDataOnlyFallback_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ai0230_AskIMEIDataOnlyFallback_DM = \"cant_find\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferReason\n          value: BUSINESS_RULE\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferPrompt\n          value: default\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M09_MainMenu_Dialog\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ai0230_AskIMEIDataOnlyFallback_DM = \"default\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/ai0230_out_01.wav\\\">Thanks</audio>\"\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.imei\n              value: ai0230_AskIMEIDataOnlyFallback_DM.returnvalue\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.A04_IMEI_03_Dialog\n```", "timestamp": 1749469693.1348608, "content_hash": "be98323d674156483ce1f5d48117d47e"}