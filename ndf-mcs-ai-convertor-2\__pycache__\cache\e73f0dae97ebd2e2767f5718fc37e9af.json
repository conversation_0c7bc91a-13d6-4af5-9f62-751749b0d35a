{"response": "```yaml\n- kind: Question\n  id: sl0215_AskSuspend_DM\n  displayName: sl0215_AskSuspend_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.sl0215_AskSuspend_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n\n            // Case 1: accountType = \"dataOnly\"\n            Global.accountType = \"dataOnly\",\n            [\n                \"I can suspend your account until you find it, but if you have remote screen lock, data wiping, or mobile locator apps on your device, you should use them first Once I suspend your account, they wont work\",\n                {Switch(\n                    true,\n                    Global.intent = \"stolenDevice\",\n                    \"I can also block your device from use on the A T and T network\"\n                )},\n            ],\n\n            // Case 2: accountType = \"gophone\" && deviceType = \"smartphone\"\n            Global.accountType = \"gophone\" && Global.deviceType = \"smartphone\",\n            [\n                \"I can suspend your account until you find it, but if you have remote screen lock, data wiping, or mobile locator apps on your phone, you should use them first Once I suspend your account, these apps wont work\",\n                {Switch(\n                    true,\n                    Global.intent = \"stolenDevice\",\n                    \"I can also block your phone from use on the A T and T network\"\n                )},\n            ],\n\n            // Case 3: intent = \"lostDevice\"\n            Global.intent = \"lostDevice\",\n            \"I can suspend your account until you find it For more information, go to a t t dot com slash stolenphone\",\n\n            // Case 4: intent <> \"lostDevice\" && accountType = \"whp\"\n            Global.intent <> \"lostDevice\" && Global.accountType = \"whp\",\n            \"I can suspend your account and block your device from use on the AT and T network For more information, go to a t t dot com slash stolenphone\",\n\n            // Case 5: intent <> \"lostDevice\" && accountType <> \"whp\"\n            Global.intent <> \"lostDevice\" && Global.accountType <> \"whp\",\n            \"I can suspend your account and block your phone from use on the AT and T network For more information, go to a t t dot com slash stolenphone\",\n\n            // Default: Always ask to continue\n            \"Do you want to continue?\"\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n              true,\n              Global.intent = \"stolenDevice\",\n              \"Do you want to suspend your account and block your device? Please say Yes or No\",\n\n              true,\n              \"Do you want to suspend your account? Please say Yes or No\"\n          )\n          }\n        - |\n          {Switch(\n              true,\n              Global.intent = \"stolenDevice\" && Global.accountType = \"gophone\",\n              \"If you suspend your account and block your phone, no one will be able to use your prepaid service or use that phone on the A T and T network If you want to do this, say Yes or press 1 Or to keep your service and phone active, say No or press 2\",\n\n              Global.intent = \"stolenDevice\" && Global.accountType <> \"gophone\",\n              \"If you suspend your account and block your data device, no one will be able to use your prepaid service or use that data device on the A T and T network If you want to do this, say Yes or press 1 Or to keep your service and device active, say No or press 2\",\n\n              true,\n              \"If you suspend your account, no one will be able to use your prepaid service on the AT and Tnetwork If you want to do this, say Yes or press 1 Or to keep your service active, say No or press 2\"\n          )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_sl0215_AskSuspend_DM\n  value: =Text(Global.sl0215_AskSuspend_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_sl0215_AskSuspend_DM = \"true\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.intent = \"lostDevice\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: sl0225_SuspendBlock_DB_DA\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.deviceMake = \"\" || Global.deviceMake = \"Blue\" || Global.deviceMake = \"unknown\", true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: sl0225_SuspendBlock_DB_DA\n\n              elseActions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: sl0220_ConfirmDevice_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_sl0215_AskSuspend_DM = \"false\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.intent = \"lostDevice\", true, false)\n                  actions:\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/sl0215_out_01.wav\\\">Ok, Ill keep your account active</audio>\"\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: sl0240_LostStolenWrapMenu_DM\n\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.accountType = \"dataOnly\" || Global.accountType = \"whp\", true, false)\n                      actions:\n                        - kind: SendActivity\n                          id: sendActivity_REPLACE_THIS\n                          activity:\n                            speak:\n                              - \"<audio src=\\\"AUDIO_LOCATION/sl0215_out_02.wav\\\">Ok, Ill keep your account and device active</audio>\"\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: sl0240_LostStolenWrapMenu_DM\n\n                  elseActions:\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/sl0215_out_03.wav\\\">Ok, Ill keep your account and phone active</audio>\"\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: sl0240_LostStolenWrapMenu_DM\n```", "timestamp": **********.610214, "content_hash": "e73f0dae97ebd2e2767f5718fc37e9af"}