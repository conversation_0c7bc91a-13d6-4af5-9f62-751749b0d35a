{"response": "```yaml\n- kind: Question\n  id: GS1025_SalesInfo_DM\n  displayName: GS1025_SalesInfo_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/GS1025_nm1_01.wav\\\">Please say repeat that, tell me more, open an account or find a store  If you are done, you can just hang up </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/GS1025_nm2_01.wav\\\">To hear about us again, say Repeat that, or press  star  If you wouldd like more information, say tell me more, or press 1  You can also say open an account or press 2, or Find a Store, - 3  If you are done, you can just hang up </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/GS1025_nm2_01.wav\\\">To hear about us again, say Repeat that, or press  star  If you wouldd like more information, say tell me more, or press 1  You can also say open an account or press 2, or Find a Store, - 3  If you are done, you can just hang up </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.GS1025_SalesInfo_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.IsFromStoreLocator = true,\n            \"You can say repeat that, tell me more, or open an account If you are all set, you can just hang up \",\n            Global.IsFromStoreLocator = false,\n            \"You can say repeat that, tell me more, open an account, or find a store If you are all set, you can just hang up \"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: more_details\n          displayName: more_details\n        - id: open_acct\n          displayName: open_acct\n        - id: find_store\n          displayName: find_store\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/GS1025_nm1_01.wav\\\">Please say repeat that, tell me more, open an account or find a store  If you are done, you can just hang up </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/GS1025_nm2_01.wav\\\">To hear about us again, say Repeat that, or press  star  If you wouldd like more information, say tell me more, or press 1  You can also say open an account or press 2, or Find a Store, - 3  If you are done, you can just hang up </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/GS1025_nm2_01.wav\\\">To hear about us again, say Repeat that, or press  star  If you wouldd like more information, say tell me more, or press 1  You can also say open an account or press 2, or Find a Store, - 3  If you are done, you can just hang up </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.IsFromStoreLocator\n  value: GlobalVars.IsFromStoreLocator\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_GS1025_SalesInfo_DM\n  value: =Text(Global.GS1025_SalesInfo_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_GS1025_SalesInfo_DM = \"more_details\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: GS1040_GoToCallTransfer_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_GS1025_SalesInfo_DM = \"open_acct\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.callType\n              value: activate_unknownDevice\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.cti_Intent\n              value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.securityRequired\n              value: false\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.needMDN\n              value: false\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: GS1065_GoToMainMenu_SD\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_GS1025_SalesInfo_DM = \"find_store\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.storeLocatorReason\n                  value: sign up\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: GS1030_GoToStoreLocator_SD\n```", "timestamp": 1749528409.7372448, "content_hash": "a126f695fea747f49dba4ded00cb86ae"}