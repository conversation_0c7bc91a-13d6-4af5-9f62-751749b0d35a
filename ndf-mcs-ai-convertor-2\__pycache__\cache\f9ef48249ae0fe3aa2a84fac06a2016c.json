{"response": "```yaml\n- kind: Question\n  id: bm0530_AskRetryRefill_DM\n  displayName: bm0530_AskRetryRefill_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bm0530_ni1_01.wav\\\">If you d like to try another PIN, say Yes If not, say No</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/bm0530_ni2_01.wav\\\">To use a different PIN number, say Yes or press 1 If not, say No or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.bm0530_AskRetryRefill_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bm0530_ini_01.wav\\\">Would you like to try another PIN?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/bm0530_ni1_01.wav\\\">If you d like to try another PIN, say Yes If not, say No</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/bm0530_ni2_01.wav\\\">To use a different PIN number, say Yes or press 1 If not, say No or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.firstTimeInBm0410\n  value: false\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.askRefillPINFromVar\n  value: bm0530_AskRetryRefill_DM\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_bm0530_AskRetryRefill_DM\n  value: =Text(Global.bm0530_AskRetryRefill_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_bm0530_AskRetryRefill_DM = \"true\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: B02_AddMoney_03.dvxml\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_bm0530_AskRetryRefill_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/bm0530_out_02.wav\\\">Okay</audio>\"\n\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.intent = \"refillAccount\" || Global.intent = \"refillPIN\", true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: bm0535_RefillPINWrap_DM\n\n              elseActions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: getReturnLink()\n```", "timestamp": **********.7340987, "content_hash": "f9ef48249ae0fe3aa2a84fac06a2016c"}