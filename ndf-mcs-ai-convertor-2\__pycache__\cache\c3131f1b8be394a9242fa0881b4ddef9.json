{"response": "```yaml\n- kind: Question\n  id: AQ1310_NoPhoneCallBack_DM\n  displayName: AQ1310_NoPhoneCallBack_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AQ1310_nm1_01.wav\\\">To get help in-person at a MetroPCS location near you, say 'find a store' Otherwise, you can simply hang up</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.AQ1310_NoPhoneCallBack_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            (Global.callType = \"esn_swap\" || Global.callType = \"switch_phone\") && !(Global.saidOperator = true),\n            \"Unfortunately I can't switch your phone without your security information, or your old phone An agent couldn't do it either To check where you can get help in-person, say 'find a store' Otherwise, you can simply hang up\",\n            !(Global.callType = \"esn_swap\" || Global.callType = \"switch_phone\") && !(Global.saidOperator = true),\n            \"Unfortunately our agents couldn't help you without your security information, or your MetroPCS phone To check where you can get help in-person, say 'find a store' Otherwise, you can simply hang up\",\n            Global.saidOperator = true,\n            \"I'm sorry, our agents wont be able to help you without your security information, or your phone To get help in-person at a MetroPCS location near you, say 'find a store' Otherwise, you can simply hang up\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: find_store\n          displayName: find_store\n        - id: operator\n          displayName: operator\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/AQ1310_nm1_01.wav\\\">To get help in-person at a MetroPCS location near you, say 'find a store' Otherwise, you can simply hang up</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.callType\n  value: GlobalVars.callType\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.saidOperator\n  value: GlobalVars.saidOperatorAQ1310?GlobalVars.saidOperatorAQ1310:false\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_AQ1310_NoPhoneCallBack_DM\n  value: =Text(Global.AQ1310_NoPhoneCallBack_DM_reco)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.saidOperatorAQ1310\n  value: false\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AQ1310_NoPhoneCallBack_DM = \"find_store\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.storeLocatorReason\n          value: securityfail\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/AQ1310_out_01.wav\\\">Alright</audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: AQ1315_GoToStoreLocator_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_AQ1310_NoPhoneCallBack_DM = \"operator\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.saidOperator = false, true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.saidOperatorAQ1310\n                      value: true\n\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: AQ1310_NoPhoneCallBack_DM\n\n              elseActions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/AQ1310_Operator_01.wav\\\">Sorry We can't go on without your security details Please call back when you have your 8-digit security code, the answer to your security question, or your MetroPCS phone</audio>\"\n                      - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: getReturnLink()\n```", "timestamp": 1749527655.1304657, "content_hash": "c3131f1b8be394a9242fa0881b4ddef9"}