{"response": "```yaml\n- kind: Question\n  id: XT1313_AskReceivedCode_DM\n  displayName: XT1313_AskReceivedCode_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/XT1313_nm1_01.wav\\\">Have you received the text message? </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/XT1313_nm2_01.wav\\\">Have you received the text message? </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.XT1313_AskReceivedCode_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.twoFactorHoldCounter = 0,\n            \"test\",\n            Global.twoFactorHoldCounter = 0 || Global.twoFactorHoldCounter = 2 || Global.twoFactorHoldCounter = 4,\n            \"Have you received the text message? \",\n            !(Global.twoFactorHoldCounter = 0 || Global.twoFactorHoldCounter = 2 || Global.twoFactorHoldCounter = 4) && (Global.twoFactorHoldCounter = 1 || Global.twoFactorHoldCounter = 3),\n            \"How about now did you receive the text? \"\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/XT1313_ni1_01.wav\\\">Have you received the text message? </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/XT1313_ni2_01.wav\\\">Have you received the text message? </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.twoFactorHoldCounter\n  value: \"GlobalVars.twoFactorHoldCounter != undefined ? GlobalVars.twoFactorHoldCounter: 0\"\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_XT1313_AskReceivedCode_DM\n  value: =Text(Global.XT1313_AskReceivedCode_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_XT1313_AskReceivedCode_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/XT1313_out_01.wav\\\">Great!</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: XT1311_CollectTempCode_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_XT1313_AskReceivedCode_DM = \"false\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(GlobalVars.twoFactorHoldCounter = 1 || GlobalVars.twoFactorHoldCounter = 3, true, false)\n                  actions:\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/XT1313_out_02.wav\\\">No problem</audio>\"\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: XT1310_WaitTempCodeSBI_PP\n\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(GlobalVars.twoFactorHoldCounter = 0 || GlobalVars.twoFactorHoldCounter = 2, true, false)\n                      actions:\n                        - kind: SendActivity\n                          id: sendActivity_REPLACE_THIS\n                          activity:\n                            speak:\n                              - \"<audio src=\\\"AUDIO_LOCATION/XT1313_out_03.wav\\\">Okay</audio>\"\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: XT1310_WaitTempCodeSBI_PP\n\n                  elseActions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: XT1315_PlayTFTimeout_PP\n```", "timestamp": 1749530158.7290025, "content_hash": "4929494931dcc3198e9d6a73381c7294"}