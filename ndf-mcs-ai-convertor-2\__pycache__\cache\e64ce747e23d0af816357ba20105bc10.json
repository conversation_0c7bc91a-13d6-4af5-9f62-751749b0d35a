{"response": "```yaml\n- kind: Question\n  id: aa6509_TXWVTransactionHistoryMainMenuDTMF_DM\n  displayName: aa6509_TXWVTransactionHistoryMainMenuDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - aa6509_TXWVTransactionHistoryMainMenuDTMF_DM_initial\n      - aa6509_TXWVTransactionHistoryMainMenuDTMF_DM_initial\n\n  alwaysPrompt: true\n  variable: Global.aa6509_TXWVTransactionHistoryMainMenuDTMF_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            // Case 1: dnisInfo.callType = \"TX\"\n            Global.dnisInfo.callType = \"TX\" && Global.balanceInfo.cashParticipantFlag = true && Global.balanceInfo.fsParticipantFlag = false,\n            \"To hear Cash transactions press 1 Deposits press 2\",\n\n            Global.dnisInfo.callType = \"TX\" && Global.balanceInfo.cashParticipantFlag = true && Global.balanceInfo.fsParticipantFlag = true,\n            \"To hear Food transactions press 1 Cash transactions, press 2 Deposits, press 3 All transactions, press 4\",\n\n            Global.dnisInfo.callType = \"TX\" && Global.balanceInfo.cashParticipantFlag = false && Global.balanceInfo.fsParticipantFlag = true,\n            \"To hear Food transactions press 1 Deposits press 2\",\n\n            // Case 2: dnisInfo.callType != \"TX\"\n            Global.dnisInfo.callType <> \"TX\" && Global.balanceInfo.cashParticipantFlag = true && Global.balanceInfo.fsParticipantFlag = false,\n            \"To hear Cash transactions press 1 Deposits press 2\",\n\n            Global.dnisInfo.callType <> \"TX\" && Global.balanceInfo.cashParticipantFlag = true && Global.balanceInfo.fsParticipantFlag = true,\n            \"To hear Cash transactions press 1 Food transactions, press 2 Deposits, press 3 All transactions, press 4\",\n\n            Global.dnisInfo.callType <> \"TX\",\n            \"To hear Food transactions press 1 Deposits press 2\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: all\n          displayName: all\n        - id: cash\n          displayName: cash\n        - id: deposits\n          displayName: deposits\n        - id: food\n          displayName: food\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - aa6509_TXWVTransactionHistoryMainMenuDTMF_DM_initial\n        - \"<audio src=\\\"AUDIO_LOCATION/aa6509_ni2_01.wav\\\">Sorry, I still didn't get that</audio>\"\n        - aa6509_TXWVTransactionHistoryMainMenuDTMF_DM_initial\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa6509_TXWVTransactionHistoryMainMenuDTMF_DM\n  value: =Text(Global.aa6509_TXWVTransactionHistoryMainMenuDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa6509_TXWVTransactionHistoryMainMenuDTMF_DM = \"all\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.typeOfHistory\n          value: AL\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa6512_LookupMessage_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa6509_TXWVTransactionHistoryMainMenuDTMF_DM = \"cash\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.typeOfHistory\n              value: CA\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa6512_LookupMessage_PP\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_aa6509_TXWVTransactionHistoryMainMenuDTMF_DM = \"deposits\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.globalVariables.typeOfHistory\n                  value: DE\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: aa6512_LookupMessage_PP\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_aa6509_TXWVTransactionHistoryMainMenuDTMF_DM = \"food\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.globalVariables.typeOfHistory\n                      value: FS\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: aa6512_LookupMessage_PP\n```", "timestamp": 1749544383.9179492, "content_hash": "e64ce747e23d0af816357ba20105bc10"}