{"response": "```yaml\n- kind: Question\n  id: BC1015_UseOtherCardYN_DM\n  displayName: BC1015_UseOtherCardYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1015_nm1_01.wav\\\">Would you like to try with a different card?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1015_nm2_01.wav\\\">I couldn't validate the card number you gave me If you have a different card you can use, say 'yes' or press 1 Otherwise, say 'no' or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1015_nm3_01.wav\\\">I can't complete your activation without a payment If you have a different card you can use, press 1 Otherwise, press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1015_nm3_02.wav\\\">I can't complete your payment without a valid card If you have a different card you can use, press 1 Otherwise, press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.BC1015_UseOtherCardYN_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.saidOperator = false,\n            \"Actually\",\n            Global.unsupportedCardIssuer = \"discover\" && Global.callType = \"activate\",\n            [\n                \"It looks like you re using a Discover card, and we don t take those right now We do take Visa, MasterCard, American Express, Star, NYCE, Pulse, and Accel \",\n                \"I wont be able to complete your actiation without a payment, so\"\n            ],\n            Global.unsupportedCardIssuer = \"discover\" && Global.callType = \"activate\",\n            \"I couldn't validate the card number you gave me And I wont be able to complete your actiation without a payment\",\n            Global.unsupportedCardIssuer = \"discover\",\n            \"I couldn't validate the card number you gave me\",\n            true,\n            [\n                \"test\",\n                \"Would you like to use a different card?\"\n            ]\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1015_nm1_01.wav\\\">Would you like to try with a different card?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1015_nm2_01.wav\\\">I couldn't validate the card number you gave me If you have a different card you can use, say 'yes' or press 1 Otherwise, say 'no' or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1015_nm3_01.wav\\\">I can't complete your activation without a payment If you have a different card you can use, press 1 Otherwise, press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1015_nm3_02.wav\\\">I can't complete your payment without a valid card If you have a different card you can use, press 1 Otherwise, press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.saidOperator\n  value: GlobalVars.saidOperatorBC1015?GlobalVars.saidOperatorBC1015:false\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.callType\n  value: GlobalVars.callType\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_BC1015_UseOtherCardYN_DM\n  value: =Text(Global.BC1015_UseOtherCardYN_DM_reco)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.saidOperatorBC1015\n  value: false\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_BC1015_UseOtherCardYN_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.failedChecksum\n          value: false\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.tryOtherCardReason\n          value: failed_checksum\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.preferredPaymentMethod\n          value: undefined\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.PaymentTable.CARD_TYPE\n          value: not_set\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.disconfirmedDetails\n          value: false\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/BC1015_out_01.wav\\\">Alright</audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: getReturnLink()\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_BC1015_UseOtherCardYN_DM = \"false\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: BC1020_GoTo_ErrorHandling_SD\n```", "timestamp": 1749527724.5270195, "content_hash": "fb3c47fc091706d413b981ae94d970d1"}