{"response": "```yaml\n- kind: Question\n  id: cp0340_NewPINWrap_DM\n  displayName: cp0340_NewPINWrap_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.numNewPINAttempts = 3,\n              \"Say Repeat or press 1 Or if you re all done, feel free to hang up\",\n          \n              true,\n              \"If that s all you needed, you can hang up For anything else, say Main Menu\"\n          )\n        }\n      - |\n        {Switch(\n              true,\n              Global.numNewPINAttempts = 3,\n              \"To hear that again, press 1 Or if you re all done, feel free to hang up\",\n          \n              true,\n              \"Say Main Menu or press star Or if you re all done, feel free to hang up\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.cp0340_NewPINWrap_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.setNewPINLandline = \"true\" || Global.setNewPINLandline = true,\n              \"Say Main Menu to continue\",\n\n              Global.numNewPINAttempts = 3 && (Global.setNewPINLandline = \"false\" || Global.setNewPINLandline = false),\n              \"Say Repeat, or if that s all you needed, go ahead and hang up\",\n\n              true,\n              \"If that s all you needed, you can hang up For anything else, say Main Menu\"\n          )\n        }\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: mainmenu\n          displayName: mainmenu\n        - id: operator\n          displayName: operator\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.numNewPINAttempts = 3,\n                \"Say Repeat or press 1 Or if you re all done, feel free to hang up\",\n            \n                true,\n                \"If that s all you needed, you can hang up For anything else, say Main Menu\"\n            )\n          }\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_cp0340_NewPINWrap_DM\n  value: =Text(Global.cp0340_NewPINWrap_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_cp0340_NewPINWrap_DM = \"mainmenu\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferDestination\n          value: attPrepaid\n\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M01_MainMenu2_Dialog\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_cp0340_NewPINWrap_DM = \"operator\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.lastTransferState\n          value: cp0340_NewPINWrap_DM\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferReason\n          value: AGENT_REQUEST\n\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M09_MainMenu_Dialog\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_cp0340_NewPINWrap_DM = \"repeat\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.numNewPINAttempts = 3, true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: cp0330_InvalidNewPIN_PP\n\n          elseActions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: cp0340_NewPINWrap_DM\n```", "timestamp": 1749470738.8565536, "content_hash": "7705a3419e5f28fab336d756e8cc19bc"}