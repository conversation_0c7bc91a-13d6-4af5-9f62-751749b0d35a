{"response": "```yaml\n- kind: Question\n  id: TT1020_PowerCycleTipOfferYN_DM\n  displayName: TT1020_PowerCycleTipOfferYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TT1020_nm1_01.wav\\\">Have you tried turning your device off and then back on?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TT1020_nm2_01.wav\\\">If you've already tried turning your device off and then back on, say 'yes' or press 1 If you haven't, say 'no' or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.TT1020_PowerCycleTipOfferYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/TT1020_ini_01.wav\\\">Alright My second tip is that turning your device off and then back on will usually fix this problem Have you already tried that?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/TT1020_ni1_01.wav\\\">Have you tried turning your device off and then back on?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/TT1020_ni2_01.wav\\\">If you've already tried turning your device off and then back on, say 'yes' or press 1 If you haven't, say 'no' or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_TT1020_PowerCycleTipOfferYN_DM\n  value: =Text(Global.TT1020_PowerCycleTipOfferYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TT1020_PowerCycleTipOfferYN_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.fromTT1020\n          value: true\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TT1030_WiFiTipOfferYN_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.usingBrokenDevice = \"yes\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: TT1026_PowerCycleTipBrokenDevice_DM\n\n      elseActions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TT1025_PowerCycleTipWaitSBI_DM\n```", "timestamp": 1749530351.645688, "content_hash": "4b2baeadd1e7e8abb794526e4ca20697"}