{"response": "```yaml\n- kind: Question\n  id: bm0935_RefillCanceledWrapMenu_DM\n  displayName: bm0935_RefillCanceledWrapMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bm0935_ni1_01.wav\\\">If you re done, just hang up Or if you need help with something else, say Main Menu or press star</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/bm0935_nm2_01.wav\\\">If you don t need help with anything else, you can hang up Otherwise, to return to the main menu, press star</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.bm0935_RefillCanceledWrapMenu_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bm0935_ini_01.wav\\\">If you re done, go ahead and hang up For anything else, say Main Menu</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: mainmenu\n          displayName: mainmenu\n        - id: operator\n          displayName: operator\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/bm0935_ni1_01.wav\\\">If you re done, just hang up Or if you need help with something else, say Main Menu or press star</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_bm0935_RefillCanceledWrapMenu_DM\n  value: =Text(Global.bm0935_RefillCanceledWrapMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_bm0935_RefillCanceledWrapMenu_DM = \"mainmenu\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferDestination\n          value: attPrepaid\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M01_MainMenu2.dvxml#ma1334_InitiateMainMenu_DS\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_bm0935_RefillCanceledWrapMenu_DM = \"operator\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.lastTransferState\n              value: bm0935_RefillCanceledWrapMenu_DM\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.transferReason\n              value: AGENT_REQUEST\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.M09_MainMenu.dvxml#ma9210_NeedTransferType_DS\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_bm0935_RefillCanceledWrapMenu_DM = \"repeat\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: bm0935_RefillCanceledWrapMenu_DM\n```", "timestamp": 1749469801.2475774, "content_hash": "3c8c5bfa7ccd9b9929f623ea7978cc9b"}