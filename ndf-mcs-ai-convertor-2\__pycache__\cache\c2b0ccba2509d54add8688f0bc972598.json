{"response": "```yaml\n- kind: Question\n  id: XR1010_ConfirmOperatorRequest_DM\n  displayName: XR1010_ConfirmOperatorRequest_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.XR1010_ConfirmOperatorRequest_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/XR1010_ini_01.wav\\\">Hmm, I think you said you wanted to talk to someone, right?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/XR1010_ini_01.wav\\\">Hmm, I think you said you wanted to talk to someone, right?</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_XR1010_ConfirmOperatorRequest_DM\n  value: =Text(Global.XR1010_ConfirmOperatorRequest_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_XR1010_ConfirmOperatorRequest_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.operatorNeedsConfirmation\n          value: true\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: XR1015_CheckAllowedConditions_JDA_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_XR1010_ConfirmOperatorRequest_DM = \"false\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.callerSaidOperator\n              value: false\n\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/XR1010_out_01.wav\\\">Sorry about that</audio>\"\n\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: |-\n                    =If(Or(Global.GlobalVars.callType = \"make_pmt\", Global.GlobalVars.suspendedOperatorRequest = true, Or(Global.GlobalVars.GetAccountDetails = null, Global.GlobalVars.GetAccountDetails = \"\", Global.GlobalVars.GetAccountDetails = undefined)), true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.suspendedOperatorRequest\n                      value: false\n\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: getReturnLink()\n\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: |-\n                        =If(Global.GlobalVars.GetBCSParameters.care_nlu_enabled = \"true\" && Global.language = \"en-US\", true, false)\n                      actions:\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.GlobalVars.operatorRequestMidFlowNLU\n                          value: true\n\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.GlobalVars.callType\n                          value: undefined\n\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.GlobalVars.cti_Intent\n                          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.GlobalVars.tag\n                          value: undefined\n\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: XR1021_InitialHandling_SD\n\n                  elseActions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: XR1065_GoToMainMenu_SD\n```", "timestamp": 1749558060.5758228, "content_hash": "c2b0ccba2509d54add8688f0bc972598"}