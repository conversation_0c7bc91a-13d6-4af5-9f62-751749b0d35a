{"response": "```yaml\n- kind: Question\n  id: aa2545_SSNDTMF_DM\n  displayName: aa2545_SSNDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.aa2545_SSNDTMF_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.validationCriteriaVariables.reprompting = true && Global.validationCriteriaVariables.currentValidationType = \"PSC\",\n            \"Enter the primary card holder s  social security number\",\n\n            Global.validationCriteriaVariables.reprompting = true,\n            \"Enter your social security number\",\n\n            Global.validationCriteriaVariables.currentValidationType = \"PSC\",\n            \"Now enter the primary card holder s  social security number\",\n\n            \"Now enter your social security number\"\n        )}\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - ni1\n        - |\n          {Switch(\n                true,\n                Global.dnisInfo.dtmfOnlyFlag = true,\n                \"ni2\",\n                true,\n                \"\"\n            )\n          }\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.result\n  value: aa2545_SSNDTMF_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.ssn\n  value: aa2545_SSNDTMF_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.validationCriteriaVariables.validationValue\n  value: validationCriteriaVariables.validationValue + ';' + result\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.validationCriteriaVariables.validationType\n  value: validationCriteriaVariables.validationType + ';' + validationCriteriaVariables.currentValidationType\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.validationCriteriaVariables.reprompting\n  value: false\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.globalVariables.currentTask = \"cardReplacement\" || Global.globalVariables.currentTask = \"altAuthVal\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa2510_ValidationDecision_JDA_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: |\n            =If(Global.result = Global.validationCriteriaVariables.currentValidationData, true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa2510_ValidationDecision_JDA_DA\n\n      elseActions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.validationCriteriaVariables.validationDataMismatch\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.validationCriteriaVariables.validationDataMismatchSsn\n          value: true\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa2510_ValidationDecision_JDA_DA\n```\n**Notes on the conversion:**\n- **Prompt**: The nested `<if>`/`<elseif>`/`<else>` structure in `<initialprompt>` is converted to a PowerFx-style Switch statement, mapping the XML logic to the YAML as per your instructions.\n- **Entity**: Since there is no `<success>` with yes/no or multiple labels, `StringPrebuiltEntity` is used.\n- **SetVariable**: All `<session-mapping>` are converted to `SetVariable` actions.\n- **ConditionGroup**: The `<if>` logic in `<success>` is mapped to nested `ConditionGroup` and `GotoAction` as per your rules.\n- **inputTimeoutResponse**: The `<noinputprompts>` are mapped, with the second one using a Switch for the conditional.\n- **unrecognizedPrompt**: Not present in the input, so not included.\n- **nomatchprompts**: Not present in the input, so not included.\n- **Indentation**: Follows your memorized YAML style.\n- **All ids**: Use `setVariable_REPLACE_THIS`, `conditionGroup_REPLACE_THIS`, `conditionItem_REPLACE_THIS`, and `goto_REPLACE_THIS` as placeholders as per your instructions.", "timestamp": 1749556901.1675165, "content_hash": "780a270a5a36026ab696efdbefd35b95"}