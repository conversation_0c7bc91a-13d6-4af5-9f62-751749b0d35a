{"response": "```yaml\n- kind: Question\n  id: sl0345_NoUnblockWrapMenu_DM\n  displayName: sl0345_NoUnblockWrapMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n            \"To move your account information to a new phone, say Replace My Phone or press 1 If you need help with something else, say Main Menu or press star If youre all done, you can hang up\",\n            true,\n            \"To move your account information to a new device, say Replace My Phone or press 1 If you need help with something else, say Main Menu or press star If youre all done, you can hang up\"\n        )\n        }\n      - |\n        {Switch(\n            true,\n            Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n            \"If Youve already replaced your phone and want to move your account information to it, press 1 If you need help with something else, press star If youre all done, you can hang up\",\n            true,\n            \"If Youve already replaced your device and want to move your account information to it, press 1 If you need help with something else, press star If youre all done, you can hang up\"\n        )\n        }\n\n  alwaysPrompt: true\n  variable: Global.sl0345_NoUnblockWrapMenu_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.accountType = \"dataOnly\" || Global.accountType = \"whp\",\n            \"If thats all you needed, go ahead and hang up You can also say Replace My Device or Main Menu\",\n            true,\n            \"If thats all you needed, go ahead and hang up You can also say Replace My Phone or Main Menu\"\n        )\n        }\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: change_device\n          displayName: change_device\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n              true,\n              Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n              \"To move your account information to a new phone, say Replace My Phone or press 1 If you need help with something else, say Main Menu or press star If youre all done, you can hang up\",\n              true,\n              \"To move your account information to a new device, say Replace My Phone or press 1 If you need help with something else, say Main Menu or press star If youre all done, you can hang up\"\n          )\n          }\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_sl0345_NoUnblockWrapMenu_DM\n  value: =Text(Global.sl0345_NoUnblockWrapMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_sl0345_NoUnblockWrapMenu_DM = \"change_device\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: changeDevice\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: sl0350_UnblockChangeDevice_SD\n```", "timestamp": **********.9983053, "content_hash": "89c00d282290d07abe8774cb72bad003"}