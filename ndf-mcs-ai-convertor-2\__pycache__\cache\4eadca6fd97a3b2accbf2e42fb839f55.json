{"response": "```yaml\n- kind: Question\n  id: TT1001_MainTroubleshootingOptions_DM\n  displayName: TT1001_MainTroubleshootingOptions_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TT1001_nm1_01.wav\\\">You can say   Metro network,   making or receiving calls,   a feature or service,  or  a payment  You can also say  I need to reset my voicemail PIN  or say  it s none of those </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TT1001_nm2_01.wav\\\">Please say  Metro network  or press 1,  making or receiving calls  or press 2,  a feature or service  - 3,  or  a payment  - 4  You can also say  I need to reset my voicemail PIN or press 5 or  it s none of those  - 6</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.TT1001_MainTroubleshootingOptions_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/TT1001_ini_01.wav\\\">Which of these are you having trouble with   The Metro network ,  making or receiving calls,   a feature or service,  or  a payment  You can also say  I need to reset my voicemail PIN  or say  it s none of those'</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: network_connection\n          displayName: network_connection\n        - id: calls\n          displayName: calls\n        - id: feature\n          displayName: feature\n        - id: payment\n          displayName: payment\n        - id: voicemail_pin\n          displayName: voicemail_pin\n        - id: none\n          displayName: none\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/TT1001_ni1_01.wav\\\">You can say   Metro network,   making or receiving calls,   a feature or service,  or  a payment  You can also say  I need to reset my voicemail PIN  or say  it s none of those </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/TT1001_ni2_01.wav\\\">Please say  Metro network  or press 1,  making or receiving calls  or press 2,  a feature or service  - 3,  or  a payment  - 4  You can also say  I need to reset my voicemail PIN or press 5 or  it s none of those  - 6</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.securityRequired\n  value: false\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.needMDN\n  value: false\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_TT1001_MainTroubleshootingOptions_DM\n  value: =Text(Global.TT1001_MainTroubleshootingOptions_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TT1001_MainTroubleshootingOptions_DM = \"network_connection\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.language = \"en-US\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.TransferTag\n                  value: Technical_Support_English\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.TransferTag\n              value: Technical_Support_Spanish\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.GlobalVars.GetAccountDetails && Global.GlobalVars.GetAccountDetails.accountStatus = \"active\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: TT1005_UsingDeviceWithProblemYN_DM\n          elseActions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: TT1015_OnNetworkTip_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TT1001_MainTroubleshootingOptions_DM = \"calls\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.language = \"en-US\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.TransferTag\n                  value: Technical_Support_English\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.TransferTag\n              value: Technical_Support_Spanish\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.GlobalVars.GetAccountDetails && Global.GlobalVars.GetAccountDetails.accountStatus = \"active\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: TT1005_UsingDeviceWithProblemYN_DM\n          elseActions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: TT1015_OnNetworkTip_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TT1001_MainTroubleshootingOptions_DM = \"feature\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.language = \"en-US\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.TransferTag\n                  value: Technical_Support_English\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.TransferTag\n              value: Technical_Support_Spanish\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.GlobalVars.GetAccountDetails && Global.GlobalVars.GetAccountDetails.accountStatus = \"active\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: TT1005_UsingDeviceWithProblemYN_DM\n          elseActions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: TT1015_OnNetworkTip_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TT1001_MainTroubleshootingOptions_DM = \"payment\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.language = \"en-US\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.TransferTag\n                  value: Customer_Support_English\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.TransferTag\n              value: Customer_Support_Spanish\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.securityRequired\n          value: false\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.needMDN\n          value: false\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.callType\n          value: payment_tip\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TT1050_TroubleshootingPayment_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TT1001_MainTroubleshootingOptions_DM = \"voicemail_pin\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.language = \"en-US\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.TransferTag\n                  value: Customer_Support_English\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.TransferTag\n              value: Customer_Support_Spanish\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.securityRequired\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.needMDN\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.callType\n          value: reset_pin\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TT1055_CheckAlreadyHavePIN_JDA\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TT1001_MainTroubleshootingOptions_DM = \"none\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.language = \"en-US\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.TransferTag\n                  value: Technical_Support_English\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.TransferTag\n              value: Technical_Support_Spanish\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.callType\n          value: help_me_out\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TT1060_GoTo_CareFAQ_SD\n```", "timestamp": 1749529747.491791, "content_hash": "4eadca6fd97a3b2accbf2e42fb839f55"}