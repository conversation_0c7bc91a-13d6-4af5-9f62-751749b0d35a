{"response": "```yaml\n- kind: Question\n  id: aa4125_EBTOverviewWrapUpDTMF_DM\n  displayName: aa4125_EBTOverviewWrapUpDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.dnisInfo.callType = \"TX\",\n              \"To hear that again, Press 1 If you're experiencing a problem with your card, Press 2 For other options, Press 3 Or if you're done here, hang up\",\n\n              Global.dnisInfo.callType = \"WV\",\n              \"To hear that again, Press 1 Other options, Press 2\",\n\n              Global.dnisInfo.ebtOverviewOfferAgent = true,\n              \"If you would like to hear that again, Press 1 To go to the main menu, Press 2 To speak with a Customer Service Representative, press 3 And if you re done feel free to hang up\",\n\n              \"If you would like to hear that again, Press 1 To go to the main menu, Press 2 And if you re done feel free to hang up\"\n          )\n        }\n      - |\n        {Switch(\n              true,\n              Global.dnisInfo.dtmfOnlyFlag = true,\n              \"Sorry, I still didn t get that\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.aa4125_EBTOverviewWrapUpDTMF_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.dnisInfo.callType = \"TX\",\n              \"To hear that again, Press 1 If you're experiencing a problem with your card, Press 2 For other options, Press 3 Or if you're done here, hang up\",\n\n              Global.dnisInfo.callType = \"WV\",\n              \"To hear that again, Press 1 Other options, Press 2\",\n\n              Global.dnisInfo.ebtOverviewOfferAgent = true,\n              \"If you would like to hear that again, Press 1 To go to the main menu, Press 2 To speak with a Customer Service Representative, press 3 And if you re done feel free to hang up\",\n\n              \"If you would like to hear that again, Press 1 To go to the main menu, Press 2 And if you re done feel free to hang up\"\n          )\n        }\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: main_menu\n          displayName: main_menu\n        - id: other_options\n          displayName: other_options\n        - id: card_problem\n          displayName: card_problem\n        - id: repeat\n          displayName: repeat\n        - id: operator\n          displayName: operator\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa4125_EBTOverviewWrapUpDTMF_DM\n  value: =Text(Global.aa4125_EBTOverviewWrapUpDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa4125_EBTOverviewWrapUpDTMF_DM = \"main_menu\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa4004_GetMainMenuOptions_DB_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa4125_EBTOverviewWrapUpDTMF_DM = \"other_options\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa4105_OtherOptionsMenuDTMF_DM\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_aa4125_EBTOverviewWrapUpDTMF_DM = \"card_problem\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.globalVariables.transferAllowed\n                  value: true\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.globalVariables.transferReason\n                  value: cardProblem\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: transferHandler_CS\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_aa4125_EBTOverviewWrapUpDTMF_DM = \"repeat\", true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: aa4120_EBTOverViewPlayout_PP\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/aa4125_out_01.wav\\\">Again</audio>\"\n\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.Var_aa4125_EBTOverviewWrapUpDTMF_DM = \"operator\", true, false)\n                      actions:\n                        - kind: ConditionGroup\n                          id: conditionGroup_REPLACE_THIS\n                          conditions:\n                            - id: conditionItem_REPLACE_THIS\n                              condition: =If(Global.dnisInfo.ebtOverviewOfferAgent = true, true, false)\n                              actions:\n                                - kind: SetVariable\n                                  id: setVariable_REPLACE_THIS\n                                  variable: Global.globalVariables.transferAllowed\n                                  value: true\n\n                                - kind: SetVariable\n                                  id: setVariable_REPLACE_THIS\n                                  variable: Global.globalVariables.transferReason\n                                  value: default\n\n                                - kind: GotoAction\n                                  id: goto_REPLACE_THIS\n                                  actionId: transferHandler_CS\n\n                          elseActions:\n                            - kind: SetVariable\n                              id: setVariable_REPLACE_THIS\n                              variable: Global.globalVariables.transferAllowed\n                              value: false\n```", "timestamp": 1749543858.0237806, "content_hash": "3a90ab5cdbb42663eac284b21253bab0"}