{"response": "```yaml\n- kind: Question\n  id: sv0115_VoicemailMenu_DM\n  displayName: sv0115_VoicemailMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/sv0115_ni1_01.wav\\\">Say Set Up Voicemail or press 1, Change Voicemail Password or press 2, or its Something Else or press 3</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/sv0115_ni2_01.wav\\\">If you need help setting up your voicemail, press 1 If you want to change the password used to access your voicemail, press 2 Or if you need help with something else about your voicemail, press 3</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.sv0115_VoicemailMenu_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/sv0115_ini_01.wav\\\">Say Set Up Voicemail, Change Voicemail Password, or its Something Else</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: setup_vmail\n          displayName: setup_vmail\n        - id: change_vmail_password\n          displayName: change_vmail_password\n        - id: other\n          displayName: other\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/sv0115_ni1_01.wav\\\">Say Set Up Voicemail or press 1, Change Voicemail Password or press 2, or its Something Else or press 3</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/sv0115_ni2_01.wav\\\">If you need help setting up your voicemail, press 1 If you want to change the password used to access your voicemail, press 2 Or if you need help with something else about your voicemail, press 3</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_sv0115_VoicemailMenu_DM\n  value: =Text(Global.sv0115_VoicemailMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_sv0115_VoicemailMenu_DM = \"setup_vmail\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: sv0130_PlayVoicemailHelp_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_sv0115_VoicemailMenu_DM = \"change_vmail_password\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.intent\n              value: changeVmailPassword\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: sv0135_VoicemailHelpReset_SD\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_sv0115_VoicemailMenu_DM = \"other\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.transferPrompt\n                  value: default\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.transferReason\n                  value: BUSINESS_RULE\n                - kind: BeginDialog\n                  id: begin_REPLACE_THIS\n                  dialog: topic.M09_MainMenu_Dialog\n```", "timestamp": 1749472150.3780093, "content_hash": "d135979408eca3b24de5daf1db1f68d5"}