{"response": "```yaml\n- kind: Question\n  id: bm0420_AskCreditDebit_DM\n  displayName: bm0420_AskCreditDebit_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bm0420_AskCreditDebit_DM_noinput_1\\\">[custom audio: com.nuance.att.application.audio.bm0420_AskCreditDebit_DM_noinput_1]</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/bm0420_AskCreditDebit_DM_noinput_2\\\">[custom audio: com.nuance.att.application.audio.bm0420_AskCreditDebit_DM_noinput_2]</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.bm0420_AskCreditDebit_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bm0420_AskCreditDebit_initial\\\">[custom audio: com.nuance.att.application.audio.bm0420_AskCreditDebit_initial]</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: yes\n          displayName: yes\n        - id: no\n          displayName: no\n        - id: more_time\n          displayName: more_time\n        - id: dont_have\n          displayName: dont_have\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/bm0420_AskCreditDebit_DM_noinput_1\\\">[custom audio: com.nuance.att.application.audio.bm0420_AskCreditDebit_DM_noinput_1]</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/bm0420_AskCreditDebit_DM_noinput_2\\\">[custom audio: com.nuance.att.application.audio.bm0420_AskCreditDebit_DM_noinput_2]</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_bm0420_AskCreditDebit_DM\n  value: =Text(Global.bm0420_AskCreditDebit_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_bm0420_AskCreditDebit_DM = \"yes\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.refillMethod\n          value: credit\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferDestination\n          value: vesta\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.B02_AddMoney_05_Dialog\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_bm0420_AskCreditDebit_DM = \"no\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.intent = \"refillPIN\" || Global.intent = \"refillAccount\", true, false)\n                  actions:\n                    - kind: BeginDialog\n                      id: begin_REPLACE_THIS\n                      dialog: topic.B02_AddMoney_09_Dialog\n              elseActions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: getReturnLink()\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_bm0420_AskCreditDebit_DM = \"more_time\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.fromBm0420\n                  value: true\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: bm0415_RefillPINHelp_DM\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_bm0420_AskCreditDebit_DM = \"dont_have\", true, false)\n                  actions:\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/bm0420_out_02.wav\\\">No problem</audio>\"\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: bm0420_AskCreditDebit_DM\n```", "timestamp": 1749469812.5076084, "content_hash": "691991b3613a29d2ed1b871b2bce69b8"}