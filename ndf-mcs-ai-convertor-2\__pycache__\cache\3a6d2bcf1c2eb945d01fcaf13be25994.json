{"response": "```yaml\n- kind: Question\n  id: SW1105_GetNewLineMDN_DM\n  displayName: SW1105_GetNewLineMDN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SW1105_nm1_01.wav\\\">Please enter the phone number for the account you want to work with, including the area code Or say 'I don't know it'</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SW1105_nm2_01.wav\\\">On your keypad, enter the phone number you want to work with If you don't know it, press star</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SW1105_nm2_01.wav\\\">On your keypad, enter the phone number you want to work with If you don't know it, press star</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.SW1105_GetNewLineMDN_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n\n            // Case 1: switchLinesMDNAttempts = 0 && requestedMDNChangeSwitchLines = true\n            Global.switchLinesMDNAttempts = 0 && Global.requestedMDNChangeSwitchLines = true,\n            \"Enter that phone number on your keypad now\",\n\n            // Case 2: switchLinesMDNAttempts = 0 && requestedMDNChangeSwitchLines <> true && (tag = 'reactivate_phone_active' || tag = 'reactivate-old_account')\n            Global.switchLinesMDNAttempts = 0 && Global.requestedMDNChangeSwitchLines <> true && (Global.tag = \"reactivate_phone_active\" || Global.tag = \"reactivate-old_account\"),\n            \"What's the phone number you'd like to reactivate?\",\n\n            // Case 3: switchLinesMDNAttempts = 0 && requestedMDNChangeSwitchLines <> true && !(tag = 'reactivate_phone_active' || tag = 'reactivate-old_account')\n            Global.switchLinesMDNAttempts = 0 && Global.requestedMDNChangeSwitchLines <> true && !(Global.tag = \"reactivate_phone_active\" || Global.tag = \"reactivate-old_account\"),\n            \"Now, what's the phone number you want to *work* with? \",\n\n            // Case 4: switchLinesMDNAttempts = 1\n            Global.switchLinesMDNAttempts = 1,\n            \"I can't seem to find that Please try again using your keypad \",\n\n            // Default\n            \"Go ahead and enter the phone number you want to work with \"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: default\n          displayName: default\n        - id: dont_know\n          displayName: dont_know\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/SW1105_nm1_01.wav\\\">Please enter the phone number for the account you want to work with, including the area code Or say 'I don't know it'</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SW1105_nm2_01.wav\\\">On your keypad, enter the phone number you want to work with If you don't know it, press star</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SW1105_nm2_01.wav\\\">On your keypad, enter the phone number you want to work with If you don't know it, press star</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.requestedMDNChangeSwitchLines\n  value: GlobalVars.requestedMDNChangeSwitchLines\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.switchLinesMDNAttempts\n  value: GlobalVars.switchLinesMDNAttempts\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.tag\n  value: GlobalVars.tag\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_SW1105_GetNewLineMDN_DM\n  value: =Text(Global.SW1105_GetNewLineMDN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_SW1105_GetNewLineMDN_DM = \"default\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.newLine_MDN\n          value: SW1105_GetNewLineMDN_DM.returnvalue\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: SW1110_GetAccountDetails_DB_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_SW1105_GetNewLineMDN_DM = \"dont_know\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/SW1105_out_01.wav\\\">No problem</audio>\"\n\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.SwitchLines_Security.dvxml#SW1505_SwitchNotAllowedReason_DS\n```", "timestamp": **********.33826, "content_hash": "3a6d2bcf1c2eb945d01fcaf13be25994"}