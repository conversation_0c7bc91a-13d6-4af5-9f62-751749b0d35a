{"response": "```yaml\n- kind: Question\n  id: TT1050_TroubleshootingPayment_DM\n  displayName: TT1050_TroubleshootingPayment_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TT1050_nm1_01.wav\\\">You can say my payment hasn't posted Otherwise, please hang up and call back if your payment takes more than 2 hours to post</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.TT1050_TroubleshootingPayment_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.transferDestination = \"agent\",\n            \"Keep in mind that payments might take a little while to post If you submitted a payment more than 2 hours ago but it hasn't posted, say my payment hasn't posted and I'll find someone to help you Otherwise, please hang up for now and call back if it takes more than 2 hours to post \",\n            Global.transferDestination <> \"agent\",\n            \"Keep in mind that payments might take a little while to post If you submitted a payment more than 2 hours ago but it hasn't posted, say my payment hasn't posted Otherwise, please hang up for now and call back if it takes more than 2 hours to post\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: not_posted\n          displayName: not_posted\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/TT1050_ni1_01.wav\\\">You can say my payment hasn't posted Otherwise, please hang up and call back if your payment takes more than 2 hours to post</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_TT1050_TroubleshootingPayment_DM\n  value: =Text(Global.TT1050_TroubleshootingPayment_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.Var_TT1050_TroubleshootingPayment_DM = \"not_posted\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.transferDestination = \"agent\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.maxRetryTroubleshooting\n                  value: false\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: TT1045_Transfer_PP\n\n          elseActions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: TT1038_StoreOffer_DM\n```", "timestamp": 1749530419.9055388, "content_hash": "6ce7327b272c52f90913d697283a6571"}