{"response": "```yaml\n- kind: Question\n  id: cd0115_OldSIMWrap_DM\n  displayName: cd0115_OldSIMWrap_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I missed that</audio>\"\n      - |\n        {Switch(\n              true,\n              Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n              \"If you need help finding your SIM card, press 1 If not, go ahead and hang up, and then try moving your SIM card to your new phone\",\n              true,\n              \"If you need help finding your SIM card, press 1 If not, go ahead and hang up, and then try moving your SIM card to your new device\"\n          )\n        }\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">Sorry</audio>\"\n      - |\n        {Switch(\n              true,\n              Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n              \"If you need help finding your SIM card, press 1 Or, if you know where to find it, hang up now and then move your SIM card to your new phone\",\n              true,\n              \"If you need help finding your SIM card, press 1 Or, if you know where to find it, hang up now and then move your SIM card to your new device\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.cd0115_OldSIMWrap_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.cd0115lastDM <> \"\" && Global.cd0115lastDM = \"cd0110\" && (Global.accountType = \"gophone\" || Global.accountType = \"spareOne\"),\n            [\n              \"You ll need to turn off your phone and try to move your SIM card into the new phone If it doesn t fit, you can use the SIM card that came with your new phone or get one at an ATandT store\",\n              \"Say Help Me Find It, or just go ahead and try moving that SIM card If you have any issues, just call us back\"\n            ],\n            Global.cd0115lastDM <> \"\" && Global.cd0115lastDM = \"cd0110\" && (Global.accountType <> \"gophone\" && Global.accountType <> \"spareOne\"),\n            [\n              \"You ll need to turn off your device and try to move your SIM card into the new device If it doesn t fit, you can use the SIM card that came with your new device or get one at an ATandT store\",\n              \"Say Help Me Find It, or just go ahead and try moving that SIM card If you have any issues, just call us back\"\n            ],\n            true,\n            [\n              \"To repeat those instructions, say Help Me Find It Otherwise, go ahead and try moving that SIM card, and if you have any issues, just call us back\"\n            ]\n        )}\n      - \"<audio src=\\\"AUDIO_LOCATION/cd0115_ini_02.wav\\\">If that s all you needed, go ahead and hang up</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: help\n          displayName: help\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n                \"If you need help finding your SIM card, press 1 If not, go ahead and hang up, and then try moving your SIM card to your new phone\",\n                true,\n                \"If you need help finding your SIM card, press 1 If not, go ahead and hang up, and then try moving your SIM card to your new device\"\n            )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_cd0115_OldSIMWrap_DM\n  value: =Text(Global.cd0115_OldSIMWrap_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_cd0115_OldSIMWrap_DM = \"help\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: cd0120_OldSIMHelp_PP\n```", "timestamp": **********.2079623, "content_hash": "93eed99306cb8df25fbbc75c732ee8cd"}