{"response": "```yaml\n- kind: Question\n  id: TS1225_DataUsageExceededWrapMenu_DM\n  displayName: TS1225_DataUsageExceededWrapMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TS1225_ini_01.wav\\\">If you think the problem is something else, say 'more tips' If you're done, you can simply hang up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TS1225_ini_02.wav\\\">For more data, say 'get a top-up', or 'change plan' If you think the problem is something else, say 'more tips' If you're done, you can simply hang up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TS1225_ini_03.wav\\\">I can offer you plans with more data, or if you think the problem is something else, I can give you some more tips So, say 'change plan' or 'more tips'  If you're done, you can simply hang up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TS1225_nm2_01.wav\\\">If your problem is NOT from a slow connection, say 'more tips' or press 1 If you're done, you can just hang up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TS1225_nm2_02.wav\\\">If your connection is too slow and you want more data, say 'get a top-up' or press 1, or 'change plan' or press 2 If your issue is something else, say 'more tips' or press 3 If you're done for now, you can just hang up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TS1225_nm2_03.wav\\\">If your connection is too slow and you want more data, say 'change plan' or press 1 If your issue is something else, say 'more tips' or press 2 If you're done for now, you can just hang up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TS1225_nm2_01.wav\\\">If your problem is NOT from a slow connection, say 'more tips' or press 1 If you're done, you can just hang up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TS1225_nm2_02.wav\\\">If your connection is too slow and you want more data, say 'get a top-up' or press 1, or 'change plan' or press 2 If your issue is something else, say 'more tips' or press 3 If you're done for now, you can just hang up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TS1225_nm2_03.wav\\\">If your connection is too slow and you want more data, say 'change plan' or press 1 If your issue is something else, say 'more tips' or press 2 If you're done for now, you can just hang up</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.TS1225_DataUsageExceededWrapMenu_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.isUnlimited = true,\n            \"If you think the problem is something else, say 'more tips' If you're done, you can simply hang up\",\n            Global.isUnlimited <> true && Global.topupEligibility = \"ELIGIBLE\",\n            \"For more data, say 'get a top-up', or 'change plan' If you think the problem is something else, say 'more tips' If you're done, you can simply hang up\",\n            Global.isUnlimited <> true && Global.topupEligibility <> \"ELIGIBLE\",\n            \"I can offer you plans with more data, or if you think the problem is something else, I can give you some more tips So, say 'change plan' or 'more tips'  If you're done, you can simply hang up\",\n            \"If you think the problem is something else, say 'more tips' If you're done, you can simply hang up\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: buy-data_topup\n          displayName: buy-data_topup\n        - id: tips\n          displayName: tips\n        - id: change-plan\n          displayName: change-plan\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/TS1225_ini_01.wav\\\">If you think the problem is something else, say 'more tips' If you're done, you can simply hang up</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/TS1225_ini_02.wav\\\">For more data, say 'get a top-up', or 'change plan' If you think the problem is something else, say 'more tips' If you're done, you can simply hang up</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/TS1225_ini_03.wav\\\">I can offer you plans with more data, or if you think the problem is something else, I can give you some more tips So, say 'change plan' or 'more tips'  If you're done, you can simply hang up</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/TS1225_nm2_01.wav\\\">If your problem is NOT from a slow connection, say 'more tips' or press 1 If you're done, you can just hang up</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/TS1225_nm2_02.wav\\\">If your connection is too slow and you want more data, say 'get a top-up' or press 1, or 'change plan' or press 2 If your issue is something else, say 'more tips' or press 3 If you're done for now, you can just hang up</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/TS1225_nm2_03.wav\\\">If your connection is too slow and you want more data, say 'change plan' or press 1 If your issue is something else, say 'more tips' or press 2 If you're done for now, you can just hang up</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/TS1225_nm2_01.wav\\\">If your problem is NOT from a slow connection, say 'more tips' or press 1 If you're done, you can just hang up</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/TS1225_nm2_02.wav\\\">If your connection is too slow and you want more data, say 'get a top-up' or press 1, or 'change plan' or press 2 If your issue is something else, say 'more tips' or press 3 If you're done for now, you can just hang up</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/TS1225_nm2_03.wav\\\">If your connection is too slow and you want more data, say 'change plan' or press 1 If your issue is something else, say 'more tips' or press 2 If you're done for now, you can just hang up</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.isUnlimited\n  value: \"GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.isUnlimited:false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.topupEligibility\n  value: GlobalVars.GetAccountDetails.topupEligibility\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_grammar1\n  value: TS1225_DataUsageExceededWrapMenu_DM.jsp\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_dtmfgrammar1\n  value: TS1225_DataUsageExceededWrapMenu_DM_dtmf.jsp\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.allowedResponses\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.allowedResponsesDtmf\n  value: \"\"\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_TS1225_DataUsageExceededWrapMenu_DM\n  value: =Text(Global.TS1225_DataUsageExceededWrapMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TS1225_DataUsageExceededWrapMenu_DM = \"buy-data_topup\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/TS1225_out_01.wav\\\">You got it! </audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.tag\n          value: buy-data_topup\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TS1405_GetPIN_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_TS1225_DataUsageExceededWrapMenu_DM = \"tips\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/TS1225_out_02.wav\\\">Sure thing! </audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: TS1210_PlayMobileTips_PP\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_TS1225_DataUsageExceededWrapMenu_DM = \"change-plan\", true, false)\n              actions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/TS1225_out_03.wav\\\">You got it! </audio>\"\n                      - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.callType\n                  value: change_plan\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.securityRequired\n                  value: true\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: TS1505_GetPIN_SD\n```", "timestamp": 1749529747.611098, "content_hash": "613861102f974b9795ebdbb4ab07171b"}