{"response": "```yaml\n- kind: Question\n  id: aa3025_WICCChildIDDTMF_DM\n  displayName: aa3025_WICCChildIDDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa3025_nm1_01.wav\\\">Sorry, I didn t get that</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/aa3025_ini_02.wav\\\">If you have a different Child ID please enter it now</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.aa3025_WICCChildIDDTMF_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa3025_ini_01.wav\\\">If you know the child ID, enter it now</audio>\"\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.wiccBalanceVariables.collectedChildID\n  value: returnvalue\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.wiccBalanceVariables.childFound = true, true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.childInfo.totalProviders = 0, true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.wiccBalanceVariables.childFound\n                  value: false\n\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/aa3025_out_07.wav\\\">This child currently doesn t have any active providers or balance Please call back soon If you have a different Child ID please enter it now</audio>\"\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: aa3025_WICCChildIDDTMF_DM\n\n          elseActions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa3026_ChildInfo_PP\n\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.globalVariables.childCount\n      value: 0\n\n    - kind: GotoAction\n      id: goto_REPLACE_THIS\n      actionId: aa3030_WICCChildIdSimplifiedDTMF_DM\n```", "timestamp": **********.7495477, "content_hash": "c85580a32d350b20567b218128ed65ea"}