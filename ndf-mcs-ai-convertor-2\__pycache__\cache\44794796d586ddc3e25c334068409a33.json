{"response": "```yaml\n- kind: Question\n  id: st0455_AskRefill_DM\n  displayName: st0455_AskRefill_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"com.nuance.att.application.audio.st0455_AskRefill_DM_noinput1\"\n      - \"com.nuance.att.application.audio.st0455_AskRefill_DM_noinput2\"\n\n  alwaysPrompt: true\n  variable: Global.st0455_AskRefill_DM_reco\n  prompt:\n    speak:\n      - \"com.nuance.att.application.audio.st0455_AskRefill_DM_initial\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: refill\n          displayName: refill\n        - id: change_plan\n          displayName: change_plan\n        - id: text\n          displayName: text\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"com.nuance.att.application.audio.st0455_AskRefill_DM_noinput1\"\n        - \"com.nuance.att.application.audio.st0455_AskRefill_DM_noinput2\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_st0455_AskRefill_DM\n  value: =Text(Global.st0455_AskRefill_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_st0455_AskRefill_DM = \"refill\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: refillAccount\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.playBalanceReminder\n          value: false\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: st0445_CallTextRefill_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_st0455_AskRefill_DM = \"change_plan\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.intent\n              value: changePlan\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: st0440_CallTextChangePlan_SD\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_st0455_AskRefill_DM = \"text\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.transferReason\n                  value: BUSINESS_RULE\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.transferPrompt\n                  value: default\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: M09_MainMenu.dvxml\n```", "timestamp": 1749472149.3546863, "content_hash": "44794796d586ddc3e25c334068409a33"}