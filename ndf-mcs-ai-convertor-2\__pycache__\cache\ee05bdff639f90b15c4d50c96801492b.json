{"response": "```yaml\n- kind: Question\n  id: SQ1020_ConfirmSchoolNameYN_DM\n  displayName: SQ1020_ConfirmSchoolNameYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SQ1020_nm1_01.wav\\\">Please say  yes  or  no   I recorded</audio>\"\n      - \"{Global.schoolNameFilename}\"\n      - \"{Global.spellSchoolFilename}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SQ1020_nm1_05.wav\\\">Is that right?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SQ1020_nm2_01.wav\\\">Please say  yes  or press one or  no  or press two  I recorded</audio>\"\n      - \"{Global.schoolNameFilename}\"\n      - \"{Global.spellSchoolFilename}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SQ1020_nm2_05.wav\\\">Is that right?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SQ1020_nm3_01.wav\\\">If your school name is</audio>\"\n      - \"{Global.schoolNameFilename}\"\n      - \"{Global.spellSchoolFilename}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SQ1020_nm3_05.wav\\\">Say  yes  or press one To re-record, say  no  or press two</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.SQ1020_ConfirmSchoolNameYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/SQ1020_ini_01.wav\\\">I recorded</audio>\"\n      - \"{Global.schoolNameFilename}\"\n      - \"{Global.spellSchoolFilename}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SQ1020_ini_05.wav\\\">Is that right?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/SQ1020_nm1_01.wav\\\">Please say  yes  or  no   I recorded</audio>\"\n        - \"{Global.schoolNameFilename}\"\n        - \"{Global.spellSchoolFilename}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SQ1020_nm1_05.wav\\\">Is that right?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SQ1020_nm2_01.wav\\\">Please say  yes  or press one or  no  or press two  I recorded</audio>\"\n        - \"{Global.schoolNameFilename}\"\n        - \"{Global.spellSchoolFilename}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SQ1020_nm2_05.wav\\\">Is that right?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SQ1020_nm3_01.wav\\\">If your school name is</audio>\"\n        - \"{Global.schoolNameFilename}\"\n        - \"{Global.spellSchoolFilename}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SQ1020_nm3_05.wav\\\">Say  yes  or press one To re-record, say  no  or press two</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.schoolNameFilename\n  value: \"'' + GlobalVars.schoolNameFilename\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.spellSchoolFilename\n  value: \"'' + GlobalVars.spellSchoolFilename\"\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_SQ1020_ConfirmSchoolNameYN_DM\n  value: =Text(Global.SQ1020_ConfirmSchoolNameYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_SQ1020_ConfirmSchoolNameYN_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.securityQuestion\n          value: school\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/SQ1020_out_01.wav\\\">Thanks, someone will listen to that too and update your account</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: SQ1025_SubmitSchoolNameToTranscription_DB_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_SQ1020_ConfirmSchoolNameYN_DM = \"false\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.securityQuestionFNameAttempts < 1, true, false)\n                  actions:\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/SQ1020_out_02.wav\\\">My mistake  Let s try one more time</audio>\"\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.securityQuestionFNameAttempts\n                      value: securityQuestionFNameAttempts + 1\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: SQ1000_RecordSchoolName_DM\n\n              elseActions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/SQ1020_out_03.wav\\\">My mistake again</audio>\"\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: getReturnLink()\n```", "timestamp": 1749529494.112292, "content_hash": "ee05bdff639f90b15c4d50c96801492b"}