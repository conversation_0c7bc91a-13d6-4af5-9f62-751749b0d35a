{"response": "```yaml\n- kind: Question\n  id: SP1320_ReturnToTaskYN_DM\n  displayName: SP1320_ReturnToTaskYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SP1320_nm1_01.wav\\\">Do you want to continue switching your phone</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SP1320_nm1_02.wav\\\">Would you like to activate your SIM card now</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SP1320_nm2_01.wav\\\">If you want to continue switching your phone,  say 'yes' or press 1 Otherwise, say 'no' or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SP1320_nm2_02.wav\\\">If you want to activate your SIM card right now, say 'yes' or press 1 Otherwise, say 'no' or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SP1320_nm3_01.wav\\\">To continue switching your phone, press 1 If you're done, press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SP1320_nm3_02.wav\\\">To activate your SIM card now, press 1 If you're done, press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.SP1320_ReturnToTaskYN_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.callType = \"esn_swap\",\n            \"Do you want to continue switching your phone\",\n            Global.callType <> \"esn_swap\",\n            \"We still need to activate your SIM card so you can begin using your phone Would you like to do that now? \"\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/SP1320_nm1_01.wav\\\">Do you want to continue switching your phone</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SP1320_nm1_02.wav\\\">Would you like to activate your SIM card now</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SP1320_nm2_01.wav\\\">If you want to continue switching your phone,  say 'yes' or press 1 Otherwise, say 'no' or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SP1320_nm2_02.wav\\\">If you want to activate your SIM card right now, say 'yes' or press 1 Otherwise, say 'no' or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SP1320_nm3_01.wav\\\">To continue switching your phone, press 1 If you're done, press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SP1320_nm3_02.wav\\\">To activate your SIM card now, press 1 If you're done, press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.callType\n  value: GlobalVars.callType\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_SP1320_ReturnToTaskYN_DM\n  value: =Text(Global.SP1320_ReturnToTaskYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_SP1320_ReturnToTaskYN_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/SP1320_out_01.wav\\\">Alright</audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.continue228TaskAfterPayment\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.paymentsEntryPoint\n          value: undefined\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: getReturnLink()\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_SP1320_ReturnToTaskYN_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/SP1320_out_02.wav\\\">Alright</audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.callType\n              value: goodbye\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.cti_Intent\n              value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.paymentsEntryPoint\n              value: undefined\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n```", "timestamp": 1749529554.8845153, "content_hash": "fd78c63588c57e3e99edc0f3ff320d8d"}