{"response": "```yaml\n- kind: Question\n  id: MW1075_ConfirmCard_DM\n  displayName: MW1075_ConfirmCard_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1075_ini_01.wav\\\">You have one saved card  So to confirm, you d like to remove</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1075_ini_02.wav\\\">You have one saved card  You d like to update your</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1075_ini_01.wav\\\">You have one saved card  So to confirm, you d like to remove</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1075_ini_02.wav\\\">You have one saved card  You d like to update your</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1075_ini_01.wav\\\">You have one saved card  So to confirm, you d like to remove</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1075_ini_02.wav\\\">You have one saved card  You d like to update your</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.MW1075_ConfirmCard_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.manageCardTask = \"removeCard\",\n            \"You have one saved card  So to confirm, you d like to remove\",\n            Global.manageCardTask <> \"removeCard\",\n            \"You have one saved card  You d like to update your\"\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1075_ini_01.wav\\\">You have one saved card  So to confirm, you d like to remove</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1075_ini_02.wav\\\">You have one saved card  You d like to update your</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1075_ini_01.wav\\\">You have one saved card  So to confirm, you d like to remove</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1075_ini_02.wav\\\">You have one saved card  You d like to update your</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1075_ini_01.wav\\\">You have one saved card  So to confirm, you d like to remove</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1075_ini_02.wav\\\">You have one saved card  You d like to update your</audio>\"\n      defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.manageCardTask\n  value: GlobalVars.manageCardTask\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_MW1075_ConfirmCard_DM\n  value: =Text(Global.MW1075_ConfirmCard_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MW1075_ConfirmCard_DM = \"true\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.manageCardTask = \"removeCard\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: MW1601_DeleteSPMItem_DB_DA\n          elseActions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: MW1300_GetExpirationDate_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_MW1075_ConfirmCard_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/MW1075_out_01.wav\\\">Ok no problem</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: MW1020_ManageCardsMenu_DM\n```", "timestamp": 1749557007.0816348, "content_hash": "608ba7cfee0a03a1eb672bd77fc2abfc"}