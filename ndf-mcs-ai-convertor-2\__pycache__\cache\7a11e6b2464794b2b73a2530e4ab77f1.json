{"response": "```yaml\n- kind: Question\n  id: TT1038_StoreOffer_DM\n  displayName: TT1038_StoreOffer_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TT1038_nm1_01.wav\\\">You can say help me find a MetroPCS store or if you're finished, you can just hang up</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.TT1038_StoreOffer_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.callType = \"payment_tip\",\n            \"For more help, you can visit a Metro  store or authorized dealer You can say  find a store  or if you re finished, you can just hang up\",\n            Global.callType <> \"payment_tip\",\n            \"For more help, you can bring your device to a Metro  store or authorized dealer You can say  find a store  or if you re finished, you can just hang up\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: find_store\n          displayName: find_store\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/TT1038_ni1_01.wav\\\">You can say help me find a MetroPCS store or if you're finished, you can just hang up</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_TT1038_StoreOffer_DM\n  value: =Text(Global.TT1038_StoreOffer_DM_reco)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.callType\n  value: GlobalVars.callType\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TT1038_StoreOffer_DM = \"find_store\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.storeLocatorReason\n          value: unknown\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TT1080_GoTo_StoreLocator_SD\n```", "timestamp": 1749530402.8907135, "content_hash": "7a11e6b2464794b2b73a2530e4ab77f1"}