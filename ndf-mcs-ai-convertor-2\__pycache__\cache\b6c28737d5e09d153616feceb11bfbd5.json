{"response": "```yaml\n- kind: Question\n  id: SH1215_PayNowYN_DM\n  displayName: SH1215_PayNowYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SH1215_nm1_01.wav\\\">Do you want to pay your new amount now</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SH1215_ini_01.wav\\\">To turn your service back on you still need to make a payment for</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SH1215_ini_02.wav\\\">Would you like to pay that right now</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SH1215_ini_01.wav\\\">To turn your service back on you still need to make a payment for</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SH1215_ini_02.wav\\\">Would you like to pay that right now</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.SH1215_PayNowYN_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.SH1215_operator_counter = 1,\n            [\n                \"Im sorry I cant transfer you right now\",\n                \"test\",\n                \"Would you like to make your payment now\"\n            ],\n            [\n                \"To turn your service back on you still need to make a payment for\",\n                \"{Global.dueImmediatelyAmount}\",\n                \"test\",\n                \"Would you like to pay that right now\"\n            ]\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/SH1215_nm1_01.wav\\\">Do you want to pay your new amount now</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SH1215_ini_01.wav\\\">To turn your service back on you still need to make a payment for</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SH1215_ini_02.wav\\\">Would you like to pay that right now</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SH1215_ini_01.wav\\\">To turn your service back on you still need to make a payment for</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SH1215_ini_02.wav\\\">Would you like to pay that right now</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.dueImmediatelyAmount\n  value: GlobalVars.GetAccountDetails.dueImmediatelyAmount\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_SH1215_PayNowYN_DM\n  value: =Text(Global.SH1215_PayNowYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_SH1215_PayNowYN_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: make_pmt\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.acceptPayByPhone\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.paymentAmount\n          value: dueImmediatelyAmount\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.paymentsEntryPoint\n          value: careSuspended\n\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.SuspendedHandling_Main.dvxml#SH1010_MakePayment_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_SH1215_PayNowYN_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/SH1215_out_01.wav\\\">No problem</audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: SH1315_PaymentMethods_PP\n```", "timestamp": 1749529617.9605465, "content_hash": "b6c28737d5e09d153616feceb11bfbd5"}