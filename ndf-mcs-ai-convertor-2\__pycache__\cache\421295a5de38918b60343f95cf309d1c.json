{"response": "```yaml\n- kind: Question\n  id: DS3005_AskLanguage_DM\n  displayName: DS3005_AskLanguage_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.DS3005_AskLanguage_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/DS3005_ini_01.wav\\\">You've reached Metro's DSG! Para continuar en espanol, marque el nueve</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: Spanish\n          displayName: Spanish\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_DS3005_AskLanguage_DM\n  value: =Text(Global.DS3005_AskLanguage_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_DS3005_AskLanguage_DM = \"Spanish\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.DS3005Pressed\n          value: '9'\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.language\n          value: es-US\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.DSG_Main.dvxml#DS3007_CheckBCSSuccess_DS\n\n  elseActions:\n    - kind: GotoAction\n      id: goto_REPLACE_THIS\n      actionId: DS3007_CheckBCSSuccess_JDA\n```", "timestamp": 1749528286.4321945, "content_hash": "421295a5de38918b60343f95cf309d1c"}