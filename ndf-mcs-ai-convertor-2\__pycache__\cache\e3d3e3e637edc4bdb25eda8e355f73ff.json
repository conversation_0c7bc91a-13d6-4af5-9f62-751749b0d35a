{"response": "```yaml\n- kind: Question\n  id: RP1305_ConfirmRatePlan_DM\n  displayName: RP1305_ConfirmRatePlan_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/RP1305_nm1_01.wav\\\">Just say  yes  or  no  - do you want to switch to that plan?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/RP1305_nm2_01.wav\\\">If you d like to switch to that plan, say  yes  or press 1 Otherwise, to stay on your current plan, say  no  or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/RP1305_nm2_01.wav\\\">If you d like to switch to that plan, say  yes  or press 1 Otherwise, to stay on your current plan, say  no  or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.RP1305_ConfirmRatePlan_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.enteringFrom = \"RP1055_GoToDisambiguateRatePlan_SD_return\",\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/RP1305_ini_01.wav\\\">Just to reconfirm, you want to switch to</audio>\",\n              \"{Global.plan}\",\n              \"<audio src=\\\"AUDIO_LOCATION/RP1305_ini_12.wav\\\">Is that right?</audio>\"\n            ],\n            Global.enteringFrom <> \"RP1055_GoToDisambiguateRatePlan_SD_return\",\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/RP1305_ini_02.wav\\\">Okay, you want to switch to</audio>\",\n              \"{Global.plan}\",\n              \"<audio src=\\\"AUDIO_LOCATION/RP1305_ini_12.wav\\\">Is that right?</audio>\"\n            ],\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/RP1305_ini_02.wav\\\">Okay, you want to switch to</audio>\",\n              \"{Global.plan}\",\n              \"<audio src=\\\"AUDIO_LOCATION/RP1305_ini_12.wav\\\">Is that right?</audio>\"\n            ]\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/RP1305_nm1_01.wav\\\">Just say  yes  or  no  - do you want to switch to that plan?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/RP1305_nm2_01.wav\\\">If you d like to switch to that plan, say  yes  or press 1 Otherwise, to stay on your current plan, say  no  or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/RP1305_nm2_01.wav\\\">If you d like to switch to that plan, say  yes  or press 1 Otherwise, to stay on your current plan, say  no  or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.plan\n  value: GlobalVars.selectedPlan\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.enteringFrom\n  value: GlobalVars.confirmingRatePlanFromState\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.ratePlans\n  value: GlobalVars.ratePlans\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_RP1305_ConfirmRatePlan_DM\n  value: =Text(Global.RP1305_ConfirmRatePlan_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_RP1305_ConfirmRatePlan_DM = \"true\", true, false)\n      actions:\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.RatePlan_Main.dvxml#RP0301_CheckPlanOptions_DS\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_RP1305_ConfirmRatePlan_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/gl_thatsfine.wav\\\">That s fine</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: RP1335_GoToAnythingElse_SD\n```", "timestamp": 1749529118.1371875, "content_hash": "e3d3e3e637edc4bdb25eda8e355f73ff"}