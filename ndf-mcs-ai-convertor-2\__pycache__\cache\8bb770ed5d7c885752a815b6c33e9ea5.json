{"response": "```yaml\n- kind: Question\n  id: ma1340_BalancePaymentsMenu_DM\n  displayName: ma1340_BalancePaymentsMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I missed that</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ma1340_ni1_01.wav\\\">Say Make a Payment or press 1, AutoPay or press 2, Check My Balance or press 3, or Another Account or press 4 Or for anything else, say Main Menu or press star</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">Sorry</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ma1340_ni2_01.wav\\\">To make a one time payment on your account, press 1 To enroll in or manage your AutoPay settings, press 2 To check your balance, press 3 For help with a different A T AND T Prepaid account, press 4 Or for anything else, press star</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ma1340_BalancePaymentsMenu_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ma1340_ini_01.wav\\\">Balance and Payments Say Make a Payment, AutoPay, Check My Balance, or Another Account Or say Main Menu</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: account_info\n          displayName: account_info\n        - id: auto_refill\n          displayName: auto_refill\n        - id: refill\n          displayName: refill\n        - id: other_account\n          displayName: other_account\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ma1340_ni1_01.wav\\\">Say Make a Payment or press 1, AutoPay or press 2, Check My Balance or press 3, or Another Account or press 4 Or for anything else, say Main Menu or press star</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ma1340_ni2_01.wav\\\">To make a one time payment on your account, press 1 To enroll in or manage your AutoPay settings, press 2 To check your balance, press 3 For help with a different A T AND T Prepaid account, press 4 Or for anything else, press star</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ma1340_BalancePaymentsMenu_DM\n  value: =Text(Global.ma1340_BalancePaymentsMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma1340_BalancePaymentsMenu_DM = \"account_info\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.playProactiveInfo\n          value: false\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/ma1340_out_01.wav\\\">Got it</audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: accountInfo\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ma1335_MainMenuRouting_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ma1340_BalancePaymentsMenu_DM = \"auto_refill\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/ma1340_out_01.wav\\\">Got it</audio>\"\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.intent\n              value: autoRefill\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: ma1335_MainMenuRouting_SD\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_ma1340_BalancePaymentsMenu_DM = \"refill\", true, false)\n              actions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/ma1340_out_01.wav\\\">Got it</audio>\"\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.intent\n                  value: refillAccount\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.simplifiedAddMoneyMenu\n                  value: true\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: ma1335_MainMenuRouting_SD\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_ma1340_BalancePaymentsMenu_DM = \"other_account\", true, false)\n                  actions:\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/ma1340_out_01.wav\\\">Got it</audio>\"\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.intent\n                      value: otherAccount\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: ma1335_MainMenuRouting_SD\n```", "timestamp": **********.689748, "content_hash": "8bb770ed5d7c885752a815b6c33e9ea5"}