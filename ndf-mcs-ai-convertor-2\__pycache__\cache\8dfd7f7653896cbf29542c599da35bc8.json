{"response": "```yaml\n- kind: Question\n  id: TF1110_WaitPhoneReadySBI_DM\n  displayName: TF1110_WaitPhoneReadySBI_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.TF1110_WaitPhoneReadySBI_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/TF1110_ini_01.wav\\\">I can wait a minute When your phone is ready to receive text messages, say 'continue' or press 1 At any time, say 'continue' or press 1 Or say 'I can't get it' or press 2 At any time, say 'continue' or press 1 Or say 'I can't get it' or press 2 When you have your phone ready, say 'continue' or press 1 If you don't think you can get it to work, say 'I can't get it' or press 2 I'll wait a little bit longer At any time, say 'continue' or press 1 Or say 'I can't get it' or press 2 If your phone is ready to get text messages, say 'continue' or press 1 Or say 'I can't get it' or press 2</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: continue\n          displayName: continue\n        - id: cant_get\n          displayName: cant_get\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_TF1110_WaitPhoneReadySBI_DM\n  value: =Text(Global.TF1110_WaitPhoneReadySBI_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TF1110_WaitPhoneReadySBI_DM = \"continue\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TF1205_PlaySendingTempCode_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_TF1110_WaitPhoneReadySBI_DM = \"cant_get\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.twoFactorAuthOutcome\n              value: no_phone\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.cti_AuthStatus\n              value: getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'tfa_failed_nophone')\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: TF1113_CheckMultiline_JDA_DA\n```", "timestamp": 1749558673.6556044, "content_hash": "8dfd7f7653896cbf29542c599da35bc8"}