{"response": "```yaml\n- kind: Question\n  id: aa6026_CCBalanceWrapUpDTMF_DM\n  displayName: aa6026_CCBalanceWrapUpDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.dnisInfo.ccTieredState = false,\n              \"To hear that again, press 1  to make a payment, press 2 for more details about your child care account, press 3 or to return to the main menu, press 4\",\n              true,\n              \"To hear that again, press 1  to make a payment to this provider, press 2 for more details about your child care account, press 3 or to return to the main menu, press 4\"\n          )\n        }\n      - |\n        {Switch(\n              true,\n              Global.dnisInfo.dtmfOnlyFlag = true,\n              \"Sorry, I still didn t get that\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.aa6026_CCBalanceWrapUpDTMF_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.dnisInfo.ccTieredState = false,\n              \"To hear that again, press 1  to make a payment, press 2 for more details about your child care account, press 3 or to return to the main menu, press 4\",\n              true,\n              \"To hear that again, press 1  to make a payment to this provider, press 2 for more details about your child care account, press 3 or to return to the main menu, press 4\"\n          )\n        }\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: repeat\n          displayName: repeat\n        - id: make_payment\n          displayName: make_payment\n        - id: more_details\n          displayName: more_details\n        - id: main_menu\n          displayName: main_menu\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa6026_CCBalanceWrapUpDTMF_DM\n  value: =Text(Global.aa6026_CCBalanceWrapUpDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa6026_CCBalanceWrapUpDTMF_DM = \"repeat\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aa6026_out_01.wav\\\">Again</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa6025_CCBalance_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa6026_CCBalanceWrapUpDTMF_DM = \"make_payment\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.currentTask\n              value: CCPayment\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.ccProviderInfo.ccPinSelect = \"N\", true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: aa6020_CCMustHavePinSelected_PP\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.dnisInfo.ccTieredState = true, true, false)\n                      actions:\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: aa6045_CCConfirmProviderIdDTMF_DM\n                  elseActions:\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/aa6026_out_02.wav\\\">Sure [audio icon] You ll be able to use your voice while making your payment</audio>\"\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: aa6029_InitializeProviderId_JDA_DA\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_aa6026_CCBalanceWrapUpDTMF_DM = \"more_details\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.globalVariables.transferAllowed\n                  value: true\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: transferHandler_CS\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_aa6026_CCBalanceWrapUpDTMF_DM = \"main_menu\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.globalVariables.nextStep\n                      value: main_menu\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: aa1015_NextStepHandling_JDA_DA\n```", "timestamp": 1749556822.1027305, "content_hash": "51e2baf358607e7f19e77d2a7a5defa2"}