{"response": "```yaml\n- kind: Question\n  id: MW1200_AskDebitOrCredit_DM\n  displayName: MW1200_AskDebitOrCredit_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1200_nm1_01.wav\\\">Please tell me if this card is a debit or credit card</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1200_nm2_01.wav\\\">For debit press 1  Credit press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1200_nm2_01.wav\\\">For debit press 1  Credit press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.MW1200_AskDebitOrCredit_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.tag = \"setup-autopay\" && Global.tryNewCard = true,\n            \"Will this be a debit or credit card?\",\n            Global.tag = \"setup-autopay\" && Global.tryNewCard <> true,\n            \"Will your autopayment card be a debit or a credit card?\",\n            (Global.manageCardTask = \"ReplaceAutopayCard\") && (Global.tag <> \"setup-autopay\"),\n            \"Ok, I have removed that card  Now is the new card going to be a debit or credit card?\",\n            !(Global.manageCardTask = \"ReplaceAutopayCard\") && (Global.tag <> \"setup-autopay\"),\n            \"Is this card a debit or credit card?\",\n            \"Is this card a debit or credit card?\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: debit\n          displayName: debit\n        - id: credit\n          displayName: credit\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1200_nm1_01.wav\\\">Please tell me if this card is a debit or credit card</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1200_nm2_01.wav\\\">For debit press 1  Credit press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1200_nm2_01.wav\\\">For debit press 1  Credit press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.tag\n  value: GlobalVars.tag\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.tryNewCard\n  value: GlobalVars.tryNewCard\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.manageCardTask\n  value: GlobalVars.manageCardTask\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_MW1200_AskDebitOrCredit_DM\n  value: =Text(Global.MW1200_AskDebitOrCredit_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MW1200_AskDebitOrCredit_DM = \"debit\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cardType\n          value: debit\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: MW1205_GetCardNumber_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_MW1200_AskDebitOrCredit_DM = \"credit\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.cardType\n              value: credit\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: MW1205_GetCardNumber_DM\n```", "timestamp": 1749557016.4881885, "content_hash": "e170c44b6a71624c7f0cdb3c0317c0ec"}