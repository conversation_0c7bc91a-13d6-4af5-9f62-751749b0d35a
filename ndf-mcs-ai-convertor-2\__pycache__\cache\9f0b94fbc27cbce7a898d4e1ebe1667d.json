{"response": "```yaml\n- kind: Question\n  id: bp0225_ConfirmDebit_DM\n  displayName: bp0225_ConfirmDebit_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bp0225_ni1_01.wav\\\">Thanks</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/bp0225_ni2_01.wav\\\">Alright</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.bp0225_ConfirmDebit_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.isFirstTimeInBp0225 = true,\n            \"Just so you know, your payment may be processed as a debit transaction Would you like to continue with this card?\",\n            true,\n            \"Your payment may be processed as a debit transaction on this card too Would you like to continue?\"\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/bp0225_ni1_01.wav\\\">Thanks</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/bp0225_ni2_01.wav\\\">Alright</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.fromBp0225\n  value: true\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_bp0225_ConfirmDebit_DM\n  value: =Text(Global.bp0225_ConfirmDebit_DM_reco)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.isFirstTimeInBp0225\n  value: false\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_bp0225_ConfirmDebit_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/bp0225_out_01.wav\\\">Thanks</audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\"></audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: bp0230_AskExpirationDate_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_bp0225_ConfirmDebit_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/bp0225_out_02.wav\\\">Alright</audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\"></audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: bp0210_AskCardNumber_DM\n```", "timestamp": 1749470051.39719, "content_hash": "9f0b94fbc27cbce7a898d4e1ebe1667d"}