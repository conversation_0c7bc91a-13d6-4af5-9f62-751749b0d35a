{"response": "```yaml\n- kind: Question\n  id: LG1115_GetSecurityCode_DM\n  displayName: LG1115_GetSecurityCode_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/LG1115_nm1_01.wav\\\">Please say or enter your 6-to-15-digit Metro account PIN  If you need to find it, say 'wait a minute' 15 seconds  You can also say 'I don't' know it' </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/LG1115_nm1_02.wav\\\">Please say or enter your 8-digit Metro account PIN  If you need to find it, say 'wait a minute' 15 seconds  You can also say 'I don't' know it' </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/LG1115_nm2_01.wav\\\">You selected a Metro account PIN when you first got your phone  Please enter your 6-to-15-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you can't find your security code account PIN, say 'I don't' know it' </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/LG1115_nm2_02.wav\\\">You selected a Metro account PIN when you first got your phone  Please enter your 8-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you can't find your security code account PIN, say 'I don't' know it'  </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/LG1115_nm2_01.wav\\\">You selected a Metro account PIN when you first got your phone  Please enter your 6-to-15-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you can't find your security code account PIN, say 'I don't' know it' </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/LG1115_nm2_02.wav\\\">You selected a Metro account PIN when you first got your phone  Please enter your 8-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you can't find your security code account PIN, say 'I don't' know it'  </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.LG1115_GetSecurityCode_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n\n            // 6-15 digit PIN, accountPinToggleOn == true\n            Global.shortSecurityCodePrompt = true && Global.accountPinToggleOn = true,\n            {Switch(\n                true,\n                Global.playTransitionalPINprompt = true && Global.pinAttempts = 0 && Global.reentry = false,\n                \"And now I need your 6-to-15-digit account PIN Say or enter it now\",\n                Global.callType = \"switch_lines\",\n                \"Just so you know, Metro Cares about your security! So first, I'll need the 6-to-15-digit account PIN for the phone you're *calling from* Say or enter it now \",\n                Global.playTransitionalPINprompt <> true && (Global.aniMatch = \"true\" || Global.aniMatch = true || Global.collectedMDNUpfront = true) && Global.callType = \"switch_phone\" && Global.pinAttempts = 0 && !Global.reentry,\n                \"Let's start with your 6-to-15-digit account PIN Say or enter it now  \",\n                Global.acceptedBCR = true,\n                \"What's your 6-to-15-digit account PIN?\",\n                true,\n                \"Now I need your 6-to-15-digit account PIN\"\n            )},\n\n            // 8 digit PIN, accountPinToggleOn != true\n            Global.shortSecurityCodePrompt = true && Global.accountPinToggleOn <> true,\n            {Switch(\n                true,\n                Global.playTransitionalPINprompt = true && Global.pinAttempts = 0 && Global.reentry = false,\n                \"And now I need your 8-digit account PIN Say or enter it now\",\n                Global.callType = \"switch_lines\",\n                \"Just so you know, Metro Cares about your security! So first, I'll need the 8-digit account PIN for the phone you're *calling from* Say or enter it now\",\n                Global.playTransitionalPINprompt <> true && (Global.aniMatch = \"true\" || Global.aniMatch = true || Global.collectedMDNUpfront = true) && Global.callType = \"switch_phone\" && Global.pinAttempts = 0 && !Global.reentry,\n                \"Let's start with your 8-digit account PIN Say or enter it now \",\n                Global.acceptedBCR = true,\n                \"What's your 8-digit account PIN?  \",\n                true,\n                \"Now I need your 8-digit account PIN\"\n            )},\n\n            // Default\n            \"<audio src=\\\"AUDIO_LOCATION/silence_3000ms.wav\\\">test</audio>\"\n        )}\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/LG1115_nm1_01.wav\\\">Please say or enter your 6-to-15-digit Metro account PIN  If you need to find it, say 'wait a minute' 15 seconds  You can also say 'I don't' know it' </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/LG1115_nm1_02.wav\\\">Please say or enter your 8-digit Metro account PIN  If you need to find it, say 'wait a minute' 15 seconds  You can also say 'I don't' know it' </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/LG1115_nm2_01.wav\\\">You selected a Metro account PIN when you first got your phone  Please enter your 6-to-15-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you can't find your security code account PIN, say 'I don't' know it' </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/LG1115_nm2_02.wav\\\">You selected a Metro account PIN when you first got your phone  Please enter your 8-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you can't find your security code account PIN, say 'I don't' know it'  </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/LG1115_nm2_01.wav\\\">You selected a Metro account PIN when you first got your phone  Please enter your 6-to-15-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you can't find your security code account PIN, say 'I don't' know it' </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/LG1115_nm2_02.wav\\\">You selected a Metro account PIN when you first got your phone  Please enter your 8-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you can't find your security code account PIN, say 'I don't' know it'  </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.acceptedBCR\n  value: GlobalVars.acceptedBCR\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.shortSecurityCodePrompt\n  value: GlobalVars.shortSecurityCodePrompt\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.playTransitionalPINprompt\n  value: GlobalVars.playTransitionalPINprompt\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.aniMatch\n  value: GlobalVars.aniMatch\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collectedMDNUpfront\n  value: GlobalVars.collectedMDNUpfront\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.callType\n  value: GlobalVars.callType\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.lastPinTry\n  value: GlobalVars.lastPinTry\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.reentry\n  value: \"GlobalVars.LG1115reentry == undefined ? false : GlobalVars.LG1115reentry\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.accountPinToggleOn\n  value: GlobalVars.GetBCSParameters.accountPinToggleOn\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.playTransitionalPINprompt\n  value: false\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.LG1115reentry\n  value: true\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_LG1115_GetSecurityCode_DM = \"wait\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: LG1120_SecurityCodeWait_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_LG1115_GetSecurityCode_DM = \"dont_know\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/LG1115_out_01.wav\\\">No problem</audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.dontKnowPIN\n              value: true\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: LG1301_CheckSQEligibility_JDA_DA\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_LG1115_GetSecurityCode_DM = \"default\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.pin\n                  value: LG1115_GetSecurityCode_DM.returnvalue\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.pinAttempts\n                  value: pinAttempts+1\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.verificationType\n                  value: pin\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.verificationValue\n                  value: LG1115_GetSecurityCode_DM.returnvalue\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.securityCode\n                  value: LG1115_GetSecurityCode_DM.returnvalue\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.cti_PIN\n                  value: LG1115_GetSecurityCode_DM.returnvalue\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: LG1124_Authenticate_DB_DA\n```", "timestamp": 1749558374.5186958, "content_hash": "4a7d5a808740927dcd8272ded04dcb77"}