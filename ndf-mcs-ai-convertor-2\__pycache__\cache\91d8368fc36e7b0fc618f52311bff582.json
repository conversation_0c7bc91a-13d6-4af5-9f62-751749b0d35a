{"response": "```yaml\n- kind: Question\n  id: MM1031_MainMenu_DM\n  displayName: MM1031_MainMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MM1031_nm1_01.wav\\\">To check your balance or make a payment, say 'Balance and payments', or press 1For things like change plan, plan details or services, say 'Plans and services', or press 2             For activating a device say 'Activations', or press 3 To request an extension say 'payment extension', or press 4For help with data, say 'Data options',press 5You can also say 'More options', or press 6</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MM1031_nm2_01.wav\\\">For 'Balance and payments'  press 1 ' Plans and services'  press 2, Activate a device', 3  Payment extension', 4 'Data options' 5  'More options' or press 6 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MM1031_nm2_01.wav\\\">For 'Balance and payments'  press 1 ' Plans and services'  press 2, Activate a device', 3  Payment extension', 4 'Data options' 5  'More options' or press 6 </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.MM1031_MainMenu_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.operatorPromptEnabled = true,\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/MM1031_ini_02.wav\\\">How can I help you? You can say 'Balance and payments', 'Plans and services' , 'Activations', 'Payment extensions', 'Data options', or say 'More options'</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_1000ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/MM1031_ini_03.wav\\\">And, you can say  Operator  at any time</audio>\"\n            ],\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/MM1031_ini_02.wav\\\">How can I help you? You can say 'Balance and payments', 'Plans and services' , 'Activations', 'Payment extensions', 'Data options', or say 'More options'</audio>\"\n            ]\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: inquire-balance_mm\n          displayName: inquire-balance_mm\n        - id: activate-phone_mm\n          displayName: activate-phone_mm\n        - id: vague-data_mm\n          displayName: vague-data_mm\n        - id: plan_addon\n          displayName: plan_addon\n        - id: switch-lines_mm\n          displayName: switch-lines_mm\n        - id: request-extension\n          displayName: request-extension\n        - id: home-internet\n          displayName: home-internet\n        - id: more_options\n          displayName: more_options\n        - id: inqire-acp\n          displayName: inqire-acp\n        - id: tmomoney\n          displayName: tmomoney\n        - id: make-payment\n          displayName: make-payment\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/MM1031_nm1_01.wav\\\">To check your balance or make a payment, say 'Balance and payments', or press 1For things like change plan, plan details or services, say 'Plans and services', or press 2             For activating a device say 'Activations', or press 3 To request an extension say 'payment extension', or press 4For help with data, say 'Data options',press 5You can also say 'More options', or press 6</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MM1031_nm2_01.wav\\\">For 'Balance and payments'  press 1 ' Plans and services'  press 2, Activate a device', 3  Payment extension', 4 'Data options' 5  'More options' or press 6 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MM1031_nm2_01.wav\\\">For 'Balance and payments'  press 1 ' Plans and services'  press 2, Activate a device', 3  Payment extension', 4 'Data options' 5  'More options' or press 6 </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.operatorPromptEnabled\n  value: \"GlobalVars.GetBCSParameters?GlobalVars.GetBCSParameters.care_operator_prompt_enabled == 'true': false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.returningFromPayments\n  value: GlobalVars.returningFromPayments\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.switchLinesSuccess\n  value: \"GlobalVars.switchLinesSuccess != undefined ? GlobalVars.switchLinesSuccess : false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.returningFromPayments\n  value: false\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_MM1031_MainMenu_DM\n  value: =Text(Global.MM1031_MainMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MM1031_MainMenu_DM = \"inquire-balance_mm\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: MM1032_BalanceandPaymentsMenu_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MM1031_MainMenu_DM = \"activate-phone_mm\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: MM1033_ActivatePhoneMenu_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MM1031_MainMenu_DM = \"vague-data_mm\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.GlobalVars.GetAccountDetails.parentDeviceType = \"INT\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: MM1047_Disambig_Home_Internet_DM\n          elseActions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: MM1034_DataMenu_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MM1031_MainMenu_DM = \"plan_addon\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.GlobalVars.GetAccountDetails.parentDeviceType = \"INT\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: MM1047_Disambig_Home_Internet_DM\n          elseActions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: MM1037_PlansandAddonsMenu_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MM1031_MainMenu_DM = \"switch-lines_mm\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/mm1031_out_01.wav\\\">Sure </audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: switch_lines\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.MainMenu_CheckSecurity.dvxml#MM1050_CheckContinueToDestination_DS\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MM1031_MainMenu_DM = \"request-extension\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/mm1031_out_01.wav\\\">Sure </audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: extension\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.MainMenu_CheckSecurity.dvxml#MM1050_CheckContinueToDestination_DS\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MM1031_MainMenu_DM = \"home-internet\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.GlobalVars.GetAccountDetails.parentDeviceType = \"INT\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.tag\n                  value: home-internet_active\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: MM1045_HomeInternetMenu_DM\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.tag\n              value: home-internet_transfer\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.MainMenu_Routing.dvxml#MM1545_GoToCallTransfer_SD\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MM1031_MainMenu_DM = \"more_options\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: MM1038_MoreOptionsMenu_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MM1031_MainMenu_DM = \"inquire-acp\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: MM1600_GoToACP_SD\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MM1031_MainMenu_DM = \"tmomoney\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.tag\n          value: T-MobileMoney\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.securityRequired\n          value: false\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.needMDN\n          value: false\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: tmomoney\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.MainMenu_CheckSecurity.dvxml#MM1050_CheckContinueToDestination_DS\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MM1031_MainMenu_DM = \"make-payment\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.securityRequired\n          value: false\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.needMDN\n          value: false\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: make_pmt\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.MainMenu_CheckSecurity.dvxml#MM1050_CheckContinueToDestination_DS\n```", "timestamp": 1749528728.826526, "content_hash": "91d8368fc36e7b0fc618f52311bff582"}