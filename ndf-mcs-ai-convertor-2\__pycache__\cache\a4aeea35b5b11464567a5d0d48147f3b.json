{"response": "```yaml\n- kind: Question\n  id: aa2588b_BBCollectPIN_DM\n  displayName: aa2588b_BBCollectPIN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.aa2588b_BBCollectPIN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa2588b_ini_01.wav\\\">Please say your 4-digit PIN now</audio>\"\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.validationCriteriaVariables.collectPin\n  value: aa2588b_BBCollectPIN_DM.returnvalue\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: aa2589_BBValidatePin_DB_DA\n```", "timestamp": 1749544340.4037278, "content_hash": "a4aeea35b5b11464567a5d0d48147f3b"}