{"response": "```yaml\n- kind: Question\n  id: ES1007_SimSwappedBlocked_DM\n  displayName: ES1007_SimSwappedBlocked_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ES1007_nomatch1_01.wav\\\">Your account is currently blocked from sim changes  Would you like to update your account settings now? </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ES1007_nm2_01.wav\\\">If you'd like to update your account settings so you can swap your sim card say 'yes' or press 1 Otherwise say 'no' or press 2 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ES1007_nm2_01.wav\\\">If you'd like to update your account settings so you can swap your sim card say 'yes' or press 1 Otherwise say 'no' or press 2 </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ES1007_SimSwappedBlocked_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ES1007_ini_01.wav\\\">Your account is currently blocked from sim changes  Would you like to update your accont settings now? </audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ES1007_nomatch1_01.wav\\\">Your account is currently blocked from sim changes  Would you like to update your account settings now? </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ES1007_nm2_01.wav\\\">If you'd like to update your account settings so you can swap your sim card say 'yes' or press 1 Otherwise say 'no' or press 2 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ES1007_nm2_01.wav\\\">If you'd like to update your account settings so you can swap your sim card say 'yes' or press 1 Otherwise say 'no' or press 2 </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ES1007_SimSwappedBlocked_DM\n  value: =Text(Global.ES1007_SimSwappedBlocked_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ES1007_SimSwappedBlocked_DM = \"true\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ES1008_CallCare2SelectProtect_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ES1007_SimSwappedBlocked_DM = \"false\", true, false)\n          actions:\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.CareESNSwap_Main_Cont.dvxml#ES2225_Transfer_SD\n```", "timestamp": 1749527965.6867287, "content_hash": "daff245b1d0af0c2cc987322e68e82d0"}