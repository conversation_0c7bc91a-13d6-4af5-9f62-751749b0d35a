{"response": "```yaml\n- kind: Question\n  id: AI1020_RepeatAcctDetails_DM\n  displayName: AI1020_RepeatAcctDetails_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AI1020_nm1_02.wav\\\">Please say  yes  or  no   Would you like to hear your telephone number and account details again?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AI1020_nm2_02.wav\\\">Please say  yes  or press one, or  no  or press two  Would you like to hear those account details again?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AI1020_nm3_02.wav\\\">To hear your phone number and account details again, say  yes  or press one  To continue, say  no  or press two</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.AI1020_RepeatAcctDetails_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_1000ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AI1020_ini_30.wav\\\">Would you like to hear that again?</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: yes\n          displayName: yes\n        - id: no\n          displayName: no\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/AI1020_nm1_02.wav\\\">Please say  yes  or  no   Would you like to hear your telephone number and account details again?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AI1020_nm2_02.wav\\\">Please say  yes  or press one, or  no  or press two  Would you like to hear those account details again?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AI1020_nm3_02.wav\\\">To hear your phone number and account details again, say  yes  or press one  To continue, say  no  or press two</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_AI1020_RepeatAcctDetails_DM\n  value: =Text(Global.AI1020_RepeatAcctDetails_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AI1020_RepeatAcctDetails_DM = \"yes\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: AI1015_PhoneNumber_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_AI1020_RepeatAcctDetails_DM = \"no\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/AI1020_out_01.wav\\\">Okay</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: AI1030_CheckNPI_JDA\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_AI1020_RepeatAcctDetails_DM = \"repeat\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: AI1015_PhoneNumber_PP\n```", "timestamp": **********.6454809, "content_hash": "819c450d726b140047f0247d57e55e5e"}