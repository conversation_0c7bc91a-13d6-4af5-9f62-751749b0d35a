{"response": "```yaml\n- kind: Question\n  id: DT1010_AcceptTermsYN_DM\n  displayName: DT1010_AcceptTermsYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - |\n        {Switch(\n              true,\n              Global.dataUsage_isUnlimited = true,\n              \"Are you ready to go ahead? You can also say 'repeat that' \",\n              true,\n              \"Are you ready to go ahead? You can also say 'repeat that', or 'change my plan' \"\n          )\n        }\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - |\n        {Switch(\n              true,\n              Global.dataUsage_isUnlimited = true,\n              \"Are you ready to add a top-up? To continue, say 'yes' or press 1 To hear the information about top-ups again, say 'repeat' or press 2 If you don't want to do anything just now, press 3 \",\n              true,\n              \"Are you ready to add a top-up? To continue, say 'yes' or press 1 To hear the information about top-ups again, say 'repeat' or press 2 To check out or other data plans, say 'change my plan' or press 3 If you don't want to do anything just now, press 4 \"\n          )\n        }\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - |\n        {Switch(\n              true,\n              Global.dataUsage_isUnlimited = true,\n              \"Are you ready to add a top-up? To continue, say 'yes' or press 1 To hear the information about top-ups again, say 'repeat' or press 2 If you don't want to do anything just now, press 3 \",\n              true,\n              \"Are you ready to add a top-up? To continue, say 'yes' or press 1 To hear the information about top-ups again, say 'repeat' or press 2 To check out or other data plans, say 'change my plan' or press 3 If you don't want to do anything just now, press 4 \"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.DT1010_AcceptTermsYN_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.dataUsage_isUnlimited = true,\n              \"Are you ready to go ahead? You can also say 'repeat that' \",\n              true,\n              \"Are you ready to go ahead? You can also say 'repeat that', or 'change my plan' \"\n          )\n        }\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - |\n          {Switch(\n                true,\n                Global.dataUsage_isUnlimited = true,\n                \"Are you ready to go ahead? You can also say 'repeat that' \",\n                true,\n                \"Are you ready to go ahead? You can also say 'repeat that', or 'change my plan' \"\n            )\n          }\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - |\n          {Switch(\n                true,\n                Global.dataUsage_isUnlimited = true,\n                \"Are you ready to add a top-up? To continue, say 'yes' or press 1 To hear the information about top-ups again, say 'repeat' or press 2 If you don't want to do anything just now, press 3 \",\n                true,\n                \"Are you ready to add a top-up? To continue, say 'yes' or press 1 To hear the information about top-ups again, say 'repeat' or press 2 To check out or other data plans, say 'change my plan' or press 3 If you don't want to do anything just now, press 4 \"\n            )\n          }\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - |\n          {Switch(\n                true,\n                Global.dataUsage_isUnlimited = true,\n                \"Are you ready to add a top-up? To continue, say 'yes' or press 1 To hear the information about top-ups again, say 'repeat' or press 2 If you don't want to do anything just now, press 3 \",\n                true,\n                \"Are you ready to add a top-up? To continue, say 'yes' or press 1 To hear the information about top-ups again, say 'repeat' or press 2 To check out or other data plans, say 'change my plan' or press 3 If you don't want to do anything just now, press 4 \"\n            )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.Var_DT1010_AcceptTermsYN_DM\n  value: =Text(Global.DT1010_AcceptTermsYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_DT1010_AcceptTermsYN_DM = \"yes\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.featureCode\n          value: GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.topUp_feature1_code:''\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: DT1015_AddTopUpData_DB_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_DT1010_AcceptTermsYN_DM = \"repeat\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: DT1005_DataTopUpTerms_PP\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_DT1010_AcceptTermsYN_DM = \"changeplan\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.callType\n                  value: change_plan\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.cti_Intent\n                  value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: return\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_DT1010_AcceptTermsYN_DM = \"no\", true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: DT1026_CheckFromNLU_JDA_DA\n```", "timestamp": 1749558177.3858492, "content_hash": "be11080730548ca5faa7ffeeddd142bf"}