{"response": "```yaml\n- kind: Question\n  id: aa2555_Zip9DTMF_DM\n  displayName: aa2555_Zip9DTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa2555_ini_01.wav\\\">Please enter your 9-digit zip code</audio>\"\n      - |\n        {Switch(\n              true,\n              Global.dnisInfo.dtmfOnlyFlag = true,\n              \"<audio src=\\\"AUDIO_LOCATION/aa2555_nm2_02.wav\\\">Sorry, I still didn t get that</audio>\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.aa2555_Zip9DTMF_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa2555_ini_01.wav\\\">Please enter your 9-digit zip code</audio>\"\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/aa2555_ini_01.wav\\\">Please enter your 9-digit zip code</audio>\"\n        - |\n          {Switch(\n                true,\n                Global.dnisInfo.dtmfOnlyFlag = true,\n                \"<audio src=\\\"AUDIO_LOCATION/aa2555_ni2_02.wav\\\">Sorry, I still didn t get that</audio>\"\n            )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.result\n  value: aa2555_Zip9DTMF_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.zip9\n  value: aa2555_Zip9DTMF_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.validationCriteriaVariables.validationValue\n  value: validationCriteriaVariables.validationValue + ';' + result\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.validationCriteriaVariables.validationType\n  value: validationCriteriaVariables.validationType + ';' + validationCriteriaVariables.currentValidationType\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.globalVariables.currentTask = \"cardReplacement\" || Global.globalVariables.currentTask = \"altAuthVal\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa2510_ValidationDecision_JDA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: |\n            =If(Global.result = Global.validationCriteriaVariables.currentValidationData, true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa2510_ValidationDecision_JDA\n\n      elseActions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.validationCriteriaVariables.validationDataMismatch\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.validationCriteriaVariables.validationDataMismatchZip9\n          value: true\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa2510_ValidationDecision_JDA\n```", "timestamp": 1749544188.3647988, "content_hash": "c4a74fbfcefc496dcd48eea8b52a7719"}