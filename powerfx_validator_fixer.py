#!/usr/bin/env python3
"""
PowerFX Validator and Fixer
===========================

A comprehensive script that validates and fixes PowerFX expressions in YAML files
based on Microsoft Learn documentation standards.

Reference: https://learn.microsoft.com/en-us/power-platform/power-fx/overview

Features:
- Validates PowerFX syntax against Microsoft documentation
- Fixes common PowerFX issues automatically
- Provides detailed logging of all changes
- Creates backup files before modifications
- Supports batch processing of YAML files

Usage:
    python powerfx_validator_fixer.py input.yml
    python powerfx_validator_fixer.py input_folder/ --recursive
    python powerfx_validator_fixer.py input.yml --dry-run --verbose
"""

import os
import re
import sys
import time
import logging
import argparse
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from ruamel.yaml import YAML
from ruamel.yaml.scalarstring import ScalarString
from ruamel.yaml.comments import TaggedScalar
from collections import OrderedDict

# Configure logging
def setup_logging(verbose: bool = False) -> logging.Logger:
    """Setup detailed logging for PowerFX validation and fixing"""
    log_level = logging.DEBUG if verbose else logging.INFO
    log_filename = f'powerfx_validator_fixer_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
    
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename),
            logging.StreamHandler()
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"📋 PowerFX Validator and Fixer Started")
    logger.info(f"📅 Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"📄 Log file: {log_filename}")
    logger.info(f"📚 Reference: https://learn.microsoft.com/en-us/power-platform/power-fx/overview")
    logger.info(f"=" * 80)
    
    return logger

class PowerFXValidatorFixer:
    """
    PowerFX Validator and Fixer based on Microsoft Learn documentation
    """
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        
        # Microsoft PowerFX functions from official documentation
        self.powerfx_functions = {
            # Core functions
            "Switch", "If", "IfError", "With", "Concatenate", "Concat",
            # Text and conversion functions
            "Text", "Value", "Float", "Decimal", "Boolean",
            # Logical functions
            "And", "Or", "Not", "IsBlank", "IsEmpty", "IsError",
            # Date/Time functions
            "Date", "Time", "DateTime", "DateAdd", "DateDiff", "TimeZoneInformation",
            # Utility functions
            "Coalesce", "Notify", "Set", "UpdateContext", "Navigate",
            # Math functions
            "Sum", "Average", "Min", "Max", "Round", "RoundUp", "RoundDown",
            # Table functions
            "Filter", "Sort", "Search", "Lookup", "First", "Last"
        }
        
        # PowerFX validation rules based on Microsoft documentation
        self.validation_rules = {
            'function_names': self.powerfx_functions,
            'string_quotes': 'double',  # PowerFX uses double quotes
            'concatenate_syntax': 'Concatenate(String1 [, String2, ...])',
            'array_syntax': 'not_supported',  # Arrays should use Table() or Concatenate()
            'boolean_values': ['true', 'false'],  # lowercase per Microsoft docs
            'operators': ['&', '+', '-', '*', '/', '=', '<>', '<', '>', '<=', '>=']
        }
        
        # Statistics tracking
        self.stats = {
            'files_processed': 0,
            'files_modified': 0,
            'issues_found': 0,
            'issues_fixed': 0,
            'validation_errors': 0,
            'backup_files_created': 0
        }
        
        # Issue tracking for detailed reporting
        self.issues_log = []

    def ensure_directory_exists(self, file_path):
        """Ensure directory exists for file path"""
        directory = os.path.dirname(file_path)
        if not os.path.exists(directory):
            os.makedirs(directory)
            self.logger.info(f"Created directory: {directory}")

    def read_yaml(self, file_path):
        """Read YAML file using your standard configuration"""
        self.logger.info(f"Starting to read YAML file: {file_path}")
        try:
            yaml = YAML()
            yaml.width = 4096
            yaml.indent(mapping=2, sequence=4, offset=2)
            yaml.preserve_quotes = True
            with open(file_path, 'r', encoding='utf-8') as file:
                data = yaml.load(file)
                self.logger.info("Successfully read YAML file")
                return data, yaml
        except FileNotFoundError:
            self.logger.error(f"File {file_path} not found")
            return None, None
        except Exception as e:
            self.logger.error(f"Error parsing YAML file: {e}")
            return None, None

    def write_yaml(self, data, yaml_instance, file_path):
        """Write YAML file using your standard configuration"""
        self.logger.info(f"Starting to write updated YAML to: {file_path}")
        try:
            if data is None or yaml_instance is None:
                self.logger.error("No valid data or YAML instance to write")
                return False

            self.ensure_directory_exists(file_path)

            with open(file_path, 'w', encoding='utf-8') as file:
                yaml_instance.dump(data, file)
            self.logger.info(f"Successfully wrote updated YAML to {file_path}")
            return True
        except Exception as e:
            self.logger.error(f"Error writing YAML file: {e}")
            return False
    
    def validate_powerfx_expression(self, expression: str, context: str = "") -> Tuple[bool, List[Dict]]:
        """
        Validate PowerFX expression against Microsoft Learn documentation
        
        Args:
            expression: PowerFX expression to validate
            context: Context information for logging
            
        Returns:
            Tuple of (is_valid, list_of_issues)
        """
        issues = []
        
        self.logger.debug(f"🔍 Validating PowerFX expression: {expression[:100]}...")
        
        # Rule 1: Check for proper function names (case-sensitive)
        function_pattern = re.compile(r'\b([A-Za-z][A-Za-z0-9]*)\s*\(')
        functions_found = function_pattern.findall(expression)
        
        for func in functions_found:
            if func not in self.powerfx_functions:
                issues.append({
                    'type': 'INVALID_FUNCTION',
                    'severity': 'ERROR',
                    'message': f"Unknown function '{func}' - not in Microsoft PowerFX documentation",
                    'suggestion': f"Check function name against Microsoft documentation",
                    'context': context,
                    'reference': 'https://learn.microsoft.com/en-us/power-platform/power-fx/formula-reference-canvas-apps'
                })
        
        # Rule 2: Check for proper string quoting (double quotes)
        single_quote_strings = re.findall(r"'[^']*'", expression)
        if single_quote_strings:
            issues.append({
                'type': 'INCORRECT_QUOTES',
                'severity': 'WARNING',
                'message': f"Found single quotes in strings: {single_quote_strings}",
                'suggestion': "PowerFX uses double quotes for strings",
                'context': context,
                'reference': 'https://learn.microsoft.com/en-us/power-platform/power-fx/data-types#text-hyperlink-image-and-media'
            })
        
        # Rule 3: Check for deprecated Concat usage with individual strings
        # COMMENTED OUT - User requested to disable Concatenate validation
        # concat_individual = re.search(r'\bConcat\s*\(\s*"[^"]*"\s*(?:,\s*"[^"]*"\s*)*\)', expression)
        # if concat_individual:
        #     issues.append({
        #         'type': 'DEPRECATED_CONCAT',
        #         'severity': 'ERROR',
        #         'message': "Using Concat() for individual strings - should use Concatenate()",
        #         'suggestion': "Use Concatenate() for individual strings, Concat() for table operations",
        #         'context': context,
        #         'reference': 'https://learn.microsoft.com/en-us/power-platform/power-fx/reference/function-concatenate'
        #     })

        # Rule 4: Check for array syntax (should use Table() or Concatenate())
        # COMMENTED OUT - User requested to disable Concatenate validation
        # array_syntax = re.search(r'\[[^\]]*\]', expression)
        # if array_syntax and 'Table(' not in expression:
        #     issues.append({
        #         'type': 'INVALID_ARRAY_SYNTAX',
        #         'severity': 'ERROR',
        #         'message': f"Found array syntax: {array_syntax.group()}",
        #         'suggestion': "Use Table() for collections or Concatenate() for string concatenation",
        #         'context': context,
        #         'reference': 'https://learn.microsoft.com/en-us/power-platform/power-fx/data-types#table'
        #     })
        
        # Rule 5: Check for proper boolean values
        boolean_pattern = re.compile(r'\b(True|False|TRUE|FALSE)\b')
        incorrect_booleans = boolean_pattern.findall(expression)
        if incorrect_booleans:
            issues.append({
                'type': 'INCORRECT_BOOLEAN',
                'severity': 'WARNING',
                'message': f"Found incorrect boolean values: {incorrect_booleans}",
                'suggestion': "PowerFX uses lowercase 'true' and 'false'",
                'context': context,
                'reference': 'https://learn.microsoft.com/en-us/power-platform/power-fx/data-types#boolean'
            })
        
        # Rule 6: Check for proper Concatenate syntax
        concatenate_matches = re.findall(r'Concatenate\s*\([^)]+\)', expression)
        for match in concatenate_matches:
            args_content = re.search(r'Concatenate\s*\(([^)]+)\)', match)
            if args_content:
                args = [arg.strip() for arg in args_content.group(1).split(',')]
                if len(args) < 1:
                    issues.append({
                        'type': 'INVALID_CONCATENATE_ARGS',
                        'severity': 'ERROR',
                        'message': f"Concatenate function requires at least one argument: {match}",
                        'suggestion': "Provide at least one string argument to Concatenate()",
                        'context': context,
                        'reference': 'https://learn.microsoft.com/en-us/power-platform/power-fx/reference/function-concatenate'
                    })
        
        # Log validation results
        if issues:
            self.logger.warning(f"⚠️  Found {len(issues)} PowerFX validation issues in {context}")
            for issue in issues:
                self.logger.warning(f"   {issue['severity']}: {issue['message']}")
                self.logger.debug(f"   Suggestion: {issue['suggestion']}")
                self.logger.debug(f"   Reference: {issue['reference']}")
        else:
            self.logger.info(f"✅ PowerFX expression validation passed for {context}")
        
        return len(issues) == 0, issues

    def fix_powerfx_expression(self, expression: str, context: str = "") -> Tuple[str, List[Dict]]:
        """
        Fix PowerFX expression based on Microsoft Learn documentation

        Args:
            expression: PowerFX expression to fix
            context: Context information for logging

        Returns:
            Tuple of (fixed_expression, list_of_fixes_applied)
        """
        fixed_expression = expression
        fixes_applied = []

        self.logger.info(f"🔧 Fixing PowerFX expression in {context}")
        self.logger.debug(f"   Original: {expression}")

        # Fix 1: Convert single quotes to double quotes
        single_quote_pattern = re.compile(r"'([^']*)'")
        if single_quote_pattern.search(fixed_expression):
            original = fixed_expression
            fixed_expression = single_quote_pattern.sub(r'"\1"', fixed_expression)
            fixes_applied.append({
                'type': 'QUOTE_CONVERSION',
                'description': 'Converted single quotes to double quotes',
                'before': original,
                'after': fixed_expression,
                'reference': 'https://learn.microsoft.com/en-us/power-platform/power-fx/data-types#text-hyperlink-image-and-media'
            })
            self.logger.info(f"   ✅ Fixed: Converted single quotes to double quotes")

        # Fix 2: Convert incorrect boolean values
        boolean_fixes = {
            r'\bTrue\b': 'true',
            r'\bFalse\b': 'false',
            r'\bTRUE\b': 'true',
            r'\bFALSE\b': 'false'
        }

        for pattern, replacement in boolean_fixes.items():
            if re.search(pattern, fixed_expression):
                original = fixed_expression
                fixed_expression = re.sub(pattern, replacement, fixed_expression)
                fixes_applied.append({
                    'type': 'BOOLEAN_CASE_FIX',
                    'description': f'Fixed boolean case: {pattern} -> {replacement}',
                    'before': original,
                    'after': fixed_expression,
                    'reference': 'https://learn.microsoft.com/en-us/power-platform/power-fx/data-types#boolean'
                })
                self.logger.info(f"   ✅ Fixed: Boolean case correction")

        # Fix 3: Convert Concat() to Concatenate() for individual strings
        # COMMENTED OUT - User requested to disable Concatenate fixing
        # concat_pattern = re.compile(r'\bConcat\s*\(\s*("(?:[^"\\]|\\.)*"(?:\s*,\s*"(?:[^"\\]|\\.)*")*)\s*\)')
        # concat_matches = list(concat_pattern.finditer(fixed_expression))
        #
        # # Process matches in reverse order to maintain positions
        # for match in reversed(concat_matches):
        #     original = fixed_expression
        #     args = match.group(1)
        #     replacement = f'Concatenate({args})'
        #     fixed_expression = fixed_expression[:match.start()] + replacement + fixed_expression[match.end():]
        #
        #     fixes_applied.append({
        #         'type': 'CONCAT_TO_CONCATENATE',
        #         'description': 'Converted Concat() to Concatenate() for individual strings',
        #         'before': match.group(0),
        #         'after': replacement,
        #         'reference': 'https://learn.microsoft.com/en-us/power-platform/power-fx/reference/function-concatenate'
        #     })
        #     self.logger.info(f"   ✅ Fixed: Converted Concat() to Concatenate()")

        # Fix 4: Convert array syntax to Concatenate()
        # COMMENTED OUT - User requested to disable Concatenate fixing
        # array_pattern = re.compile(r'\[\s*("(?:[^"\\]|\\.)*"(?:\s*,\s*"(?:[^"\\]|\\.)*")*)\s*\]')
        # array_matches = list(array_pattern.finditer(fixed_expression))
        #
        # # Process matches in reverse order to maintain positions
        # for match in reversed(array_matches):
        #     original = fixed_expression
        #     args = match.group(1)
        #     replacement = f'Concatenate({args})'
        #     fixed_expression = fixed_expression[:match.start()] + replacement + fixed_expression[match.end():]
        #
        #     fixes_applied.append({
        #         'type': 'ARRAY_TO_CONCATENATE',
        #         'description': 'Converted array syntax to Concatenate() function',
        #         'before': match.group(0),
        #         'after': replacement,
        #         'reference': 'https://learn.microsoft.com/en-us/power-platform/power-fx/reference/function-concatenate'
        #     })
        #     self.logger.info(f"   ✅ Fixed: Converted array syntax to Concatenate()")

        # Fix 5: Remove escaped quotes in strings (user preference)
        escaped_quote_pattern = re.compile(r'(")([^"]*)\\"([^"]*)(")')
        if escaped_quote_pattern.search(fixed_expression):
            original = fixed_expression
            fixed_expression = re.sub(r'\\"', '', fixed_expression)
            fixes_applied.append({
                'type': 'ESCAPED_QUOTE_REMOVAL',
                'description': 'Removed escaped quotes from strings',
                'before': original,
                'after': fixed_expression,
                'reference': 'User preference for quote handling'
            })
            self.logger.info(f"   ✅ Fixed: Removed escaped quotes")

        # Log final result
        if fixes_applied:
            self.logger.info(f"   🎉 Applied {len(fixes_applied)} fixes to PowerFX expression")
            self.logger.debug(f"   Final result: {fixed_expression}")
        else:
            self.logger.info(f"   ℹ️  No fixes needed for PowerFX expression")

        return fixed_expression, fixes_applied

    def process_yaml_content(self, content: str, file_path: str) -> Tuple[str, bool, List[Dict]]:
        """
        Process YAML content to validate and fix PowerFX expressions

        Args:
            content: YAML content as string
            file_path: Path to the file being processed

        Returns:
            Tuple of (modified_content, was_modified, all_issues_and_fixes)
        """
        self.logger.info(f"📄 Processing YAML content from {file_path}")

        modified_content = content
        was_modified = False
        all_issues_and_fixes = []

        # Find PowerFX expressions in the content
        powerfx_pattern = re.compile(
            r'(' + '|'.join(self.powerfx_functions) + r')\s*\([^)]*\)',
            re.IGNORECASE | re.DOTALL
        )

        expressions_found = list(powerfx_pattern.finditer(content))
        self.logger.info(f"   🔍 Found {len(expressions_found)} potential PowerFX expressions")

        # Process each expression
        for i, match in enumerate(reversed(expressions_found), 1):
            expression = match.group(0)
            start_pos = match.start()
            end_pos = match.end()
            context = f"{file_path}:position_{start_pos}-{end_pos}"

            self.logger.info(f"\n   📋 [{i}/{len(expressions_found)}] Processing expression at position {start_pos}-{end_pos}")

            # Validate the expression
            is_valid, issues = self.validate_powerfx_expression(expression, context)

            if issues:
                self.stats['issues_found'] += len(issues)
                all_issues_and_fixes.extend(issues)

                # Try to fix the expression
                fixed_expression, fixes = self.fix_powerfx_expression(expression, context)

                if fixes:
                    # Apply the fix to the content
                    modified_content = (modified_content[:start_pos] +
                                      fixed_expression +
                                      modified_content[end_pos:])

                    was_modified = True
                    self.stats['issues_fixed'] += len(fixes)
                    all_issues_and_fixes.extend(fixes)

                    self.logger.info(f"   ✅ Successfully fixed expression")
                else:
                    self.logger.warning(f"   ⚠️  Could not automatically fix expression")
            else:
                self.logger.info(f"   ✅ Expression is valid - no fixes needed")

        # Process SetVariable entries for quote removal
        setvariable_pattern = re.compile(
            r'(\s+value:\s*)"([^"]*)"(\s*$)',
            re.MULTILINE
        )

        def process_setvariable_value(match):
            prefix = match.group(1)
            value = match.group(2)
            suffix = match.group(3)

            # Check if this is within a SetVariable context
            text_before = modified_content[:match.start()]
            if 'kind: SetVariable' in text_before.split('\n')[-10:]:
                self.logger.info(f"   🏷️  Processing SetVariable value: {value}")
                processed_value = value.replace('\\"', '')  # Remove escaped quotes

                if processed_value != value:
                    self.stats['issues_fixed'] += 1
                    all_issues_and_fixes.append({
                        'type': 'SETVARIABLE_QUOTE_REMOVAL',
                        'description': 'Removed quotes from SetVariable value',
                        'before': f'"{value}"',
                        'after': processed_value,
                        'reference': 'User preference for SetVariable processing'
                    })
                    self.logger.info(f"   ✅ Removed quotes from SetVariable value")
                    return prefix + processed_value + suffix

            return match.group(0)

        # Apply SetVariable processing
        setvariable_modified = setvariable_pattern.sub(process_setvariable_value, modified_content)
        if setvariable_modified != modified_content:
            modified_content = setvariable_modified
            was_modified = True

        return modified_content, was_modified, all_issues_and_fixes

    def process_yaml_data(self, data, file_path: str = "") -> Tuple[Any, bool, List[Dict]]:
        """
        Process YAML data structure directly to validate and fix PowerFX expressions

        Args:
            data: YAML data structure (dict/list)
            file_path: Path to the file being processed (for logging)

        Returns:
            Tuple of (modified_data, was_modified, all_issues_and_fixes)
        """
        self.logger.info(f"📄 Processing YAML data structure from {file_path}")

        was_modified = False
        all_issues_and_fixes = []

        def process_node(node, path=""):
            """Recursively process YAML nodes to find and fix PowerFX expressions"""
            nonlocal was_modified, all_issues_and_fixes

            if isinstance(node, dict):
                for key, value in node.items():
                    current_path = f"{path}.{key}" if path else key

                    if isinstance(value, str):
                        # Check if this string contains PowerFX expressions
                        powerfx_pattern = re.compile(
                            r'(' + '|'.join(self.powerfx_functions) + r')\s*\([^)]*\)',
                            re.IGNORECASE | re.DOTALL
                        )

                        if powerfx_pattern.search(value):
                            self.logger.info(f"   🔍 Found PowerFX expression in {current_path}")

                            # Validate the expression
                            is_valid, issues = self.validate_powerfx_expression(value, current_path)

                            if issues:
                                self.stats['issues_found'] += len(issues)
                                all_issues_and_fixes.extend(issues)

                                # Try to fix the expression
                                fixed_expression, fixes = self.fix_powerfx_expression(value, current_path)

                                if fixes:
                                    node[key] = fixed_expression
                                    was_modified = True
                                    self.stats['issues_fixed'] += len(fixes)
                                    all_issues_and_fixes.extend(fixes)

                                    self.logger.info(f"   ✅ Fixed PowerFX expression in {current_path}")
                                else:
                                    self.logger.warning(f"   ⚠️  Could not fix PowerFX expression in {current_path}")
                            else:
                                self.logger.info(f"   ✅ PowerFX expression is valid in {current_path}")

                    elif isinstance(value, (dict, list)):
                        process_node(value, current_path)

                    # Special handling for SetVariable values
                    if key == 'value' and isinstance(value, str) and 'SetVariable' in str(node.get('kind', '')):
                        self.logger.info(f"   🏷️  Processing SetVariable value in {current_path}")
                        processed_value = value.replace('\\"', '').strip('"')  # Remove escaped quotes and outer quotes

                        if processed_value != value:
                            node[key] = processed_value
                            was_modified = True
                            self.stats['issues_fixed'] += 1
                            all_issues_and_fixes.append({
                                'type': 'SETVARIABLE_QUOTE_REMOVAL',
                                'description': 'Removed quotes from SetVariable value',
                                'before': value,
                                'after': processed_value,
                                'reference': 'User preference for SetVariable processing',
                                'path': current_path
                            })
                            self.logger.info(f"   ✅ Removed quotes from SetVariable value in {current_path}")

            elif isinstance(node, list):
                for i, item in enumerate(node):
                    current_path = f"{path}[{i}]" if path else f"[{i}]"
                    process_node(item, current_path)

        # Process the entire YAML data structure
        process_node(data)

        return data, was_modified, all_issues_and_fixes

    def process_yaml_file(self, file_path: Path, dry_run: bool = False) -> bool:
        """
        Process a single YAML file to validate and fix PowerFX expressions

        Args:
            file_path: Path to YAML file
            dry_run: If True, don't modify files

        Returns:
            True if file was modified (or would be modified in dry run)
        """
        try:
            self.logger.info(f"\n🔄 Processing file: {file_path}")

            # Read YAML using your standard method
            data, yaml_instance = self.read_yaml(str(file_path))
            if data is None:
                self.logger.error(f"Failed to read YAML file: {file_path}")
                self.stats['validation_errors'] += 1
                return False

            # Get original file size for reporting
            file_size = os.path.getsize(file_path)
            self.logger.info(f"   📄 File size: {file_size} bytes")

            # Process YAML data structure directly for PowerFX validation and fixing
            modified_data, was_modified, issues_and_fixes = self.process_yaml_data(
                data, str(file_path)
            )

            if was_modified:
                if not dry_run:
                    # Create backup using your standard method
                    backup_path = file_path.with_suffix(file_path.suffix + '.backup')

                    # Write original data as backup
                    if self.write_yaml(data, yaml_instance, str(backup_path)):
                        self.logger.info(f"   📁 Backup created: {backup_path}")
                        self.stats['backup_files_created'] += 1

                    # Write modified data using your standard method
                    if self.write_yaml(modified_data, yaml_instance, str(file_path)):
                        new_size = os.path.getsize(file_path)
                        size_change = new_size - file_size

                        self.logger.info(f"   💾 File modified and saved")
                        self.logger.info(f"   📊 Size change: {size_change:+d} bytes ({file_size} → {new_size})")
                    else:
                        self.logger.error(f"Failed to write modified YAML file")
                        return False
                else:
                    self.logger.info(f"   🔍 DRY RUN: File would be modified")
                    self.logger.info(f"   📋 Changes that would be applied:")
                    for item in issues_and_fixes:
                        if 'before' in item and 'after' in item:
                            self.logger.info(f"      - {item['type']}: {item['description']}")

                self.stats['files_modified'] += 1
                self.logger.info(f"✅ Successfully processed: {file_path}")
                return True
            else:
                self.logger.info(f"   ℹ️  No changes needed")
                self.logger.info(f"✅ Completed processing: {file_path}")
                return False

        except Exception as e:
            self.logger.error(f"❌ Error processing {file_path}: {str(e)}")
            self.stats['validation_errors'] += 1
            return False

    def process_directory(self, directory_path: Path, recursive: bool = False, pattern: str = "*.yml") -> Dict[str, Any]:
        """
        Process all YAML files in directory

        Args:
            directory_path: Path to directory
            recursive: Process subdirectories recursively
            pattern: File pattern to match

        Returns:
            Processing results dictionary
        """
        results = {
            'files_found': 0,
            'files_processed': 0,
            'files_modified': 0,
            'total_issues': 0,
            'total_fixes': 0,
            'errors': 0
        }

        try:
            # Find YAML files
            if recursive:
                yaml_files = list(directory_path.rglob(pattern))
                yaml_files.extend(list(directory_path.rglob("*.yaml")))
            else:
                yaml_files = list(directory_path.glob(pattern))
                yaml_files.extend(list(directory_path.glob("*.yaml")))

            results['files_found'] = len(yaml_files)
            self.logger.info(f"📁 Directory scan complete: {directory_path}")
            self.logger.info(f"📄 Found {len(yaml_files)} YAML files to process")

            if yaml_files:
                self.logger.info(f"📋 Files to process:")
                for i, file_path in enumerate(yaml_files, 1):
                    self.logger.info(f"  {i}. {file_path.name}")
            else:
                self.logger.warning(f"⚠️  No YAML files found in {directory_path}")
                return results

            self.logger.info(f"\n🚀 Starting batch processing of {len(yaml_files)} files...")

            # Process each file
            for i, file_path in enumerate(yaml_files, 1):
                self.logger.info(f"\n📋 [{i}/{len(yaml_files)}] Processing: {file_path.name}")

                try:
                    self.stats['files_processed'] += 1
                    was_modified = self.process_yaml_file(file_path)

                    if was_modified:
                        results['files_modified'] += 1

                    results['files_processed'] += 1

                except Exception as e:
                    self.logger.error(f"Error processing {file_path}: {e}")
                    results['errors'] += 1

            # Update results with current stats
            results['total_issues'] = self.stats['issues_found']
            results['total_fixes'] = self.stats['issues_fixed']
            results['errors'] = self.stats['validation_errors']

            return results

        except Exception as e:
            self.logger.error(f"Error processing directory {directory_path}: {e}")
            results['errors'] += 1
            return results

    def print_final_report(self):
        """Print comprehensive final report"""
        self.logger.info(f"\n" + "=" * 80)
        self.logger.info(f"POWERFX VALIDATOR AND FIXER - FINAL REPORT")
        self.logger.info(f"=" * 80)
        self.logger.info(f"📊 PROCESSING STATISTICS:")
        self.logger.info(f"   📄 Files processed:        {self.stats['files_processed']}")
        self.logger.info(f"   📝 Files modified:         {self.stats['files_modified']}")
        self.logger.info(f"   🔍 Issues found:           {self.stats['issues_found']}")
        self.logger.info(f"   🔧 Issues fixed:           {self.stats['issues_fixed']}")
        self.logger.info(f"   📁 Backup files created:   {self.stats['backup_files_created']}")
        self.logger.info(f"   ❌ Validation errors:      {self.stats['validation_errors']}")

        if self.stats['issues_fixed'] > 0:
            success_rate = (self.stats['issues_fixed'] / self.stats['issues_found']) * 100
            self.logger.info(f"   📈 Fix success rate:       {success_rate:.1f}%")

        self.logger.info(f"\n📚 MICROSOFT POWERFX COMPLIANCE:")
        self.logger.info(f"   ✅ Validated against Microsoft Learn documentation")
        self.logger.info(f"   📖 Reference: https://learn.microsoft.com/en-us/power-platform/power-fx/overview")
        self.logger.info(f"   🔧 Applied Microsoft PowerFX standards and best practices")

        if self.stats['files_modified'] > 0:
            self.logger.info(f"\n💾 BACKUP INFORMATION:")
            self.logger.info(f"   📁 Original files backed up with .backup extension")
            self.logger.info(f"   🔄 {self.stats['files_modified']} files successfully modified")

        self.logger.info(f"=" * 80)


def main():
    """Main function for command-line usage"""
    parser = argparse.ArgumentParser(
        description="PowerFX Validator and Fixer based on Microsoft Learn documentation",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python powerfx_validator_fixer.py input.yml
  python powerfx_validator_fixer.py input_folder/ --recursive
  python powerfx_validator_fixer.py input.yml --dry-run --verbose
  python powerfx_validator_fixer.py input_folder/ --pattern "*.yaml"

This script validates and fixes PowerFX expressions based on:
- Microsoft Learn PowerFX documentation
- Official PowerFX function reference
- PowerFX syntax and best practices
- Automatic correction of common issues

Reference: https://learn.microsoft.com/en-us/power-platform/power-fx/overview
        """
    )

    parser.add_argument(
        'input_path',
        help="Path to YAML file or directory containing YAML files"
    )

    parser.add_argument(
        '--dry-run',
        action='store_true',
        help="Show what would be fixed without making changes"
    )

    parser.add_argument(
        '--verbose',
        action='store_true',
        help="Enable verbose logging with detailed information"
    )

    parser.add_argument(
        '--recursive',
        action='store_true',
        help="Process directories recursively (includes subdirectories)"
    )

    parser.add_argument(
        '--pattern',
        default="*.yml",
        help="File pattern to match (default: *.yml)"
    )

    args = parser.parse_args()

    # Setup logging
    logger = setup_logging(args.verbose)

    print("PowerFX Validator and Fixer")
    print("=" * 50)
    print(f"Input path: {args.input_path}")
    print(f"Reference: Microsoft Learn PowerFX Documentation")

    if args.dry_run:
        print("DRY RUN MODE - No files will be modified")

    if args.recursive:
        print("RECURSIVE MODE - Processing subdirectories")

    print("=" * 50)

    # Create validator/fixer
    validator_fixer = PowerFXValidatorFixer(logger)

    try:
        input_path = Path(args.input_path)

        if not input_path.exists():
            print(f"❌ ERROR: Input path does not exist: {input_path}")
            logger.error(f"Input path does not exist: {input_path}")
            return 1

        if input_path.is_file():
            # Process single file
            logger.info(f"🔍 Processing single file: {input_path}")

            if not input_path.suffix.lower() in ['.yml', '.yaml']:
                print(f"❌ ERROR: File must be a YAML file (.yml or .yaml): {input_path}")
                logger.error(f"Invalid file type: {input_path}")
                return 1

            was_modified = validator_fixer.process_yaml_file(input_path, args.dry_run)

            if was_modified:
                if args.dry_run:
                    print(f"✅ File would be modified: {input_path}")
                else:
                    print(f"✅ File successfully processed: {input_path}")
            else:
                print(f"ℹ️  No changes needed: {input_path}")

        elif input_path.is_dir():
            # Process directory
            logger.info(f"🔍 Processing directory: {input_path}")

            results = validator_fixer.process_directory(
                input_path,
                recursive=args.recursive,
                pattern=args.pattern
            )

            # Display results
            print(f"\n📊 PROCESSING RESULTS:")
            print(f"   Files found:       {results['files_found']}")
            print(f"   Files processed:   {results['files_processed']}")

            if not args.dry_run:
                print(f"   Files modified:    {results['files_modified']}")
            else:
                print(f"   Files that would be modified: {results['files_modified']}")

            print(f"   Issues found:      {results['total_issues']}")
            print(f"   Issues fixed:      {results['total_fixes']}")
            print(f"   Errors:           {results['errors']}")

            if results['total_fixes'] > 0:
                if args.dry_run:
                    print(f"\n✅ SUCCESS: Found {results['total_fixes']} PowerFX issues that would be fixed")
                    print("   Run without --dry-run to apply changes")
                else:
                    print(f"\n✅ SUCCESS: Fixed {results['total_fixes']} PowerFX issues")
                    if results['files_modified'] > 0:
                        print(f"   Modified {results['files_modified']} files")
                        print("   Backup files created with .backup extension")
            else:
                print(f"\n📝 INFO: No PowerFX issues found to fix")

            if results['errors'] > 0:
                print(f"\n⚠️  WARNING: {results['errors']} errors encountered")

        else:
            print(f"❌ ERROR: Input path is neither file nor directory: {input_path}")
            logger.error(f"Invalid input path type: {input_path}")
            return 1

        # Print final comprehensive report
        validator_fixer.print_final_report()

        # Summary message
        if validator_fixer.stats['issues_fixed'] > 0:
            print(f"\n🎉 COMPLETED: PowerFX validation and fixing completed successfully")
            print(f"   📚 All fixes applied according to Microsoft PowerFX documentation")
            print(f"   📖 Reference: https://learn.microsoft.com/en-us/power-platform/power-fx/overview")
        else:
            print(f"\n✅ COMPLETED: All PowerFX expressions are already compliant")

        if validator_fixer.stats['validation_errors'] > 0:
            return 1

        return 0

    except KeyboardInterrupt:
        print("\n⚠️  Interrupted by user")
        logger.warning("Process interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        print(f"\n❌ ERROR: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
