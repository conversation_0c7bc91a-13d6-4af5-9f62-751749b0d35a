{"response": "```yaml\n- kind: Question\n  id: AQ1305_HaveMetroPhoneYN_DM\n  displayName: AQ1305_HaveMetroPhoneYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AQ1305_nm1_01.wav\\\">Do you still have your old phone handy and turned on</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AQ1305_nm1_02.wav\\\">Do you have your MetroPCS phone handy and turned on</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AQ1305_nm2_01.wav\\\">I'll send the text message to the old phone on your account If you have it handy, say 'yes' or press 1 Otherwise, say 'no' or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AQ1305_nm2_02.wav\\\">I'll send the text message to the phone on your MetroPCS account If you have it handy, say 'yes' or press 1 Otherwise, say 'no' or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AQ1305_nm3_01.wav\\\">If you have your old  phone with you, press 1 If not, press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AQ1305_nm3_02.wav\\\">If you have your MetroPCS phone with you, press 1 If not, press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.AQ1305_HaveMetroPhoneYN_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.askSQEntry = \"228login\",\n            [\n              \"I can text you the answer so we can continue switching your phone\",\n              \"test\"\n            ],\n            Global.askSQEntry = \"EWallet\" || Global.askSQEntry = \"careLogin\",\n            \"I can text you the answer to your security question\",\n            Global.callType = \"esn_swap\" || Global.callType = \"switch_phone\",\n            \"Do you have your old phone handy and turned on? That's where I'll be sending the message\",\n            !(Global.callType = \"esn_swap\" || Global.callType = \"switch_phone\"),\n            \"Do you have your MetroPCS phone handy and turned on? That's where I'll be sending the message\"\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/AQ1305_nm1_01.wav\\\">Do you still have your old phone handy and turned on</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AQ1305_nm1_02.wav\\\">Do you have your MetroPCS phone handy and turned on</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AQ1305_nm2_01.wav\\\">I'll send the text message to the old phone on your account If you have it handy, say 'yes' or press 1 Otherwise, say 'no' or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AQ1305_nm2_02.wav\\\">I'll send the text message to the phone on your MetroPCS account If you have it handy, say 'yes' or press 1 Otherwise, say 'no' or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AQ1305_nm3_01.wav\\\">If you have your old  phone with you, press 1 If not, press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AQ1305_nm3_02.wav\\\">If you have your MetroPCS phone with you, press 1 If not, press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.askSQEntry\n  value: GlobalVars.askSQEntry\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.callType\n  value: GlobalVars.callType\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.saidOperator\n  value: GlobalVars.saidOperatorAQ1305?GlobalVars.saidOperatorAQ1305:false\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_AQ1305_HaveMetroPhoneYN_DM\n  value: =Text(Global.AQ1305_HaveMetroPhoneYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AQ1305_HaveMetroPhoneYN_DM = \"true\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: AQ1205_GetAcctZip_DM\n\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.GlobalVars.securityCheckNoPhone\n      value: true\n\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.careAlterHandContainment = true, true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/AQ1305_out_01.wav\\\">Unfortunately our agents couldn't help you without your security information, or your MetroPCS phone You can manage your account or get help through the myMetro app, online, or at one of our stores or authorized dealers For a map of our locations near you, please visit metropcscom</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n\n      elseActions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: AQ1310_NoPhoneCallBack_DM\n```", "timestamp": **********.8291416, "content_hash": "4e33c3f226678d48bcd6437785eb1f67"}