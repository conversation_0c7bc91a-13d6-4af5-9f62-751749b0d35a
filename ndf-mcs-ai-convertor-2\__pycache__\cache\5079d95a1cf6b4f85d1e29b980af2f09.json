{"response": "```yaml\n- kind: Question\n  id: AU1305_AskUsePaymentCard_DM\n  displayName: AU1305_AskUsePaymentCard_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AU1305_nm1_01.wav\\\">Would you like to use the card you just made a payment with as your primary autopay card? </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AU1305_nm2_01.wav\\\">If you want to use the card you just made a payment with as your primary autopay card say 'yes' or press 1   To enter a different card say 'no' or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AU1305_nm2_01.wav\\\">If you want to use the card you just made a payment with as your primary autopay card say 'yes' or press 1   To enter a different card say 'no' or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.AU1305_AskUsePaymentCard_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/AU1305_ini_01.wav\\\">Would you like to save the card you just paid with as your primary autopay card? </audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/AU1305_nm1_01.wav\\\">Would you like to use the card you just made a payment with as your primary autopay card? </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AU1305_nm2_01.wav\\\">If you want to use the card you just made a payment with as your primary autopay card say 'yes' or press 1   To enter a different card say 'no' or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AU1305_nm2_01.wav\\\">If you want to use the card you just made a payment with as your primary autopay card say 'yes' or press 1   To enter a different card say 'no' or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.visitedAu1305\n  value: true\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_AU1305_AskUsePaymentCard_DM\n  value: =Text(Global.AU1305_AskUsePaymentCard_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AU1305_AskUsePaymentCard_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.useLastPaymentCard\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.autopay\n          value: true\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: AU1320_GoToManageCards_SD\n\n  elseActions:\n    - kind: GotoAction\n      id: goto_REPLACE_THIS\n      actionId: AU1320_GoToManageCards_SD\n```", "timestamp": 1749527782.4825594, "content_hash": "5079d95a1cf6b4f85d1e29b980af2f09"}