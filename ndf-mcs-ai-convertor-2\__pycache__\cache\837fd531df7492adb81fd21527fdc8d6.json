{"response": "```yaml\n- kind: Question\n  id: DH1320_AskPayNowYN_DM\n  displayName: DH1320_AskPayNowYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DH1320_nm1_01.wav\\\">Do you want to make a payment now, so we can continue with your swap? </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DH1320_nm2_01.wav\\\">Do you want to make a payment now, so we can continue with your swap? </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DH1320_nm2_01.wav\\\">Do you want to make a payment now, so we can continue with your swap? </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.DH1320_AskPayNowYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/DH1320_ini_01.wav\\\">Now, because your account is suspended, we'll need to take your payment before we can swap your phone Do you want to pay right now? </audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/DH1320_nm1_01.wav\\\">Do you want to make a payment now, so we can continue with your swap? </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DH1320_nm2_01.wav\\\">Do you want to make a payment now, so we can continue with your swap? </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DH1320_nm2_01.wav\\\">Do you want to make a payment now, so we can continue with your swap? </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_DH1320_AskPayNowYN_DM\n  value: =Text(Global.DH1320_AskPayNowYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_DH1320_AskPayNowYN_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.acceptPayByPhone\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.paymentsEntryPoint\n          value: careESN\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.paymentAmount\n          value: GlobalVars.GetAccountDetails.dueImmediatelyAmount\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: DH1325_MakePayment_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_DH1320_AskPayNowYN_DM = \"false\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.callType\n              value: undefined\n\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/DH1320_out_01.wav\\\">Okay, you can pay any time online or in the myMetro app</audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n```", "timestamp": 1749528406.213451, "content_hash": "837fd531df7492adb81fd21527fdc8d6"}