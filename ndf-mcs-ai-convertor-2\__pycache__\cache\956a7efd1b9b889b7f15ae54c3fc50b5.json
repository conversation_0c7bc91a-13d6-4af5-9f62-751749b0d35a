{"response": "```yaml\n- kind: Question\n  id: ma2110_AskAlreadyStarted_DM\n  displayName: ma2110_AskAlreadyStarted_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I missed that</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ma2110_ni1_01.wav\\\">Did you already try to activate your device earlier? Just say Yes or No</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">Sorry</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ma2110_ni2_01.wav\\\">If you already started the activation process online, on your device, or in an earlier phone call, say Yes or press 1 If not, say No or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ma2110_AskAlreadyStarted_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ma2110_ini_01.wav\\\">Have you already started activating your device? </audio>\"\n\n  entity: \n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: yes\n          displayName: yes\n        - id: no\n          displayName: no\n        - id: spanish\n          displayName: spanish\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ma2110_ni1_01.wav\\\">Did you already try to activate your device earlier? Just say Yes or No</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ma2110_ni2_01.wav\\\">If you already started the activation process online, on your device, or in an earlier phone call, say Yes or press 1 If not, say No or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ma2110_AskAlreadyStarted_DM\n  value: =Text(Global.ma2110_AskAlreadyStarted_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma2110_AskAlreadyStarted_DM = \"yes\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.continueActivation\n          value: true\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ma2215_AskIfHavePhoneNumber_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ma2110_AskAlreadyStarted_DM = \"no\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.continueActivation\n              value: false\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: ma2115_AskIfReplacingDevice_DM\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_ma2110_AskAlreadyStarted_DM = \"spanish\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.systemLanguage\n                  value: spanish\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.language\n                  value: es-US\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.nluEnabled\n                  value: false\n                - kind: BeginDialog\n                  id: begin_REPLACE_THIS\n                  dialog: topic.M02_MainMenu_Dialog\n\n# SetVariable for collection_commandgrammar based on callType\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.callType = \"611\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_commandgrammar\n          value: 'GlobalCommands.grxml?SWI_vars.allow=mainmenu&SWI_vars.operator_cnf_mode=NEVER'\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.collection_commandgrammar\n      value: 'GlobalCommands.grxml?SWI_vars.operator_cnf_mode=NEVER'\n\n# SetVariable for collection_commandgrammar in success based on callType\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.callType = \"611\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_commandgrammar\n          value: 'GlobalCommands.grxml?SWI_vars.allow=mainmenu'\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.collection_commandgrammar\n      value: 'GlobalCommands.grxml'\n\n# Command actions (mainmenu, operator, repeat)\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma2110_AskAlreadyStarted_DM = \"mainmenu\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferDestination\n          value: attPrepaid\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M01_MainMenu2_Dialog\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma2110_AskAlreadyStarted_DM = \"operator\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/ma2110_operator_01.wav\\\">Sorry, I need to get some information from you before I can transfer you to a representative For any questions, please refer to your activation materials</audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\"></audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.lastTransferState\n          value: ma2110_AskAlreadyStarted_DM\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferReason\n          value: AGENT_REQUEST\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.ma2110_AskAlreadyStarted_DM_Dialog\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma2110_AskAlreadyStarted_DM = \"repeat\", true, false)\n      actions:\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.ma2110_AskAlreadyStarted_DM_Dialog\n```", "timestamp": 1749471345.2149587, "content_hash": "956a7efd1b9b889b7f15ae54c3fc50b5"}