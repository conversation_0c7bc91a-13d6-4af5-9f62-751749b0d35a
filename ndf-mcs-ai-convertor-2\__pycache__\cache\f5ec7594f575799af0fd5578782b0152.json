{"response": "```yaml\n- kind: Question\n  id: DH1238_BYODDevice_DM\n  displayName: DH1238_BYODDevice_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DH1238_nm1_01.wav\\\">To hear those instructions again, say 'repeat that  You can also say 'find a store,' or 'I need something else'  If you're finished, you can just hang up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DH1238_nm2_01.wav\\\">To hear those instructions again, say 'repeat that,' or press star  You can also say 'find a store' or press 1; or 'I need something else,' or press 2  If you're finished, you can just hang up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DH1238_nm2_01.wav\\\">To hear those instructions again, say 'repeat that,' or press star  You can also say 'find a store' or press 1; or 'I need something else,' or press 2  If you're finished, you can just hang up</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.DH1238_BYODDevice_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.callType = \"switch_phone\",\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/DH1238_ini_02.wav\\\">To switch to a non-Metro device, you ll need to bring it to a Metro store or authorized dealer  The device must be unlocked and not active on another account  At the store, you ll need to buy a Metro SIM card  Also, you ll be asked to provide your phone number and account PIN</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/DH1238_ini_05.wav\\\">You can say 'repeat that,' 'help me find a MetroPCS store,' or 'I need something else'  If you're finished, you can just hang up</audio>\"\n            ],\n            Global.callType <> \"switch_phone\",\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/DH1238_ini_03.wav\\\">To add a non-Metro device to your account, you ll need to bring it to a Metro store or authorized dealer  The device must be unlocked and not active on another account  At the store, you ll need to buy a Metro SIM card  Also, you ll be asked to provide your phone number and account PIN</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/DH1238_ini_05.wav\\\">You can say 'repeat that,' 'help me find a MetroPCS store,' or 'I need something else'  If you're finished, you can just hang up</audio>\"\n            ],\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/DH1238_ini_03.wav\\\">To add a non-Metro device to your account, you ll need to bring it to a Metro store or authorized dealer  The device must be unlocked and not active on another account  At the store, you ll need to buy a Metro SIM card  Also, you ll be asked to provide your phone number and account PIN</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/DH1238_ini_05.wav\\\">You can say 'repeat that,' 'help me find a MetroPCS store,' or 'I need something else'  If you're finished, you can just hang up</audio>\"\n            ]\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: inquire-store_location\n          displayName: inquire-store_location\n        - id: something_else\n          displayName: something_else\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/DH1238_nm1_01.wav\\\">To hear those instructions again, say 'repeat that  You can also say 'find a store,' or 'I need something else'  If you're finished, you can just hang up</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DH1238_nm2_01.wav\\\">To hear those instructions again, say 'repeat that,' or press star  You can also say 'find a store' or press 1; or 'I need something else,' or press 2  If you're finished, you can just hang up</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DH1238_nm2_01.wav\\\">To hear those instructions again, say 'repeat that,' or press star  You can also say 'find a store' or press 1; or 'I need something else,' or press 2  If you're finished, you can just hang up</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.callType\n  value: GlobalVars.callType\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_DH1238_BYODDevice_DM\n  value: =Text(Global.DH1238_BYODDevice_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_DH1238_BYODDevice_DM = \"inquire-store_location\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: activate_byod\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: DH1285_GoToStoreLocator_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_DH1238_BYODDevice_DM = \"something_else\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.callType\n              value: undefined\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.cti_Intent\n              value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: DH1075_CheckNLUMenuConfig_JDA\n```", "timestamp": 1749528346.7776852, "content_hash": "f5ec7594f575799af0fd5578782b0152"}