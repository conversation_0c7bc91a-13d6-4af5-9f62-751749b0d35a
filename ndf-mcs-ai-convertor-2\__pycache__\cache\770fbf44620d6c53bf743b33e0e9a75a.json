{"response": "```yaml\n- kind: Question\n  id: DP1000_DisambiguatePlan_DM\n  displayName: DP1000_DisambiguatePlan_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DP1000_nm1_01.wav\\\">Please choose one of the following plans - You can say</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DP1000_nm1_04.wav\\\">Or if none of those is right, say none of them</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DP1000_nm1_02.wav\\\">Or if neither of those is right, say neither of them </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DP1000_nm2_01.wav\\\">Say</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DP1000_nm1_05.wav\\\">Or if none of these are right, say none of them or press 9</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DP1000_nm2_02.wav\\\">Or again, if neither of those is right, say  neither of them  or press 9</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DP1000_nm2_01.wav\\\">Say</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DP1000_nm1_05.wav\\\">Or if none of these are right, say none of them or press 9</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DP1000_nm2_02.wav\\\">Or again, if neither of those is right, say  neither of them  or press 9</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.DP1000_DisambiguatePlan_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.ratePlanSelectionType = \"price\",\n            [\n                \"<audio src=\\\"AUDIO_LOCATION/DP1000_ini_02.wav\\\">I have a few plans at that price I'll read them out When you hear the one you want, say its full name back to me You can say</audio>\"\n            ],\n            true,\n            [\n                \"<audio src=\\\"AUDIO_LOCATION/DP1000_ini_04.wav\\\">I have a few plans that match that I'll read them out When you hear the one you want, say its full name back to me You can say</audio>\"\n            ]\n        )}\n        <audio src=\\\"AUDIO_LOCATION/DP1000_ini_07.wav\\\">So, which of those plans do you want?</audio>\n        <audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\n        {Switch(\n            true,\n            Global.numberOfAvailablePlans > 2,\n            \"<audio src=\\\"AUDIO_LOCATION/DP1000_ini_03.wav\\\">You can also say, none of them</audio>\",\n            Global.numberOfAvailablePlans = 2,\n            \"<audio src=\\\"AUDIO_LOCATION/DP1000_ini_09.wav\\\">You can also say, neither of them</audio>\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: choose-plan_none\n          displayName: choose-plan_none\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/DP1000_nm1_01.wav\\\">Please choose one of the following plans - You can say</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DP1000_nm1_04.wav\\\">Or if none of those is right, say none of them</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DP1000_nm1_02.wav\\\">Or if neither of those is right, say neither of them </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DP1000_nm2_01.wav\\\">Say</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DP1000_nm1_05.wav\\\">Or if none of these are right, say none of them or press 9</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DP1000_nm2_01.wav\\\">Say</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DP1000_nm1_05.wav\\\">Or if none of these are right, say none of them or press 9</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DP1000_nm2_02.wav\\\">Or again, if neither of those is right, say  neither of them  or press 9</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.allowedResponses\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.planGrammarURL\n  value: GlobalVars.RatePlanUnambiguousGrammarURL\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.planGrammarDtmfURL\n  value: GlobalVars.RatePlanDTMFGrammarURL\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.availablePlansArray\n  value: GlobalVars.availablePlansArray\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.price\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.availablePlansList\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.numberOfAvailablePlans\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.ratePlanSelectionType\n  value: GlobalVars.ratePlanSelectionType\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.ratePlans\n  value: GlobalVars.ratePlans\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.RatePlanUnambiguousGrammarURL\n  value: GlobalVars.RatePlanUnambiguousGrammarURL\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.ratePlanSelectionType = \"price\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.price\n          value: GlobalVars.nbestresults[0].interpretation.dm_root\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.price\n      value: getPriceFromNBest(GlobalVars.nbestresults)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.GlobalVars.serviceDialed = \"228\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.availablePlansList\n          value: getAllRatePlans(availablePlansArray)\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: |\n            =If(Global.GlobalVars.activationEntryPoint = \"care\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.availablePlansList\n              value: getAllRatePlans(availablePlansArray)\n      elseActions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.availablePlansList\n          value: getAllRatePlansExcludingCurrentPlan(availablePlansArray, GlobalVars.GetAccountDetails.ratePlan)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.numberOfAvailablePlans\n  value: numberOfRatePlans(availablePlansArray)\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_DP1000_DisambiguatePlan_DM\n  value: =Text(Global.DP1000_DisambiguatePlan_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |-\n        =If(Global.Var_DP1000_DisambiguatePlan_DM = \"choose-plan_none\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/DP1000_out_01.wav\\\">That s all right</audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.disambigNoMatches\n          value: true\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: return\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.GlobalVars.disambigNoMatches\n      value: false\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.GlobalVars.newPlanSOC\n      value: DP1000_DisambiguatePlan_DM.returnvalue\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.GlobalVars.newRatePlan\n      value: DP1000_DisambiguatePlan_DM.returnvalue\n    - kind: GotoAction\n      id: goto_REPLACE_THIS\n      actionId: return\n```", "timestamp": 1749528296.7831228, "content_hash": "770fbf44620d6c53bf743b33e0e9a75a"}