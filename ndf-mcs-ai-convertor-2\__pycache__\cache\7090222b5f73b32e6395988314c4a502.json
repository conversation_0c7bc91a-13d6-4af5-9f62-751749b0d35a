{"response": "```yaml\n- kind: Question\n  id: pf0530_PackagePurchaseWrap_DM\n  displayName: pf0530_PackagePurchaseWrap_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/pf0530_PackagePurchaseWrap_noinput_01.wav\\\">Say Yes or press 1, or say No or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/pf0530_PackagePurchaseWrap_nomatch_02.wav\\\">Say Yes or press 1, or say No or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.pf0530_PackagePurchaseWrap_DM_reco\n  prompt:\n    speak:\n      - \"{CustomAudio: com.nuance.att.application.audio.pf0530_PackagePurchaseWrap_initial}\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: auto_renew\n          displayName: auto_renew\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/pf0530_PackagePurchaseWrap_noinput_01.wav\\\">Say Yes or press 1, or say No or press 2</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_pf0530_PackagePurchaseWrap_DM\n  value: =Text(Global.pf0530_PackagePurchaseWrap_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pf0530_PackagePurchaseWrap_DM = \"auto_renew\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: autoRenew\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: pf0535_PackagePurchaseAutoRenew_SD\n```", "timestamp": 1749471774.5732043, "content_hash": "7090222b5f73b32e6395988314c4a502"}