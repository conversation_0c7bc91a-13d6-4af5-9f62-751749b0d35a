{"response": "```yaml\n- kind: Question\n  id: aa9905_HoldOnDtmf_DM\n  displayName: aa9905_HoldOnDtmf_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.aa9905_HoldOnDtmf_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n\n            // Case 1: childCareInfoVariables.holdOnProviderId = true && noInputCounter = 0 && noMatchCounter = 0\n            Global.childCareInfoVariables.holdOnProviderId = true && Global.noInputCounter = 0 && Global.noMatchCounter = 0,\n            \"Alright, I ll wait while you look for your provider ID number When you re ready to continue, just press #\",\n\n            // Case 2: noInputCounter = 0 && noMatchCounter = 0\n            Global.noInputCounter = 0 && Global.noMatchCounter = 0,\n            \"Alright, I ll wait while you look for your card number When you re ready to continue, just press #\",\n\n            // Case 3: noInputCounter = 0 && noMatchCounter = 0 (hold music)\n            Global.noInputCounter = 0 && Global.noMatchCounter = 0,\n            \"20 seconds hold music\",\n\n            // Default Case\n            \"Please wait...\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: ready\n          displayName: ready\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa9905_HoldOnDtmf_DM\n  value: =Text(Global.aa9905_HoldOnDtmf_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa9905_HoldOnDtmf_DM = \"ready\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.childCareInfoVariables.holdOnProviderId = true, true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: return\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_aa9905_HoldOnDtmf_DM = \"ready\", true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: return\n```", "timestamp": **********.4583158, "content_hash": "b68bdadcabfe4f4a78352bef23b68b17"}