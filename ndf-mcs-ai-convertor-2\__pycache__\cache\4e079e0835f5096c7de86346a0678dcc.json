{"response": "```yaml\n- kind: Question\n  id: AU1117_AskResetInformation_DM\n  displayName: AU1117_AskResetInformation_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AU1117_nm1_01.wav\\\">If you'd like information on how to reset your PIN online say Yes  Otherwise say No</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AU1117_nm2_01.wav\\\">If you'd like information on how to reset your PIN online say Yes or press 1If you'd like to try your pin once more say 'no' or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AU1117_nm2_01.wav\\\">If you'd like information on how to reset your PIN online say Yes or press 1If you'd like to try your pin once more say 'no' or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.AU1117_AskResetInformation_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/AU1117_ini_01.wav\\\">Sorry, that still didnt matchWould you like information on how to reset your pin online?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/AU1117_nm1_01.wav\\\">If you'd like information on how to reset your PIN online say Yes  Otherwise say No</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AU1117_nm2_01.wav\\\">If you'd like information on how to reset your PIN online say Yes or press 1If you'd like to try your pin once more say 'no' or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AU1117_nm2_01.wav\\\">If you'd like information on how to reset your PIN online say Yes or press 1If you'd like to try your pin once more say 'no' or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_AU1117_AskResetInformation_DM\n  value: =Text(Global.AU1117_AskResetInformation_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AU1117_AskResetInformation_DM = \"true\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: AU1118_GoToPasswordReset_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_AU1117_AskResetInformation_DM = \"false\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.lastPinTry\n              value: true\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: AU1100_GetSecurityCode_DM\n```", "timestamp": 1749527653.4445963, "content_hash": "4e079e0835f5096c7de86346a0678dcc"}