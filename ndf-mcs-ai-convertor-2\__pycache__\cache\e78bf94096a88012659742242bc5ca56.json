{"response": "```yaml\n- kind: Question\n  id: pr0340_ACPInfo_DM\n  displayName: pr0340_ACPInfo_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/pr0340_nm1_01.wav\\\">Would you like to hear more about the federal Affordable Connectivity Program?  Just say Yes or No</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/pr0340_nm2_01.wav\\\">If you'd like to hear more about the Affordable Connectivity Program say yes or press 1 If not, say No or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.pr0340_ACPInfo_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/pr0340_ini_01.wav\\\">You may be able to reduce the cost of your monthly internet or wireless service if you qualify for the federal governments Affordable Connectivity Program for low income households Would you like to hear more about the federal Affordable Connectivity Program? Yes or No</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/pr0340_ni1_01.wav\\\">Would you like to hear more about the federal Affordable Connectivity Program?  Just say Yes or No</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/pr0340_ni2_01.wav\\\">If you'd like to hear more about the Affordable Connectivity Program say yes or press 1 If not, say No or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_pr0340_ACPInfo_DM\n  value: =Text(Global.pr0340_ACPInfo_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pr0340_ACPInfo_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.heardACPInfo\n          value: true\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: pr0350_ACPDetails_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_pr0340_ACPInfo_DM = \"false\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.heardACPInfo\n              value: true\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.callType = 611, true, false)\n                  actions:\n                    - kind: BeginDialog\n                      id: begin_REPLACE_THIS\n                      dialog: topic.P02_ChoosePlan_04.dvxml\n              elseActions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: getReturnLink()\n```", "timestamp": 1749471713.124426, "content_hash": "e78bf94096a88012659742242bc5ca56"}