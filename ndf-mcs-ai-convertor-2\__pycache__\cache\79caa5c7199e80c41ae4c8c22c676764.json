{"response": "```yaml\n- kind: Question\n  id: sc0104_OfferCallBackOptions_DM\n  displayName: sc0104_OfferCallBackOptions_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/sc0104_OfferCallBackOptions_DM_initial\\\"> </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/sc0104_OfferCallBackOptions_DM_initial\\\"> </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.sc0104_OfferCallBackOptions_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/sc0104_OfferCallBackOptions_DM_initial\\\"> </audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: call_back\n          displayName: call_back\n        - id: representative\n          displayName: representative\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/sc0104_OfferCallBackOptions_DM_initial\\\"> </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/sc0104_OfferCallBackOptions_DM_initial\\\"> </audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_dtmfgrammar1\n  value: |\n    {Switch(\n      true,\n      Global.offerCallBackManager = true || Global.offerCallBackManager = \"true\",\n      \"sc0104_OfferCallBackOptions_DM_dtmf.jsp?options=call_back^representative&\",\n      \"sc0104_OfferCallBackOptions_DM_dtmf.jsp?options=representative&\"\n    )}\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.operatorAtOfferAgentOptions\n  value: 0\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_sc0104_OfferCallBackOptions_DM\n  value: =Text(Global.sc0104_OfferCallBackOptions_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_sc0104_OfferCallBackOptions_DM = \"call_back\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: sc0105_CheckPhoneNumber_JDA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_sc0104_OfferCallBackOptions_DM = \"representative\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.loginStatus = \"password\", true, false)\n                  actions:\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/sc0104_out_02.wav\\\">Okay, stay on the line for a representative Thank you for your patience</audio>\"\n                    - kind: BeginDialog\n                      id: begin_REPLACE_THIS\n                      dialog: topic.S04_CallTransfer_02.dvxml#sc0110_TransferMessage_PP\n              elseActions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.collectPinFromTransfer\n                  value: true\n                - kind: BeginDialog\n                  id: begin_REPLACE_THIS\n                  dialog: topic.S04_CallTransfer_02.dvxml#sc0146_CheckPINFlow_DS\n```", "timestamp": 1749472336.3335865, "content_hash": "79caa5c7199e80c41ae4c8c22c676764"}