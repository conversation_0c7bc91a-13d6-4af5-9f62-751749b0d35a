import re

def replace_legacy_globals_format(input_file, output_file):
    # Read the YAML file content
    with open(input_file, 'r') as file:
        yaml_content = file.read()

    # Replace Global.legacy.AV.<something> with Global.legacy_AV_<something>
    yaml_content = re.sub(
        r'\bGlobal\.legacy\.AV\.([^. \n]+)',  # Match variable after Global.legacy.AV.
        r'Global.legacy_AV_\1',
        yaml_content
    )

    # Replace Global.legacy.GV.<something> with Global.<something>
    yaml_content = re.sub(
        r'\bGlobal\.legacy\.GV\.([^. \n]+)',  # Match variable after Global.legacy.GV.
        r'Global.\1',
        yaml_content
    )

    # Write the updated content back to the output file
    with open(output_file, 'w') as file:
        file.write(yaml_content)

    print(f"Legacy format updated. YAML saved to: {output_file}")

# Example usage (commented out to prevent execution on import)
# input_file = r'C:\Projects\ndf-mcs-files\fixVariableForMetro_changes\Final-Output-Bot-Metro.yml'
# output_file = r'C:\Projects\ndf-mcs-files\fixVariableForMetro_changes\metro_output_new.yml'
# replace_legacy_globals_format(input_file, output_file)

if __name__ == "__main__":
    # This will only run if the script is executed directly, not when imported
    input_file = r's10-final1.yml'
    output_file = r's10-final2.yml'
    replace_legacy_globals_format(input_file, output_file)
