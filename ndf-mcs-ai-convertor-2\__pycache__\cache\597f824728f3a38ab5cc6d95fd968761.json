{"response": "```yaml\n- kind: Question\n  id: AP1020_FirstStep_DM\n  displayName: AP1020_FirstStep_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AP1020_nm1_01.wav\\\">Have you applied for the discount at the federal verifier webpage yet?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AP1020_nm2_01.wav\\\">If you have already applied for the discount at the federal verify webpage say 'yes' or press 1If not, say 'no' or press 2'</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AP1020_nm2_01.wav\\\">If you have already applied for the discount at the federal verify webpage say 'yes' or press 1If not, say 'no' or press 2'</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.AP1020_FirstStep_DM_reco\n  prompt:\n    speak:\n      - \"{Switch(         true,         Global.saidOperatorAcpAt1020 <> true,         \\\"The first step in applying for the ACP discount would be to receive approval through the federal national verifier webpage If approved, you will receive an approval ID  Have you done that yet?\\\"     )}\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/AP1020_ni1_01.wav\\\">Sorry, I didnt hear anything  The first step in applying for the ACP discount would be to receive approval through the federal national verifier webpage If approved, you will receive an approval ID  Have you done that yet?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AP1020_nm2_01.wav\\\">If you have already applied for the discount at the federal verify webpage say 'yes' or press 1If not, say 'no' or press 2'</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AP1020_nm2_01.wav\\\">If you have already applied for the discount at the federal verify webpage say 'yes' or press 1If not, say 'no' or press 2'</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.saidOperatorAcpAt1020\n  value: GlobalVars.saidOperatorAcpAt1020\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.operatorReqCount_AP1020\n  value: \"GlobalVars.operatorReqCount_AP1020 != undefined ? GlobalVars.operatorReqCount_AP1020 : 0\"\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_AP1020_FirstStep_DM\n  value: =Text(Global.AP1020_FirstStep_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AP1020_FirstStep_DM = \"true\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: AP1030_SecondStep_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_AP1020_FirstStep_DM = \"false\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: AP1025_FullProcess_DM\n\n# Command: operator\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.command = \"operator\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.operatorReqCount_AP1020\n          value: GlobalVars.operatorReqCount_AP1020+1\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callerSaidOperator\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.saidOperatorAcpAt1020\n          value: true\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.GlobalVars.operatorReqCount_AP1020 = 1, true, false)\n              actions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/AP1020_Operator_01.wav\\\">Our representative cannot complete the national verifier application on your behalf  You can call them direct at 800 324 9473 again the number is 800-324-9472   Have you applied at the national verifier webpage yet? '</audio>\"\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: AP1020_FirstStep_DM\n\n          elseActions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: AP1065_CallTransfer_SD\n```", "timestamp": 1749527469.3916075, "content_hash": "597f824728f3a38ab5cc6d95fd968761"}