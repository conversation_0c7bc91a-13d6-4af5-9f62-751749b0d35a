{"response": "```yaml\n- kind: Question\n  id: RP1411_OfferOnePlanYN_DM\n  displayName: RP1411_OfferOnePlanYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/RP1411_nm1_01.wav\\\">Sorry, do you want to switch to that plan To hear about it again, say repeat  </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/RP1411_nm2_01.wav\\\">If you want to switch to that plan, say yes or press 1 Otherwise, say no or press 2 To hear about the plan again, say repeat or press star  </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/RP1411_nm2_01.wav\\\">If you want to switch to that plan, say yes or press 1 Otherwise, say no or press 2 To hear about the plan again, say repeat or press star  </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.RP1411_OfferOnePlanYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/RP1411_ini_01.wav\\\">There s one plan I can offer you today  </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"{DynamicAudio: soc, className: com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlanAndFeatures_RP1411}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/RP1411_ini_05.wav\\\">Do you want to switch to that plan </audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/RP1411_nm1_01.wav\\\">Sorry, do you want to switch to that plan To hear about it again, say repeat  </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/RP1411_nm2_01.wav\\\">If you want to switch to that plan, say yes or press 1 Otherwise, say no or press 2 To hear about the plan again, say repeat or press star  </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/RP1411_nm2_01.wav\\\">If you want to switch to that plan, say yes or press 1 Otherwise, say no or press 2 To hear about the plan again, say repeat or press star  </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.ratePlans\n  value: GlobalVars.ratePlans\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.comingFrom\n  value: RP1411\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.soc\n  value: GlobalVars.ratePlans[0].soc\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_RP1411_OfferOnePlanYN_DM\n  value: =Text(Global.RP1411_OfferOnePlanYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_RP1411_OfferOnePlanYN_DM = \"yes\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.selectedPlan\n          value: soc\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.isComingFromRp1411\n          value: true\n\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.RatePlan_Main.dvxml#RP0301_CheckPlanOptions_DS\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_RP1411_OfferOnePlanYN_DM = \"no\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: RP1425_NoOtherPlans_DM\n```", "timestamp": 1749529177.675447, "content_hash": "6fefca5448c05ef946b272b05cbf88b9"}