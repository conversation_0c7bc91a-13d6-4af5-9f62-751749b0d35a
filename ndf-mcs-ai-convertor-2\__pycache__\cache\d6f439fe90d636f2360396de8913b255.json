{"response": "```yaml\n- kind: Question\n  id: CF1010_CurrentFeatures_DM\n  displayName: CF1010_CurrentFeatures_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"{Switch(\n            true,\n            Global.fromDisamg = true && Global.hasFeatures = true,\n            \\\"Please say repeat that', 'add a feature' or 'remove a feature'\\\",\n            Global.fromDisamg = true && Global.hasFeatures <> true,\n            \\\"Please say repeat that',or  'add a feature' \\\",\n            !(Global.fromDisamg = true),\n            \\\"To hear that information again, say 'repeat that' You can also say 'add a feature' or 'what's my rate plan' If you're finished, you can just hang up\\\"\n        )}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"{Switch(\n            true,\n            Global.fromDisamg = true && Global.hasFeatures = true,\n            \\\"To hear your features again say, repeat that',or press star  Or say 'add a feature' or press 1, 'remove a feature' or press 2\\\",\n            Global.fromDisamg = true && Global.hasFeatures <> true,\n            \\\"To hear your features again say repeat that', or press star  'add a feature' or press 1 \\\",\n            !(Global.fromDisamg = true),\n            \\\"Would you like me to repeat the  services you re signed up for?\\\"\n        )}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"{Switch(\n            true,\n            Global.fromDisamg = true && Global.hasFeatures = true,\n            \\\"To hear your features again say, repeat that',or press star  Or say 'add a feature' or press 1, 'remove a feature' or press 2\\\",\n            Global.fromDisamg = true && Global.hasFeatures <> true,\n            \\\"To hear your features again say repeat that', or press star  'add a feature' or press 1 \\\",\n            !(Global.fromDisamg = true),\n            \\\"Would you like me to repeat the  services you re signed up for?\\\"\n        )}\"\n\n  alwaysPrompt: true\n  variable: Global.CF1010_CurrentFeatures_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.heardThirdPartyInfo <> true && Global.hasThirdPartyFeatures = true,\n            [\n              \"Third Party charges will be clearly defined on your statement To block those purchases, check your account settings on metrobyt-mobilecom, or say 'operator' now\",\n              \"test\"\n            ],\n            Global.fromDisamg = true && Global.hasFeatures = true,\n            \"You can say repeat that', 'add a feature' or 'remove a feature\",\n            Global.fromDisamg = true && Global.hasFeatures <> true,\n            \"You can say repeat that', or 'add a feature'\",\n            !(Global.fromDisamg = true),\n            \"If you need to, say 'repeat that'  You can also say 'add a feature' or 'what's my rate plan'  If you're finished, you can just hang up\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: add-feature\n          displayName: add-feature\n        - id: hear-plan_details\n          displayName: hear-plan_details\n        - id: remove_feature\n          displayName: remove_feature\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"{Switch(\n              true,\n              Global.fromDisamg = true && Global.hasFeatures = true,\n              \\\"Please say repeat that', 'add a feature' or 'remove a feature'\\\",\n              Global.fromDisamg = true && Global.hasFeatures <> true,\n              \\\"Please say repeat that',or  'add a feature' \\\",\n              !(Global.fromDisamg = true),\n              \\\"To hear that information again, say 'repeat that' You can also say 'add a feature' or 'what's my rate plan' If you're finished, you can just hang up\\\"\n          )}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"{Switch(\n              true,\n              Global.fromDisamg = true && Global.hasFeatures = true,\n              \\\"To hear your features again say, repeat that',or press star  Or say 'add a feature' or press 1, 'remove a feature' or press 2\\\",\n              Global.fromDisamg = true && Global.hasFeatures <> true,\n              \\\"To hear your features again say repeat that', or press star  'add a feature' or press 1 \\\",\n              !(Global.fromDisamg = true),\n              \\\"Would you like me to repeat the  services you re signed up for?\\\"\n          )}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"{Switch(\n              true,\n              Global.fromDisamg = true && Global.hasFeatures = true,\n              \\\"To hear your features again say, repeat that',or press star  Or say 'add a feature' or press 1, 'remove a feature' or press 2\\\",\n              Global.fromDisamg = true && Global.hasFeatures <> true,\n              \\\"To hear your features again say repeat that', or press star  'add a feature' or press 1 \\\",\n              !(Global.fromDisamg = true),\n              \\\"Would you like me to repeat the  services you re signed up for?\\\"\n          )}\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.featureSocs\n  value: getFeaturesFromAccountDetails(GlobalVars.GetAccountDetails)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.featureSocsThirdParty\n  value: getThirdPartyFeaturesFromAccountDetails(GlobalVars.GetAccountDetails)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.heardThirdPartyInfo\n  value: GlobalVars.heardThirdPartyInfo\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.hasThirdPartyFeatures\n  value: featureSocsThirdParty != null && featureSocsThirdParty.length > 0\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.hasFeatures\n  value: (featureSocs != null && featureSocs.length > 0) || (featureSocsThirdParty != null && featureSocsThirdParty.length > 0)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.futureAddOns\n  value: GlobalVars.futureAddOns\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.fromDisamg\n  value: \"GlobalVars.fromDisamg != undefined ? GlobalVars.fromDisamg : false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_dtmfgrammar1\n  value: CF1010_CurrentFeatures_DM_dtmf.jsp\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_grammar1\n  value: CF1010_CurrentFeatures_DM.jsp\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: GlobalVars.fromDisamg\n  value: false\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.hasThirdPartyFeatures = true, true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: GlobalVars.heardThirdPartyInfo\n          value: true\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.Var_CF1010_CurrentFeatures_DM = \"add-feature\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: GlobalVars.callType\n          value: add_feature\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: GlobalVars.ifFromCurrentFeatures\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.GetAccountDetails.accountStatus = \"active\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: CF1020_GoToVoiceStore_SD\n\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: GlobalVars.tag\n              value: add-feature_suspended\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: CF1015_GoToCallTransfer_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: |\n            =If(Global.Var_CF1010_CurrentFeatures_DM = \"hear-plan_details\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: GlobalVars.callType\n              value: plan_details\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: GlobalVars.cti_Intent\n              value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: CF1025_GoToRatePlan_SD\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.Var_CF1010_CurrentFeatures_DM = \"remove_feature\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: CF1015_GoToCallTransfer_SD\n```", "timestamp": 1749558182.9741566, "content_hash": "d6f439fe90d636f2360396de8913b255"}