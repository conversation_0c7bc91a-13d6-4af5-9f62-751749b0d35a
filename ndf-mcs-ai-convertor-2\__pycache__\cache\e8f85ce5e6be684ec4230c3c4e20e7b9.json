{"response": "```yaml\n- kind: Question\n  id: aa4115_BenefitsAccessWrapUpDTMF_DM\n  displayName: aa4115_BenefitsAccessWrapUpDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.dtmfOnlyFlag = true,\n            \"Sorry, I still didn t get that\",\n            true,\n            \"\"\n        )\n        }\n\n  alwaysPrompt: true\n  variable: Global.aa4115_BenefitsAccessWrapUpDTMF_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.dnisInfo.callType = \"TX\",\n            \"To hear that again, press 1  If you're experiencing a problem with your card, press 2  For other options, press 3  Or if you're done here, hang up\",\n            Global.dnisInfo.callType = \"WV\",\n            \"To hear that again, press 1  Other options, press 2  Or if you're done here, hang up\",\n            [\n              \"If you would like to hear that again, Press 1\",\n              \"To go to the main menu, press 2\",\n              \"And if you're done feel free to hang up\"\n            ]\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: main_menu\n          displayName: main_menu\n        - id: card_problem\n          displayName: card_problem\n        - id: other_options\n          displayName: other_options\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa4115_BenefitsAccessWrapUpDTMF_DM\n  value: =Text(Global.aa4115_BenefitsAccessWrapUpDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa4115_BenefitsAccessWrapUpDTMF_DM = \"main_menu\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa4004_GetMainMenuOptions_DB_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa4115_BenefitsAccessWrapUpDTMF_DM = \"card_problem\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.transferAllowed\n              value: true\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.transferReason\n              value: cardProblem\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa9805_ProcessTransfer_PP\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_aa4115_BenefitsAccessWrapUpDTMF_DM = \"other_options\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: aa4105_OtherOptionsMenuDTMF_DM\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_aa4115_BenefitsAccessWrapUpDTMF_DM = \"repeat\", true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: aa4110_BenefitsAccessPlayout_PP\n```", "timestamp": 1749543730.7348683, "content_hash": "e8f85ce5e6be684ec4230c3c4e20e7b9"}