{"response": "```yaml\n- kind: Question\n  id: UP1020_OfferSalesAgentYN_DM\n  displayName: UP1020_OfferSalesAgentYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/UP1020_nm1_01.wav\\\">Would you like to talk to someone about buying a phone today? Say yes no or repeat</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/UP1020_nm2_01.wav\\\">If you d like to talk to someone about buying a phone at the regular price today, say yes or press 1 If you re all set, say no or press 2 To hear your eligibility date again, say repeat or press 3</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/UP1020_nm2_01.wav\\\">If you d like to talk to someone about buying a phone at the regular price today, say yes or press 1 If you re all set, say no or press 2 To hear your eligibility date again, say repeat or press 3</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.UP1020_OfferSalesAgentYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/UP1020_ini_01.wav\\\">If you re looking to buy a phone at the regular price right now I ll take you to someone who can help you find the best match Would you like to do that? </audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/UP1020_nm1_01.wav\\\">Would you like to talk to someone about buying a phone today? Say yes no or repeat</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/UP1020_nm2_01.wav\\\">If you d like to talk to someone about buying a phone at the regular price today, say yes or press 1 If you re all set, say no or press 2 To hear your eligibility date again, say repeat or press 3</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/UP1020_nm2_01.wav\\\">If you d like to talk to someone about buying a phone at the regular price today, say yes or press 1 If you re all set, say no or press 2 To hear your eligibility date again, say repeat or press 3</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_UP1020_OfferSalesAgentYN_DM\n  value: =Text(Global.UP1020_OfferSalesAgentYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_UP1020_OfferSalesAgentYN_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: transfer\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.tag\n          value: purchase_phone\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: UP1025_Transfer_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_UP1020_OfferSalesAgentYN_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/UP1020_out_01.wav\\\">No problem</audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n```", "timestamp": 1749529856.4170237, "content_hash": "ddb6986966086b81bc3e54c4de5a50ac"}