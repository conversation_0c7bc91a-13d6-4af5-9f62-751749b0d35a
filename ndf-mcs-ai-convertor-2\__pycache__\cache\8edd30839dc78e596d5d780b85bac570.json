{"response": "```yaml\n- kind: Question\n  id: PA1015_GetAmount_DM\n  displayName: PA1015_GetAmount_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/PA1015_nm1_01.wav\\\">How much would you like to pay today</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/PA1015_nm1_02.wav\\\">Or say use a  Payment PIN</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/PA1015_nm2_01.wav\\\">Please say the amount you want to pay or enter on your keypad, followed by the sign For example if you want to pay 25 dollars, enter 2500</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/PA1015_nm2_02.wav\\\">If you'll be redeeming a prepaid  Payment PIN today, press star and we'll take care of that first </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/PA1015_nm3_01.wav\\\">Please enter the amount you want to pay, in dollars and cents, followed by the # sign If you want to pay 57 dollars, enter it like 5700</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/PA1015_nm3_02.wav\\\">To redeem a  Payment PIN, press star </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.PA1015_GetAmount_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n\n            // Case 1: parseFloat(paymentAmount) = 0.00 || authorizationFailureHandling = \"sameAmountDeclined\"\n            parseFloat(Global.paymentAmount) = 0.00 || Global.authorizationFailureHandling = \"sameAmountDeclined\",\n            [\n                \"<audio src=\\\"AUDIO_LOCATION/PA1015_ini_06.wav\\\">How much would you like to pay today</audio>\",\n                \"{Switch( true, Global.offerPrepaid = true, \\\"<audio src=\\\\\\\"AUDIO_LOCATION/PA1015_ini_03.wav\\\\\\\">Or say use a  Payment PIN </audio>\\\" )}\"\n            ],\n\n            // Case 2: parseFloat(paymentAmount) < parseFloat(minLimit)\n            parseFloat(Global.paymentAmount) < parseFloat(Global.minLimit),\n            [\n                \"<audio src=\\\"AUDIO_LOCATION/PA1015_ini_02.wav\\\">I'm sorry, the amount you entered is too small for your type of card Please choose an amount of at least</audio>\",\n                \"{Global.minLimit}\"\n            ],\n\n            // Case 3: parseFloat(paymentAmount) > parseFloat(maxLimit)\n            parseFloat(Global.paymentAmount) > parseFloat(Global.maxLimit),\n            [\n                \"<audio src=\\\"AUDIO_LOCATION/PA1015_ini_04.wav\\\">I'm sorry, the amount you entered is too much for your type of card Please choose an amount under </audio>\",\n                \"{Global.maxLimit}\"\n            ],\n\n            // Case 4: else\n            true,\n            [\n                \"<audio src=\\\"AUDIO_LOCATION/PA1015_ini_05.wav\\\">How much would you like to pay today? </audio>\",\n                \"{Switch( true, Global.offerPrepaid = true, \\\"<audio src=\\\\\\\"AUDIO_LOCATION/PA1015_ini_07.wav\\\\\\\">Or say 'use a  Payment PIN'</audio>\\\" )}\"\n            ]\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: prepaid\n          displayName: prepaid\n        - id: switch_account\n          displayName: switch_account\n        - id: request-extension\n          displayName: request-extension\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/PA1015_nm1_01.wav\\\">How much would you like to pay today</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/PA1015_nm1_02.wav\\\">Or say use a  Payment PIN</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/PA1015_nm2_01.wav\\\">Please say the amount you want to pay or enter on your keypad, followed by the sign For example if you want to pay 25 dollars, enter 2500</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/PA1015_nm2_02.wav\\\">If you'll be redeeming a prepaid  Payment PIN today, press star and we'll take care of that first </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/PA1015_nm3_01.wav\\\">Please enter the amount you want to pay, in dollars and cents, followed by the # sign If you want to pay 57 dollars, enter it like 5700</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/PA1015_nm3_02.wav\\\">To redeem a  Payment PIN, press star </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.paymentAmount\n  value: \"GlobalVars.paymentAmount != undefined ? GlobalVars.paymentAmount : 0.0\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.dueImmediatelyAmount\n  value: GlobalVars.GetAccountDetails.dueImmediatelyAmount\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.maxLimit\n  value: \"(GlobalVars.ValidateCardOptions)?GlobalVars.ValidateCardOptions.maxLimit : ''\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.minLimit\n  value: \"(GlobalVars.ValidateCardOptions)?GlobalVars.ValidateCardOptions.minLimit : ''\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.extensionAllowed\n  value: \"GlobalVars.extensionAllowed != undefined ? GlobalVars.extensionAllowed : false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.authorizationFailureHandling\n  value: GlobalVars.authorizationFailureHandling\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.offerPrepaid\n  value: GlobalVars.offerPrepaid\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_grammar1\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_dtmfgrammar1\n  value: \"\"\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_PA1015_GetAmount_DM\n  value: =Text(Global.PA1015_GetAmount_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_PA1015_GetAmount_DM = \"prepaid\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.payingWithPrepaid\n          value: true\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: getReturnLink()\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_PA1015_GetAmount_DM = \"switch_account\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/PA1015_out_01.wav\\\">Sure To manage a diffrent account, you can call 611 from the phone you want to work with If you don t have it, you ll need to call from a landline, and dial 888 8METRO8 Again, that s 888-8-METRO8, from a landline Then, you ll just enter the number you want to work with </audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\"\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.callType\n              value: goodbye\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.cti_Intent\n              value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_PA1015_GetAmount_DM = \"request-extension\", true, false)\n              actions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/PA1015_out_02.wav\\\">Sure!</audio>\"\n                      - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.callType\n                  value: extension\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.cti_Intent\n                  value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.extensionEntryPoint\n                  value: payment_amount\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: getReturnLink()\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_PA1015_GetAmount_DM = \"default\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.paymentAmount\n                      value: PA1015_GetAmount_DM.returnvalue\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.repeatPA1005\n                      value: false\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: PA1020_CheckDueImmediately_JDA\n```", "timestamp": 1749529037.170697, "content_hash": "8edd30839dc78e596d5d780b85bac570"}