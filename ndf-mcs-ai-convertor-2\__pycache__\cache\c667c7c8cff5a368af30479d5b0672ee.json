{"response": "```yaml\n- kind: Question\n  id: MC1130_NewNumberWait_DM\n  displayName: MC1130_NewNumberWait_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.MC1130_NewNumberWait_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/MC1130_ini_02.wav\\\">I ve got it</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MC1130_ini_01.wav\\\">When you're ready for your new number, say 'continue' 8 seconds wait music When you're ready, say 'Continue', or press 1 8 seconds wait music You can say 'Continue', or press 1, at any time 8 seconds wait music If you're ready to hear your new number, say Continue or press 1  8 seconds wait music If you're ready,  say 'Continue' or press 1 8 seconds wait music When you're ready say 'Continue' or press 1 8 seconds wait music I'm having some trouble Let's move on</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: continue\n          displayName: continue\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_MC1130_NewNumberWait_DM\n  value: =Text(Global.MC1130_NewNumberWait_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MC1130_NewNumberWait_DM = \"continue\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/MC1130_out_01.wav\\\">Okay, here we go </audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: MC1150_ReadNewNum_DM\n```", "timestamp": 1749528680.2331052, "content_hash": "c667c7c8cff5a368af30479d5b0672ee"}