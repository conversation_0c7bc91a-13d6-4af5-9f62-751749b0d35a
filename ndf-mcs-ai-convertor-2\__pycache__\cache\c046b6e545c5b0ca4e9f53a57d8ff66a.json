{"response": "```yaml\n- kind: Question\n  id: cp0215_TempPINWait_DM\n  displayName: cp0215_TempPINWait_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.cp0215_TempPINWait_DM_reco\n  prompt:\n    speak:\n      - \"{CustomAudio: com.nuance.att.application.audio.cp0215_TempPINWait_DM_initial}\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: got_it\n          displayName: got_it\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_cp0215_TempPINWait_DM\n  value: =Text(Global.cp0215_TempPINWait_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_cp0215_TempPINWait_DM = \"got_it\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/cp0215_out_01.wav\\\">Great</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: cp0225_AskTempPIN_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_cp0215_TempPINWait_DM = \"default\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.numTempPINAttempts\n              value: numTempPINAttempts + 1\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.tempPIN\n              value: cp0215_TempPINWait_DM.returnvalue\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: cp0235_ValidateTempPIN_DB_DA\n```\n\n---\n\n**Explanation of the YAML:**\n\n- **kind: Question**: Always set as per instruction.\n- **id/displayName**: Set to `cp0215_TempPINWait_DM` as per the `<dm-state id=\"...\">`.\n- **prompt.speak**: Since the initial prompt is a custom audio class, we use a placeholder `{CustomAudio: ...}` to indicate this (as per your pattern for custom className).\n- **entity**: EmbeddedEntity with ClosedListEntity for the two possible labels: `got_it` and `default`.\n- **voiceInputSettings**: Standard settings, with empty `inputTimeoutResponse` as there are no noinput/nomatch prompts.\n- **SetVariable**: For the variable mapping as per instruction 11.\n- **ConditionGroup**: \n  - First condition checks for `\"got_it\"` and sends the audio, then goes to `cp0225_AskTempPIN_DM`.\n  - Else, checks for `\"default\"`, sets the variables, and goes to `cp0235_ValidateTempPIN_DB_DA`.\n- **SendActivity**: Used to play the audio prompt for \"Great\" as per the `<audio>` in the success action.\n\n**Note:**  \n- No unrecognizedPrompt or inputTimeoutResponse since there are no nomatch/noinput prompts in the XML.\n- The custom audio class for the initial prompt is represented as `{CustomAudio: ...}` for clarity, as per your pattern.\n- All ids for SetVariable, ConditionGroup, etc., are set to `REPLACE_THIS` as per your placeholder instruction.\n- The YAML is indented and structured as per your memorized format and examples.", "timestamp": 1749470435.3573997, "content_hash": "c046b6e545c5b0ca4e9f53a57d8ff66a"}