{"response": "```yaml\n- kind: Question\n  id: UW1115_ChooseCard_DM\n  displayName: UW1115_ChooseCard_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/UW1115_nm1_01.wav\\\">Say or enter the last four digits for the card you want to use Or say 'none of those'</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/UW1115_nm2_01.wav\\\">Please say or enter the last four digits for the card you want to use You can also say 'none of those' or press star</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/UW1115_nm3_01.wav\\\">Please enter the last four digits for the card you want to use If you want to pay with a new card, press star</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.UW1115_ChooseCard_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/UW1115_ini_01.wav\\\">Here are the cards I have for you When you hear the one you want, tell me the last 4 digits</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/UW1115_ini_05.wav\\\">Just say or enter the four digits for the card you want to use today Or say 'none of those'</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: choose-card_none\n          displayName: choose-card_none\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/UW1115_nm1_01.wav\\\">Say or enter the last four digits for the card you want to use Or say 'none of those'</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/UW1115_nm2_01.wav\\\">Please say or enter the last four digits for the card you want to use You can also say 'none of those' or press star</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/UW1115_nm3_01.wav\\\">Please enter the last four digits for the card you want to use If you want to pay with a new card, press star</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_UW1115_ChooseCard_DM\n  value: =Text(Global.UW1115_ChooseCard_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_UW1115_ChooseCard_DM = \"choose-card_none\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: UW1125_UseNewCard_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_UW1115_ChooseCard_DM = \"default\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.paymentOptionId\n              value: UW1115_ChooseCard_DM.returnvalue\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.payingWithEWallet\n              value: true\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.PaymentTable.ACTIVATION_STATUS\n              value: '106'\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: UW1130_CheckHaveMethodSet_JDA_DA\n```", "timestamp": 1749558629.6985688, "content_hash": "dd005e9a52a5031e9a3a1970f287fc53"}