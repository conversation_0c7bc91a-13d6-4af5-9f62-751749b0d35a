{"response": "```yaml\n- kind: Question\n  id: aa1098_UnfreezeCardDTMF_DM\n  displayName: aa1098_UnfreezeCardDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa1098_ini_01.wav\\\">Your card is frozen, and no transactions are currently allowed To unfreeze your card now, press 1 Otherwise, press 2</audio>\"\n      - |\n        {Switch(\n              true,\n              Global.dnisInfo.dtmfOnlyFlag = true,\n              \"<audio src=\\\"AUDIO_LOCATION/aa1098_nm2_01.wav\\\">Sorry, I still didn't get that</audio>\",\n              true,\n              \"\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.aa1098_UnfreezeCardDTMF_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa1098_ini_01.wav\\\">Your card is frozen, and no transactions are currently allowed To unfreeze your card now, press 1 Otherwise, press 2</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/aa1098_ini_01.wav\\\">Your card is frozen, and no transactions are currently allowed To unfreeze your card now, press 1 Otherwise, press 2</audio>\"\n        - |\n          {Switch(\n                true,\n                Global.dnisInfo.dtmfOnlyFlag = true,\n                \"<audio src=\\\"AUDIO_LOCATION/aa1098_ni2_01.wav\\\">Sorry, I still didn't get that</audio>\",\n                true,\n                \"\"\n            )\n          }\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa1098_UnfreezeCardDTMF_DM\n  value: =Text(Global.aa1098_UnfreezeCardDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa1098_UnfreezeCardDTMF_DM = \"yes\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.nextStep\n          value: freeze_card\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.currentTask\n          value: freezeCard\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aa1098_out_01.wav\\\">Okay</audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: return\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa1098_UnfreezeCardDTMF_DM = \"no\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.nextStep\n              value: balance_inquiry\n\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/aa1098_out_02.wav\\\">Okay You can unfreeze your card at any time</audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: return\n```", "timestamp": 1749458606.7750552, "content_hash": "df5d7ae5007b1611c39cf9bf20e64f7b"}