{"response": "```yaml\n- kind: Question\n  id: PI1015_GetPaymentMethod_DM\n  displayName: PI1015_GetPaymentMethod_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"{Switch(\n            true,\n            Global.offerPrepaid = true,\n            \\\"Do you want to pay with a 'Payment PIN', a *credit* card or a *debit* card? If you need some time to find it, say 'wait a minute' If you want to use both a payment PIN and a bank card, say 'Payment PIN' and we'll do that one first\\\",\n            Global.offerPrepaid = false,\n            \\\"Do you want to pay with *credit*  or *debit*? If you need some time to find your card, say 'wait a minute' \\\"\n        )}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"{Switch(\n            true,\n            Global.offerPrepaid = true,\n            \\\"Please say 'Payment PIN' or press 1, 'credit' or press 2, 'debit' - 3, or  'wait a minute' - 4\\\",\n            Global.offerPrepaid = false,\n            \\\"Please say 'credit' or press 1, 'debit' or press 2,or 'wait a minute' or press 3 \\\"\n        )}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"{Switch(\n            true,\n            Global.offerPrepaid = true,\n            \\\"To use a Payment PIN, press 1 For credit, press 2 For debit, press 3 If you have a card that has both credit and debit, just choose the method you prefer If you want me to wait while you look, press 4\\\",\n            Global.offerPrepaid = false,\n            \\\"If you have a card that has both credit and debit, just choose the method you prefer For credit, press 1 For debit, press 2 If you want me to wait while you look, press 3 \\\"\n        )}\"\n\n  alwaysPrompt: true\n  variable: Global.PI1015_GetPaymentMethod_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.fromAnotherPaymentMenu = false && Global.offerPrepaid = true,\n            [\n                \"We take Metro  Payment PINs and Visa, MasterCard, American Express, Star, NYCE, Pulse, and Accel We *don't* take Discover cards\",\n                \"test\",\n                \"If you need time, say wait a minute \",\n                \"test\",\n                \"If you're ready, say 'Payment PIN', 'credit', or 'debit' If you want to use a payment PIN AND a bank card, say 'Payment PIN' and we'll take care of that first \"\n            ],\n            Global.fromAnotherPaymentMenu = false && Global.offerPrepaid = false,\n            [\n                \"We take Visa, MasterCard, American Express, Star, NYCE, Pulse, and Accel We dont take Discover cards \",\n                \"test\",\n                \"If you need time, say wait a minute \",\n                \"test\",\n                \"If you're ready, say 'credit', or 'debit' \"\n            ],\n            // Default\n            [\n                \"test\",\n                \"If you need time, say wait a minute \",\n                \"test\"\n            ]\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: credit\n          displayName: credit\n        - id: debit\n          displayName: debit\n        - id: prepaid\n          displayName: prepaid\n        - id: wait\n          displayName: wait\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"{Switch(\n            true,\n            Global.offerPrepaid = true,\n            \\\"Do you want to pay with a 'Payment PIN', a *credit* card or a *debit* card? If you need some time to find it, say 'wait a minute' If you want to use both a payment PIN and a bank card, say 'Payment PIN' and we'll do that one first\\\",\n            Global.offerPrepaid = false,\n            \\\"Do you want to pay with *credit*  or *debit*? If you need some time to find your card, say 'wait a minute' \\\"\n        )}\"\n        - \"{Switch(\n            true,\n            Global.offerPrepaid = true,\n            \\\"Please say 'Payment PIN' or press 1, 'credit' or press 2, 'debit' - 3, or  'wait a minute' - 4\\\",\n            Global.offerPrepaid = false,\n            \\\"Please say 'credit' or press 1, 'debit' or press 2,or 'wait a minute' or press 3 \\\"\n        )}\"\n        - \"{Switch(\n            true,\n            Global.offerPrepaid = true,\n            \\\"To use a Payment PIN, press 1 For credit, press 2 For debit, press 3 If you have a card that has both credit and debit, just choose the method you prefer If you want me to wait while you look, press 4\\\",\n            Global.offerPrepaid = false,\n            \\\"If you have a card that has both credit and debit, just choose the method you prefer For credit, press 1 For debit, press 2 If you want me to wait while you look, press 3 \\\"\n        )}\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.preferredPaymentMethod\n  value: GlobalVars.preferredPaymentMethod\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.fromAnotherPaymentMenu\n  value: \"GlobalVars.fromAnotherPaymentMenu != undefined ? GlobalVars.fromAnotherPaymentMenu : false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.offerPrepaid\n  value: \"GlobalVars.offerPrepaid  != undefined ? GlobalVars.offerPrepaid : false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_dtmfgrammar1\n  value: PI1015_GetPaymentMethod_DM_dtmf.jsp\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_grammar1\n  value: PI1015_GetPaymentMethod_DM.jsp\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_PI1015_GetPaymentMethod_DM\n  value: =Text(Global.PI1015_GetPaymentMethod_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_PI1015_GetPaymentMethod_DM = \"credit\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.preferredPaymentMethod\n          value: credit\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.PaymentTable.CARD_TYPE\n          value: credit\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.GlobalVars.callType = \"activate\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.PaymentTable.ACTIVATION_STATUS\n                  value: 83\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: PI1039_CheckCallType_JDA\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.PaymentTable.ACTIVATION_STATUS\n              value: 104\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: PI1039_CheckCallType_JDA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_PI1015_GetPaymentMethod_DM = \"debit\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.preferredPaymentMethod\n              value: debit\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.PaymentTable.CARD_TYPE\n              value: debit\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.GlobalVars.callType = \"activate\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.PaymentTable.ACTIVATION_STATUS\n                      value: 83\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: PI1039_CheckCallType_JDA\n              elseActions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.PaymentTable.ACTIVATION_STATUS\n                  value: 104\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: PI1039_CheckCallType_JDA\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_PI1015_GetPaymentMethod_DM = \"prepaid\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.payingWithPrepaid\n                  value: true\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/PI1015_out_01.wav\\\">Okay</audio>\"\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: PI1016_PrepaidCard_SD\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_PI1015_GetPaymentMethod_DM = \"wait\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.preferredPaymentMethod\n                      value: undefined\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: PI1010_FindCardWaitSBI_DM\n```", "timestamp": 1749529101.16884, "content_hash": "7242985bccc24ce7e9779625f1faf8ba"}