{"response": "```yaml\n- kind: Question\n  id: ca0166_PortSubMenu_DM\n  displayName: ca0166_PortSubMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ca0166_nm1_01.wav\\\">Before you can port out a phone number to  another carrier, you'll need your A T and T prepaid account number If you need to retrieve your account number, say account number If you have your account number, say I have it</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ca0166_nm2_01.wav\\\">Before you can port out a phone number to  another carrier, you'll need your A T and T prepaid account number If you need to retrieve your account number, say account number or Press 1  If you have your account number, say I have it or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ca0166_PortSubMenu_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ca0166_ini_01.wav\\\">Before you can port out a phone number to another carrier, youll need your A T and T prepaid account number If you need to retrieve your account number, say account number If you have your account number, say I have it</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: account_number\n          displayName: account_number\n        - id: i_have_it\n          displayName: i_have_it\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ca0166_ni1_01.wav\\\">Before you can port out a phone number to  another carrier, you'll need your A T and T prepaid account number If you need to retrieve your account number, say account number If you have your account number, say I have it</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ca0166_ni2_01.wav\\\">Before you can port out a phone number to  another carrier, you'll need your A T and T prepaid account number If you need to retrieve your account number, say account number or Press 1  If you have your account number, say I have it or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ca0166_PortSubMenu_DM\n  value: =Text(Global.ca0166_PortSubMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ca0166_PortSubMenu_DM = \"account_number\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.accountType = \"whp\" || Global.accountType = \"dataOnly\", true, false)\n              actions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/ca0166_out_01.wav\\\">Ill need to connect you to someone who can help with that</audio>\"\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.intent\n                  value: port_out\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.transferReason\n                  value: BUSINESS_RULE\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.transferPrompt\n                  value: short\n                - kind: BeginDialog\n                  id: begin_REPLACE_THIS\n                  dialog: topic.M09_MainMenu.dvxml#ma9210_NeedTransferType_DS\n\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.intent\n              value: port_out\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: ca0161_AccountRetrieveAuth_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ca0166_PortSubMenu_DM = \"i_have_it\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.intent\n              value: port_out\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: ca0169_ReadyToPort_PP\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_ca0166_PortSubMenu_DM = \"repeat\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: ca0166_PortSubMenu_DM\n```", "timestamp": **********.2107382, "content_hash": "d80f6c1a0e366f8e878de3d9e9d50f33"}