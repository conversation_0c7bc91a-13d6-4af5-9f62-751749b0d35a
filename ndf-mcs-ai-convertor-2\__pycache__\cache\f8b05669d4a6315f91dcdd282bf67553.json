{"response": "```yaml\n- kind: Question\n  id: RP0520_AskRatePlan_DM\n  displayName: RP0520_AskRatePlan_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/RP0520_nm1_01.wav\\\">You can say 'repeat that', 'none of those', or 'main menu'  </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/RP0520_nm2_02.wav\\\">You can also say more details, none of those, or don t change my plan</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/RP0520_nm2_02.wav\\\">You can also say more details, none of those, or don t change my plan</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.RP0520_AskRatePlan_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.FirstTimeRP0520 = true,\n            [\n                \"To make sure all the features on your new plan will work on your phone, you can check out metrobyt-mobilecom\",\n                \"test\",\n                \"These prices include all taxes and regulatory fees You'll get a chance to hear further plan details once you've made a selection\",\n                \"Please choose the one you're interested \",\n                \"for \"\n            ],\n            Global.FirstTimeRP0520 = false && Global.declineChangeCounter = 1,\n            [\n                \"Ok, please make another selection  or say 'dont change my plan' \",\n                \"test\",\n                \"for \"\n            ],\n            Global.reentry && Global.choseInvalidPlan = true && Global.noneOfThoseCount = 0 && !((Global.FirstTimeRP0520 = true) || (Global.FirstTimeRP0520 = false && Global.declineChangeCounter = 1)),\n            [\n                \"Here are the plans we DO have \",\n                \"test\"\n            ],\n            Global.reentry && Global.choseInvalidPlan <> true && Global.noneOfThoseCount = 0 && !((Global.FirstTimeRP0520 = true) || (Global.FirstTimeRP0520 = false && Global.declineChangeCounter = 1)),\n            [\n                \"Ok, so choose the plan you may be interested in \",\n                \"test\"\n            ],\n            true,\n            [\n                \"for \",\n                \"test\",\n                \"You can say 'repeat that', 'none of those', or 'main menu' \"\n            ]\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: dont_change\n          displayName: dont_change\n        - id: none_of_those\n          displayName: none_of_those\n        - id: main_menu\n          displayName: main_menu\n        - id: start_over\n          displayName: start_over\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/RP0520_nm1_01.wav\\\">You can say 'repeat that', 'none of those', or 'main menu'  </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/RP0520_nm2_02.wav\\\">You can also say more details, none of those, or don t change my plan</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/RP0520_nm2_02.wav\\\">You can also say more details, none of those, or don t change my plan</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.FirstTimeRP0520\n  value: GlobalVars.FirstTimeRP0520\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.choseInvalidPlan\n  value: GlobalVars.choseInvalidPlan\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.declineChangeCounter\n  value: GlobalVars.declineChangeCounter\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.revisit\n  value: \"GlobalVars.revisit == undefined ? false : GlobalVars.revisit\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.plansList\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.subPlansList\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.finalList\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.planType\n  value: Both\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.reentry\n  value: \"GlobalVars.RP0520reentry == undefined ? false : GlobalVars.RP0520reentry\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.Var_RP0520_AskRatePlan_DM\n  value: =Text(Global.RP0520_AskRatePlan_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_RP0520_AskRatePlan_DM = \"dont_change\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: GlobalVars.FirstTimeRP0520\n          value: false\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: GlobalVars.RP0520reentry\n          value: true\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/RP0520_out_01.wav\\\">No problem</audio>\"\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.RatePlan_Change.dvxml#RP1335_GoToAnythingElse_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_RP0520_AskRatePlan_DM = \"none_of_those\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.noneOfThoseCount\n              value: noneOfThoseCount + 1\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: GlobalVars.revisit\n              value: true\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.noneOfThoseCount < 2, true, false)\n                  actions:\n                    - kind: ConditionGroup\n                      id: conditionGroup_REPLACE_THIS\n                      conditions:\n                        - id: conditionItem_REPLACE_THIS\n                          condition: =If(GlobalVars.ratePlans.length <= 6, true, false)\n                          actions:\n                            - kind: BeginDialog\n                              id: begin_REPLACE_THIS\n                              dialog: topic.RatePlan_Main.dvxml#RP1280_GoToCallTransfer_SD\n                      elseActions:\n                        - kind: ConditionGroup\n                          id: conditionGroup_REPLACE_THIS\n                          conditions:\n                            - id: conditionItem_REPLACE_THIS\n                              condition: =If(GlobalVars.ratePlans.length = 7, true, false)\n                              actions:\n                                - kind: SendActivity\n                                  id: sendActivity_REPLACE_THIS\n                                  activity:\n                                    speak:\n                                      - \"<audio src=\\\"AUDIO_LOCATION/RP0520_out_03.wav\\\">Ok I have one more plan For </audio>\"\n                                - kind: GotoAction\n                                  id: goto_REPLACE_THIS\n                                  actionId: RP0520_AskRatePlan_DM\n                            - id: conditionItem_REPLACE_THIS\n                              condition: =If(GlobalVars.ratePlans.length > 7, true, false)\n                              actions:\n                                - kind: SendActivity\n                                  id: sendActivity_REPLACE_THIS\n                                  activity:\n                                    speak:\n                                      - \"<audio src=\\\"AUDIO_LOCATION/RP0520_out_04.wav\\\">Ok, here are the last ones For </audio>\"\n                                - kind: GotoAction\n                                  id: goto_REPLACE_THIS\n                                  actionId: RP0520_AskRatePlan_DM\n                  elseActions:\n                    - kind: BeginDialog\n                      id: begin_REPLACE_THIS\n                      dialog: topic.RatePlan_Main.dvxml#RP1280_GoToCallTransfer_SD\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_RP0520_AskRatePlan_DM = \"main_menu\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: getReturnLink()\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_RP0520_AskRatePlan_DM = \"start_over\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: GlobalVars.FirstTimeRP0520\n                      value: true\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: GlobalVars.RP0520reentry\n                      value: false\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: GlobalVars.revisit\n                      value: false\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: RP0520_AskRatePlan_DM\n\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.Var_RP0520_AskRatePlan_DM = \"default\", true, false)\n                      actions:\n                        - kind: ConditionGroup\n                          id: conditionGroup_REPLACE_THIS\n                          conditions:\n                            - id: conditionItem_REPLACE_THIS\n                              condition: =If(Global.RP0520_AskRatePlan_DM.nbestresults <> undefined, true, false)\n                              actions:\n                                - kind: SetVariable\n                                  id: setVariable_REPLACE_THIS\n                                  variable: GlobalVars.ratePlanSelectionType\n                                  value: RP0520_AskRatePlan_DM.nbestresults[0].interpretation.selectionType\n                                - kind: SetVariable\n                                  id: setVariable_REPLACE_THIS\n                                  variable: GlobalVars.nbestresults\n                                  value: RP0520_AskRatePlan_DM.nbestresults\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: GlobalVars.selectedPlan\n                          value: RP0520_AskRatePlan_DM.returnvalue\n                        - kind: ConditionGroup\n                          id: conditionGroup_REPLACE_THIS\n                          conditions:\n                            - id: conditionItem_REPLACE_THIS\n                              condition: =If(GlobalVars.ratePlanSelectionType <> undefined && GlobalVars.ratePlanSelectionType = \"dtmfOption\", true, false)\n                              actions:\n                                - kind: GotoAction\n                                  id: goto_REPLACE_THIS\n                                  actionId: RP0526_DescribePlanAskChange_DM\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: RP1050_CheckDisambigNeeded_JDA_DA\n```", "timestamp": 1749558499.9871173, "content_hash": "f8b05669d4a6315f91dcdd282bf67553"}