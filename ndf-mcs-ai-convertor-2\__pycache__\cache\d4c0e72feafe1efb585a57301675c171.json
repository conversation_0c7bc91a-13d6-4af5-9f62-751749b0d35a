{"response": "```yaml\n- kind: Question\n  id: SP1314_RepeatOtherPayment_DM\n  displayName: SP1314_RepeatOtherPayment_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.SP1314_RepeatOtherPayment_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.callType = \"make_pmt_auto_pay\" && Global.dueImmediatelyAmount < 3 && !(Global.amountDue > 0),\n            \"To hear that again say 'repeat that', otherwise to continue  to autopay set up say 'I'm done here'\",\n            (!(Global.callType = \"make_pmt_auto_pay\" && Global.dueImmediatelyAmount < 3 && !(Global.amountDue > 0)) && Global.isAutopayEnabled = false),\n            \"Say 'repeat that', 'make another payment', 'set up autopay', or  'I'm done'\",\n            (!(Global.callType = \"make_pmt_auto_pay\" && Global.dueImmediatelyAmount < 3 && !(Global.amountDue > 0)) && Global.isAutopayEnabled = true),\n            \"Say 'repeat that', 'make another payment', 'I'm done'\",\n            \"Say 'repeat that', 'make another payment', 'I'm done'\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: repeat\n          displayName: repeat\n        - id: make-payment\n          displayName: make-payment\n        - id: done\n          displayName: done\n        - id: setup-autopay\n          displayName: setup-autopay\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n              true,\n              Global.callType = \"make_pmt_auto_pay\" && Global.dueImmediatelyAmount < 3 && !(Global.amountDue > 0),\n              \"To hear that again say 'repeat that', otherwise to continue  to autopay set up say 'I'm done here'\",\n              (!(Global.callType = \"make_pmt_auto_pay\" && Global.dueImmediatelyAmount < 3 && !(Global.amountDue > 0)) && Global.isAutopayEnabled = false),\n              \"Say 'repeat that', 'make another payment', 'set up autopay', or  'I'm done'\",\n              (!(Global.callType = \"make_pmt_auto_pay\" && Global.dueImmediatelyAmount < 3 && !(Global.amountDue > 0)) && Global.isAutopayEnabled = true),\n              \"Say 'repeat that', 'make another payment', 'I'm done'\",\n              \"Say 'repeat that', 'make another payment', 'I'm done'\"\n          )}\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SP1314_nm2_01.wav\\\">To hear your confirmation again, say 'repeat' or press 1 To make another payment, say 'another payment' or press 2 To move on, say 'I'm done here' or press 3</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SP1314_nm2_02.wav\\\">To hear that again say 'repeat that', or press 1  otherwise to continue  to autopay set up say 'I'm done here' or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SP1314_nm2_03.wav\\\">To hear your confirmation again, say 'repeat' or press 1 To make another payment, say 'another payment' or press 2 To move on, say 'I'm done here' or press 3</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SP1314_nm2_04.wav\\\">Please say  'repeat that' or press 1 'make another payment', or press 2  'set up autopay', or press 3  or  say 'I'm done'  or press 4</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SP1314_nm2_01.wav\\\">To hear your confirmation again, say 'repeat' or press 1 To make another payment, say 'another payment' or press 2 To move on, say 'I'm done here' or press 3</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SP1314_nm2_02.wav\\\">To hear that again say 'repeat that', or press 1  otherwise to continue  to autopay set up say 'I'm done here' or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SP1314_nm2_03.wav\\\">To hear your confirmation again, say 'repeat' or press 1 To make another payment, say 'another payment' or press 2 To move on, say 'I'm done here' or press 3</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SP1314_nm2_04.wav\\\">Please say  'repeat that' or press 1 'make another payment', or press 2  'set up autopay', or press 3  or  say 'I'm done'  or press 4</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.guestPayment\n  value: GlobalVars.guestPayment\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.callType\n  value: GlobalVars.callType\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.dueImmediatelyAmount\n  value: GlobalVars.GetAccountDetails.dueImmediatelyAmount\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.payments_enable_prepaid_methods\n  value: GlobalVars.GetBCSParameters.payments_enable_prepaid_methods\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.amountDue\n  value: GlobalVars.GetAccountDetails.balance\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.isAutopayEnabled\n  value: GlobalVars.GetAccountDetails.isAutopayEnabled\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.isAutopayEnrolled\n  value: GlobalVars.GetAccountDetails.isAutopayEnabled\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.isAutopayEligPlanExists\n  value: GlobalVars.GetAccountDetails.isAutopayEligPlanExists\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.payingWithPrepaid\n  value: \"GlobalVars.payingWithPrepaid != undefined ? GlobalVars.payingWithPrepaid : false\"\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_SP1314_RepeatOtherPayment_DM\n  value: =Text(Global.SP1314_RepeatOtherPayment_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_SP1314_RepeatOtherPayment_DM = \"repeat\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/SP1314_out_01.wav\\\">Sure</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: SP1313_PlayBalanceUpdate_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_SP1314_RepeatOtherPayment_DM = \"make-payment\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.guestPayment = false && (Global.dueImmediatelyAmount > 3), true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.paymentAmount\n                      value: GlobalVars.GetAccountDetails.dueImmediatelyAmount\n              elseActions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.paymentAmount\n                  value: 0.00\n\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.payments_enable_prepaid_methods = true || Global.payments_enable_prepaid_methods = \"true\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.offerPrepaid\n                      value: true\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.paymentErrorCount\n              value: 0\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.payingWithEWallet\n              value: false\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.bankCardNumber\n              value: undefined\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.bankCardDate\n              value: undefined\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.bankCardCVV\n              value: undefined\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.bankCardZip\n              value: undefined\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.cardStatus\n              value: undefined\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.tryOtherCardReason\n              value: undefined\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.preferredPaymentMethod\n              value: undefined\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.failedChecksum\n              value: false\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.payingWithPrepaid\n              value: false\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.fromAnotherPaymentMenu\n              value: true\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.skipBalance\n              value: false\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: SP1205_PaymentAmount_SD\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_SP1314_RepeatOtherPayment_DM = \"done\", true, false)\n              actions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.callType = \"make_pmt_auto_pay\", true, false)\n                      actions:\n                        - kind: ConditionGroup\n                          id: conditionGroup_REPLACE_THIS\n                          conditions:\n                            - id: conditionItem_REPLACE_THIS\n                              condition: =If(Global.dueImmediatelyAmount > 3 || Global.amountDue > 0, true, false)\n                              actions:\n                                - kind: SetVariable\n                                  id: setVariable_REPLACE_THIS\n                                  variable: Global.GlobalVars.callType\n                                  value: undefined\n                                - kind: GotoAction\n                                  id: goto_REPLACE_THIS\n                                  actionId: SP1312_PostPaymentRouting_JDA_DA\n                          elseActions:\n                            - kind: GotoAction\n                              id: goto_REPLACE_THIS\n                              actionId: SP1328_AutoPay_SD\n                      elseActions:\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.GlobalVars.callType\n                          value: undefined\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: SP1312_PostPaymentRouting_JDA_DA\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_SP1314_RepeatOtherPayment_DM = \"setup-autopay\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.tag\n                      value: setup-autopay\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: SP1328_AutoPay_SD\n```", "timestamp": **********.105345, "content_hash": "d4c0e72feafe1efb585a57301675c171"}