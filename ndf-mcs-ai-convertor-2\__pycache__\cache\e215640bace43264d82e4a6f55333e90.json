{"response": "```yaml\n- kind: Question\n  id: sa0210_SimCardAvailable_DM\n  displayName: sa0210_SimCardAvailable_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/sa0210_ni1_01.wav\\\">Do you already have the new SIM card? Please say Yes or No</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/sa0210_ni2_01.wav\\\">If you already have the new SIM card with you, press 1 Otherwise, press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.sa0210_SimCardAvailable_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/sa0210_ini_01.wav\\\">Do you already have a new Sim?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/sa0210_ni1_01.wav\\\">Do you already have the new SIM card? Please say Yes or No</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/sa0210_ni2_01.wav\\\">If you already have the new SIM card with you, press 1 Otherwise, press 2</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_sa0210_SimCardAvailable_DM\n  value: =Text(Global.sa0210_SimCardAvailable_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_sa0210_SimCardAvailable_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/sa0210_out_01.wav\\\">Alright Ill need to connect you to someone who can help</audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.bypassPinReset\n          value: true\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M09_MainMenu_Dialog\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_sa0210_SimCardAvailable_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/sa0210_out_02.wav\\\">We apologize but we cannot assist you any further until you have the new SIM card ready</audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/sa0210_out_03.wav\\\">You can get a new SIM card or E-SIM at one of our stores, or from another online retailer</audio>\"\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.M09_MainMenu_Dialog\n```", "timestamp": 1749472460.4203374, "content_hash": "e215640bace43264d82e4a6f55333e90"}