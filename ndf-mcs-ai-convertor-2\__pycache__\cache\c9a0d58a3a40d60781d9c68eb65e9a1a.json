{"response": "```yaml\n- kind: Question\n  id: DT1020_PayNowYN_DM\n  displayName: DT1020_PayNowYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DT1020_nm1_01.wav\\\">Would you like to pay over the phone *now*? </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DT1020_nm2_01.wav\\\">If you'd like to pay by phone now, say 'yes' or press 1 Otherwise, say 'no' or press 2 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DT1020_nm2_01.wav\\\">If you'd like to pay by phone now, say 'yes' or press 1 Otherwise, say 'no' or press 2 </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.DT1020_PayNowYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/DT1020_ini_01.wav\\\">Alright! I added your top-up! </audio><audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio><audio src=\\\"AUDIO_LOCATION/DT1020_ini_02.wav\\\">Are you ready to pay now? </audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/DT1020_ni1_01.wav\\\">Would you like to pay over the phone *now*? </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DT1020_nm2_01.wav\\\">If you'd like to pay by phone now, say 'yes' or press 1 Otherwise, say 'no' or press 2 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DT1020_nm2_01.wav\\\">If you'd like to pay by phone now, say 'yes' or press 1 Otherwise, say 'no' or press 2 </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_DT1020_PayNowYN_DM\n  value: =Text(Global.DT1020_PayNowYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_DT1020_PayNowYN_DM = \"yes\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: make_pmt\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.acceptPayByPhone\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.dataTopUpPayment\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.paymentAmount\n          value: GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.topUp_feature1_price:0.0\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: DT1025_MakePayment_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_DT1020_PayNowYN_DM = \"no\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: DT1026_CheckFromNLU_JDA_DA\n```", "timestamp": 1749558241.2090263, "content_hash": "c9a0d58a3a40d60781d9c68eb65e9a1a"}