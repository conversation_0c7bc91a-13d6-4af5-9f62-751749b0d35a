{"response": "```yaml\n- kind: Question\n  id: IU1006_AskIfPhoneAvailable_DM\n  displayName: IU1006_AskIfPhoneAvailable_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/IU1006_match1_01.wav\\\">Do you have access to your Metro activated Phone?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/IU1006_nm2_01.wav\\\">If you have access to your Metro activated phone say 'yes' or press 1 If not say 'no' or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/IU1006_nm2_01.wav\\\">If you have access to your Metro activated phone say 'yes' or press 1 If not say 'no' or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.IU1006_AskIfPhoneAvailable_DM_reco\n  prompt:\n    speak:\n      - \"{Switch(         true,         Global.IU1006saidOperator <> true,         \\\"Do you have access to your Metro activated phone?\\\"     )}\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/IU1006_match1_01.wav\\\">Do you have access to your Metro activated Phone?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/IU1006_nm2_01.wav\\\">If you have access to your Metro activated phone say 'yes' or press 1 If not say 'no' or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/IU1006_nm2_01.wav\\\">If you have access to your Metro activated phone say 'yes' or press 1 If not say 'no' or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.requestOperatorUnidentified\n  value: GlobalVars.requestOperatorUnidentified\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.IU1006saidOperator\n  value: GlobalVars.IU1006saidOperator\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_IU1006_AskIfPhoneAvailable_DM\n  value: =Text(Global.IU1006_AskIfPhoneAvailable_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_IU1006_AskIfPhoneAvailable_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.providedFindMDNhint\n          value: true\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/IU1006_out_01.wav\\\">Great On or Metro activated phone, dial pound N U M pound to find your 10-digit phone number Again that is pound, N U M, Pound</audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_3000ms.wav\\\">test</audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: IU1005_GetMDN_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_IU1006_AskIfPhoneAvailable_DM = \"false\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: IU1065_GoToTransfer_SD\n```", "timestamp": 1749530265.2239878, "content_hash": "61607a3ea58c262ad584f736eccc4382"}