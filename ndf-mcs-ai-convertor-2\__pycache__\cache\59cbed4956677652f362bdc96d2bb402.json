{"response": "```yaml\n- kind: Question\n  id: MW1315_GetVerificationCode_DM\n  displayName: MW1315_GetVerificationCode_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1315_nm1_01.wav\\\">Please say or enter the verification code on the card</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"{Switch(\n            true,\n            Global.cardTypeAmex = true,\n            \\\"Please enter the four-digit verification code on the front of your card\\\",\n            Global.cardTypeAmex <> true,\n            \\\"Please enter the three-digit verification code on the back of your card\\\"\n        )}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"{Switch(\n            true,\n            Global.cardTypeAmex = true,\n            \\\"Please enter the four-digit verification code on the front of your card\\\",\n            Global.cardTypeAmex <> true,\n            \\\"Please enter the three-digit verification code on the back of your card\\\"\n        )}\"\n\n  alwaysPrompt: true\n  variable: Global.MW1315_GetVerificationCode_DM_reco\n  prompt:\n    speak:\n      - \"{Switch(\n            true,\n            Global.cardTypeAmex = true,\n            \\\"Now, the four-digit verification code\\\",\n            Global.cardTypeAmex <> true,\n            \\\"Now, the three digit verification code\\\"\n        )}\"\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1315_nm1_01.wav\\\">Please say or enter the verification code on the card</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"{Switch(\n              true,\n              Global.cardTypeAmex = true,\n              \\\"Please enter the four-digit verification code on the front of your card\\\",\n              Global.cardTypeAmex <> true,\n              \\\"Please enter the three-digit verification code on the back of your card\\\"\n          )}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"{Switch(\n              true,\n              Global.cardTypeAmex = true,\n              \\\"Please enter the four-digit verification code on the front of your card\\\",\n              Global.cardTypeAmex <> true,\n              \\\"Please enter the three-digit verification code on the back of your card\\\"\n          )}\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.cardTypeAmex\n  value: GlobalVars.cardTypeAmex\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.bankCardCVV\n  value: MW1315_GetVerificationCode_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.VerificationCode_voiceOrDtmf\n  value: application.lastresult$.inputmode\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: MW1400_GetBillingZipCode_DM\n```", "timestamp": **********.3118021, "content_hash": "59cbed4956677652f362bdc96d2bb402"}