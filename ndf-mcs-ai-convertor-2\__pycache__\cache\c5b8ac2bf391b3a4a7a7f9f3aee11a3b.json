{"response": "```yaml\n- kind: Question\n  id: aa4025_TXandWVMainMenuDTMF_DM\n  displayName: aa4025_TXandWVMainMenuDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa4025_TXandWVMainMenuDTMF_DM_initial.wav\\\"></audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/aa4025_TXandWVMainMenuDTMF_DM_initial.wav\\\"></audio>\"\n\n  alwaysPrompt: true\n  variable: Global.aa4025_TXandWVMainMenuDTMF_DM_reco\n  prompt:\n    speak:\n      - \"{CustomPrompt: com.nuance.fis.audio.aa4025_TXandWVMainMenuDTMF_DM_Render2}\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: transaction_history\n          displayName: transaction_history\n        - id: benefit_schedule\n          displayName: benefit_schedule\n        - id: change_pin\n          displayName: change_pin\n        - id: last_deposits\n          displayName: last_deposits\n        - id: other_Options\n          displayName: other_Options\n        - id: missing_card\n          displayName: missing_card\n        - id: freeze_card\n          displayName: freeze_card\n        - id: unfreeze_card\n          displayName: unfreeze_card\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/aa4025_TXandWVMainMenuDTMF_DM_initial.wav\\\"></audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/aa4025_TXandWVMainMenuDTMF_DM_initial.wav\\\"></audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa4025_TXandWVMainMenuDTMF_DM\n  value: =Text(Global.aa4025_TXandWVMainMenuDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa4025_TXandWVMainMenuDTMF_DM = \"transaction_history\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.currentTask\n          value: TransactionHistory\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.nextStep\n          value: transaction_history\n        # Event log script EBT008 is not a variable, so not included as SetVariable\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: return\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa4025_TXandWVMainMenuDTMF_DM = \"benefit_schedule\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.currentTask\n              value: BenefitSchedule\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.nextStep\n              value: benefit_schedule\n            # Event log script EBT011 is not a variable, so not included as SetVariable\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: return\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_aa4025_TXandWVMainMenuDTMF_DM = \"change_pin\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.cardInfoVariables.pinChangeOffered\n                  value: true\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.globalVariables.currentTask\n                  value: ChangePin\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.globalVariables.nextStep\n                  value: change_pin\n                # Event log script EBT021 is not a variable, so not included as SetVariable\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: return\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_aa4025_TXandWVMainMenuDTMF_DM = \"last_deposits\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.globalVariables.typeOfHistory\n                      value: DE\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.globalVariables.currentTask\n                      value: LastDeposits\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.globalVariables.nextStep\n                      value: last_deposits\n                    # Event log script EBT060 is not a variable, so not included as SetVariable\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: return\n\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.Var_aa4025_TXandWVMainMenuDTMF_DM = \"other_Options\", true, false)\n                      actions:\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.globalVariables.currentTask\n                          value: OtherOptions\n                        # Event log script EBT037 is not a variable, so not included as SetVariable\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: aa4104_isPersonalization_JDA_DA\n\n                  elseActions:\n                    - kind: ConditionGroup\n                      id: conditionGroup_REPLACE_THIS\n                      conditions:\n                        - id: conditionItem_REPLACE_THIS\n                          condition: =If(Global.Var_aa4025_TXandWVMainMenuDTMF_DM = \"missing_card\", true, false)\n                          actions:\n                            - kind: SetVariable\n                              id: setVariable_REPLACE_THIS\n                              variable: Global.cardInfoVariables.collectedLostStolenCardNumber\n                              value: cardInfoVariables.collectedCardNumber\n                            - kind: SetVariable\n                              id: setVariable_REPLACE_THIS\n                              variable: Global.globalVariables.currentTask\n                              value: cardReplacement\n                            - kind: SetVariable\n                              id: setVariable_REPLACE_THIS\n                              variable: Global.globalVariables.nextStep\n                              value: card_replacement\n                            - kind: SetVariable\n                              id: setVariable_REPLACE_THIS\n                              variable: Global.cardInfoVariables.tranType\n                              value: L\n                            # Event log script EBT200 is not a variable, so not included as SetVariable\n                            - kind: SendActivity\n                              id: sendActivity_REPLACE_THIS\n                              activity:\n                                speak:\n                                  - \"<audio src=\\\"AUDIO_LOCATION/aa4025_out_01.wav\\\">Sure, I can assist in cancelling your lost, stolen or damaged card 1 second pause  To do that, I will need some additional information</audio>\"\n                            - kind: GotoAction\n                              id: goto_REPLACE_THIS\n                              actionId: return\n\n                      elseActions:\n                        - kind: ConditionGroup\n                          id: conditionGroup_REPLACE_THIS\n                          conditions:\n                            - id: conditionItem_REPLACE_THIS\n                              condition: =If(Global.Var_aa4025_TXandWVMainMenuDTMF_DM = \"freeze_card\", true, false)\n                              actions:\n                                - kind: SetVariable\n                                  id: setVariable_REPLACE_THIS\n                                  variable: Global.globalVariables.currentTask\n                                  value: freezeCard\n                                - kind: SetVariable\n                                  id: setVariable_REPLACE_THIS\n                                  variable: Global.globalVariables.freezeUnfreezeOffered\n                                  value: true\n                                - kind: SetVariable\n                                  id: setVariable_REPLACE_THIS\n                                  variable: Global.globalVariables.nextStep\n                                  value: freeze_card\n                                # Event log script EBT338 is not a variable, so not included as SetVariable\n                                - kind: SendActivity\n                                  id: sendActivity_REPLACE_THIS\n                                  activity:\n                                    speak:\n                                      - \"<audio src=\\\"AUDIO_LOCATION/aa4025_out_02.wav\\\">Alright You or anyone else will be unable to complete any transactions while your card is frozen In order to complete any transactions, you will have to unfreeze your card, which you can do at any time I'll assist you in freezing your card now</audio>\"\n                                - kind: GotoAction\n                                  id: goto_REPLACE_THIS\n                                  actionId: return\n\n                          elseActions:\n                            - kind: ConditionGroup\n                              id: conditionGroup_REPLACE_THIS\n                              conditions:\n                                - id: conditionItem_REPLACE_THIS\n                                  condition: =If(Global.Var_aa4025_TXandWVMainMenuDTMF_DM = \"unfreeze_card\", true, false)\n                                  actions:\n                                    - kind: SetVariable\n                                      id: setVariable_REPLACE_THIS\n                                      variable: Global.globalVariables.currentTask\n                                      value: freezeCard\n                                    - kind: SetVariable\n                                      id: setVariable_REPLACE_THIS\n                                      variable: Global.globalVariables.freezeUnfreezeOffered\n                                      value: true\n                                    - kind: SetVariable\n                                      id: setVariable_REPLACE_THIS\n                                      variable: Global.globalVariables.nextStep\n                                      value: freeze_card\n                                    # Event log script EBT339 is not a variable, so not included as SetVariable\n                                    - kind: SendActivity\n                                      id: sendActivity_REPLACE_THIS\n                                      activity:\n                                        speak:\n                                          - \"<audio src=\\\"AUDIO_LOCATION/aa4025_out_03.wav\\\">Alright I can assist you in unfreezing your card Once your card is unfrozen, you will be able to complete any transactions</audio>\"\n                                    - kind: GotoAction\n                                      id: goto_REPLACE_THIS\n                                      actionId: return\n```", "timestamp": 1749556888.438457, "content_hash": "c5b8ac2bf391b3a4a7a7f9f3aee11a3b"}