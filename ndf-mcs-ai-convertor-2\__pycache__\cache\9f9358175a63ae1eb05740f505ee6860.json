{"response": "```yaml\n- kind: Question\n  id: TT1035_WiFiTipWaitSBI_DM\n  displayName: TT1035_WiFiTipWaitSBI_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.TT1035_WiFiTipWaitSBI_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/TT1035_ini_01.wav\\\">Ok, please open your device's Settings and turn off Wi-Fi I'll wait for you to do that If you still have the same problem, say 'it's not fixed' If the problem is corrected, feel free to hang up You can also say 'skip this step' You're turning off Wi-Fi in your device's settings If you still have the same problem after you've done that, say 'it's not fixed' If the problem is corrected, feel free to hang up You can say 'it's not fixed' or press 1 at any time or you can just hang up When you're ready, say 'it's not fixed' or press 1 or if your problem is fixed, you can just hang up You're turning off Wi-Fi in your device's settings If you still have the same problem after you've done that, say 'it's not fixed' or press 1 If the problem is corrected, feel free to hang up When you're ready, say 'it's not fixed' or press 1 or you can just hang up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: not_fixed\n          displayName: not_fixed\n        - id: skip\n          displayName: skip\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_TT1035_WiFiTipWaitSBI_DM\n  value: =Text(Global.TT1035_WiFiTipWaitSBI_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TT1035_WiFiTipWaitSBI_DM = \"not_fixed\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.transferDestination = \"agent\", true, false)\n              actions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.usingBrokenDevice = \"yes\", true, false)\n                      actions:\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: TT1040_TransferOffer_DM\n                  elseActions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.maxRetryTroubleshooting\n                      value: false\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: TT1045_Transfer_PP\n          elseActions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: TT1038_StoreOffer_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_TT1035_WiFiTipWaitSBI_DM = \"skip\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.transferDestination = \"agent\", true, false)\n                  actions:\n                    - kind: ConditionGroup\n                      id: conditionGroup_REPLACE_THIS\n                      conditions:\n                        - id: conditionItem_REPLACE_THIS\n                          condition: =If(Global.usingBrokenDevice = \"yes\", true, false)\n                          actions:\n                            - kind: GotoAction\n                              id: goto_REPLACE_THIS\n                              actionId: TT1040_TransferOffer_DM\n                      elseActions:\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.GlobalVars.maxRetryTroubleshooting\n                          value: false\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: TT1045_Transfer_PP\n              elseActions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: TT1038_StoreOffer_DM\n```", "timestamp": 1749530394.3662121, "content_hash": "9f9358175a63ae1eb05740f505ee6860"}