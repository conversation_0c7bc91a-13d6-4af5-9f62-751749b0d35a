{"response": "```yaml\n- kind: Question\n  id: aa6536_TransactionHistoryWrapUp_DM\n  displayName: aa6536_TransactionHistoryWrapUp_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.aa6536_TransactionHistoryWrapUp_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa6536_ini_01.wav\\\">To hear that again, say  Repeat That  For other kinds of transactions,  Other Kinds  To get a Statement,  I want a Statement  To dispute a transaction, say  I have a dispute  For the main menu, say  Main Menu  And if you re done, feel free to hang up</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: other\n          displayName: other\n        - id: repeat\n          displayName: repeat\n        - id: disputes\n          displayName: disputes\n        - id: main_menu\n          displayName: main_menu\n        - id: statements\n          displayName: statements\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa6536_TransactionHistoryWrapUp_DM\n  value: =Text(Global.aa6536_TransactionHistoryWrapUp_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa6536_TransactionHistoryWrapUp_DM = \"other\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa6510_TransactionHistoryMainMenuDTMF_DM\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aa6536_out_03.wav\\\">Okay</audio>\"\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa6536_TransactionHistoryWrapUp_DM = \"repeat\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/aa6536_out_01.wav\\\">Again</audio>\"\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.returnCodeDetail = \"68\" || Global.returnCodeDetail = \"10\", true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: aa6520_NoTransactionsFound_PP\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.returnCodeDetail = \"11\", true, false)\n                      actions:\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: aa6525_OneTransactionsFound_PP\n                  elseActions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: aa6527_MultipleTransactionsFound_PP\n\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa6536_TransactionHistoryWrapUp_DM = \"disputes\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.transferReason\n              value: Disputes\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.transferAllowed\n              value: true\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa9805_ProcessTransfer_PP\n\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa6536_TransactionHistoryWrapUp_DM = \"main_menu\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: handleMainMenu_CS\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/aa6536_out_06.wav\\\">Sure</audio>\"\n\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa6536_TransactionHistoryWrapUp_DM = \"statements\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.transferReason\n              value: Statements\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.transferAllowed\n              value: true\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa9805_ProcessTransfer_PP\n```", "timestamp": 1749543857.9724257, "content_hash": "56548ec1eaa8a23f16456d033a97c371"}