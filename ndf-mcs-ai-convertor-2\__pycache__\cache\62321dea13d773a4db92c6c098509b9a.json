{"response": "```yaml\n- kind: Question\n  id: VS1025_SignUp_DM\n  displayName: VS1025_SignUp_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/VS1025_nm1_01.wav\\\">Would you like to sign up for </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/VS1025_nm1_02.wav\\\">Please say Yes or No </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/VS1025_nm2_01.wav\\\">Would you like to sign up for this? Say yes or press 1, or no or press 2 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/VS1025_nm2_01.wav\\\">Would you like to sign up for this? Say yes or press 1, or no or press 2 </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.VS1025_SignUp_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n\n            // Case 1: playOneFeature = true && isThirdPartyFeature <> true\n            Global.playOneFeature = true && Global.isThirdPartyFeature <> true,\n            \"<audio src=\\\"AUDIO_LOCATION/VS1025_ini_01.wav\\\">Theres one feature you can sign up for</audio>\",\n\n            // Case 2: playOneFeature = true && isThirdPartyFeature = true\n            Global.playOneFeature = true && Global.isThirdPartyFeature = true,\n            \"<audio src=\\\"AUDIO_LOCATION/VS1025_ini_02.wav\\\">Theres one third party feature you can sign up for</audio>\",\n\n            // Case 3: playOneFeature = true\n            Global.playOneFeature = true,\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\",\n              \"{selectedFeature}\", // dynamic custom prompt\n              \"<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\"\n            ],\n\n            // Default: details, third party info, and main options\n            [\n              \"{selectedFeature}_details\", // dynamic custom prompt\n              \"<audio src=\\\"AUDIO_LOCATION/silence_1000ms.wav\\\">test</audio>\",\n              // Third party info if needed\n              (Global.isThirdPartyFeature = true && Global.heardThirdPartyInfo <> true) ? \"<audio src=\\\"AUDIO_LOCATION/VS1025_ini_04.wav\\\">Third party charges will appear separately on your bill To block those purchases, check your account settings on metrobyt-mobilecom, or say operator now to find out more</audio>\" : \"\",\n              (Global.isThirdPartyFeature = true && (Global.fromIOM = true || Global.heardThirdPartyInfo <> true)) ? \"<audio src=\\\"AUDIO_LOCATION/silence_1000ms.wav\\\">test</audio>\" : \"\",\n              \"<audio src=\\\"AUDIO_LOCATION/VS1025_ini_11.wav\\\">Would you like to hear that again, sign up, or cancel?</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/VS1025_ini_12.wav\\\">You can also say 'repeat that'</audio>\"\n            ]\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/VS1025_nm1_01.wav\\\">Would you like to sign up for </audio>\"\n        - \"{selectedFeature}_medial\" # dynamic custom prompt\n        - |\n          {Switch(\n            true,\n            Global.fromIOM = true,\n            \"<audio src=\\\"AUDIO_LOCATION/VS1025_nm1_03.wav\\\">say 'repeat' Otherwise, please say 'sign Up' or 'skip it'</audio>\",\n            true,\n            \"<audio src=\\\"AUDIO_LOCATION/VS1025_nm1_02.wav\\\">Please say Yes or No </audio>\"\n          )}\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"{selectedFeature}_details\" # dynamic custom prompt\n        - |\n          {Switch(\n            true,\n            Global.fromIOM = true,\n            \"<audio src=\\\"AUDIO_LOCATION/VS1025_nm2_02.wav\\\">Please say 'sign up' or press 1, or say 'skip it' or press 2</audio>\",\n            true,\n            \"<audio src=\\\"AUDIO_LOCATION/VS1025_nm2_01.wav\\\">Would you like to sign up for this? Say yes or press 1, or no or press 2 </audio>\"\n          )}\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"{selectedFeature}_details\" # dynamic custom prompt\n        - |\n          {Switch(\n            true,\n            Global.fromIOM = true,\n            \"<audio src=\\\"AUDIO_LOCATION/VS1025_nm2_02.wav\\\">Please say 'sign up' or press 1, or say 'skip it' or press 2</audio>\",\n            true,\n            \"<audio src=\\\"AUDIO_LOCATION/VS1025_nm2_01.wav\\\">Would you like to sign up for this? Say yes or press 1, or no or press 2 </audio>\"\n          )}\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.selectedFeature\n  value: GlobalVars.selectedFeature\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.isThirdPartyFeature\n  value: isSelectedFeatureAThirdPartyFeature(GlobalVars.GetAvailableFeatureOffers.featureSocsThirdParty, GlobalVars.selectedFeature)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.playOneFeature\n  value: GlobalVars.playOneFeature\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.fromIOM\n  value: GlobalVars.fromIOM\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.heardThirdPartyInfo\n  value: GlobalVars.heardThirdPartyInfo\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.addFeatureAction\n  value: \"\"\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_VS1025_SignUp_DM\n  value: =Text(Global.VS1025_SignUp_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.isThirdPartyFeature = true, true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.heardThirdPartyInfo\n          value: true\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_VS1025_SignUp_DM = \"yes\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.addFeatureAction = \"Custom after selected\", true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: VS1205_CheckServiceIndicatorAddMessage_JDA_DA\n\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.addFeatureAction = \"Transfer after selected\", true, false)\n                  actions:\n                    - kind: BeginDialog\n                      id: begin_REPLACE_THIS\n                      dialog: topic.Voicestore_Routing.dvxml#VS1315_GoToCallTransfer_SD\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.securityRequired\n              value: true\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.addFeatures\n              value: GlobalVars.selectedFeature\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: VS1035_CheckNeedPIN_JDA_DA\n\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_VS1025_SignUp_DM = \"no\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: VS1045_AnotherFeature_DM\n\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_VS1025_SignUp_DM = \"repeat\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: VS1025_SignUp_DM\n```\n**Notes:**\n- All audio references are formatted as `<audio src=\"AUDIO_LOCATION/filename.wav\">text</audio>`.\n- Dynamic custom prompts like `{selectedFeature}` and `{selectedFeature}_details` are placeholders for the custom audio rendering logic.\n- All session-mapping keys are mapped to `SetVariable` actions.\n- The nested if/else logic in `<success>` is represented as nested `ConditionGroup` and `GotoAction`/`BeginDialog` as per your instructions.\n- The prompt logic is condensed into a PowerFx-like Switch statement, with comments for clarity.\n- The entity is set to `BooleanPrebuiltEntity` as the success labels are \"yes\" and \"no\".\n- All ids for SetVariable, ConditionGroup, etc., are left as `REPLACE_THIS` for you to fill in as needed.", "timestamp": 1749558684.9157183, "content_hash": "62321dea13d773a4db92c6c098509b9a"}