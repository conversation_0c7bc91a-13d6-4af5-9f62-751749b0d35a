{"response": "```yaml\n- kind: Question\n  id: NP1010_AskLanguage_DM\n  displayName: NP1010_AskLanguage_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.NP1010_AskLanguage_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/NP1010_ini_01.wav\\\">Para continuar en espa�ol, marque '9'</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: Spanish\n          displayName: Spanish\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.langAskedAlready\n  value: true\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_NP1010_AskLanguage_DM\n  value: =Text(Global.NP1010_AskLanguage_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_NP1010_AskLanguage_DM = \"Spanish\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.language\n          value: es-US\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: NP1012_GoToInitialHandling_SD\n```", "timestamp": 1749528927.424596, "content_hash": "acb0b3a15496f0918967529485affed1"}