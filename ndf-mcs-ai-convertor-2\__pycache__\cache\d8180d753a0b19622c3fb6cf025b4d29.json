{"response": "```yaml\n- kind: Question\n  id: aa6091_CCDefaultWrapUp_DM\n  displayName: aa6091_CCDefaultWrapUp_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.aa6091_CCDefaultWrapUp_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa6091_ini_01.wav\\\">To hear that again, say,  Repeat That  To go to the main menu, say  Main Menu  Or if you re done, simply hang up</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: main_menu\n          displayName: main_menu\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa6091_CCDefaultWrapUp_DM\n  value: =Text(Global.aa6091_CCDefaultWrapUp_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa6091_CCDefaultWrapUp_DM = \"main_menu\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.nextStep\n          value: main_menu\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa1015_NextStepHandling_JDA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa6091_CCDefaultWrapUp_DM = \"repeat\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.globalVariables.cameFrom = \"aa6020\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.globalVariables.cameFrom\n                      value: ''\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: aa6020_CCMustHavePinSelected_PP\n\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.globalVariables.cameFrom = \"aa6031\", true, false)\n                      actions:\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.globalVariables.cameFrom\n                          value: ''\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: aa6031_CCMustGetPIDFromProvider_PP\n\n                  elseActions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: aa6075_CCPaymentSuccessful_PP\n```", "timestamp": **********.567415, "content_hash": "d8180d753a0b19622c3fb6cf025b4d29"}