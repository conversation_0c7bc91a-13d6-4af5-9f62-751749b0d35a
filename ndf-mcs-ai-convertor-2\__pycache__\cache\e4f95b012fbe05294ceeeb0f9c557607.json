{"response": "```yaml\n- kind: Question\n  id: st1025_PowercycleWait_DM\n  displayName: st1025_PowercycleWait_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I missed that</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">Sorry</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.st1025_PowercycleWait_DM_reco\n  prompt:\n    speak:\n      - \"{CustomAudio: com.nuance.att.application.audio.st1025_PowercycleWait_DM_initial}\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: ready\n          displayName: ready\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_st1025_PowercycleWait_DM\n  value: =Text(Global.st1025_PowercycleWait_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_st1025_PowercycleWait_DM = \"ready\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: st1032_TestSolution_PP\n```", "timestamp": 1749472334.42372, "content_hash": "e4f95b012fbe05294ceeeb0f9c557607"}