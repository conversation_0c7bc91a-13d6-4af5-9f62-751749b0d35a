{"response": "```yaml\n- kind: Question\n  id: AC1150_GetAnotherZIP_DM\n  displayName: AC1150_GetAnotherZIP_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AC1150_nm1_01.wav\\\">Tell me the zip code where youll use the phone the most</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AC1150_nm2_01.wav\\\">Say or enter the zip code where youll use the phone the most</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AC1150_nm3_01.wav\\\">Say the zip code where youll use the phone the most, or enter it using the telephone keypad</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.AC1150_GetAnotherZIP_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/AC1150_ini_01.wav\\\">It looks like we dont have service in that ZIP code In what ZIP code will you be using the phone the most?</audio>\"\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/AC1150_ni1_01.wav\\\">Sorry, I didnt hear you  Tell me the zip code where youll use the phone the most</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AC1150_ni2_01.wav\\\">Sorry, say or enter the zip code where youll use the phone the most</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AC1150_ni3_01.wav\\\">Sorry, say the zip code where youll use the phone the most, or enter it using the telephone keypad</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.noServiceInZipCount\n  value: GlobalVars.noServiceInZipCount\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.comingFromAC1130\n  value: GlobalVars.comingFromAC1130\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.zipCode\n  value: AC1150_GetAnotherZIP_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.comingFromAC1130\n  value: false\n\n- kind: SendActivity\n  id: sendActivity_REPLACE_THIS\n  activity:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/AC1150_out_01.wav\\\">Thanks again Let me check that one</audio>\"\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: AC1110_CheckCoverage_DB_DA\n```", "timestamp": 1749527402.8173356, "content_hash": "7b4442e59084c71104540542967622ca"}