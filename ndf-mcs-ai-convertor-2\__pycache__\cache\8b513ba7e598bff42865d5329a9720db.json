{"response": "```yaml\n- kind: Question\n  id: cp0120_AskAccountZipCode_DM\n  displayName: cp0120_AskAccountZipCode_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/cp0120_ni1_01.wav\\\">Please say or enter the zip code you used when activating your Prepaid account</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/cp0120_ni2_01.wav\\\">We need to confirm the zip code you used when you *first* activated your Prepaid account Please enter it now</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.cp0120_AskAccountZipCode_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n\n            Global.numAccountZipAttempts = 0 && Global.fromCustomerService <> true,\n            \"First, I need to confirm the zip code associated with your account Please say or enter it now\",\n\n            Global.numAccountZipAttempts = 1 && Global.fromCustomerService <> true,\n            \"That zip code doesnt match my records Please say or enter the zip code you used when activating your Prepaid account\",\n\n            Global.fromCustomerService = true,\n            \"Sorry Youll need to reset your account PIN before I can connect you with an agent\",\n\n            \"That zip code still doesnt match my records We need to confirm the zip code you used when you first activated your Prepaid account Please say or enter it now\"\n        )}\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/cp0120_ni1_01.wav\\\">Please say or enter the zip code you used when activating your Prepaid account</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/cp0120_ni2_01.wav\\\">We need to confirm the zip code you used when you *first* activated your Prepaid account Please enter it now</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.fromCustomerService\n  value: false\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.zipCodeEntered\n  value: cp0120_AskAccountZipCode_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.numAccountZipAttempts\n  value: numAccountZipAttempts + 1\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: cp0125_VerifyAccountZipCode_DB_DA\n```", "timestamp": **********.8100278, "content_hash": "8b513ba7e598bff42865d5329a9720db"}