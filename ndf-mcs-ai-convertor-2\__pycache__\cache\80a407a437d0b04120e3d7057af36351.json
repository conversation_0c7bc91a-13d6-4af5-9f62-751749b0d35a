{"response": "```yaml\n- kind: Question\n  id: SO1410_ApproveUpdates_DM\n  displayName: SO1410_ApproveUpdates_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SO1410_ini_04.wav\\\">Say 'approve', 'repeat' or 'cancel'  </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SO1410_nm2_02.wav\\\">To continue with this change, say 'approve' or press 1 Or say 'repeat' or press 2, or 'cancel' or press 3</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SO1410_nm2_02.wav\\\">To continue with this change, say 'approve' or press 1 Or say 'repeat' or press 2, or 'cancel' or press 3</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.SO1410_ApproveUpdates_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/SO1410_ini_04.wav\\\">Say 'approve', 'repeat' or 'cancel'  </audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: approve\n          displayName: approve\n        - id: repeat\n          displayName: repeat\n        - id: cancel\n          displayName: cancel\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SO1410_ini_04.wav\\\">Say 'approve', 'repeat' or 'cancel'  </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SO1410_nm2_02.wav\\\">To continue with this change, say 'approve' or press 1 Or say 'repeat' or press 2, or 'cancel' or press 3</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SO1410_nm2_02.wav\\\">To continue with this change, say 'approve' or press 1 Or say 'repeat' or press 2, or 'cancel' or press 3</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_SO1410_ApproveUpdates_DM\n  value: =Text(Global.SO1410_ApproveUpdates_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_SO1410_ApproveUpdates_DM = \"approve\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/SO1410_out_03.wav\\\">Great, I'll make the change</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: SO1411_SetUpdatesVars_JDA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_SO1410_ApproveUpdates_DM = \"repeat\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/SO1410_out_04.wav\\\">Sure</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: SO1405_PlayUpdates_PP\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_SO1410_ApproveUpdates_DM = \"cancel\", true, false)\n              actions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/SO1410_out_05.wav\\\">No problem, I won't change anything</audio>\"\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: SO1415_CancelPendingOrder_DB_DA\n```", "timestamp": 1749529564.9163117, "content_hash": "80a407a437d0b04120e3d7057af36351"}