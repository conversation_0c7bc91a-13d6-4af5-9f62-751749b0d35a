{"response": "```yaml\n- kind: Question\n  id: sl0220_ConfirmDevice_DM\n  displayName: sl0220_ConfirmDevice_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/sl0220_ni1_01.wav\\\">If this is the device you want to block, say Yes If not, say No To hear the name of the device again, say Repeat</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/sl0220_ni2_01.wav\\\">If you want to block this device, so that no one will be able to use it, say Yes or press 1 To block a different device, say No or press 2 To hear the device that will be blocked, say Repeat or press 3</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.sl0220_ConfirmDevice_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.accountType = \"dataOnly\" || Global.accountType = \"whp\",\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/sl0220_ini_01.wav\\\">The device you want to block is the</audio>\",\n              \"{CustomAudio: com.nuance.att.application.audio.sl0220_ConfirmDevice_initial_playDeviceMake}\",\n              \"{If(Global.deviceModel <> 'unknown' && Global.deviceModel <> '' && Global.deviceModel <> 'null', Global.deviceModel, '')}\",\n              \"<audio src=\\\"AUDIO_LOCATION/sl0220_ini_03.wav\\\">Is that right?</audio>\"\n            ],\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/sl0220_ini_02.wav\\\">The phone you want to block is the</audio>\",\n              \"{CustomAudio: com.nuance.att.application.audio.sl0220_ConfirmDevice_initial_playDeviceMake}\",\n              \"{If(Global.deviceModel <> 'unknown' && Global.deviceModel <> '' && Global.deviceModel <> 'null', Global.deviceModel, '')}\",\n              \"<audio src=\\\"AUDIO_LOCATION/sl0220_ini_03.wav\\\">Is that right?</audio>\"\n            ]\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/sl0220_ni1_01.wav\\\">If this is the device you want to block, say Yes If not, say No To hear the name of the device again, say Repeat</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/sl0220_ni2_01.wav\\\">If you want to block this device, so that no one will be able to use it, say Yes or press 1 To block a different device, say No or press 2 To hear the device that will be blocked, say Repeat or press 3</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_sl0220_ConfirmDevice_DM\n  value: =Text(Global.sl0220_ConfirmDevice_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_sl0220_ConfirmDevice_DM = \"true\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: sl0225_SuspendBlock_DB_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_sl0220_ConfirmDevice_DM = \"false\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.accountType = \"dataOnly\" || Global.accountType = \"whp\", true, false)\n                  actions:\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/sl0220_out_01.wav\\\">Ill need to connect you to someone to get the right device information</audio>\"\n              elseActions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/sl0220_out_02.wav\\\">Ill need to connect you to someone to get the right phone information</audio>\"\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.transferPrompt\n              value: short\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.transferReason\n              value: BUSINESS_RULE\n\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.M09_MainMenu_Dialog\n```", "timestamp": 1749472026.5560987, "content_hash": "d330ac1415f81ffb434fcae6a7ee6b43"}