{"response": "```yaml\n- kind: Question\n  id: EP1115_ChooseNewPlanLong_DM\n  displayName: EP1115_ChooseNewPlanLong_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/EP1115_nm1_01.wav\\\">Say </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/EP1115_nm2_01.wav\\\">Say </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/EP1115_nm2_01.wav\\\">Say </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/EP1115_nm3_01.wav\\\">Or say more information </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.EP1115_ChooseNewPlanLong_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.isOnFamilyPlan = true,\n            \"<audio src=\\\"AUDIO_LOCATION/EP1115_ini_01.wav\\\"> There are several plans you can change to </audio>\",\n            Global.numberOfPlans > 1 && Global.lowestPrice <> Global.highestPrice,\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/EP1115_ini_02.wav\\\">There are several plans you can change to, ranging in price from  </audio>\",\n              \"{Global.lowestPrice}\",\n              \"<audio src=\\\"AUDIO_LOCATION/EP1115_ini_03.wav\\\">to </audio>\",\n              \"{Global.highestPrice}\",\n              \"<audio src=\\\"AUDIO_LOCATION/EP1115_ini_04.wav\\\"> dollars a month, and the price for each plan includes all taxes and regulatory fees </audio>\"\n            ],\n            true,\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/EP1115_ini_05.wav\\\"> When you hear the one you want, just say the name of the plan back to me </audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\",\n              \"{Dynamic: ratePlanSOCs, className: com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansAndFeatures_EP1115_ChooseNewPlanLong}\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/EP1115_ini_10.wav\\\"> Now, which one would you like You can say</audio>\",\n              \"{Dynamic: ratePlanSOCs, className: com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansPlayPlanPromptURLOnly}\",\n              \"<audio src=\\\"AUDIO_LOCATION/EP1115_ini_11.wav\\\"> Or you can say repeat that to hear those options again </audio>\",\n              Global.choseInvalidPlan = true,\n              \"<audio src=\\\"AUDIO_LOCATION/EP1115_ini_12.wav\\\">If none of those are what you wanted, you can say different plan  </audio>\"\n            ]\n        )}\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/EP1115_nm1_01.wav\\\">Say </audio>{Dynamic: ratePlanSOCs, className: com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansPlayPlanPromptURLOnly}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/EP1115_nm2_01.wav\\\">Say </audio>{Dynamic: ratePlanSOCs, className: com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansOnly_EP1115_NM2}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/EP1115_nm2_01.wav\\\">Say </audio>{Dynamic: ratePlanSOCs, className: com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansOnly_EP1115_NM2}\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.ratePlanSOCs\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.numberOfPlans\n  value: numberOfRatePlans(GlobalVars.ratePlans)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.lowestPrice\n  value: getLowestRatePlanPrice(GlobalVars.ratePlans)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.highestPrice\n  value: getHighestRatePlanPrice(GlobalVars.ratePlans)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.choseInvalidPlan\n  value: GlobalVars.choseInvalidPlan\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.isOnFamilyPlan\n  value: GlobalVars.isOnFamilyPlan\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.planGrammarURL\n  value: GlobalVars.RatePlanGrammarURL\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.planGrammarDtmfURL\n  value: GlobalVars.RatePlanDTMFGrammarURL\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.RatePlanUnambiguousGrammarURL\n  value: GlobalVars.RatePlanUnambiguousGrammarURL\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.ratePlanSOCs\n  value: getAllRatePlans(GlobalVars.ratePlans)\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_EP1115_ChooseNewPlanLong_DM\n  value: =Text(Global.EP1115_ChooseNewPlanLong_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_EP1115_ChooseNewPlanLong_DM = \"more_info\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/gl_cnf_ini_01.wav\\\">Okay,</audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.reentry_EP1115_ChooseNewPlanLong_DM\n          value: true\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: EP1115_ChooseNewPlanLong_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_EP1115_ChooseNewPlanLong_DM = \"repeat\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/EP1115_out_01.wav\\\"> Sure, I ll go through those details again </audio>\"\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.reentry_EP1115_ChooseNewPlanLong_DM\n              value: true\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: EP1115_ChooseNewPlanLong_DM\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_EP1115_ChooseNewPlanLong_DM = \"different_plan\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.ActivationTable.ACTIVATION_STATUS\n                  value: 134\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: EP1020_CallTransfer_SD\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_EP1115_ChooseNewPlanLong_DM = \"default\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.bHeardInNewPlanLong\n                      value: true\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.ratePlanSelectionType\n                      value: EP1115_ChooseNewPlanLong_DM.nbestresults[0].interpretation.selectionType\n                    - kind: ConditionGroup\n                      id: conditionGroup_REPLACE_THIS\n                      conditions:\n                        - id: conditionItem_REPLACE_THIS\n                          condition: =If(Global.EP1115_ChooseNewPlanLong_DM.nbestresults <> undefined, true, false)\n                          actions:\n                            - kind: SetVariable\n                              id: setVariable_REPLACE_THIS\n                              variable: Global.GlobalVars.nbestresults\n                              value: EP1115_ChooseNewPlanLong_DM.nbestresults\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.selectedPlan\n                      value: EP1115_ChooseNewPlanLong_DM.returnvalue\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.choseInvalidPlan\n                      value: undefined\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.offerRepeatESNPlan\n                      value: false\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: EP1201_CheckAmbiguity_JDA\n```", "timestamp": 1749527913.19614, "content_hash": "adb43945e6c14558176e9eeb629cb42b"}