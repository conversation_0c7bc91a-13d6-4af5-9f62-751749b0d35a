{"response": "```yaml\n- kind: Question\n  id: aa4141_CardPinIssuesWrapUpDTMF_DM\n  displayName: aa4141_CardPinIssuesWrapUpDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - aa4141_CardPinIssuesWrapUpDTMF_DM_initial\n      - aa4141_CardPinIssuesWrapUpDTMF_DM_initial\n\n  alwaysPrompt: true\n  variable: Global.aa4141_CardPinIssuesWrapUpDTMF_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.dnisInfo.callType = \"TX\",\n            \"To hear that again, press 1For other options, press 2Or if you're done here, hang up\",\n            \"To hear that again, press 1If you're experiencing a problem with your card,For press 2For Other options, press 3Or if you're done here, hang up\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: repeat\n          displayName: repeat\n\n        - id: other_options\n          displayName: other_options\n\n        - id: card_problem\n          displayName: card_problem\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - aa4141_CardPinIssuesWrapUpDTMF_DM_initial\n        - aa4141_CardPinIssuesWrapUpDTMF_DM_initial\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa4141_CardPinIssuesWrapUpDTMF_DM\n  value: =Text(Global.aa4141_CardPinIssuesWrapUpDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa4141_CardPinIssuesWrapUpDTMF_DM = \"repeat\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa4140_CardPinIssuesPlayout_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa4141_CardPinIssuesWrapUpDTMF_DM = \"other_options\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa4105_OtherOptionsMenuDTMF_DM\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_aa4141_CardPinIssuesWrapUpDTMF_DM = \"card_problem\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.globalVariables.transferReason\n                  value: cardProblem\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.globalVariables.transferAllowed\n                  value: true\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: transferHandler_CS\n```", "timestamp": 1749544052.4047685, "content_hash": "494f4e592557e99d04f16020f3cb6d26"}