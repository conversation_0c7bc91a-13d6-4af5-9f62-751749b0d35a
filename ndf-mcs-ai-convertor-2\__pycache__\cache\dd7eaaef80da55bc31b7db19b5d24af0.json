{"response": "```yaml\n- kind: Question\n  id: aa6511_TransactionHistoryMainMenu_DM\n  displayName: aa6511_TransactionHistoryMainMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.aa6511_TransactionHistoryMainMenu_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n\n            Global.balanceInfo.cashParticipantFlag = true && Global.balanceInfo.fsParticipantFlag = false && Global.balanceInfo.ccParticipantFlag = false,\n            \"What kind of transactions would you like to hear? Cash or Deposits\",\n\n            Global.balanceInfo.cashParticipantFlag = true && Global.balanceInfo.fsParticipantFlag = true && Global.balanceInfo.ccParticipantFlag = false,\n            \"What kind of transactions would you like to hear? Cash, Food, Deposits or All of Them\",\n\n            Global.balanceInfo.cashParticipantFlag = true && Global.balanceInfo.fsParticipantFlag = false && Global.balanceInfo.ccParticipantFlag = true,\n            \"What kind of transactions would you like to hear? Cash, Child Care, Deposits, or All of Them\",\n\n            Global.balanceInfo.cashParticipantFlag = false && Global.balanceInfo.fsParticipantFlag = true && Global.balanceInfo.ccParticipantFlag = false,\n            \"What kind of transactions would you like to hear? Food or Deposits\",\n\n            Global.balanceInfo.cashParticipantFlag = false && Global.balanceInfo.fsParticipantFlag = true && Global.balanceInfo.ccParticipantFlag = true,\n            \"What kind of transactions would you like to hear? Food, ChildCare, Deposits, or All of Them\",\n\n            Global.balanceInfo.cashParticipantFlag = false && Global.balanceInfo.fsParticipantFlag = false && Global.balanceInfo.ccParticipantFlag = true,\n            \"What kind of transactions would you like to hear? ChildCare or Deposits\",\n\n            \"What kind of transactions would you like to hear? Cash, Food, ChildCare, Deposits, or All of Them\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: all\n          displayName: all\n        - id: cash\n          displayName: cash\n        - id: child_care\n          displayName: child_care\n        - id: deposit\n          displayName: deposit\n        - id: food\n          displayName: food\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa6511_TransactionHistoryMainMenu_DM\n  value: =Text(Global.aa6511_TransactionHistoryMainMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa6511_TransactionHistoryMainMenu_DM = \"all\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.typeOfHistory\n          value: AL\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa6512_LookupMessage_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa6511_TransactionHistoryMainMenu_DM = \"cash\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.typeOfHistory\n              value: CA\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa6512_LookupMessage_PP\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_aa6511_TransactionHistoryMainMenu_DM = \"child_care\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.globalVariables.typeOfHistory\n                  value: CC\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: aa6512_LookupMessage_PP\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_aa6511_TransactionHistoryMainMenu_DM = \"deposit\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.globalVariables.typeOfHistory\n                      value: DE\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: aa6512_LookupMessage_PP\n\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.Var_aa6511_TransactionHistoryMainMenu_DM = \"food\", true, false)\n                      actions:\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.globalVariables.typeOfHistory\n                          value: FS\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: aa6512_LookupMessage_PP\n```", "timestamp": 1749543669.583829, "content_hash": "dd7eaaef80da55bc31b7db19b5d24af0"}