{"response": "```yaml\n- kind: Question\n  id: dc0415_AskAddData_DM\n  displayName: dc0415_AskAddData_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/dc0415_AskAddData_DM_noinput1\\\">com.nuance.att.application.audio.dc0415_AskAddData_DM_noinput1</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/dc0415_AskAddData_DM_noinput2\\\">com.nuance.att.application.audio.dc0415_AskAddData_DM_noinput2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.dc0415_AskAddData_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/dc0415_AskAddData_DM_initial\\\">com.nuance.att.application.audio.dc0415_AskAddData_DM_initial</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: add_packages\n          displayName: add_packages\n        - id: change_plan\n          displayName: change_plan\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/dc0415_AskAddData_DM_noinput1\\\">com.nuance.att.application.audio.dc0415_AskAddData_DM_noinput1</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/dc0415_AskAddData_DM_noinput2\\\">com.nuance.att.application.audio.dc0415_AskAddData_DM_noinput2</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_dc0415_AskAddData_DM\n  value: =Text(Global.dc0415_AskAddData_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_dc0415_AskAddData_DM = \"add_packages\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: addPackages\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.packageDestination\n          value: data\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: dc0420_DataAddPackages_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_dc0415_AskAddData_DM = \"change_plan\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.intent\n              value: changePlan\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: D02_CheckData_01.dvxml\n```", "timestamp": 1749470803.826724, "content_hash": "3dcb73285299f0f0e90e6e0aade9dfcd"}