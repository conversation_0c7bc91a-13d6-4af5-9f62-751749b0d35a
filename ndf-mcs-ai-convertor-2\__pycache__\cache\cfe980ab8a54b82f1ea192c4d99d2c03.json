{"response": "```yaml\n- kind: Question\n  id: aa2535_SSN4_DM\n  displayName: aa2535_SSN4_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.aa2535_SSN4_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n\n            // Case 1: currentValidationType is 'PSS' or 'PSN' and dnisInfo.ssn4PrimaryPromptVoiceOn is true\n            (Global.validationCriteriaVariables.currentValidationType = \"PSS\" || Global.validationCriteriaVariables.currentValidationType = \"PSN\") && Global.dnisInfo.ssn4PrimaryPromptVoiceOn = true,\n            dnisInfo.ssn4PrimaryPromptVoice,\n\n            // Case 2: currentValidationType is 'PSS' or 'PSN' and dnisInfo.ssn4PrimaryPromptVoiceOn is false\n            (Global.validationCriteriaVariables.currentValidationType = \"PSS\" || Global.validationCriteriaVariables.currentValidationType = \"PSN\"),\n            \"What are the last four digits of the primary card holder s social security number?\",\n\n            // Case 3: dnisInfo.ssn4PromptVoiceOn is true\n            Global.dnisInfo.ssn4PromptVoiceOn = true,\n            dnisInfo.ssn4PromptVoice,\n\n            // Default case\n            \"What are the last four digits of your social security number?\"\n        )}\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.result\n  value: aa2535_SSN4_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.ssn4\n  value: aa2535_SSN4_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.validationCriteriaVariables.validationValue\n  value: validationCriteriaVariables.validationValue + ';' + result\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.validationCriteriaVariables.validationType\n  value: validationCriteriaVariables.validationType + ';' + validationCriteriaVariables.currentValidationType\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.globalVariables.currentTask = \"cardReplacement\" || Global.globalVariables.currentTask = \"altAuthVal\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa2510_ValidationDecision_JDA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: |\n            =If(Global.result = Global.validationCriteriaVariables.currentValidationData, true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa2510_ValidationDecision_JDA\n\n      elseActions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.validationCriteriaVariables.validationDataMismatch\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.validationCriteriaVariables.validationDataMismatchSsn4\n          value: true\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa2510_ValidationDecision_JDA\n```\n**Notes on the conversion:**\n- The `prompt.speak` is constructed as a Switch statement, following the nested if/else logic in the XML, with PowerFx-style conditions and variable names prefixed with `Global.`.\n- All session-mapping tags are converted to `SetVariable` actions.\n- The nested `<if>` in `<success>` is converted to a `ConditionGroup` with `elseActions` for the elseif and else branches.\n- No `unrecognizedPrompt` or `inputTimeoutResponse` is generated since there are no nomatch/noinput prompts in the XML.\n- All ids for SetVariable, ConditionGroup, GotoAction are set to `REPLACE_THIS` as per instruction.\n- The entity is set to `StringPrebuiltEntity` as there is no closed list or boolean entity in the success actions.\n- The structure and indentation follow the memorized YAML format.", "timestamp": 1749544129.499574, "content_hash": "cfe980ab8a54b82f1ea192c4d99d2c03"}