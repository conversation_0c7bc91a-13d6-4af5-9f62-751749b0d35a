{"response": "```yaml\n- kind: Question\n  id: ES1215_FindIMEIWaitSBI_DM\n  displayName: ES1215_FindIMEIWaitSBI_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.ES1215_FindIMEIWaitSBI_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ES1215_ini_01.wav\\\">I ll wait while you try that Dial star, pound, zero, six, pound on your new phone When you see the serial number, say continue </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ES1215_ini_02.wav\\\">10 seconds wait music Dial star, pound, zero, six, pound on your new phone When you see the serial number, say continue Or say It s not working 10 seconds wait music Dial star, pound, zero, six, pound When you see the serial number, say continue or press 1 Or say It s not working or press 2 10 seconds wait music Dial star, pound, zero, six, pound When you see the serial number, say continue or press 1 Or say It s not working or press 2 10 seconds wait music Dial star, pound, zero, six, pound When you see the serial number, say continue or press 1 Or say It s not working or press 210 seconds wait music Dial star, pound, zero, six, pound When you see the serial number, say continue or press 1 Or say It s not working or press 210 seconds wait music Dial star, pound, zero, six, pound When you see the serial number, say continue or press 1 Or say It s not working or press 210 seconds wait music Dial star, pound, zero, six, pound When you see the serial number, say continue or press 1 Or say It s not working or press 210 seconds wait music Dial star, pound, zero, six, pound When you see the serial number, say continue or press 1 Or say It s not working or press 2 10 seconds wait music We seem to be having some trouble</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: continue\n          displayName: continue\n        - id: not_working\n          displayName: not_working\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ES1215_FindIMEIWaitSBI_DM\n  value: =Text(Global.ES1215_FindIMEIWaitSBI_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ES1215_FindIMEIWaitSBI_DM = \"continue\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ES1220_CollectIMEI_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ES1215_FindIMEIWaitSBI_DM = \"not_working\", true, false)\n          actions:\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.CareESNSwap_Conflicts_Main.dvxml#ES1935_Transfer_SD\n```", "timestamp": 1749528089.8889399, "content_hash": "d7eba3b47afcdf563bba72dbaff0d66a"}