{"response": "```yaml\n- kind: Question\n  id: LG1120_SecurityCodeWait_DM\n  displayName: LG1120_SecurityCodeWait_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.LG1120_SecurityCodeWait_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.accountPinToggleOn = true,\n            \"No problem I ll wait Once you re ready with your 6-to-15-digit account PIN, say Continue  When you re ready, say  Continue , or press 1,  I don t know it  or press 2  You can say  Continue , or press 1,  I don t know it  or press 2  If you have your 6-to-15-digit account PIN, say Continue  or press 1 You can also say  I don t know it  or press 2  If you re ready,  say  Continue  or press 1 Otherwise say  I don t know it , or press 2  You can say  Continue , press 1,  I don t know it  or press 2 Hmmm I seem to be having some trouble\",\n            true,\n            \" No problem I'll wait Once you're ready with your 8-digit account PIN, say Continue When you're ready, say 'Continue', or press 1, 'I don't know it' or press 2 You can say 'Continue', or press 1, 'I don't know it' or press 2 If you have your 6-to-15-digit account PIN, say Continue' or press 1 You can also say 'I don't know it' or press 2 If you're ready,  say 'Continue' or press 1 Otherwise say 'I don't know it', or press 2 You can say 'Continue', press 1, 'I don't know it' or press 2 Hmmm I seem to be having some trouble \"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: ready\n          displayName: ready\n        - id: dont_know\n          displayName: dont_know\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n              true,\n              Global.accountPinToggleOn = true,\n              \"No problem I ll wait Once you re ready with your 6-to-15-digit account PIN, say Continue  When you re ready, say  Continue , or press 1,  I don t know it  or press 2  You can say  Continue , or press 1,  I don t know it  or press 2  If you have your 6-to-15-digit account PIN, say Continue  or press 1 You can also say  I don t know it  or press 2  If you re ready,  say  Continue  or press 1 Otherwise say  I don t know it , or press 2  You can say  Continue , press 1,  I don t know it  or press 2 Hmmm I seem to be having some trouble\",\n              true,\n              \" No problem I'll wait Once you're ready with your 8-digit account PIN, say Continue When you're ready, say 'Continue', or press 1, 'I don't know it' or press 2 You can say 'Continue', or press 1, 'I don't know it' or press 2 If you have your 6-to-15-digit account PIN, say Continue' or press 1 You can also say 'I don't know it' or press 2 If you're ready,  say 'Continue' or press 1 Otherwise say 'I don't know it', or press 2 You can say 'Continue', press 1, 'I don't know it' or press 2 Hmmm I seem to be having some trouble \"\n          )}\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.accountPinToggleOn\n  value: GlobalVars.GetBCSParameters.accountPinToggleOn\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_LG1120_SecurityCodeWait_DM\n  value: =Text(Global.LG1120_SecurityCodeWait_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_LG1120_SecurityCodeWait_DM = \"ready\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/LG1120_out_01.wav\\\">Let s continue</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: LG1115_GetSecurityCode_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_LG1120_SecurityCodeWait_DM = \"dont_know\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/LG1120_out_02.wav\\\">No problem</audio>\"\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.dontKnowPIN\n              value: true\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: LG1301_CheckSQEligibility_JDA_DA\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_LG1120_SecurityCodeWait_DM = \"default\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.pin\n                  value: LG1120_SecurityCodeWait_DM.returnvalue\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.verificationType\n                  value: pin\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.verificationValue\n                  value: LG1120_SecurityCodeWait_DM.returnvalue\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: LG1124_Authenticate_DB_DA\n```", "timestamp": 1749558428.2946436, "content_hash": "bce3014ae5efe204cf206ccddb6a24db"}