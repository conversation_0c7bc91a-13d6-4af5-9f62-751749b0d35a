{"response": "```yaml\n- kind: Question\n  id: DA1110_OfferGetDataPlanYN_DM\n  displayName: DA1110_OfferGetDataPlanYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.tag = \"buy-data_topup\" && Global.topupEligibility = \"NOT-ELIGIBLE\",\n              \"Your current plan doesn't allow data top-ups So would you like to change to a different one ?\",\n          \n              true,\n              \"Your current plan doesn t have any high speed data So would you like to change to one that does?\"\n          )\n        }\n      - |\n        {Switch(\n              true,\n              Global.tag = \"buy-data_topup\" && Global.topupEligibility = \"NOT-ELIGIBLE\",\n              \"Would you like to change to a plan with more data? Say 'yes' or press 1, or say 'no' or press 2\",\n          \n              true,\n              \"Would you like to change to a plan with High Speed data? Say  yes  or press 1, or say  no  or press 2\"\n          )\n        }\n      - |\n        {Switch(\n              true,\n              Global.tag = \"buy-data_topup\" && Global.topupEligibility = \"NOT-ELIGIBLE\",\n              \"Let's try one more time\",\n          \n              true,\n              \"Let's try one more time\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.DA1110_OfferGetDataPlanYN_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.tag = \"buy-data_topup\" && Global.topupEligibility = \"NOT-ELIGIBLE\",\n              \"Would you like to change to a different plan with more data ?\",\n          \n              true,\n              \"Would you like to change to a plan with High Speed data?\"\n          )\n        }\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.tag = \"buy-data_topup\" && Global.topupEligibility = \"NOT-ELIGIBLE\",\n                \"Your current plan doesn't allow data top-ups So would you like to change to a different one ?\",\n            \n                true,\n                \"Your current plan doesn t have any high speed data So would you like to change to one that does?\"\n            )\n          }\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - |\n          {Switch(\n                true,\n                Global.tag = \"buy-data_topup\" && Global.topupEligibility = \"NOT-ELIGIBLE\",\n                \"Would you like to change to a plan with more data? Say 'yes' or press 1, or say 'no' or press 2\",\n            \n                true,\n                \"Would you like to change to a plan with High Speed data? Say  yes  or press 1, or say  no  or press 2\"\n            )\n          }\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - |\n          {Switch(\n                true,\n                Global.tag = \"buy-data_topup\" && Global.topupEligibility = \"NOT-ELIGIBLE\",\n                \"Would you like to change to a plan with more data? Say 'yes' or press 1, or say 'no' or press 2\",\n            \n                true,\n                \"Would you like to change to a plan with High Speed data? Say  yes  or press 1, or say  no  or press 2\"\n            )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.tag\n  value: GlobalVars.tag\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.topupEligibility\n  value: GlobalVars.GetAccountDetails.topupEligibility\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_DA1110_OfferGetDataPlanYN_DM\n  value: =Text(Global.DA1110_OfferGetDataPlanYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_DA1110_OfferGetDataPlanYN_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.tag\n          value: change-plan\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: change_plan\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: DA1015_CheckNeedPIN_JDA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_DA1110_OfferGetDataPlanYN_DM = \"false\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.callType\n              value: transfer\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.cti_Intent\n              value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: DA1115_CallTransfer_SD\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_DA1110_OfferGetDataPlanYN_DM = \"repeat\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: DA1105_NotifyNoHighSpeedData_PP\n```", "timestamp": 1749528470.897272, "content_hash": "503003f3021f970a39b56d574be44133"}