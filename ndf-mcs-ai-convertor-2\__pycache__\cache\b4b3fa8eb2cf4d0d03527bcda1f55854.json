{"response": "```yaml\n- kind: Question\n  id: AX1706_DSGExtensionTerms_DM\n  displayName: AX1706_DSGExtensionTerms_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AX1706_ini_01.wav\\\">Please confirm with the customer that they agree to the following terms </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AX1706_ini_02.wav\\\">If they agree, press 1 To hear this again, press 2 Otherwise, please hang up </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AX1706_ini_01.wav\\\">Please confirm with the customer that they agree to the following terms </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AX1706_ini_02.wav\\\">If they agree, press 1 To hear this again, press 2 Otherwise, please hang up </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AX1706_ini_01.wav\\\">Please confirm with the customer that they agree to the following terms </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AX1706_ini_02.wav\\\">If they agree, press 1 To hear this again, press 2 Otherwise, please hang up </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.AX1706_DSGExtensionTerms_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/AX1706_ini_01.wav\\\">Please confirm with the customer that they agree to the following terms </audio>\"\n      - \"{DynamicAudio: Global.audioMessageKey}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AX1706_ini_02.wav\\\">If they agree, press 1 To hear this again, press 2 Otherwise, please hang up </audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: yes\n          displayName: yes\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/AX1706_ini_01.wav\\\">Please confirm with the customer that they agree to the following terms </audio>\"\n        - \"{DynamicAudio: Global.audioMessageKey}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AX1706_ini_02.wav\\\">If they agree, press 1 To hear this again, press 2 Otherwise, please hang up </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AX1706_ini_01.wav\\\">Please confirm with the customer that they agree to the following terms </audio>\"\n        - \"{DynamicAudio: Global.audioMessageKey}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AX1706_ini_02.wav\\\">If they agree, press 1 To hear this again, press 2 Otherwise, please hang up </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AX1706_ini_01.wav\\\">Please confirm with the customer that they agree to the following terms </audio>\"\n        - \"{DynamicAudio: Global.audioMessageKey}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AX1706_ini_02.wav\\\">If they agree, press 1 To hear this again, press 2 Otherwise, please hang up </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_AX1706_DSGExtensionTerms_DM\n  value: =Text(Global.AX1706_DSGExtensionTerms_DM_reco)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.audioMessageKey\n  value: care_configurable_extension_terms_audio\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AX1706_DSGExtensionTerms_DM = \"yes\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/AX1706_out_01.wav\\\">Hold the line </audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: AX1710_CreateUnhotlineCase_DB_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_AX1706_DSGExtensionTerms_DM = \"repeat\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/AX1706_out_02.wav\\\">Sure </audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: AX1706_DSGExtensionTerms_DM\n```\n\n**Notes:**\n- For `{DynamicAudio: Global.audioMessageKey}`: This is a placeholder for the dynamic audio message referenced by `audioMessageKey`. If your YAML processor supports a different syntax for dynamic audio, replace accordingly.\n- All audio file references are wrapped in `<audio src=\"AUDIO_LOCATION/...\">...</audio>` as per your pattern.\n- The `entity` is a closed list with \"yes\" and \"repeat\" as per the `<success>` actions.\n- The `SetVariable` for `audioMessageKey` is included as per the `<session-mapping>`.\n- The `SendActivity` blocks are used to play the audio sequences before the next action.\n- All IDs for set/goto/condition are left as `REPLACE_THIS` for you to fill in as needed.\n- The structure and indentation follow your memorized YAML format.", "timestamp": 1749527472.190791, "content_hash": "b4b3fa8eb2cf4d0d03527bcda1f55854"}