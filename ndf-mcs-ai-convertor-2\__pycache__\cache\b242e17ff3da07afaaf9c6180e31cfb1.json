{"response": "```yaml\n- kind: Question\n  id: sc0141_AgentSMSYesNo_DM\n  displayName: sc0141_AgentSMSYesNo_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/sc0141_ni1_01.wav\\\">I can send you a text message thatll connect you with a chat agent If a chat agent is not immediately available, your place in line is automatically saved and they will message you back as soon as they can! Would you like to do that now?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/sc0141_ni2_01.wav\\\">If you would like to connect with a chat agent, say yes or press 1 If not, say no or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.sc0141_AgentSMSYesNo_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/sc0141_ini_01.wav\\\">I can send you a text message that will connect you with a chat agent If a chat agent is not immediately available, your place in line is automatically saved and they will message you back as soon as they can! Would you like to do that now?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/sc0141_ni1_01.wav\\\">I can send you a text message thatll connect you with a chat agent If a chat agent is not immediately available, your place in line is automatically saved and they will message you back as soon as they can! Would you like to do that now?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/sc0141_ni2_01.wav\\\">If you would like to connect with a chat agent, say yes or press 1 If not, say no or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_sc0141_AgentSMSYesNo_DM\n  value: =Text(Global.sc0141_AgentSMSYesNo_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_sc0141_AgentSMSYesNo_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/sc0141_out_01.wav\\\">You have selected to start a text chat with one of our agents Make sure to look for a new text message on your phone and follow the instructions to start chatting</audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.SMSChoice\n          value: text\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: sc0147_TrackAgentSMSChoice_JDA_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_sc0141_AgentSMSYesNo_DM = \"false\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.SMSChoice\n              value: NoText\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: sc0147_TrackAgentSMSChoice_JDA_DA\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_sc0141_AgentSMSYesNo_DM = \"mainmenu\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.lastIntent\n                  value: intent\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.intent\n                  value: ''\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.callerIntent\n                  value: ''\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.transferDestinationEBB\n                  value: false\n                - kind: BeginDialog\n                  id: begin_REPLACE_THIS\n                  dialog: topic.M01_MainMenu2.dvxml#ma1334_InitiateMainMenu_DS\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_dtmfcommandgrammar\n  value: 'GlobalCommands_dtmf.grxml?SWI_vars.disallow=operator&SWI_vars.allow=mainmenu&'\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.operatorAtOfferAgentOptions\n  value: 0\n```", "timestamp": 1749472213.9105546, "content_hash": "b242e17ff3da07afaaf9c6180e31cfb1"}