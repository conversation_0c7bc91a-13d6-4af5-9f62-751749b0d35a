{"response": "```yaml\n- kind: Question\n  id: DU1125_CappedRepeatYN_DM\n  displayName: DU1125_CappedRepeatYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DU1125_nm1_01.wav\\\">Do you want to hear your data usage again before we continue? </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DU1125_nm2_01.wav\\\">To hear your usage information again, say 'yes' or press 1 Otherwise, say 'no' or press 2, and we'll continue </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DU1125_nm2_01.wav\\\">To hear your usage information again, say 'yes' or press 1 Otherwise, say 'no' or press 2, and we'll continue </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.DU1125_CappedRepeatYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/DU1125_ini_01.wav\\\">Before we continue, would you like to hear that again? </audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/DU1125_nm1_01.wav\\\">Do you want to hear your data usage again before we continue? </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DU1125_nm2_01.wav\\\">To hear your usage information again, say 'yes' or press 1 Otherwise, say 'no' or press 2, and we'll continue </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DU1125_nm2_01.wav\\\">To hear your usage information again, say 'yes' or press 1 Otherwise, say 'no' or press 2, and we'll continue </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_DU1125_CappedRepeatYN_DM\n  value: =Text(Global.DU1125_CappedRepeatYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_DU1125_CappedRepeatYN_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/DU1125_out_01.wav\\\">Sure! </audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.tag = \"home-internet_active\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: DU1107_InternetCappedPlayCurrentPlan_PP\n\n          elseActions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: DU1105_CappedPlayCurrentPlan_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_DU1125_CappedRepeatYN_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/DU1125_out_02.wav\\\">Alright </audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.tag = \"home-internet_active\", true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: DU1315_InternetUsageWrapMenu_DM\n\n              elseActions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: DU1301_CheckContext_JDA\n```", "timestamp": 1749528105.6164598, "content_hash": "6a34c4855e959c792f968e4f88327b31"}