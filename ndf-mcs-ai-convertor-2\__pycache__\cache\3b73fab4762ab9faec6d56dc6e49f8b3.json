{"response": "```yaml\n- kind: Question\n  id: sc0107_CollectPhoneNumber_DM\n  displayName: sc0107_CollectPhoneNumber_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/sc0107_nm1_01.wav\\\">Please say or enter the phone number youd like to be called back on</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/sc0107_nm2_01.wav\\\">Please enter the phone number youd like to be called back on</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.sc0107_CollectPhoneNumber_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.callBackNumberUnknown = \"true\" || Global.callBackNumberUnknown = true,\n            \"Whats the phone number youd like to be called back on?\",\n            true,\n            \"Whats the number youd like to use?\"\n        )}\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/sc0107_ni1_01.wav\\\">Please say or enter the phone number youd like to be called back on</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/sc0107_ni2_01.wav\\\">Please enter the phone number youd like to be called back on</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.callBackPhoneNumber\n  value: sc0107_CollectPhoneNumber_DM.returnvalue\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: sc0108_CallbackManager_DB_DA\n```", "timestamp": 1749472459.071844, "content_hash": "3b73fab4762ab9faec6d56dc6e49f8b3"}