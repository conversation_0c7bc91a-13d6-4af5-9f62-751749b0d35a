{"response": "```yaml\n- kind: Question\n  id: aa2567_CaseNumber2DTMF_DM\n  displayName: aa2567_CaseNumber2DTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa2567_ini_01.wav\\\">Using your keypad, please enter your case number, with the last letter included For example for the letter A, you would press 2 and for the letter Z you would press 9 Go ahead</audio>\"\n      - |\n        {Switch(\n              true,\n              Global.dnisInfo.dtmfOnlyFlag = true,\n              \"Sorry, I still didn t get that\",\n              true,\n              \"\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.aa2567_CaseNumber2DTMF_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa2567_ini_01.wav\\\">Using your keypad, please enter your case number, with the last letter included For example for the letter A, you would press 2 and for the letter Z you would press 9 Go ahead</audio>\"\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/aa2567_ini_01.wav\\\">Using your keypad, please enter your case number, with the last letter included For example for the letter A, you would press 2 and for the letter Z you would press 9 Go ahead</audio>\"\n        - |\n          {Switch(\n                true,\n                Global.dnisInfo.dtmfOnlyFlag = true,\n                \"Sorry, I still didn t get that\",\n                true,\n                \"\"\n            )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.result\n  value: aa2567_CaseNumber2DTMF_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.case2\n  value: aa2567_CaseNumber2DTMF_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.validationCriteriaVariables.validationValue\n  value: validationCriteriaVariables.validationValue + ';' + result\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.validationCriteriaVariables.validationType\n  value: validationCriteriaVariables.validationType + ';' + validationCriteriaVariables.currentValidationType\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.globalVariables.currentTask = \"cardReplacement\" || Global.globalVariables.currentTask = \"altAuthVal\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa2510_ValidationDecision_JDA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: |\n            =If(Global.result = Global.validationCriteriaVariables.currentValidationData, true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa2510_ValidationDecision_JDA\n\n      elseActions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.validationCriteriaVariables.validationDataMismatch\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.validationCriteriaVariables.validationDataMismatchCaseNum2\n          value: true\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa2510_ValidationDecision_JDA\n```", "timestamp": 1749544250.2602386, "content_hash": "3f40c9a2fcd0418514f2714c0778754f"}