{"response": "```yaml\n- kind: Question\n  id: bc0135_AskSSN_DM\n  displayName: bc0135_AskSSN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bc0135_ni1_01.wav\\\">Please say or enter the last 4 digits of the cardholders social security number</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/bc0135_ni2_01.wav\\\">Please enter the last 4 digits of the cardholders social security number</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.bc0135_AskSSN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bc0135_ini_01.wav\\\">And what are the last 4 digits of the cardholders social security number?</audio>\"\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/bc0135_ni1_01.wav\\\">Please say or enter the last 4 digits of the cardholders social security number</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/bc0135_ni2_01.wav\\\">Please enter the last 4 digits of the cardholders social security number</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.last4SSN\n  value: bc0135_AskSSN_DM.returnvalue\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.intent = \"autoRefill\" && Global.planStatus = \"active\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/bc0135_out_01.wav\\\">Thanks</audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\"></audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: bc0220_TermsAndConditions_DM\n\n  elseActions:\n    - kind: SendActivity\n      id: sendActivity_REPLACE_THIS\n      activity:\n        speak:\n          - \"<audio src=\\\"AUDIO_LOCATION/bc0135_out_01.wav\\\">Thanks</audio>\"\n          - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\"></audio>\"\n    - kind: GotoAction\n      id: goto_REPLACE_THIS\n      actionId: bc0210_ConfirmPaymentDetails_DM\n\n# Command actions (not part of the main Question, but included for completeness)\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.command = \"dont_have\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.intent = \"autoRefill\" || Global.intent = \"refillPlusAutoPay\", true, false)\n              actions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/bc0135_out_03.wav\\\">OK I will need to connect you to someone who can assist with completing your Auto Pay</audio>\"\n          elseActions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/bc0135_out_02.wav\\\">OK I will need to connect you to someone who can assist with completing your payment</audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferPrompt\n          value: short\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferDestination\n          value: vesta\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferReason\n          value: BUSINESS_RULE\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M09_MainMenu.dvxml#ma9210_NeedTransferType_DS\n\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.command = \"mainmenu\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferDestination\n          value: attPrepaid\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M01_MainMenu2.dvxml#ma1334_InitiateMainMenu_DS\n\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.command = \"operator\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.lastTransferState\n          value: bc0135_AskSSN_DM\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferReason\n          value: AGENT_REQUEST\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M09_MainMenu.dvxml#ma9210_NeedTransferType_DS\n\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.command = \"repeat\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: bc0135_AskSSN_DM\n```", "timestamp": 1749470053.9359584, "content_hash": "6ef31ecfe2ea8cc9854e87ef97becd44"}