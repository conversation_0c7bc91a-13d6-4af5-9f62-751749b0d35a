{"response": "```yaml\n- kind: Question\n  id: aa1020_LanguageSelectionDTMF_DM\n  displayName: aa1020_LanguageSelectionDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.aa1020_LanguageSelectionDTMF_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa1020_ini_01.wav\\\">Para español, marque 2</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: \"2\"\n          displayName: \"2\"\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa1020_LanguageSelectionDTMF_DM\n  value: =Text(Global.aa1020_LanguageSelectionDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa1020_LanguageSelectionDTMF_DM = \"2\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.dnisInfo.selectLanguage\n          value: false\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.language\n          value: es-US\n\n        # Skipping script actions as per instructions\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.dnisInfo.callType = \"TX\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: SetLanguage_TX_CS\n\n          elseActions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: SetLanguage_CS\n```", "timestamp": 1749458377.1622345, "content_hash": "fc8cec1d41c315e4d89d8943bd433579"}