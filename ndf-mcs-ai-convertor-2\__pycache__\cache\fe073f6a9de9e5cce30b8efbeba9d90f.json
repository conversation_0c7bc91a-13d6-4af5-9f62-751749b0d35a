{"response": "```yaml\n- kind: Question\n  id: ma2430_Offer611_DM\n  displayName: ma2430_Offer611_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.esim = \"true\" || Global.esim = true,\n              \"You're account is set Now, Have you already downloaded your eSIM? Just sat Yes or No\",\n              true,\n              \"Your device is activated and ready to use Can I do anything else for you? Just say Yes or No \"\n          )\n        }\n      - |\n        {Switch(\n              true,\n              Global.esim = \"true\" || Global.esim = true,\n              \"If you've already downloaded your esim say yes or press 1 If not, say no or press 2\",\n              true,\n              \"Your activation is complete and your device is ready to use If there's anything else you'd like to do with your account, like adding a package, say Yes or press 1 If not, say No or press 2 Or feel free to just hang up\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.ma2430_Offer611_DM_reco\n  prompt:\n    speak:\n      - \"{CustomAudio: com.nuance.att.application.audio.ma2430_Offer611_DM_initial}\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.esim = \"true\" || Global.esim = true,\n                \"You're account is set Now, Have you already downloaded your eSIM? Just sat Yes or No\",\n                true,\n                \"Your device is activated and ready to use Can I do anything else for you? Just say Yes or No \"\n            )\n          }\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ma2430_Offer611_DM\n  value: =Text(Global.ma2430_Offer611_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma2430_Offer611_DM = \"true\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.esim = \"true\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.skipEsimActivationSteps\n                  value: true\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: ma2450_eSimActivationCompleted_DM\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If((Global.offerChatAgent = \"true\" || Global.offerChatAgent = true) && Global.language = \"en-US\" && Global.matchedANI = true, true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.offerChatAgent\n                      value: 'true'\n              elseActions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.offerChatAgent\n                  value: 'false'\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.addMoneyActivations\n              value: 'false'\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.callType\n              value: '611'\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.collection_commandgrammar\n              value: 'GlobalCommands.grxml?SWI_vars.allow=mainmenu'\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.collection_dtmfcommandgrammar\n              value: 'GlobalCommands_dtmf.grxml?SWI_vars.allow=mainmenu'\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.matchedANI\n              value: true\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.intent\n              value: ''\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.M01_MainMenu.dvxml#ma1120_IOMInitialize_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ma2430_Offer611_DM = \"false\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.esim = \"true\", true, false)\n                  actions:\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/ma2430_out_01.wav\\\">Alright, lets go through the steps to download your eSIM</audio>\"\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: ma2440_eSimActivationFirstStep_DM\n              elseActions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.callType\n                  value: '611'\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.collection_commandgrammar\n                  value: 'GlobalCommands.grxml?SWI_vars.allow=mainmenu'\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.collection_dtmfcommandgrammar\n                  value: 'GlobalCommands_dtmf.grxml?SWI_vars.allow=mainmenu'\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.matchedANI\n                  value: true\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.intent\n                  value: ''\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.addMoneyActivations\n                  value: 'false'\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If((Global.offerChatAgent = \"true\" || Global.offerChatAgent = true) && Global.language = \"en-US\" && Global.matchedANI = true, true, false)\n                      actions:\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.offerChatAgent\n                          value: 'true'\n                  elseActions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.offerChatAgent\n                      value: 'false'\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: ma2435_ActiveReminder_PP\n```", "timestamp": 1749471469.7556498, "content_hash": "fe073f6a9de9e5cce30b8efbeba9d90f"}