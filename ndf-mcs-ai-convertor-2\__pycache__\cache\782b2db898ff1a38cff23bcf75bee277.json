{"response": "```yaml\n- kind: Question\n  id: BC1425_StartOverYN_DM\n  displayName: BC1425_StartOverYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1425_nm1_01.wav\\\">To start over with your details, press 1 Otherwise, press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1425_nm2_01.wav\\\">I can't complete your activation without a payment on this call To start over with your card details, press 1 Otherwise, press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1425_nm3_01.wav\\\">I can't complete your activation without a payment on this call, and if we stop now, all the account information you gave me before will be lost To start over with your card details, press 1 Otherwise, press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.BC1425_StartOverYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1425_ini_01.wav\\\">We seem to be having some trouble I can't complete your activation without a payment on this call, and if we stop now, all the account information you gave me before will be lost So if you'd like to go back and re-enter your card details again, please press 1 Otherwise, press 2</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1425_nm1_01.wav\\\">To start over with your details, press 1 Otherwise, press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1425_nm2_01.wav\\\">I can't complete your activation without a payment on this call To start over with your card details, press 1 Otherwise, press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1425_nm3_01.wav\\\">I can't complete your activation without a payment on this call, and if we stop now, all the account information you gave me before will be lost To start over with your card details, press 1 Otherwise, press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_BC1425_StartOverYN_DM\n  value: =Text(Global.BC1425_StartOverYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_BC1425_StartOverYN_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.correctAllDetails\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.bankCardNumber\n          value: undefined\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.bankCardDate\n          value: undefined\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.bankCardCVV\n          value: undefined\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.bankCardZip\n          value: undefined\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: BC1001_CheckContext_JDA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_BC1425_StartOverYN_DM = \"false\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: BC1430_CallTransfer_SD\n```", "timestamp": **********.9285173, "content_hash": "782b2db898ff1a38cff23bcf75bee277"}