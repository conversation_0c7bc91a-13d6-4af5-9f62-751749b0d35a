{"response": "```yaml\n- kind: Question\n  id: st1012_AskPowerCycleQuestion_DM\n  displayName: st1012_AskPowerCycleQuestion_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/st1012_nm1_01.wav\\\">Have you already tried powering off your phone? Please say yes or no</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/st1012_ni2_02.wav\\\">If you already tried powering off your phone, press 1 Otherwise, press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.st1012_AskPowerCycleQuestion_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/st1012_ini_01.wav\\\">Have you already tried powering off your phone?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/st1012_ni1_01.wav\\\">Have you already tried powering off your phone ? Please say yes or no</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/st1012_ni2_01.wav\\\">If you already tried powering off your phone, press 1 Otherwise, press 2</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_st1012_AskPowerCycleQuestion_DM\n  value: =Text(Global.st1012_AskPowerCycleQuestion_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_st1012_AskPowerCycleQuestion_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferDestination\n          value: attPrepaid\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/st1012_out_01.wav\\\">Alright Let me connect you to someone who can help</audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_100ms.wav\\\"></audio>\"\n\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M09_MainMenu_Dialog\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_st1012_AskPowerCycleQuestion_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/st1012_out_02.wav\\\">Alright</audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_100ms.wav\\\"></audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: st1020_PowercycleInstructions_PP\n```", "timestamp": 1749472149.691333, "content_hash": "b52eb0f6f33bb1533984a64ad0ffe874"}