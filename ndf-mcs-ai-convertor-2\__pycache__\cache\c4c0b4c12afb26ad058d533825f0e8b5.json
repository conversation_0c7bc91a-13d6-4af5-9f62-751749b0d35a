{"response": "```yaml\n- kind: Question\n  id: MM1032_BalanceandPaymentsMenu_DM\n  displayName: MM1032_BalanceandPaymentsMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MM1032_nm1_01.wav\\\">Please say 'auto pay', 'make a payment', 'payment help' or 'balance details' </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MM1032_nm2_01.wav\\\">You can say 'auto pay', or press 1;  'make a payment' or press 2; 'Payment help' or press 3 or 'Balance details' or press 4 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MM1032_nm2_01.wav\\\">You can say 'auto pay', or press 1;  'make a payment' or press 2; 'Payment help' or press 3 or 'Balance details' or press 4 </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.MM1032_BalanceandPaymentsMenu_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/MM1032_ini_01.wav\\\">Say  make a payment ,  payment help  or  balance details </audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: make-payment\n          displayName: make-payment\n        - id: request-extension\n          displayName: request-extension\n        - id: payment-help\n          displayName: payment-help\n        - id: hear-plan_details\n          displayName: hear-plan_details\n        - id: vague-autopay\n          displayName: vague-autopay\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/MM1032_nm1_01.wav\\\">Please say 'auto pay', 'make a payment', 'payment help' or 'balance details' </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MM1032_nm2_01.wav\\\">You can say 'auto pay', or press 1;  'make a payment' or press 2; 'Payment help' or press 3 or 'Balance details' or press 4 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MM1032_nm2_01.wav\\\">You can say 'auto pay', or press 1;  'make a payment' or press 2; 'Payment help' or press 3 or 'Balance details' or press 4 </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.isAutopayEligPlanExists\n  value: GlobalVars.GetAccountDetails && GlobalVars.GetAccountDetails.isAutopayEligPlanExists\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.isSuspended\n  value: (GlobalVars.GetAccountDetails && GlobalVars.GetAccountDetails.accountStatus = \"suspended\") ? true : false\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_MM1032_BalanceandPaymentsMenu_DM\n  value: =Text(Global.MM1032_BalanceandPaymentsMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MM1032_BalanceandPaymentsMenu_DM = \"make-payment\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: make_pmt\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.securityRequired\n          value: false\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.needMDN\n          value: false\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MM1032_BalanceandPaymentsMenu_DM = \"request-extension\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/mm1032_out_01.wav\\\">Sure</audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: extension\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MM1032_BalanceandPaymentsMenu_DM = \"payment-help\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: pmt_help\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.securityRequired\n          value: true\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.needMDN\n          value: true\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MM1032_BalanceandPaymentsMenu_DM = \"hear-plan_details\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: acct_bal\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.needMDN\n          value: true\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.isAutopayEligPlanExists = true, true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.securityRequired\n                  value: true\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.GlobalVars.aniMatch = true && Global.GlobalVars.switchLinesSuccess = false, true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.securityRequired\n                      value: false\n              elseActions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.securityRequired\n                  value: true\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MM1032_BalanceandPaymentsMenu_DM = \"vague-autopay\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: auto_pay\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.tag\n          value: vague-autopay\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.isSuspended = true, true, false)\n              actions:\n                - kind: BeginDialog\n                  id: begin_REPLACE_THIS\n                  dialog: topic.MainMenu_Routing.dvxml#MM1599_GoToSuspended_SD\n          elseActions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: MM1610_GoToAutoPay_SD\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MM1032_BalanceandPaymentsMenu_DM = \"default\", true, false)\n      actions:\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.MainMenu_CheckSecurity.dvxml#MM1050_CheckContinueToDestination_DS\n```", "timestamp": **********.9171104, "content_hash": "c4c0b4c12afb26ad058d533825f0e8b5"}