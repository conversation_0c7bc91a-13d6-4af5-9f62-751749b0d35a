{"response": "```yaml\n- kind: Question\n  id: dc0130_RefillOrChangePlan_DM\n  displayName: dc0130_RefillOrChangePlan_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"com.nuance.att.application.audio.dc0130_RefillOrChangePlan_DM_noinput1\"\n      - \"com.nuance.att.application.audio.dc0130_RefillOrChangePlan_DM_noinput2\"\n\n  alwaysPrompt: true\n  variable: Global.dc0130_RefillOrChangePlan_DM_reco\n  prompt:\n    speak:\n      - \"com.nuance.att.application.audio.dc0130_RefillOrChangePlan_DM_initial\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: refill\n          displayName: refill\n        - id: change_plan\n          displayName: change_plan\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"com.nuance.att.application.audio.dc0130_RefillOrChangePlan_DM_noinput1\"\n        - \"com.nuance.att.application.audio.dc0130_RefillOrChangePlan_DM_noinput2\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_dc0130_RefillOrChangePlan_DM\n  value: =Text(Global.dc0130_RefillOrChangePlan_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_dc0130_RefillOrChangePlan_DM = \"refill\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: refillAccount\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: dc0135_DataRefill_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_dc0130_RefillOrChangePlan_DM = \"change_plan\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.intent\n              value: changePlan\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: dc0120_DataChangePlan_SD\n```", "timestamp": **********.4900236, "content_hash": "8ffcb973945e62527cdb70e472a4b711"}