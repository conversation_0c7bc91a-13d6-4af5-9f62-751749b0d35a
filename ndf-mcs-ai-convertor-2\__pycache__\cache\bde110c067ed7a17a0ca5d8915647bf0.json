{"response": "```yaml\n- kind: Question\n  id: AP1025_FullProcess_DM\n  displayName: AP1025_FullProcess_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.AP1025_FullProcess_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.AcpSMSFailure = true,\n            [\n                \"Sorry, I wasn't able to send the text  But here is how the process works\",\n                \"Keep in mind our representatives can't complete these steps on your behalf  You'll have a chance to repeat this information when I'm doneFirst you need to submit an application at the following federal webpage a c p benefitorg  If approved for the discount you will be provided an 'approval ID'You can also call them direct at 800 324 9473For the second step you will use this approval ID to apply your discount to your Metro by T-Mobile account  To do this you'll log into 'my account' and go the profile section Scroll down until you see the link for the Affordable connectivity program  From there just follow the instructions to apply the discount to your billYou'll receive a text message within 3-5 business days of submitting your application with Metro that will contain approval information Please note, it may come under a different sender other than metro by Tmobile If approved, your ACP discount will apply to your account within 1-2 billing cycles\",\n                \"You can say 'repeat that' or 'main menu'\"\n            ],\n            true,\n            [\n                \"Ok, that's the first step in applying for this federal discount Here's how it works\",\n                \"Keep in mind our representatives can't complete these steps on your behalf  You'll have a chance to repeat this information when I'm doneFirst you need to submit an application at the following federal webpage a c p benefitorg  If approved for the discount you will be provided an 'approval ID'You can also call them direct at 800 324 9473For the second step you will use this approval ID to apply your discount to your Metro by T-Mobile account  To do this you'll log into 'my account' and go the profile section Scroll down until you see the link for the Affordable connectivity program  From there just follow the instructions to apply the discount to your billYou'll receive a text message within 3-5 business days of submitting your application with Metro that will contain approval information Please note, it may come under a different sender other than metro by Tmobile If approved, your ACP discount will apply to your account within 1-2 billing cycles\",\n                \"To hear this information again say, 'repeat that'I can also text a link to our ACP information page just say, 'text me'  You can also say 'main menu'\"\n            ]\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: repeat_that\n          displayName: repeat_that\n        - id: text_me\n          displayName: text_me\n        - id: main_menu\n          displayName: main_menu\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n              true,\n              Global.AcpSMSFailure = true,\n              \"You can say 'repeat that' or if you'd like, I can text you a link to the ACP information page, just say 'text me'  You can also say 'main menu'\",\n              true,\n              \"You can say 'repeat that' or 'main menu'\"\n          )\n          }\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - |\n          {Switch(\n              true,\n              Global.AcpSMSFailure = true,\n              \"You can say 'repeat that' or press 1  If you'd like a link to the ACP information page, say 'text me', or press 2   You can also say 'main menu', or  press 3\",\n              true,\n              \"You can say 'repeat that' or press 1main menu' or  press 2\"\n          )\n          }\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - |\n          {Switch(\n              true,\n              Global.AcpSMSFailure = true,\n              \"You can say 'repeat that' or press 1  If you'd like a link to the ACP information page, say 'text me', or press 2   You can also say 'main menu', or  press 3\",\n              true,\n              \"You can say 'repeat that' or press 1main menu' or  press 2\"\n          )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.saidOperatorAcpAt1025\n  value: GlobalVars.saidOperatorAcpAt1025\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.AcpSMSFailure\n  value: GlobalVars.AcpSMSFailure\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.stateVar\n  value: \"GlobalVars.AcpSMSFailure != undefined ? GlobalVars.AcpSMSFailure : false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.operatorReqCount_AC1025\n  value: GlobalVars.operatorReqCount_AC1025 != undefined ? GlobalVars.operatorReqCount_AC1025 : 0\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_AP1025_FullProcess_DM\n  value: =Text(Global.AP1025_FullProcess_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AP1025_FullProcess_DM = \"repeat_that\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: AP1025_FullProcess_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_AP1025_FullProcess_DM = \"text_me\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.campaignId\n              value: GlobalVars.GetBCSParameters.acp_info_campaign_id\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: AP1010_SendACPLinks_DB_DA\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_AP1025_FullProcess_DM = \"main_menu\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: getReturnLink()\n\n# Command handling for \"operator\"\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.command = \"operator\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callerSaidOperator\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.saidOperatorAcpAt1025\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.operatorReqCount_AC1025\n          value: GlobalVars.operatorReqCount_AC1025 + 1\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.GlobalVars.operatorReqCount_AC1025 = 1, true, false)\n              actions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/AP1025_Operator_01.wav\\\">Our representatives cannot complete the national verifier application on your behalf  You can call them direct at 800 324 9473 again the number is 800-324-9472'</audio>\"\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.AcpSMSFailure = true, true, false)\n                      actions:\n                        - kind: SendActivity\n                          id: sendActivity_REPLACE_THIS\n                          activity:\n                            speak:\n                              - \"<audio src=\\\"AUDIO_LOCATION/AP1025_command_01.wav\\\">To hear the ACP information again say, repeat that  You can also say main menu</audio>\"\n                  elseActions:\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/AP1025_command_02.wav\\\">To hear the ACP information again say, repeat that  You can also say main menu</audio>\"\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: AP1025_FullProcess_DM\n          elseActions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: AP1065_CallTransfer_SD\n```", "timestamp": 1749527538.4453707, "content_hash": "bde110c067ed7a17a0ca5d8915647bf0"}