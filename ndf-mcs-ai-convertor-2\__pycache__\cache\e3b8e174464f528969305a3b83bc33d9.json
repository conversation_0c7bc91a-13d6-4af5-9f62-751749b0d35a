{"response": "```yaml\n- kind: Question\n  id: WR1000_CancelSetupAutoPayWrap_DM\n  displayName: WR1000_CancelSetupAutoPayWrap_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/WR1000_nm1_01.wav\\\">If you need anything else say 'main menu' or press 1  Otherwise you can simply hang up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/WR1000_nm2_01.wav\\\">If you need anything else press 1  Otherwise you can simply hang up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/WR1000_nm2_01.wav\\\">If you need anything else press 1  Otherwise you can simply hang up</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.WR1000_CancelSetupAutoPayWrap_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/WR1000_ini_01.wav\\\">If you're finished  you can simply hang up  If you need anything else say Main Menu</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: main_menu\n          displayName: main_menu\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/WR1000_nm1_01.wav\\\">If you need anything else say 'main me", "timestamp": 1749529976.3998058, "content_hash": "e3b8e174464f528969305a3b83bc33d9"}