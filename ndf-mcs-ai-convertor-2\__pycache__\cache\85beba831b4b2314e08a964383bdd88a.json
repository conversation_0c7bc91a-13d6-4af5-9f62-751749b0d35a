{"response": "```yaml\n- kind: Question\n  id: SQ1000_RecordSchoolName_DM\n  displayName: SQ1000_RecordSchoolName_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SQ1000_nm1_01.wav\\\">Please SAY the name of your first elementary school, or press 1 to answer another question</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SQ1000_nm2_01.wav\\\">If you don t want to answer this question, press 1 Otherwise, SAY the name of your first elementary school now</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SQ1000_nm3_01.wav\\\">If you ever forget your security code, you ll be able to access it by answering this security question If you prefer to answer the other security question, press one After the beep, please say the name of your first elementary school</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.SQ1000_RecordSchoolName_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/SQ1000_ini_01.wav\\\">After the beep, go ahead and tell me the name of the school</audio>\"\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/SQ1000_ni1_01.wav\\\">Sorry, I didn t hear you Please say the name of your first elementary school, or press 1 to answer another question</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SQ1000_ni1_01.wav\\\">Sorry, I didn t hear you Please say the name of your first elementary school, or press 1 to answer another question</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SQ1000_ni1_01.wav\\\">Sorry, I didn t hear you Please say the name of your first elementary school, or press 1 to answer another question</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.SQ1000Reentry\n  value: true\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.recordDuration\n  value: audio$.duration\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.recordTermchar\n  value: audio$.termchar\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.recordMaxtime\n  value: audio$.maxtime\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.recordTermchar = \"1\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/SQ1000_out_01.wav\\\">Okay, if you want to go back to this previous question, just press 1</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: SQ1030_RecordStreetName_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: |\n            =If(And(Global.recordTermchar <> null , Global.recordTermchar <> \"\" , Global.recordTermchar <> \"#\"), true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: SQ1000_RecordSchoolName_DM\n\n      elseActions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.schoolNameFilename\n          value: SQ1000_RecordSchoolName_DM.returnvalue\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: SQ1010_SpellSchoolName_DM\n```", "timestamp": 1749529428.6056676, "content_hash": "85beba831b4b2314e08a964383bdd88a"}