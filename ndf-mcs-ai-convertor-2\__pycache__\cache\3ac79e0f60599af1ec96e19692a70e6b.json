{"response": "```yaml\n- kind: Question\n  id: AQ1415_SMSWaitSBI_DM\n  displayName: AQ1415_SMSWaitSBI_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.AQ1415_SMSWaitSBI_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.askSQEntry = \"transfer\",\n            \"When you receive it, just say 'I got it' Your message is on the way You'll need the answer to your security question when you talk to the agent When you receive it, say 'I got it' It shouldn't be much longer When you have it, just say 'I got it', and I'll take you to an agent\",\n            true,\n            \"When you receive it, just say 'I got it' Your message is on the way When you receive it, say 'I got it' I'll ask you for the answer again before we continue It shouldn't be much longer When you have it, just say 'I got it'\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: got_it\n          displayName: got_it\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.askSQEntry\n  value: GlobalVars.askSQEntry\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_AQ1415_SMSWaitSBI_DM\n  value: =Text(Global.AQ1415_SMSWaitSBI_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AQ1415_SMSWaitSBI_DM = \"got_it\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.askSQEntry = \"transfer\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.securityCheckPassed\n                  value: true\n\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/AQ1415_out_02.wav\\\">Great When you get to the agent, you can give them the answer in the message</audio>\"\n                      - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: AQ1430_ReceivedSMSXfer_JDA\n\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.smsReceived\n              value: true\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.securityQuestionAttempts\n              value: 0\n\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/AQ1415_out_01.wav\\\">Great</audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: AQ1425_ReceivedSMSLogin_JDA_DA\n```", "timestamp": 1749558794.5219538, "content_hash": "3ac79e0f60599af1ec96e19692a70e6b"}