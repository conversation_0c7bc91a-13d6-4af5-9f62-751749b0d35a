{"response": "```yaml\n- kind: Question\n  id: ca0111_DisambiguatePINReset_DM\n  displayName: ca0111_DisambiguatePINReset_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I missed that</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ca0111_ni1_01.wav\\\">What PIN would you like to reset? Say Account PIN or press 1, or say Voicemail or press 2 Or say Main Menu or press star</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">Sorry</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ca0111_ni2_01.wav\\\">To reset your 4-digit account PIN, press 1 To reset your *voicemail* PIN, press 2 Or to go back to the main menu, press star</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ca0111_DisambiguatePINReset_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ca0111_ini_01.wav\\\">Do you want to reset your 4-digit *Account* PIN, or your *Voicemail* PIN?</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: account\n          displayName: account\n        - id: voicemail\n          displayName: voicemail\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ca0111_ni1_01.wav\\\">What PIN would you like to reset? Say Account PIN or press 1, or say Voicemail or press 2 Or say Main Menu or press star</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ca0111_ni2_01.wav\\\">To reset your 4-digit account PIN, press 1 To reset your *voicemail* PIN, press 2 Or to go back to the main menu, press star</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ca0111_DisambiguatePINReset_DM\n  value: =Text(Global.ca0111_DisambiguatePINReset_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ca0111_DisambiguatePINReset_DM = \"account\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/ca0111_out_01.wav\\\">Okay</audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: accountPINReset\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ca0150_AccountChangePINReset_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ca0111_DisambiguatePINReset_DM = \"voicemail\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.intent\n              value: changeVmailPassword\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: ca0130_AccountChangeVMPassword_SD\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_ca0111_DisambiguatePINReset_DM = \"repeat\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: ca0111_DisambiguatePINReset_DM\n```", "timestamp": **********.0946035, "content_hash": "6b1c93b907f8d2b5fb1fb0670330c74b"}