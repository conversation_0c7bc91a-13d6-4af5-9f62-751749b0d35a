{"response": "```yaml\n- kind: Question\n  id: bp0320_AskBillingPhoneNumber_DM\n  displayName: bp0320_AskBillingPhoneNumber_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I missed that</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/bp0320_nm1_01.wav\\\">Please say or enter the phone number on file for that card </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">Sorry</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/bp0320_nm2_01.wav\\\">And the phone number you have on file with your bank?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/bp0320_nm3_01.wav\\\">Please enter the billing phone number that's associated with your credit or debit card account, or enter it, followed by pound </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.bp0320_AskBillingPhoneNumber_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bp0320_ini_01.wav\\\">And the phone number you have on file with your bank?</audio>\"\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/bp0320_ni1_01.wav\\\">Please say the billing phone number thats associated with your credit or debit card account, or enter it, followed by pound</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/bp0320_ni2_01.wav\\\">To continue, I need to verify the 10 digit phone number that your credit or debit card issuer has on file for you Please enter it now, followed by pound</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.bankPhoneNumber\n  value: bp0320_AskBillingPhoneNumber_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.addressLookupByPhoneNumber\n  value: true\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: bp0325_AddressLookupByPhoneNumber_DB_DA\n```", "timestamp": **********.0581179, "content_hash": "98d1977c365100ed7b3da0c84e926c74"}