{"response": "```yaml\n- kind: Question\n  id: NP0005_PortNumberYN_DM\n  displayName: NP0005_PortNumberYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/NP0005_nm1_01.wav\\\">Do you want to keep a telephone number you already have with a different phone company?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/NP0005_nm2_02.wav\\\">Do you want to keep a telephone number you have with another phone company? Say 'yes' or press one, or 'no' or press two</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/NP0005_nm3_01.wav\\\">If you d like to keep a phone number you have with a different phone company, say  yes , or press one  To activate and get a new number, say  no , or press two</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.NP0005_PortNumberYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/NP0005_ini_01.wav\\\">And do you want to keep using a phone number from another carrier?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/NP0005_ni1_01.wav\\\">Do you want to keep a telephone number you already have with a different phone company?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/NP0005_ni2_01.wav\\\">Do you want to keep a telephone number you have with another phone company? Say 'yes' or press one, or 'no' or press two</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/NP0005_ni3_01.wav\\\">If you d like to keep a phone number you have with a different phone company, say  yes  or press one  To activate and get a new number, say  no  or press two</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.npi_flag\n  value: false\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.NumberPortInVars.portingNumber\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.NumberPortInVars.accountNumber\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.NumberPortInVars.npSecPin\n  value: \"\"\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_NP0005_PortNumberYN_DM\n  value: =Text(Global.NP0005_PortNumberYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_NP0005_PortNumberYN_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.npi_flag\n          value: true\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: NP0010_GetOSPMDN_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_NP0005_PortNumberYN_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/NP0005_out_01.wav\\\">Alright</audio>\"\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.npi_flag\n              value: false\n```", "timestamp": **********.6493428, "content_hash": "4e8bfc20605d6de4686db938e547c23d"}