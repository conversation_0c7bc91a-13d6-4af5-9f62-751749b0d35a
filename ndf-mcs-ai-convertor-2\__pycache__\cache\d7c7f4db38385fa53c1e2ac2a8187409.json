{"response": "```yaml\n- kind: Question\n  id: aa5010_PinSelect2DTMF_DM\n  displayName: aa5010_PinSelect2DTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.dnisInfo.callType = \"TX\" || Global.dnisInfo.callType = \"WV\",\n              \"Re-enter your new four digit PIN\",\n          \n              true,\n              \"To verify your entry, please reenter your new four digit PIN\"\n          )\n        }\n      - |\n        {Switch(\n              true,\n              Global.dnisInfo.dtmfOnlyFlag = true,\n              \"Sorry, I still didnt get that\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.aa5010_PinSelect2DTMF_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.dnisInfo.callType = \"TX\" || Global.dnisInfo.callType = \"WV\",\n              \"Re-enter your new four digit PIN\",\n          \n              true,\n              \"To verify your entry, please reenter your new four digit PIN\"\n          )\n        }\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.dnisInfo.callType = \"TX\" || Global.dnisInfo.callType = \"WV\",\n                \"Re-enter your new four digit PIN\",\n            \n                true,\n                \"To verify your entry, please reenter your new four digit PIN\"\n            )\n          }\n        - |\n          {Switch(\n                true,\n                Global.dnisInfo.dtmfOnlyFlag = true,\n                \"Sorry, I still didnt get that\"\n            )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.pinVariables.pin2\n  value: aa5010_PinSelect2DTMF_DM.returnvalue\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.pinVariables.pinMatch = true, true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa5008_PinCheck_DB_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: |\n            =If(Global.pinVariables.pinChangeCounter <= 2, true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/aa5010_out_03.wav\\\">We are sorry, but the PINs you entered dont  match</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa5005_PinSelect1DTMF_DM\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.dnisInfo.pinChangeFailureDisconnect = true, true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.globalVariables.transferAllowed\n                  value: false\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/aa5010_out_01.wav\\\">The PINs you entered don't match We are unable to complete your PIN selection request at this time</audio>\"\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: transferHandler_CS\n\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.transferReason\n              value: pin\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.transferAllowed\n              value: true\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/aa5010_out_04.wav\\\">We are unable to complete your PIN selection request at this time You will now be transferred to a customer service representative</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: transferHandler_CS\n```", "timestamp": 1749543551.8211474, "content_hash": "d7c7f4db38385fa53c1e2ac2a8187409"}