{"response": "```yaml\n- kind: Question\n  id: st0510_AskSpecificNumber_DM\n  displayName: st0510_AskSpecificNumber_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.intent = \"techSupportCallText\",\n              \"Are your calling or texting problems related to a specific number? Say Yes or No\",\n          \n              true,\n              \"Are your calling problems related to a specific number? Say Yes or No\"\n          )\n        }\n      - |\n        {Switch(\n              true,\n              Global.intent = \"techSupportCallText\",\n              \"If youre having problems placing calls or sending texts to a particular number, say Yes or press 1 Or, if youre having problems placing calls or texting to more than one number, say No or press 2\",\n          \n              true,\n              \"If youre having problems calling a particular number, say Yes or press 1 Or, if youre having problems calling more than one number, say No or press 2\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.st0510_AskSpecificNumber_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.intent = \"techSupportCallText\",\n              \"I dont see any calling or texting issues on your account, so well need to do some troubleshooting Are you having problems making calls or sending texts to a specific phone number?\",\n          \n              true,\n              \"I dont see any calling issues on your account, so well need to do some troubleshooting Are you having problems making calls to a specific phone number?\"\n          )\n        }\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.intent = \"techSupportCallText\",\n                \"Are your calling or texting problems related to a specific number? Say Yes or No\",\n            \n                true,\n                \"Are your calling problems related to a specific number? Say Yes or No\"\n            )\n          }\n        - |\n          {Switch(\n                true,\n                Global.intent = \"techSupportCallText\",\n                \"If youre having problems placing calls or sending texts to a particular number, say Yes or press 1 Or, if youre having problems placing calls or texting to more than one number, say No or press 2\",\n            \n                true,\n                \"If youre having problems calling a particular number, say Yes or press 1 Or, if youre having problems calling more than one number, say No or press 2\"\n            )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_st0510_AskSpecificNumber_DM\n  value: =Text(Global.st0510_AskSpecificNumber_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_st0510_AskSpecificNumber_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.callTextProblem\n          value: specific\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: st0515_PlayTips_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_st0510_AskSpecificNumber_DM = \"false\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.callTextProblem\n              value: general\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: st0515_PlayTips_PP\n```", "timestamp": 1749472211.9216309, "content_hash": "a107f01c1b74a8c3159b6eada4617f94"}