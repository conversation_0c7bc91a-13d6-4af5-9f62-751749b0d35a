{"response": "```yaml\n- kind: Question\n  id: MC1030_ConfirmSingleAreaCodeYN_DM\n  displayName: MC1030_ConfirmSingleAreaCodeYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MC1050_nm1_01.wav\\\">Please say  yes  or  no   Is that the area code you d like for your new phone number?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MC1050_nm2_01.wav\\\">If you d like that area code for your new phone number say  yes  or press 1  If you want a different area code, say  no  or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MC1050_nm3_01.wav\\\">If you like that area code for your new phone number say  yes  or press 1  If you want a different area code, say  no  or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.MC1030_ConfirmSingleAreaCodeYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/MC1050_ini_01.wav\\\">It looks as though, there's one area code I can offer you - it's </audio>{Global.areaCode}<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio><audio src=\\\"AUDIO_LOCATION/MC1050_ini_02.wav\\\">Can I go ahead and use that one?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MC1050_nm1_01.wav\\\">Please say  yes  or  no   Is that the area code you d like for your new phone number?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MC1050_nm2_01.wav\\\">If you d like that area code for your new phone number say  yes  or press 1  If you want a different area code, say  no  or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MC1050_nm3_01.wav\\\">If you like that area code for your new phone number say  yes  or press 1  If you want a different area code, say  no  or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.areaCode\n  value: GlobalVars.NPA\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_MC1030_ConfirmSingleAreaCodeYN_DM\n  value: =Text(Global.MC1030_ConfirmSingleAreaCodeYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MC1030_ConfirmSingleAreaCodeYN_DM = \"true\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: MC1070_MDNChangeMsg_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_MC1030_ConfirmSingleAreaCodeYN_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/MC1050_out_01.wav\\\">That s fine</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: MC1090_CallTransfer_SD\n```", "timestamp": 1749528655.332138, "content_hash": "9ecf7c58413218b403ad6e7573a51ba0"}