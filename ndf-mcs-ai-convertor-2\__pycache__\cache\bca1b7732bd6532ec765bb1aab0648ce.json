{"response": "```yaml\n- kind: Question\n  id: bm0535_RefillPINWrap_DM\n  displayName: bm0535_RefillPINWrap_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bm0535_RefillPINWrap_DM_noinput_1.wav\\\">[custom audio: com.nuance.att.application.audio.bm0535_RefillPINWrap_DM_noinput_1]</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/bm0535_RefillPINWrap_DM_nomatch_2.wav\\\">[custom audio: com.nuance.att.application.audio.bm0535_RefillPINWrap_DM_nomatch_2]</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.bm0535_RefillPINWrap_DM_reco\n  prompt:\n    speak:\n      - \"[custom audio: com.nuance.att.application.audio.bm0535_RefillPINWrap_initial]\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: credit_card\n          displayName: credit_card\n        - id: debit_card\n          displayName: debit_card\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/bm0535_RefillPINWrap_DM_noinput_1.wav\\\">[custom audio: com.nuance.att.application.audio.bm0535_RefillPINWrap_DM_noinput_1]</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_bm0535_RefillPINWrap_DM\n  value: =Text(Global.bm0535_RefillPINWrap_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_bm0535_RefillPINWrap_DM = \"credit_card\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.refillMethod\n          value: credit\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferDestination\n          value: vesta\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: bm0610_IsVestaLoginRequired_JDA_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_bm0535_RefillPINWrap_DM = \"debit_card\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.refillMethod\n              value: debit\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.transferDestination\n              value: vesta\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: bm0610_IsVestaLoginRequired_JDA_DA\n```", "timestamp": 1749469750.436449, "content_hash": "bca1b7732bd6532ec765bb1aab0648ce"}