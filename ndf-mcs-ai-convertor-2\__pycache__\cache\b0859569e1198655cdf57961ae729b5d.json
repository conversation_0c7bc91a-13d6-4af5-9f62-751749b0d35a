{"response": "```yaml\n- kind: Question\n  id: MC1150_ReadNewNum_DM\n  displayName: MC1150_ReadNewNum_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MC1150_nm1_02.wav\\\">To hear your new account number again, say  repeat  Otherwise, please wait and someone will be right with you</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MC1150_nm1_03.wav\\\">Please say  yes  or  no  Would you like to hear your new telephone number again?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MC1150_nm2_02.wav\\\">To hear your new account number again, say  repeat  Otherwise, please wait while I connect you</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MC1150_nm2_03.wav\\\">If you d like me to repeat your new telephone number, say  yes  or press one If not, say  no  or press two</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MC1150_nm3_02.wav\\\">If you d like to hear your new account number again, say  repeat  or press seven Otherwise, please wait and an activation specialist will be with you shortly</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MC1150_nm2_03.wav\\\">If you d like me to repeat your new telephone number, say  yes  or press one If not, say  no  or press two</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.MC1150_ReadNewNum_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/MC1150_ini_01.wav\\\">Your new phone number is </audio>{Global.NewMdn}<audio src=\\\"AUDIO_LOCATION/MC1150_ini_03.wav\\\">That phone number again is </audio>{Global.NewMdn}<audio src=\\\"AUDIO_LOCATION/MC1150_ini_02.wav\\\">Once you have paid the $12 fee plus tax, you may need to restart your phone If you have any problems, you can call 888 8METRO8 for customer service</audio><audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio><audio src=\\\"AUDIO_LOCATION/MC1150_ini_12.wav\\\">Would you like to hear that again? </audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/MC1150_ni1_01.wav\\\">When you re ready to write down the information, say  continue   Otherwise, I ll keep waiting</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.NewMdn\n  value: GlobalVars.ChangeNumber.newMdn\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.mdn\n  value: GlobalVars.ChangeNumber.newMdn\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_MC1150_ReadNewNum_DM\n  value: =Text(Global.MC1150_ReadNewNum_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MC1150_ReadNewNum_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/MC1150_out_01.wav\\\">Sure</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: MC1150_ReadNewNum_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_MC1150_ReadNewNum_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/MC1150_out_02.wav\\\">All right</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n```\n**Notes:**\n- The `{Global.NewMdn}` in the prompt is used to represent the custom prompt for the new phone number, as per the XML's `<prompt type=\"custom\" expr=\"NewMdn\">`.\n- All audio references are wrapped in `<audio src=\"AUDIO_LOCATION/...\">...</audio>` as per your format.\n- The `entity` is set to `BooleanPrebuiltEntity` because the success actions are for \"true\" and \"false\".\n- The `SetVariable` actions are created for the session-mapping keys.\n- The `SendActivity` is used to play the audio in the success actions before the next action.\n- The `ConditionGroup` structure follows your instructions for handling multiple labels under `<success>`.\n- All ids for set/goto/sendActivity/conditionGroup/conditionItem are set to `REPLACE_THIS` as per your placeholder convention.", "timestamp": 1749528720.448103, "content_hash": "b0859569e1198655cdf57961ae729b5d"}