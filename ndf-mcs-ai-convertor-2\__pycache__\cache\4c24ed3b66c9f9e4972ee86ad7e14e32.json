{"response": "```yaml\n- kind: Question\n  id: aa4126_EBTOverviewWrapUp_DM\n  displayName: aa4126_EBTOverviewWrapUp_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.aa4126_EBTOverviewWrapUp_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.dnisInfo.ebtOverviewOfferAgent = true,\n            \"If you would like to hear that again, say  Repeat That  To go to the main menu, say  Main Menu  To speak with e Customer Service Representative, say  Agent  And if you re done feel free to hang up\",\n            true,\n            \"If you would like to hear that again, say  Repeat That  To go to the main menu, say  Main Menu  And if you re done feel free to hang up\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: main_menu\n          displayName: main_menu\n        - id: repeat\n          displayName: repeat\n        - id: agent\n          displayName: agent\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa4126_EBTOverviewWrapUp_DM\n  value: =Text(Global.aa4126_EBTOverviewWrapUp_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa4126_EBTOverviewWrapUp_DM = \"main_menu\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa4004_GetMainMenuOptions_DB_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa4126_EBTOverviewWrapUp_DM = \"repeat\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/aa4125_out_01.wav\\\">Again</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa4120_EBTOverViewPlayout_PP\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_aa4126_EBTOverviewWrapUp_DM = \"agent\", true, false)\n              actions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.dnisInfo.ebtOverviewOfferAgent = true, true, false)\n                      actions:\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.globalVariables.transferAllowed\n                          value: true\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.globalVariables.transferReason\n                          value: default\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: transferHandler_CS\n\n                  elseActions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.globalVariables.transferAllowed\n                      value: false\n```", "timestamp": 1749543916.0048223, "content_hash": "4c24ed3b66c9f9e4972ee86ad7e14e32"}