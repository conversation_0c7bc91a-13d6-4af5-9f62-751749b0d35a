{"response": "```yaml\n- kind: Question\n  id: CM0110_OfferContinueOrAgent_DM\n  displayName: CM0110_OfferContinueOrAgent_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/CM0110_ini_01.wav\\\">It looks like your phone's not fully compatible with our network, so you might get lower signal quality on it Would you like to 'continue', or 'talk to an agent'? </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/CM0110_nm2_01.wav\\\">To continue activating your new phone, say 'continue' or press 1 To speak to someone now, say 'agent' or press 0 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/CM0110_maxnomatch2_01.wav\\\">To continue switching to this phone, say 'continue' or press 1 To speak to someone now, say 'agent' or press 0</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/CM0110_nm2_01.wav\\\">To continue activating your new phone, say 'continue' or press 1 To speak to someone now, say 'agent' or press 0 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/CM0110_maxnomatch2_01.wav\\\">To continue switching to this phone, say 'continue' or press 1 To speak to someone now, say 'agent' or press 0</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.CM0110_OfferContinueOrAgent_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/CM0110_ini_01.wav\\\">It looks like your phone's not fully compatible with our network, so you might get lower signal quality on it Would you like to 'continue', or 'talk to an agent'? </audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: agent\n          displayName: agent\n        - id: continue\n          displayName: continue\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/CM0110_ini_01.wav\\\">It looks like your phone's not fully compatible with our network, so you might get lower signal quality on it Would you like to 'continue', or 'talk to an agent'? </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/CM0110_nm2_01.wav\\\">To continue activating your new phone, say 'continue' or press 1 To speak to someone now, say 'agent' or press 0 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/CM0110_maxnomatch2_01.wav\\\">To continue switching to this phone, say 'continue' or press 1 To speak to someone now, say 'agent' or press 0</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/CM0110_nm2_01.wav\\\">To continue activating your new phone, say 'continue' or press 1 To speak to someone now, say 'agent' or press 0 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/CM0110_maxnomatch2_01.wav\\\">To continue switching to this phone, say 'continue' or press 1 To speak to someone now, say 'agent' or press 0</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.callType\n  value: GlobalVars.callType\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_CM0110_OfferContinueOrAgent_DM\n  value: =Text(Global.CM0110_OfferContinueOrAgent_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_CM0110_OfferContinueOrAgent_DM = \"agent\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/CM0110_out_01.wav\\\">Sure</audio>\"\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.language = \"en-US\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.TransferTag\n                  value: Customer_Support_English\n\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.TransferTag\n              value: Customer_Support_Spanish\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.callType = \"activate\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.activationResult\n                  value: transfer\n\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.compatibilityTransfer\n              value: true\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: getReturnLink()\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_CM0110_OfferContinueOrAgent_DM = \"continue\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n```", "timestamp": 1749528030.8215668, "content_hash": "3b21105e7d018f4cc48ed3fc4bb66483"}