{"response": "```yaml\n- kind: Question\n  id: IU1051_AskMetroDevice_DM\n  displayName: IU1051_AskMetroDevice_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/IU1051_nm1_01.wav\\\">If the device you're trying to activate puchased directly from Metro by Tmobile say 'yes' or press '  If not say 'no' or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/IU1051_nm2_01.wav\\\">If you purchased your device directly from Metro by Tmobile press 1 If not press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/IU1051_nm2_01.wav\\\">If you purchased your device directly from Metro by Tmobile press 1 If not press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.IU1051_AskMetroDevice_DM_reco\n  prompt:\n    speak:\n      - \"{Switch(         true,         Global.IU1051saidOperator <> true,         \\\"Is your device a Metro by Tmobile device?\\\"     )}\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/IU1051_nm1_01.wav\\\">If the device you're trying to activate puchased directly from Metro by Tmobile say 'yes' or press '  If not say 'no' or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/IU1051_nm2_01.wav\\\">If you purchased your device directly from Metro by Tmobile press 1 If not press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/IU1051_nm2_01.wav\\\">If you purchased your device directly from Metro by Tmobile press 1 If not press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.requestOperatorUnidentified\n  value: GlobalVars.requestOperatorUnidentified\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.IU1051saidOperator\n  value: GlobalVars.IU1051saidOperator\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_IU1051_AskMetroDevice_DM\n  value: =Text(Global.IU1051_AskMetroDevice_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_IU1051_AskMetroDevice_DM = \"true\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: IU1055_ActivationInformation_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_IU1051_AskMetroDevice_DM = \"false\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: IU1065_GoToTransfer_SD\n```", "timestamp": 1749530294.0789444, "content_hash": "95840bd252e5b04c23a97adca7a1e911"}