{"response": "```yaml\n- kind: Question\n  id: bp0415_AskNewExpDate_DM\n  displayName: bp0415_AskNewExpDate_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bp0415_nm1_01.wav\\\">Please say or enter the new four-digit expiration date on your card For example zero three nineteen</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/bp0415_nm2_01.wav\\\">Please enter the new four-digit expiration date on your card For example, if the date is March 2019, you should enter zero three one nine</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.bp0415_AskNewExpDate_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.autoRefillUpdate = true,\n            \"What's the new expiration date on your card?\",\n            true,\n            \"It looks like your card is about to expire What's the new expiration date? \"\n        )}\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n              true,\n              Global.autoRefillUpdate = false || Global.autoRefillUpdate = \"\",\n              \"It looks like your card is about to expire\",\n              true,\n              \"Please say or enter the new four-digit expiration date on your card For example zero three nineteen\"\n          )\n          }\n        - |\n          {Switch(\n              true,\n              Global.autoRefillUpdate = false || Global.autoRefillUpdate = \"\",\n              \"It looks like your card is about to expire\",\n              true,\n              \"Please enter the new four-digit expiration date on your card For example, if the date is March 2019, you should enter zero three one nine\"\n          )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.chargeExpirationDate\n  value: bp0415_AskNewExpDate_DM.returnvalue\n```", "timestamp": 1749470299.2459457, "content_hash": "847541bdaf3d9f73f1d383cd60905638"}