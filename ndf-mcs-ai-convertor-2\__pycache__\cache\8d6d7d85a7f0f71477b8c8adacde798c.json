{"response": "```yaml\n- kind: Question\n  id: aa2568_CaseNumber2_DM\n  displayName: aa2568_CaseNumber2_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.aa2568_CaseNumber2_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa2568_ini_01.wav\\\">Please say your case number including the letter</audio>\"\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.result\n  value: aa2568_CaseNumber2_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.case2\n  value: aa2568_CaseNumber2_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.validationCriteriaVariables.validationValue\n  value: validationCriteriaVariables.validationValue + ';' + result\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.validationCriteriaVariables.validationType\n  value: validationCriteriaVariables.validationType + ';' + validationCriteriaVariables.currentValidationType\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.globalVariables.currentTask = \"cardReplacement\" || Global.globalVariables.currentTask = \"altAuthVal\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa2510_ValidationDecision_JDA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: |\n            =If(Global.result = Global.validationCriteriaVariables.currentValidationData, true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa2510_ValidationDecision_JDA\n\n      elseActions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.validationCriteriaVariables.validationDataMismatch\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.validationCriteriaVariables.validationDataMismatchCaseNum2\n          value: true\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa2510_ValidationDecision_JDA\n```", "timestamp": 1749544259.126192, "content_hash": "8d6d7d85a7f0f71477b8c8adacde798c"}