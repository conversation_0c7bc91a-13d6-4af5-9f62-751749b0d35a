{"response": "```yaml\n- kind: Question\n  id: aa1511_ConfirmAddress_DM\n  displayName: aa1511_ConfirmAddress_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.aa1511_ConfirmAddress_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.validateLostStolenCriteriaInfo.poBox = false,\n            \"Our records indicate that your mailing address starts with\",\n            Global.validateLostStolenCriteriaInfo.poBox = true,\n            \"Our records indicate that your mailing address starts with PO Box\"\n        )}\n        {If(\n            Global.validateLostStolenCriteriaInfo.cardholderMailingAddress <> null && Global.validateLostStolenCriteriaInfo.cardholderMailingAddress <> \"\",\n            Global.validateLostStolenCriteriaInfo.cardholderMailingAddress,\n            \"\"\n        )}\n        <audio src=\"AUDIO_LOCATION/silence_500ms.wav\">silence_500ms</audio>\n        <audio src=\"AUDIO_LOCATION/aa1511_ini_02.wav\">Is this correct?</audio>\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa1511_ConfirmAddress_DM\n  value: =Text(Global.aa1511_ConfirmAddress_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa1511_ConfirmAddress_DM = \"yes\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.cardReplacementVariables.callerConfirmedAddress\n          value: true\n        # AddEventLogValue script for EBT218 (no YAML action needed)\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa1520_CardWillBeDeactivatedMsg_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa1511_ConfirmAddress_DM = \"no\", true, false)\n          actions:\n            # AddEventLogValue script for EBT219 (no YAML action needed)\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.dnisInfo.invalidAddressTransferAllowed = true, true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.globalVariables.transferAllowed\n                      value: true\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/aa1511_out_01.wav\\\">To update your records, I ll transfer you to customer service</audio>\"\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: transferHandler_CS\n\n              elseActions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: aa1520_CardWillBeDeactivatedMsg_PP\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - |\n                        {Switch(\n                            true,\n                            Global.dnisInfo.invalidAddressMsgOn = true,\n                            Global.dnisInfo.invalidAddressMsg,\n                            true,\n                            \"<audio src=\\\"AUDIO_LOCATION/aa1511_out_02.wav\\\">In order to receive a new card, you will need to contact your local state office They will update your records and provide instructions on how to receive a new card</audio>\"\n                        )}\n```", "timestamp": 1749543543.8877516, "content_hash": "f6e209903fb2008c54d0a0b533fac18b"}