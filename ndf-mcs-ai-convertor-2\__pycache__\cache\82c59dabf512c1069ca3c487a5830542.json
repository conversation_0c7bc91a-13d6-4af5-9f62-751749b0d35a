{"response": "```yaml\n- kind: Question\n  id: NP0062_AskIfSameZIPYN_DM\n  displayName: NP0062_AskIfSameZIPYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/NP0062_nm1_01.wav\\\">Just say  yes  or  no  - Is the ZIP code that your old phone company has for you the same as as the one you just gave me?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/NP0062_nm2_01.wav\\\">I d just like to check whether the ZIP code that your old provider has on file for you is the same as the one you gave me before pause If it is, say  yes  or press 1 If not, say  no  or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/NP0062_nm2_01.wav\\\">I d just like to check whether the ZIP code that your old provider has on file for you is the same as the one you gave me before pause If it is, say  yes  or press 1 If not, say  no  or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.NP0062_AskIfSameZIPYN_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.npi_type = \"landline\",\n            \"Is the ZIP code your old provider has for you the same as the one you just told me?\",\n            true,\n            \"And is the ZIP code your old provider has on file the same as the one you just gave me?\"\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/NP0062_nm1_01.wav\\\">Just say  yes  or  no  - Is the ZIP code that your old phone company has for you the same as as the one you just gave me?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/NP0062_nm2_01.wav\\\">I d just like to check whether the ZIP code that your old provider has on file for you is the same as the one you gave me before pause If it is, say  yes  or press 1 If not, say  no  or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/NP0062_nm2_01.wav\\\">I d just like to check whether the ZIP code that your old provider has on file for you is the same as the one you gave me before pause If it is, say  yes  or press 1 If not, say  no  or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.npi_type\n  value: NumberPortInVars.npi_type\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_NP0062_AskIfSameZIPYN_DM\n  value: =Text(Global.NP0062_AskIfSameZIPYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_NP0062_AskIfSameZIPYN_DM = \"true\", true, false)\n      actions: []\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_NP0062_AskIfSameZIPYN_DM = \"false\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: NP0065_GetOSPZipCode_DM\n```", "timestamp": **********.8693395, "content_hash": "82c59dabf512c1069ca3c487a5830542"}