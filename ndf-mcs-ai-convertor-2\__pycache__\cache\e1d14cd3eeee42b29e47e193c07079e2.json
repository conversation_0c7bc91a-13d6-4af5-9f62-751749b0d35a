{"response": "```yaml\n- kind: Question\n  id: MW1020_ManageCardsMenu_DM\n  displayName: MW1020_ManageCardsMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1020_nm1_01.wav\\\">Please say  replace primary payment card    add a card ,  remove a card , or  update expiration date ,</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1020_nm2_01.wav\\\">Please say  replace primary payment card  or press 1  add a card  or press 2  remove a card  or press 3  update expiration date or press 4</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1020_nm2_01.wav\\\">Please say  replace primary payment card  or press 1  add a card  or press 2  remove a card  or press 3  update expiration date or press 4</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.MW1020_ManageCardsMenu_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.firstTimeInMW1020 = true,\n            \"<audio src=\\\"AUDIO_LOCATION/MW1020_ini_01.wav\\\">Manage Card Menu  You can say  replace primary payment card    add a card ,  remove a card , or  update expiration date ,</audio>\",\n            Global.firstTimeInMW1020 = false,\n            \"<audio src=\\\"AUDIO_LOCATION/MW1020_ini_02.wav\\\">If you re finished you can simply hang up  Otherwise you can say  replace primary payment card ,  add a card ,  remove a card ,  or  update expiration date </audio>\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: remove-card\n          displayName: remove-card\n        - id: change-primary_card\n          displayName: change-primary_card\n        - id: update-exp\n          displayName: update-exp\n        - id: add-card\n          displayName: add-card\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1020_nm1_01.wav\\\">Please say  replace primary payment card    add a card ,  remove a card , or  update expiration date ,</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1020_nm2_01.wav\\\">Please say  replace primary payment card  or press 1  add a card  or press 2  remove a card  or press 3  update expiration date or press 4</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1020_nm2_01.wav\\\">Please say  replace primary payment card  or press 1  add a card  or press 2  remove a card  or press 3  update expiration date or press 4</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.numDaysBtwPayAndCurrentDate\n  value: \"GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.numDaysBtwPayAndCurrentDate:0\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.firstTimeInMW1020\n  value: false\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_MW1020_ManageCardsMenu_DM\n  value: =Text(Global.MW1020_ManageCardsMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MW1020_ManageCardsMenu_DM = \"remove-card\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.manageCardTask\n          value: removeCard\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.GlobalVars.GetWalletItems.walletItems.length = 0, true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: MW1045_NoSavedCards_PP\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.GlobalVars.GetWalletItems.walletItems.length > 1, true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: MW1070_ChooseFromCardList_DM\n\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.GlobalVars.isAutopayEnrolled = true, true, false)\n                      actions:\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: MW1065_RemoveAutopayCard_DM\n\n                  elseActions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: MW1075_ConfirmCard_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MW1020_ManageCardsMenu_DM = \"change-primary_card\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.manageCardTask\n          value: changePrimaryCard\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.GlobalVars.isAutopayEnrolled = true, true, false)\n              actions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.numDaysBtwPayAndCurrentDate < 7, true, false)\n                      actions:\n                        - kind: SendActivity\n                          id: sendActivity_REPLACE_THIS\n                          activity:\n                            speak:\n                              - \"<audio src=\\\"AUDIO_LOCATION/MW1020_out_01.wav\\\">You are too close to your next autopayment to update your primary card at this time  You can do this after  your next auto payment is complete</audio>\"\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: MW1020_ManageCardsMenu_DM\n\n                  elseActions:\n                    - kind: ConditionGroup\n                      id: conditionGroup_REPLACE_THIS\n                      conditions:\n                        - id: conditionItem_REPLACE_THIS\n                          condition: =If(Global.GlobalVars.GetWalletItems.walletItems.length > 1, true, false)\n                          actions:\n                            - kind: GotoAction\n                              id: goto_REPLACE_THIS\n                              actionId: MW1070_ChooseFromCardList_DM\n\n                      elseActions:\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: MW1200_AskDebitOrCredit_DM\n\n            elseActions:\n              - kind: GotoAction\n                id: goto_REPLACE_THIS\n                actionId: MW1200_AskDebitOrCredit_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MW1020_ManageCardsMenu_DM = \"update-exp\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.manageCardTask\n          value: updateExp\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.GlobalVars.GetWalletItems.walletItems.length = 0, true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: MW1045_NoSavedCards_PP\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.GlobalVars.GetWalletItems.walletItems.length > 1, true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: MW1070_ChooseFromCardList_DM\n\n              elseActions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: MW1075_ConfirmCard_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MW1020_ManageCardsMenu_DM = \"add-card\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.manageCardTask\n          value: addCard\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: MW1200_AskDebitOrCredit_DM\n```", "timestamp": 1749528668.6725187, "content_hash": "e1d14cd3eeee42b29e47e193c07079e2"}