{"response": "```yaml\n- kind: Question\n  id: bp0235_AskPastExpiration_DM\n  displayName: bp0235_AskPastExpiration_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bp0235_ni1_01.wav\\\">If you have a *future* expiration date for your card, say or enter it now Or say Use A Different Card or press 1, or Cancel or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/bp0235_ni2_01.wav\\\">Please enter the *future* four-digit expiration date listed on your card For example, if the date is March 2017, you should enter zero three one seven To use a different card instead, press 1 Or to cancel, press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.bp0235_AskPastExpiration_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bp0235_ini_01.wav\\\">We can only process payments for cards with expiration dates in the future Please give me your 4-digit expiration date again You can also say Use A Different Card or Cancel</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: different_card\n          displayName: different_card\n        - id: cancel\n          displayName: cancel\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/bp0235_ni1_01.wav\\\">If you have a *future* expiration date for your card, say or enter it now Or say Use A Different Card or press 1, or Cancel or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/bp0235_ni2_01.wav\\\">Please enter the *future* four-digit expiration date listed on your card For example, if the date is March 2017, you should enter zero three one seven To use a different card instead, press 1 Or to cancel, press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_bp0235_AskPastExpiration_DM\n  value: =Text(Global.bp0235_AskPastExpiration_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_bp0235_AskPastExpiration_DM = \"different_card\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: bp0210_AskCardNumber_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_bp0235_AskPastExpiration_DM = \"cancel\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.isPaymentCanceled\n              value: true\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_bp0235_AskPastExpiration_DM = \"default\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.chargeExpirationDate\n                  value: bp0235_AskPastExpiration_DM.returnvalue\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: bp0310_AskBillingZipCode_DM\n```", "timestamp": 1749470176.2217782, "content_hash": "c721e3f1d1db8c2e0958a9dc0c6e1711"}