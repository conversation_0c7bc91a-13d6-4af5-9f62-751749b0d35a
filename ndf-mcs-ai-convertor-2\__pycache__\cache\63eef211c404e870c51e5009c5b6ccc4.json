{"response": "```yaml\n- kind: Question\n  id: aa0115_AskSetLanguage_DM\n  displayName: aa0115_AskSetLanguage_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa0115_ni1_01.wav\\\">Should I set your account language preference to Spanish? Yes or no </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/aa0115_ni2_01.wav\\\">To set your account language preference to Spanish, press 1 Or to keep it in English, press 2 </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.aa0115_AskSetLanguage_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa0115_ini_01.wav\\\">One last question before I start processing Do you want me to set your account language preference to Spanish? </audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/aa0115_ni1_01.wav\\\">Should I set your account language preference to Spanish? Yes or no </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/aa0115_ni2_01.wav\\\">To set your account language preference to Spanish, press 1 Or to keep it in English, press 2 </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.Var_aa0115_AskSetLanguage_DM\n  value: =Text(Global.aa0115_AskSetLanguage_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.callType = \"611\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_commandgrammar\n          value: 'GlobalCommands.grxml?SWI_vars.allow=mainmenu&SWI_vars.operator_cnf_mode=NEVER'\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.collection_commandgrammar\n      value: 'GlobalCommands.grxml?SWI_vars.operator_cnf_mode=NEVER'\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.callType = \"611\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_commandgrammar\n          value: 'GlobalCommands.grxml?SWI_vars.allow=mainmenu'\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.collection_commandgrammar\n      value: 'GlobalCommands.grxml'\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa0115_AskSetLanguage_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.accountLanguage\n          value: spanish\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa0118_ActivationTC_DM\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa0115_AskSetLanguage_DM = \"false\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.accountLanguage\n              value: english\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa0118_ActivationTC_DM\n```", "timestamp": **********.151743, "content_hash": "63eef211c404e870c51e5009c5b6ccc4"}