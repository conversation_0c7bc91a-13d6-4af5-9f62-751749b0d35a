{"response": "```yaml\n- kind: Question\n  id: aa4130_AssistancePersonalizationDTMF_DM\n  displayName: aa4130_AssistancePersonalizationDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.mainMenuVariables.firstTimeAssistancePerson = true,\n              \"Okay Assistance\",\n              Global.dnisInfo.assistanceMessageOn = true,\n              \"{Global.dnisInfo.assistanceMessage}\",\n              true,\n              \"To update your personal information press 1 To report unauthoerized card use, press 2 For other assistance press 3 For Benefit Access press 4 Or to return to the begining of the menu press 5\"\n          )\n        }\n      - |\n        {Switch(\n              true,\n              Global.mainMenuVariables.firstTimeAssistancePerson = true,\n              \"Okay Assistance\",\n              Global.dnisInfo.assistanceMessageOn = true,\n              \"{Global.dnisInfo.assistanceMessage}\",\n              true,\n              \"To update your personal information press 1 To report unauthoerized card use, press 2 For other assistance press 3 For Benefit Access press 4 Or to return to the begining of the menu press 5\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.aa4130_AssistancePersonalizationDTMF_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.mainMenuVariables.firstTimeAssistancePerson = true,\n              \"Okay Assistance\",\n              Global.dnisInfo.assistanceMessageOn = true,\n              \"{Global.dnisInfo.assistanceMessage}\",\n              true,\n              \"To update your personal information press 1 To report unauthoerized card use, press 2 For other assistance press 3 For Benefit Access press 4 Or to return to the begining of the menu press 5\"\n          )\n        }\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: personal_information\n          displayName: personal_information\n        - id: unauthorized_card_use\n          displayName: unauthorized_card_use\n        - id: assistance\n          displayName: assistance\n        - id: benefit_access\n          displayName: benefit_access\n        - id: main_menu\n          displayName: main_menu\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.mainMenuVariables.firstTimeAssistancePerson = true,\n                \"Okay Assistance\",\n                Global.dnisInfo.assistanceMessageOn = true,\n                \"{Global.dnisInfo.assistanceMessage}\",\n                true,\n                \"To update your personal information press 1 To report unauthoerized card use, press 2 For other assistance press 3 For Benefit Access press 4 Or to return to the begining of the menu press 5\"\n            )\n          }\n        - |\n          {Switch(\n                true,\n                Global.mainMenuVariables.firstTimeAssistancePerson = true,\n                \"Okay Assistance\",\n                Global.dnisInfo.assistanceMessageOn = true,\n                \"{Global.dnisInfo.assistanceMessage}\",\n                true,\n                \"To update your personal information press 1 To report unauthoerized card use, press 2 For other assistance press 3 For Benefit Access press 4 Or to return to the begining of the menu press 5\"\n            )\n          }\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa4130_AssistancePersonalizationDTMF_DM\n  value: =Text(Global.aa4130_AssistancePersonalizationDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa4130_AssistancePersonalizationDTMF_DM = \"personal_information\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.mainMenuVariables.firstTimeAssistancePerson\n          value: false\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.transferAllowed\n          value: true\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.transferReason\n          value: default\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aa4130_out_01.wav\\\">As a reminder, you can update your personal information on the internet at wwwetbedgecom</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: transferHandler_CS\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa4130_AssistancePersonalizationDTMF_DM = \"unauthorized_card_use\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.mainMenuVariables.firstTimeAssistancePerson\n              value: false\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.transferAllowed\n              value: true\n            # Script for eventLog EBT064 is not converted to YAML as per instructions\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: transferHandler_CS\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_aa4130_AssistancePersonalizationDTMF_DM = \"assistance\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.mainMenuVariables.firstTimeAssistancePerson\n                  value: false\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.globalVariables.transferAllowed\n                  value: true\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.globalVariables.transferReason\n                  value: default\n                # Script for eventLog EBT065 is not converted to YAML as per instructions\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: transferHandler_CS\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_aa4130_AssistancePersonalizationDTMF_DM = \"benefit_access\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.mainMenuVariables.firstTimeAssistancePerson\n                      value: false\n                    # Script for eventLog EBT066 is not converted to YAML as per instructions\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: aa4110_BenefitsAccessPlayout_PP\n\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.Var_aa4130_AssistancePersonalizationDTMF_DM = \"main_menu\", true, false)\n                      actions:\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.mainMenuVariables.firstTimeAssistancePerson\n                          value: false\n                        # Script for eventLog EBT068 is not converted to YAML as per instructions\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: aa4004_GetMainMenuOptions_DB_DA\n```", "timestamp": 1749543927.835697, "content_hash": "8d9431f19d2813fc66f17996a9b51357"}