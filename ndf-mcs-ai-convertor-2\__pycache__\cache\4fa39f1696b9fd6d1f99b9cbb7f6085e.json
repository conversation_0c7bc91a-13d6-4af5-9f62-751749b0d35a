{"response": "```yaml\n- kind: Question\n  id: st1015_AskHaveDevice_DM\n  displayName: st1015_AskHaveDevice_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n              \"Can you get to the phone that youre calling about? Say Yes or No\",\n\n              true,\n              \"Can you get to the device that youre calling about? Say Yes or No\"\n          )\n        }\n      - |\n        {Switch(\n              true,\n              Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n              \"If you have access to the phone that youre having problems with right now, say Yes or press 1 If you dont have that phone with you now, say No or press 2\",\n\n              true,\n              \"If you have access to the device that youre having problems with right now, say Yes or press 1 If you dont have that device with you now, say No or press 2\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.st1015_AskHaveDevice_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.intent = \"techSupportData\" && (Global.deviceType = \"featurephone\" || Global.deviceType = \"smartphone\" || Global.deviceType = \"tablet\"),\n            {Switch(\n                true,\n                Global.deviceType = \"featurephone\" || Global.deviceType = \"smartphone\",\n                \"To check your internet connection, I'll need you to access the settings on your phone Do you have it with you?\",\n                true,\n                \"To check your internet connection, I'll need you to access the settings on your device Do you have it with you?\"\n            )},\n            true,\n            {Switch(\n                true,\n                Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n                \"Do you have access to the phone youre having problems with?\",\n                true,\n                \"Do you have access to the device youre having problems with?\"\n            )}\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n                \"Can you get to the phone that youre calling about? Say Yes or No\",\n\n                true,\n                \"Can you get to the device that youre calling about? Say Yes or No\"\n            )\n          }\n        - |\n          {Switch(\n                true,\n                Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n                \"If you have access to the phone that youre having problems with right now, say Yes or press 1 If you dont have that phone with you now, say No or press 2\",\n\n                true,\n                \"If you have access to the device that youre having problems with right now, say Yes or press 1 If you dont have that device with you now, say No or press 2\"\n            )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_st1015_AskHaveDevice_DM\n  value: =Text(Global.st1015_AskHaveDevice_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_st1015_AskHaveDevice_DM = \"true\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: st1020_PowercycleInstructions_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_st1015_AskHaveDevice_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/st1015_out_01.wav\\\">No problem</audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_100ms.wav\\\"></audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: st1045_InstructionsWrapMenu_DM\n```", "timestamp": 1749472214.0817235, "content_hash": "4fa39f1696b9fd6d1f99b9cbb7f6085e"}