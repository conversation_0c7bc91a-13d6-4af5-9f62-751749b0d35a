{"response": "```yaml\n- kind: Question\n  id: pf0415_ResolveConflict_DM\n  displayName: pf0415_ResolveConflict_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/pf0415_ni1_01.wav\\\">To continue buying that add-on, say Yes To cancel, say No</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/com.nuance.att.application.audio.pf0415_ResolveConflict_noinput_2\\\"> </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.pf0415_ResolveConflict_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/com.nuance.att.application.audio.pf0415_ResolveConflict_initial\\\"> </audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/pf0415_ni1_01.wav\\\">To continue buying that add-on, say Yes To cancel, say No</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/com.nuance.att.application.audio.pf0415_ResolveConflict_noinput_2\\\"> </audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_pf0415_ResolveConflict_DM\n  value: =Text(Global.pf0415_ResolveConflict_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pf0415_ResolveConflict_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.isConflictsResolved\n          value: true\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: pf0410_NeedToResolveConflicts_JDA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_pf0415_ResolveConflict_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/pf0415_out_01.wav\\\">Okay</audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\"></audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: pf0430_CancelPackageWrapMenu_DM\n```", "timestamp": 1749471837.4748542, "content_hash": "90cd48aa3398cad5c616b74a1dbb4145"}