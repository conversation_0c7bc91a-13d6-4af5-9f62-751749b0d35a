{"response": "```yaml\n- kind: Question\n  id: AC2620_AskWiFiConnected_DM\n  displayName: AC2620_AskWiFiConnected_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AC2620_nm1_01.wav\\\">Can you connect your new phone to WiFi? </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AC2620_nm2_01.wav\\\">If you have WIFi connection, say 'yes' or press 1 Otherwise, say 'no' or press 2 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AC2620_nm3_01.wav\\\">If you have WiFI connection, press 1 If not, press 2 </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.AC2620_AskWiFiConnected_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.simType = \"ESIM\",\n            [\n              \"Just so you know, the phone you want to activate only has an eSIM, so we will be activating it today \",\n              \"test\",\n              \"After this call you will need a WiFi connection to complete your activation Will you be able to connect to WiFi from your new phone? \"\n            ],\n            [\n              \"After this call you will need a WiFi connection to complete your activation Will you be able to connect to WiFi from your new phone? \"\n            ]\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/AC2620_nm1_01.wav\\\">Can you connect your new phone to WiFi? </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AC2620_nm2_01.wav\\\">If you have WIFi connection, say 'yes' or press 1 Otherwise, say 'no' or press 2 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AC2620_nm3_01.wav\\\">If you have WiFI connection, press 1 If not, press 2 </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.simType\n  value: GlobalVars.GetDeviceStatus.simType\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_AC2620_AskWiFiConnected_DM\n  value: =Text(Global.AC2620_AskWiFiConnected_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AC2620_AskWiFiConnected_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/AC2620_out_01.wav\\\">OK, let me check your phone</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: AC2405_ValidateDeviceForActivation_DB_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_AC2620_AskWiFiConnected_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/AC2620_out_02.wav\\\">No problem</audio>\"\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.activationResult\n              value: transfer\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n```", "timestamp": 1749527591.4456506, "content_hash": "d5bead778688967ce1281255a8411f98"}