{"response": "```yaml\n- kind: Question\n  id: aa6015_CCZeroBalanceDTMF_DM\n  displayName: aa6015_CCZeroBalanceDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.balanceInfo.ccBalance = 0 && Global.globalVariables.currentTask = \"CCBalance\",\n              \"The balance in your child care account is zero dollars pause  If that s all you wanted to know, feel free to hang up now  And to go to Main Menu, press 1  3 second pause Otherwise, to speak with an agent about your child care account press 0\",\n\n              Global.ccProviderInfo.providerBalance = 0 && Global.globalVariables.currentTask = \"CCBalance\",\n              \"The balance available in your child care account for *this provider* is zero dollars pause  If that s all you wanted to know, feel free to hang up now  And to go to Main Menu, press 1  3 second pause Otherwise, to speak with an agent about your child care account press 0\",\n\n              Global.ccProviderInfo.providerBalance = 0 && Global.globalVariables.currentTask = \"CCPayment\",\n              \"The balance available in your child care account for *this provider* is zero dollars so you can t currently schedule any payments  pause To go to Main Menu, press 1  3 second pause Otherwise, to speak with an agent about your child care account press 0\",\n\n              \"The balance in your child care account is zero dollars so you can t currently schedule any payments To go to Main Menu, press 1 Otherwise, to speak with an agent about your child care account press 0\"\n          )\n        }\n      - |\n        {Switch(\n              true,\n              Global.dnisInfo.dtmfOnlyFlag = true,\n              \"Sorry, I still didn t get that\",\n\n              \"\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.aa6015_CCZeroBalanceDTMF_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.balanceInfo.ccBalance = 0 && Global.globalVariables.currentTask = \"CCBalance\",\n              \"The balance in your child care account is zero dollars pause  If that s all you wanted to know, feel free to hang up now  And to go to Main Menu, press 1  3 second pause Otherwise, to speak with an agent about your child care account press 0\",\n\n              Global.ccProviderInfo.providerBalance = 0 && Global.globalVariables.currentTask = \"CCBalance\",\n              \"The balance available in your child care account for *this provider* is zero dollars pause  If that s all you wanted to know, feel free to hang up now  And to go to Main Menu, press 1  3 second pause Otherwise, to speak with an agent about your child care account press 0\",\n\n              Global.ccProviderInfo.providerBalance = 0 && Global.globalVariables.currentTask = \"CCPayment\",\n              \"The balance available in your child care account for *this provider* is zero dollars so you can t currently schedule any payments  pause To go to Main Menu, press 1  3 second pause Otherwise, to speak with an agent about your child care account press 0\",\n\n              \"The balance in your child care account is zero dollars so you can t currently schedule any payments To go to Main Menu, press 1 Otherwise, to speak with an agent about your child care account press 0\"\n          )\n        }\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: main_menu\n          displayName: main_menu\n        - id: operator\n          displayName: operator\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.balanceInfo.ccBalance = 0 && Global.globalVariables.currentTask = \"CCBalance\",\n                \"The balance in your child care account is zero dollars pause  If that s all you wanted to know, feel free to hang up now  And to go to Main Menu, press 1  3 second pause Otherwise, to speak with an agent about your child care account press 0\",\n\n                Global.ccProviderInfo.providerBalance = 0 && Global.globalVariables.currentTask = \"CCBalance\",\n                \"The balance available in your child care account for *this provider* is zero dollars pause  If that s all you wanted to know, feel free to hang up now  And to go to Main Menu, press 1  3 second pause Otherwise, to speak with an agent about your child care account press 0\",\n\n                Global.ccProviderInfo.providerBalance = 0 && Global.globalVariables.currentTask = \"CCPayment\",\n                \"The balance available in your child care account for *this provider* is zero dollars so you can t currently schedule any payments  pause To go to Main Menu, press 1  3 second pause Otherwise, to speak with an agent about your child care account press 0\",\n\n                \"The balance in your child care account is zero dollars so you can t currently schedule any payments To go to Main Menu, press 1 Otherwise, to speak with an agent about your child care account press 0\"\n            )\n          }\n        - |\n          {Switch(\n                true,\n                Global.dnisInfo.dtmfOnlyFlag = true,\n                \"Sorry, I still didn t get that\",\n\n                \"\"\n            )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa6015_CCZeroBalanceDTMF_DM\n  value: =Text(Global.aa6015_CCZeroBalanceDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa6015_CCZeroBalanceDTMF_DM = \"main_menu\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.nextStep\n          value: main_menu\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aa6015_out_01.wav\\\">Sure</audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa1015_NextStepHandling_JDA_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa6015_CCZeroBalanceDTMF_DM = \"operator\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: handleGlobalOperator_CS\n```", "timestamp": 1749556758.9180746, "content_hash": "60603120e5edb33e763aa418587ec0d7"}