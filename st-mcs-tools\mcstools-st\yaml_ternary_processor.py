#!/usr/bin/env python3
"""
YAML Ternary Operation Processor

This script processes YAML files containing PowerFX/Power Platform configurations
and replaces ternary operations in SetVariable entries with their true values,
prepending an equals sign (=) to the result.

Usage:
    python yaml_ternary_processor.py input_file.yml [output_file.yml]

If no output file is specified, the input file will be modified in place.
"""

from ruamel.yaml import YAML
import re
import sys
import os
import logging
from typing import Any, Dict, List, Union
from pathlib import Path
from ruamel.yaml.scalarstring import ScalarString, PlainScalarString
from ruamel.yaml.comments import TaggedScalar
from collections import OrderedDict

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class YAMLTernaryProcessor:
    """Processes YAML files to replace ternary operations with true values."""
    
    def __init__(self):
        # Regex pattern to match ternary operations
        # Matches: condition ? true_value : false_value
        self.ternary_pattern = re.compile(
            r'([^?]+)\s*\?\s*([^:]+)\s*:\s*(.+)',
            re.DOTALL
        )
        
    def extract_true_value(self, ternary_expression: str) -> str:
        """
        Extract the true value from a ternary expression.
        
        Args:
            ternary_expression: The ternary expression string
            
        Returns:
            The true value with equals sign prepended
        """
        # Remove outer quotes if present
        expression = ternary_expression.strip()
        if (expression.startswith("'") and expression.endswith("'")) or \
           (expression.startswith('"') and expression.endswith('"')):
            expression = expression[1:-1]
        
        # Match the ternary pattern
        match = self.ternary_pattern.match(expression)
        if not match:
            logger.warning(f"Could not parse ternary expression: {ternary_expression}")
            return f"={expression}"
        
        condition, true_value, false_value = match.groups()
        
        # Clean up the true value
        true_value = true_value.strip()
        
        # Remove quotes from true value if it's a string literal
        if (true_value.startswith("'") and true_value.endswith("'")) or \
           (true_value.startswith('"') and true_value.endswith('"')):
            true_value = true_value[1:-1]
        
        # Prepend equals sign
        result = f"={true_value}"
        
        logger.debug(f"Converted ternary: {ternary_expression} -> {result}")
        return result

    def remove_quotes_from_value(self, value: str) -> str:
        """
        Remove outer quotes from a value string if present.

        Args:
            value: The value string that may have quotes

        Returns:
            The value string without outer quotes
        """
        if isinstance(value, str):
            # Remove outer single or double quotes if they wrap the entire string
            if (value.startswith("'") and value.endswith("'")) or \
               (value.startswith('"') and value.endswith('"')):
                return value[1:-1]
        return value
    
    def process_value(self, value: Any) -> Any:
        """
        Process a value, converting ternary operations if found.

        Args:
            value: The value to process

        Returns:
            The processed value
        """
        if isinstance(value, str):
            # Check if this looks like a ternary operation
            if '?' in value and ':' in value:
                # Additional validation to ensure it's actually a ternary
                if self.ternary_pattern.match(value.strip().strip("'\"")) or \
                   self.ternary_pattern.match(value.strip()):
                    return self.extract_true_value(value)
            return value
        elif isinstance(value, dict):
            return {k: self.process_value(v) for k, v in value.items()}
        elif isinstance(value, list):
            return [self.process_value(item) for item in value]
        else:
            return value
    
    def process_set_variable(self, action: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a SetVariable action, converting ternary operations in the value field.

        Args:
            action: The SetVariable action dictionary

        Returns:
            The processed action dictionary
        """
        if action.get('kind') == 'SetVariable' and 'value' in action:
            original_value = action['value']
            processed_value = self.process_value(original_value)

            if processed_value != original_value:
                # Remove quotes from the processed value
                processed_value = self.remove_quotes_from_value(processed_value)
                logger.info(f"Processed SetVariable '{action.get('variable', 'unknown')}': "
                          f"{original_value} -> {processed_value}")

                # Use PlainScalarString to ensure no quotes in YAML output
                action['value'] = PlainScalarString(processed_value)

        return action
    
    def process_yaml_structure(self, data: Any) -> Any:
        """
        Recursively process the YAML structure to find and convert SetVariable entries.
        
        Args:
            data: The YAML data structure
            
        Returns:
            The processed data structure
        """
        if isinstance(data, dict):
            # Check if this is a SetVariable action
            if data.get('kind') == 'SetVariable':
                return self.process_set_variable(data)
            else:
                # Recursively process all dictionary values
                return {k: self.process_yaml_structure(v) for k, v in data.items()}
        elif isinstance(data, list):
            # Process each item in the list
            return [self.process_yaml_structure(item) for item in data]
        else:
            return data
    
    def read_yaml(self, file_path: str):
        """Read YAML file using ruamel.yaml for better preservation."""
        logger.info(f"Reading YAML file: {file_path}")
        try:
            yaml = YAML()
            yaml.width = 4096
            yaml.indent(mapping=2, sequence=4, offset=2)
            yaml.preserve_quotes = True
            with open(file_path, 'r', encoding='utf-8') as file:
                data = yaml.load(file)
                logger.info("Successfully read YAML file")
                return data, yaml
        except FileNotFoundError:
            logger.error(f"File {file_path} not found")
            return None, None
        except Exception as e:
            logger.error(f"Error parsing YAML file: {e}")
            return None, None

    def write_yaml(self, data, yaml_instance, file_path: str):
        """Write YAML file using ruamel.yaml for better preservation."""
        logger.info(f"Writing updated YAML to: {file_path}")
        try:
            if data is None or yaml_instance is None:
                logger.error("No valid data or YAML instance to write")
                return False

            # Ensure directory exists
            directory = os.path.dirname(file_path)
            if directory and not os.path.exists(directory):
                os.makedirs(directory)
                logger.info(f"Created directory: {directory}")

            with open(file_path, 'w', encoding='utf-8') as file:
                yaml_instance.dump(data, file)
            logger.info(f"Successfully wrote updated YAML to {file_path}")
            return True
        except Exception as e:
            logger.error(f"Error writing YAML file: {e}")
            return False

    def process_file(self, input_file: str, output_file: str = None) -> bool:
        """
        Process a YAML file, converting ternary operations in SetVariable entries.

        Args:
            input_file: Path to the input YAML file
            output_file: Path to the output YAML file (optional)

        Returns:
            True if processing was successful, False otherwise
        """
        try:
            # Validate input file
            input_path = Path(input_file)
            if not input_path.exists():
                logger.error(f"Input file does not exist: {input_file}")
                return False

            if not input_path.is_file():
                logger.error(f"Input path is not a file: {input_file}")
                return False

            # Set output file if not provided
            if output_file is None:
                output_file = input_file
                logger.info(f"No output file specified, will modify input file in place")

            logger.info(f"Processing YAML file: {input_file}")

            # Read the YAML file using ruamel.yaml
            yaml_data, yaml_instance = self.read_yaml(input_file)

            if yaml_data is None:
                logger.error("Failed to load YAML data or file is empty")
                return False

            # Process the YAML structure
            processed_data = self.process_yaml_structure(yaml_data)

            # Write the processed YAML using ruamel.yaml
            success = self.write_yaml(processed_data, yaml_instance, output_file)

            if success:
                logger.info(f"Successfully processed YAML file. Output saved to: {output_file}")
                return True
            else:
                return False

        except Exception as e:
            logger.error(f"Unexpected error processing file: {e}")
            return False


def main():
    """Main function to handle command line arguments and execute processing."""
    if len(sys.argv) < 2:
        print("Usage: python yaml_ternary_processor.py input_file.yml [output_file.yml]")
        print("\nThis script processes YAML files containing PowerFX/Power Platform configurations")
        print("and replaces ternary operations in SetVariable entries with their true values.")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    processor = YAMLTernaryProcessor()
    success = processor.process_file(input_file, output_file)
    
    if success:
        print(f"✅ Successfully processed {input_file}")
        if output_file and output_file != input_file:
            print(f"   Output saved to: {output_file}")
        else:
            print(f"   File modified in place")
    else:
        print(f"❌ Failed to process {input_file}")
        sys.exit(1)


if __name__ == "__main__":
    main()
