{"response": "```yaml\n- kind: Question\n  id: aa2585_LostStolenPinDTMF_DM\n  displayName: aa2585_LostStolenPinDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa2585_ini_01.wav\\\">Please enter your 4-digit PIN</audio>\"\n      - |\n        {Switch(\n              true,\n              Global.dnisInfo.dtmfOnlyFlag = true,\n              \"<audio src=\\\"AUDIO_LOCATION/aa2585_nm2_01.wav\\\">Sorry, I still didn t get that</audio>\",\n              true,\n              \"\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.aa2585_LostStolenPinDTMF_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa2585_ini_01.wav\\\">Please enter your 4-digit PIN</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/aa2585_ini_01.wav\\\">Please enter your 4-digit PIN</audio>\"\n        - |\n          {Switch(\n                true,\n                Global.dnisInfo.dtmfOnlyFlag = true,\n                \"<audio src=\\\"AUDIO_LOCATION/aa2585_ni2_01.wav\\\">Sorry, I still didn t get that</audio>\",\n                true,\n                \"\"\n            )\n          }\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.result\n  value: aa2585_LostStolenPinDTMF_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.validationCriteriaVariables.lostStolenPin\n  value: result\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.validationCriteriaVariables.validationValue\n  value: validationCriteriaVariables.validationValue + ';' + result\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.validationCriteriaVariables.validationType\n  value: validationCriteriaVariables.validationType + ';' + 'PIN'\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: aa2578_ValidateLostStolenCriteria_DB_DA\n```", "timestamp": **********.3984072, "content_hash": "9e04accb97081f25f06a48fa359a42b3"}