{"response": "```yaml\n- kind: Question\n  id: ma2123_AskMobilePhone_DM\n  displayName: ma2123_AskMobilePhone_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I missed that</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ma2123_ni1_01.wav\\\">Is the device you re activating a cell phone? Just say Yes or No  </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">Sorry</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ma2123_ni2_01.wav\\\">If you re calling to activate a mobile phone, say Yes or press 1 If its a different type of device, say No or press 2  </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ma2123_AskMobilePhone_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ma2123_ini_01.wav\\\">Are you activating a mobile phone? </audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ma2123_ini_01.wav\\\">Are you activating a mobile phone? </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ma2123_ni2_01.wav\\\">If you re calling to activate a mobile phone, say Yes or press 1 If its a different type of device, say No or press 2  </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.Var_ma2123_AskMobilePhone_DM\n  value: =Text(Global.ma2123_AskMobilePhone_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma2123_AskMobilePhone_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.accountType\n          value: gophone\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.continueActivation = true, true, false)\n              actions:\n                - kind: BeginDialog\n                  id: begin_REPLACE_THIS\n                  dialog: topic.M03_MainMenu.dvxml#ma2315_AskHavePackage_DM\n\n          elseActions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: ma2126_OtherActivationChannels_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ma2123_AskMobilePhone_DM = \"false\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: ma2125_AskAccountType_DM\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.callType = \"611\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_commandgrammar\n          value: 'GlobalCommands.grxml?SWI_vars.allow=mainmenu&SWI_vars.operator_cnf_mode=NEVER'\n\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.collection_commandgrammar\n      value: 'GlobalCommands.grxml?SWI_vars.operator_cnf_mode=NEVER'\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.callType = \"611\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_commandgrammar\n          value: 'GlobalCommands.grxml?SWI_vars.allow=mainmenu'\n\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.collection_commandgrammar\n      value: 'GlobalCommands.grxml'\n```", "timestamp": 1749471466.2488418, "content_hash": "b9f04998c2e56a17eff8590ef882f4fb"}