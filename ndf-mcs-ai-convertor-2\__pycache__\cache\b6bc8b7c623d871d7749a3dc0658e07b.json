{"response": "```yaml\n- kind: Question\n  id: RP0526_DescribePlanAskChange_DM\n  displayName: RP0526_DescribePlanAskChange_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/RP0526_nm1_01.wav\\\">Would you like to switch to this plan? </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/RP0526_nm2_01.wav\\\">To switch to this plan say 'yes' or press 1  Or say 'no' or press 2 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/RP0526_nm2_01.wav\\\">To switch to this plan say 'yes' or press 1  Or say 'no' or press 2 </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.RP0526_DescribePlanAskChange_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/RP0526_ini_04.wav\\\">Would you like to switch to this plan? </audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/RP0526_nm1_01.wav\\\">Would you like to switch to this plan? </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/RP0526_nm2_01.wav\\\">To switch to this plan say 'yes' or press 1  Or say 'no' or press 2 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/RP0526_nm2_01.wav\\\">To switch to this plan say 'yes' or press 1  Or say 'no' or press 2 </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.soc\n  value: GlobalVars.selectedPlan\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.audioMessageKey\n  value: care_configurable_rate_plan_downgrade_audio\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_RP0526_DescribePlanAskChange_DM\n  value: =Text(Global.RP0526_DescribePlanAskChange_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_RP0526_DescribePlanAskChange_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.newRatePlan\n          value: GlobalVars.selectedPlan\n\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.RatePlan_Main.dvxml#RP0301_CheckPlanOptions_DS\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_RP0526_DescribePlanAskChange_DM = \"false\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.declineChangeCounter\n              value: GlobalVars.declineChangeCounter+1\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.revisit\n              value: false\n\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.RatePlan_Main.dvxml#RP0326_CheckDeclineNextSteps_DS\n```", "timestamp": 1749529367.5351748, "content_hash": "b6bc8b7c623d871d7749a3dc0658e07b"}