{"response": "```yaml\n- kind: Question\n  id: cv0130_AskNewPassword_DM\n  displayName: cv0130_AskNewPassword_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I missed that</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/cv0130_ni1_01.wav\\\">Please say or enter a voicemail password thats between 4 and 6 digits long</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">Sorry</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/cv0130_ni2_01.wav\\\">Your new voicemail password can be any 4, 5, or 6 digit number yuod like, except for the last 4 digits of your wireless number Please enter your new password now</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.cv0130_AskNewPassword_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.numVmailPasswordAttempts = 0,\n            \"Your new voicemail password can be 4 to 6 digits long, and you cant use the last 4 digits of your wireless number Please say or enter the number yuod like to use\",\n            \"Please choose a different voicemail password between 4 and 6 digits long\"\n        )}\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/cv0130_ni1_01.wav\\\">Please say or enter a voicemail password thats between 4 and 6 digits long</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/cv0130_ni2_01.wav\\\">Your new voicemail password can be any 4, 5, or 6 digit number yuod like, except for the last 4 digits of your wireless number Please enter your new password now</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.newPassword\n  value: cv0130_AskNewPassword_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.numVmailPasswordAttempts\n  value: numVmailPasswordAttempts + 1\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: cv0135_ValidPassword_JDA_DA\n```", "timestamp": 1749470549.9092352, "content_hash": "f30f0567c718abe5706d4752926231a1"}