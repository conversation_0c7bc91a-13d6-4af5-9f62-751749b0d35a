{"response": "```yaml\n- kind: Question\n  id: PI1010_FindCardWaitSBI_DM\n  displayName: PI1010_FindCardWaitSBI_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.PI1010_FindCardWaitSBI_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.needResidualPayment = true,\n            \"You can pay with a credit or debit card, or another Payment PIN When you have it ready, say 'continue' \",\n            Global.needResidualPayment <> true,\n            \"When you have your card ready, say continue\",\n            \"When you're ready, say Continue or press 1, or say I can't find it or press 2\",\n            \"test\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: continue\n          displayName: continue\n        - id: cant_find\n          displayName: cant_find\n        - id: credit\n          displayName: credit\n        - id: debit\n          displayName: debit\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.needResidualPayment\n  value: GlobalVars.needResidualPayment\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_PI1010_FindCardWaitSBI_DM\n  value: =Text(Global.PI1010_FindCardWaitSBI_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_PI1010_FindCardWaitSBI_DM = \"continue\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.GlobalVars.preferredPaymentMethod = \"credit\" || Global.GlobalVars.preferredPaymentMethod = \"debit\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: PI1025_GoTo_BankCardDetails_SD\n          elseActions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: PI1015_GetPaymentMethod_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_PI1010_FindCardWaitSBI_DM = \"cant_find\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: PI1030_GoTo_ErrorHandling_SD\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_PI1010_FindCardWaitSBI_DM = \"credit\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.preferredPaymentMethod\n          value: credit\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.PaymentTable.CARD_TYPE\n          value: credit\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.GlobalVars.callType = \"activate\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.PaymentTable.ACTIVATION_STATUS\n                  value: '83'\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.PaymentTable.ACTIVATION_STATUS\n              value: '104'\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: PI1039_CheckCallType_JDA\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_PI1010_FindCardWaitSBI_DM = \"debit\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.preferredPaymentMethod\n          value: debit\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.PaymentTable.CARD_TYPE\n          value: debit\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.GlobalVars.callType = \"activate\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.PaymentTable.ACTIVATION_STATUS\n                  value: '83'\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.PaymentTable.ACTIVATION_STATUS\n              value: '104'\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: PI1039_CheckCallType_JDA\n```", "timestamp": 1749529030.4590917, "content_hash": "78bbe58e35d285736fb664fcaea3a7f7"}