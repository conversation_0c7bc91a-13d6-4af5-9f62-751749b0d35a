{"response": "```yaml\n- kind: Question\n  id: RP0130_CurrentPlanOptions_DM\n  displayName: RP0130_CurrentPlanOptions_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/RP0130_nm1_01.wav\\\">If you d like to hear that information again, say repeat that  To hear more information about your plan, say tell me more  You can also say change my plan  If you d like to hear your current features, say my features</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/RP0130_nm2_01.wav\\\">To hear about your calling plan again, say repeat that, or press *  To hear more details about it, say tell me more or press 1  You can also say change my plan or press 2 Or my features or press 3</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/RP0130_nm2_01.wav\\\">To hear about your calling plan again, say repeat that, or press *  To hear more details about it, say tell me more or press 1  You can also say change my plan or press 2 Or my features or press 3</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.RP0130_CurrentPlanOptions_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.ratePlanActionUndefinedOrRepeat = true,\n            \"You can say repeat that or tell me more You can also say change my plan or my features\",\n            Global.ratePlanActionUndefinedOrRepeat = false,\n            \"You can say repeat that  You can also say change my plan or my features\",\n            \"test\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: change-plan\n          displayName: change-plan\n        - id: tell_me_more\n          displayName: tell_me_more\n        - id: my_features\n          displayName: my_features\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/RP0130_nm1_01.wav\\\">If you d like to hear that information again, say repeat that  To hear more information about your plan, say tell me more  You can also say change my plan  If you d like to hear your current features, say my features</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/RP0130_nm2_01.wav\\\">To hear about your calling plan again, say repeat that, or press *  To hear more details about it, say tell me more or press 1  You can also say change my plan or press 2 Or my features or press 3</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/RP0130_nm2_01.wav\\\">To hear about your calling plan again, say repeat that, or press *  To hear more details about it, say tell me more or press 1  You can also say change my plan or press 2 Or my features or press 3</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.ratePlanActionUndefinedOrRepeat\n  value: \"GlobalVars.ratePlanAction == undefined || GlobalVars.ratePlanAction == 'repeat'\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.isOnFamilyPlan\n  value: GlobalVars.GetAccountDetails.isOnFamilyPlan\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_RP0130_CurrentPlanOptions_DM\n  value: =Text(Global.RP0130_CurrentPlanOptions_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_RP0130_CurrentPlanOptions_DM = \"change-plan\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: change_plan\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/RP0130_out_01.wav\\\">Just so you know, if you change your plan, it's possible you won't be able to get your current plan back</audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_1000ms.wav\\\">test</audio>\"\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.isOnFamilyPlan = true, true, false)\n              actions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/RP0130_out_02.wav\\\">Other lines on your account may be impacted as well  The plan choices provided will not include multiline pricing BUT, I will be able to calculate your new total monthly price, including *ALL* your lines before you confirm the change</audio>\"\n                      - \"<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\"\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.ratePlanAction\n                  value: undefined\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: RP0010_BroadcastMessages_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_RP0130_CurrentPlanOptions_DM = \"tell_me_more\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.ratePlanAction\n              value: tell_me_more\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: RP0120_PlayCurrentPlan_PP\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_RP0130_CurrentPlanOptions_DM = \"my_features\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.ratePlanAction\n                  value: undefined\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.cti_Intent\n                  value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.callType\n                  value: my_features\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: RP0135_CurrentFeatures_SD\n```", "timestamp": 1749529224.76923, "content_hash": "3cb60f96d91b446a2c8df0e1c150ab1c"}