{"response": "```yaml\n- kind: Question\n  id: bc0125_AskDeviceCVN_DM\n  displayName: bc0125_AskDeviceCVN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"{Switch(\n            true,\n            true,\n            \\\"com.nuance.att.application.audio.bc0125_AskDeviceCVN_DM_noinput_1\\\"\n        )}\"\n      - \"{Switch(\n            true,\n            true,\n            \\\"com.nuance.att.application.audio.bc0125_AskDeviceCVN_DM_noinput_2\\\"\n        )}\"\n\n  alwaysPrompt: true\n  variable: Global.bc0125_AskDeviceCVN_DM_reco\n  prompt:\n    speak:\n      - \"com.nuance.att.application.audio.bc0125_AskDeviceCVN_initial\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: dont_have\n          displayName: dont_have\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"com.nuance.att.application.audio.bc0125_AskDeviceCVN_DM_noinput_1\"\n        - \"com.nuance.att.application.audio.bc0125_AskDeviceCVN_DM_noinput_2\"\n    defaultValueMissingAction: Escalate\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.paymentDeviceTypeName = \"AE\" || Global.paymentDeviceTypeName = \"American Express\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_dtmfgrammar1\n          value: 'bc0125_AskDeviceCVN_DM_dtmf.grxml?SWI_vars.cardType=amex'\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_grammar1\n          value: 'bc0125_AskDeviceCVN_DM.grxml?SWI_vars.cardType=amex'\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.collection_dtmfgrammar1\n      value: 'bc0125_AskDeviceCVN_DM_dtmf.grxml'\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.collection_grammar1\n      value: 'bc0125_AskDeviceCVN_DM.grxml'\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_bc0125_AskDeviceCVN_DM\n  value: =Text(Global.bc0125_AskDeviceCVN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_bc0125_AskDeviceCVN_DM = \"dont_have\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.intent = \"autoRefill\" || Global.intent = \"refillPlusAutoPay\", true, false)\n              actions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/bc0125_out_03.wav\\\">OK I will need to connect you to someone who can assist with completing your Auto Pay</audio>\"\n          elseActions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/bc0125_out_02.wav\\\">OK I will need to connect you to someone who can assist with completing your payment</audio>\"\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferPrompt\n          value: short\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferDestination\n          value: vesta\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferReason\n          value: BUSINESS_RULE\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M09_MainMenu.dvxml#ma9210_NeedTransferType_DS\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_bc0125_AskDeviceCVN_DM = \"default\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.isSSNRequired = false, true, false)\n                  actions:\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/bc0125_out_01.wav\\\">Thanks</audio>\"\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.cvn\n              value: bc0125_AskDeviceCVN_DM.returnvalue\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: bc0130_IsSSNReq_JDA_DA\n```", "timestamp": 1749469992.1202838, "content_hash": "ad658d0513627a852843476fffa60e2b"}