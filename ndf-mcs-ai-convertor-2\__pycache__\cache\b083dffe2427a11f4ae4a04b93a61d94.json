{"response": "```yaml\n- kind: Question\n  id: aa2586_LostStolenPin_DM\n  displayName: aa2586_LostStolenPin_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.aa2586_LostStolenPin_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa2586_ini_01.wav\\\">Please say your 4-digit PIN</audio>\"\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.result\n  value: aa2586_LostStolenPin_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.validationCriteriaVariables.lostStolenPin\n  value: result\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.validationCriteriaVariables.validationValue\n  value: validationCriteriaVariables.validationValue + ';' + result\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.validationCriteriaVariables.validationType\n  value: validationCriteriaVariables.validationType + ';' + 'PIN'\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: aa2578_ValidateLostStolenCriteria_DB_DA\n```", "timestamp": **********.44703, "content_hash": "b083dffe2427a11f4ae4a04b93a61d94"}