{"response": "```yaml\n- kind: Question\n  id: ma9310_MaxDialogOffer_DM\n  displayName: ma9310_MaxDialogOffer_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.ma9310_MaxDialogOffer_DM_reco\n  prompt:\n    speak:\n      - \"{CustomAudio: com.nuance.att.application.audio.ma9310_MaxDialogOffer_DM_initial}\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: operator\n          displayName: operator\n        - id: mainmenu\n          displayName: mainmenu\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.maxnoinputs = true,\n                \"<audio src=\\\"AUDIO_LOCATION/ma9310_ini_02.wav\\\">Sorry, I am having trouble understanding you For more assistance, press 0 Or, if youre done, just hang up</audio>\",\n                true,\n                \"<audio src=\\\"AUDIO_LOCATION/ma9310_ini_01.wav\\\">Are you still there? If yuod like to speak to someone, press 0 Or, if youre done, just hang up</audio>\"\n            )\n          }\n        - |\n          {Switch(\n                true,\n                Global.maxnoinputs = true,\n                \"<audio src=\\\"AUDIO_LOCATION/ma9310_ini_02.wav\\\">Sorry, I am having trouble understanding you For more assistance, press 0 Or, if youre done, just hang up</audio>\",\n                true,\n                \"<audio src=\\\"AUDIO_LOCATION/ma9310_ini_01.wav\\\">Are you still there? If yuod like to speak to someone, press 0 Or, if youre done, just hang up</audio>\"\n            )\n          }\n    defaultValueMissingAction: Escalate\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |-\n        =If((Global.transferDestination = \"vesta\" || Global.transferDestination = \"vestaCollections\" || Global.transferDestination = \"vestaRevenueAssurance\") || (Global.callType <> \"611\"), true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_commandgrammar\n          value: \"'GlobalCommands.grxml?SWI_vars.callType='+callType+'&SWI_vars.operator_cnf_mode=NEVER'\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_dtmfcommandgrammar\n          value: \"'GlobalCommands_dtmf_rp.grxml?SWI_vars.callType='+callType\"\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.collection_commandgrammar\n      value: \"'GlobalCommands.grxml?SWI_vars.callType='+callType+'&SWI_vars.disallow=operator&SWI_vars.allow=mainmenu&SWI_vars.operator_cnf_mode=NEVER'\"\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.collection_dtmfcommandgrammar\n      value: \"'GlobalCommands_dtmf_rp.grxml?SWI_vars.callType='+callType+'&SWI_vars.disallow=operator&SWI_vars.allow=mainmenu'\"\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ma9310_MaxDialogOffer_DM\n  value: =Text(Global.ma9310_MaxDialogOffer_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma9310_MaxDialogOffer_DM = \"operator\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.lastTransferState\n          value: ma9310_MaxDialogOffer_DM\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferReason\n          value: MAX_ATTEMPTS\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferPrompt\n          value: default\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ma9210_NeedTransferType_JDA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ma9310_MaxDialogOffer_DM = \"mainmenu\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.transferDestinationEBB\n              value: false\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.M01_MainMenu2.dvxml#ma1334_InitiateMainMenu_DS\n\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ma9310_MaxDialogOffer_DM = \"repeat\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: ma9310_MaxDialogOffer_DM\n```", "timestamp": 1749471348.4596786, "content_hash": "51bba564cc171ff7ea478f0ab2f53c1c"}