{"response": "```yaml\n- kind: Question\n  id: aa6066_CCCollectPIN_DM\n  displayName: aa6066_CCCollectPIN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.aa6066_CCCollectPIN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa6066_ini_01.wav\\\">Please say your 4-digit PIN now pause Or to speak with an agent, say  Representative </audio>\"\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.childCareInfoVariables.collectPin\n  value: aa6066_CCCollectPIN_DM.returnvalue\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.dnisInfo.callType = \"WICC\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aa6066_out_01.wav\\\">Thank you I ll go ahead and try to apply that payment for you  One Moment</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa6069_WICCValidatePin_DB_DA\n\n  elseActions:\n    - kind: SendActivity\n      id: sendActivity_REPLACE_THIS\n      activity:\n        speak:\n          - \"<audio src=\\\"AUDIO_LOCATION/aa6066_out_01.wav\\\">Thank you I ll go ahead and try to apply that payment for you  One Moment</audio>\"\n    - kind: GotoAction\n      id: goto_REPLACE_THIS\n      actionId: aa6070_CCApplyPayment_DB_DA\n```", "timestamp": 1749544039.9104807, "content_hash": "9ce9b6e7a7c7d577d05832fe0f9e1a9d"}