{"response": "```yaml\n- kind: Question\n  id: bc0220_TermsAndConditions_DM\n  displayName: bc0220_TermsAndConditions_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bc0220_ni1_01.wav\\\">Before I can process your Auto Pay request I need to know if you agree to the terms and conditions Please say Yes or No Or to hear the conditions again say Repeat</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/bc0220_ni2_01.wav\\\">In order to process the Auto Pay that you have just set up, I need to know if you agree to the terms and conditions If you agree, say Yes or press 1 If you do not agree, say No or press 2 Or to hear the conditions again press 3</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.bc0220_TermsAndConditions_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bc0220_TermsAndConditions_DM_initial.wav\\\">Terms and Conditions initial prompt</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/bc0220_ni1_01.wav\\\">Before I can process your Auto Pay request I need to know if you agree to the terms and conditions Please say Yes or No Or to hear the conditions again say Repeat</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/bc0220_ni2_01.wav\\\">In order to process the Auto Pay that you have just set up, I need to know if you agree to the terms and conditions If you agree, say Yes or press 1 If you do not agree, say No or press 2 Or to hear the conditions again press 3</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_bc0220_TermsAndConditions_DM\n  value: =Text(Global.bc0220_TermsAndConditions_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_bc0220_TermsAndConditions_DM = \"true\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.autoRefillChange = true, true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: bc0230_AutoPayRemoveChange_DB_DA\n\n          elseActions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: bc0235_ProcessChargeOrder_DB_JDA_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_bc0220_TermsAndConditions_DM = \"false\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.intent = \"refillPlusAutoPay\", true, false)\n                  actions:\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/bc0220_out_01.wav\\\">Okay Ive canceled your payment and your Auto Pay enrollment</audio>\"\n\n              elseActions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/bc0220_out_02.wav\\\">Okay Ive canceled your Auto Pay enrollment</audio>\"\n\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\"></audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n```", "timestamp": 1749470177.7341292, "content_hash": "7a9fdc83ef29935a7231b1a31a22ff3b"}