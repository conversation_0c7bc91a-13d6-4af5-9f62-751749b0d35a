{"response": "```yaml\n- kind: Question\n  id: aa1531_AskCardReplacementReason_DM\n  displayName: aa1531_AskCardReplacementReason_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.aa1531_AskCardReplacementReason_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            // Default prompt\n            true,\n            [\n                \"<audio src=\\\"AUDIO_LOCATION/aa1531_ini_01.wav\\\">Is your card lost, stolen, or damaged?</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">silence_500ms</audio>\",\n                {Switch(\n                    true,\n                    Global.dnisInfo.cardReplacementReasonOtherOption = true,\n                    \"<audio src=\\\"AUDIO_LOCATION/aa1531_ini_02.wav\\\">For any other reason, say  it s something else </audio>\"\n                )}\n            ]\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: lost\n          displayName: lost\n        - id: stolen\n          displayName: stolen\n        - id: damaged\n          displayName: damaged\n        - id: something_else\n          displayName: something_else\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa1531_AskCardReplacementReason_DM\n  value: =Text(Global.aa1531_AskCardReplacementReason_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa1531_AskCardReplacementReason_DM = \"lost\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.eventLog\n          value: EBT220\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |-\n                =If(\n                  And(\n                    Global.validationCriteriaInfo.stateAllowCardIssue = true,\n                    Global.validateLostStolenCriteriaInfo.cardReplacementRange <> 1,\n                    Global.cardReplacementVariables.isCallerConfirmedAddress() = true,\n                    Or(\n                      Global.validateLostStolenCriteriaInfo.cardIssuableClient = \"Y\",\n                      Global.validateLostStolenCriteriaInfo.cardIssuableClient = \"W\"\n                    ),\n                    Or(\n                      And(\n                        Global.validateLostStolenCriteriaInfo.cardFee <> 0.00,\n                        Global.validateLostStolenCriteriaInfo.cardFee <> null,\n                        Global.validateLostStolenCriteriaInfo.cardFee <> \" \"\n                      ),\n                      Global.validateLostStolenCriteriaInfo.cardIssuableClient <> \"N\"\n                    )\n                  ),\n                  true,\n                  false\n                )\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.cardReplacementVariables.statusReasonCode\n                  value: A2\n\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.cardReplacementVariables.statusReasonCode\n              value: AA\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa1539_CancelLostStolenCard_DB_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa1531_AskCardReplacementReason_DM = \"stolen\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.eventLog\n              value: EBT221\n\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: |-\n                    =If(\n                      And(\n                        Global.validationCriteriaInfo.stateAllowCardIssue = true,\n                        Global.validateLostStolenCriteriaInfo.cardReplacementRange <> 1,\n                        Global.cardReplacementVariables.isCallerConfirmedAddress() = true,\n                        Or(\n                          Global.validateLostStolenCriteriaInfo.cardIssuableClient = \"Y\",\n                          Global.validateLostStolenCriteriaInfo.cardIssuableClient = \"W\"\n                        ),\n                        Or(\n                          And(\n                            Global.validateLostStolenCriteriaInfo.cardFee <> 0.00,\n                            Global.validateLostStolenCriteriaInfo.cardFee <> null,\n                            Global.validateLostStolenCriteriaInfo.cardFee <> \" \"\n                          ),\n                          Global.validateLostStolenCriteriaInfo.cardIssuableClient <> \"N\"\n                        )\n                      ),\n                      true,\n                      false\n                    )\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.cardReplacementVariables.statusReasonCode\n                      value: A1\n\n              elseActions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.cardReplacementVariables.statusReasonCode\n                  value: AC\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa1539_CancelLostStolenCard_DB_DA\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_aa1531_AskCardReplacementReason_DM = \"damaged\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.eventLog\n                  value: EBT222\n\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: |-\n                        =If(\n                          And(\n                            Global.validationCriteriaInfo.stateAllowCardIssue = true,\n                            Global.validateLostStolenCriteriaInfo.cardReplacementRange <> 1,\n                            Global.cardReplacementVariables.isCallerConfirmedAddress() = true,\n                            Or(\n                              Global.validateLostStolenCriteriaInfo.cardIssuableClient = \"Y\",\n                              Global.validateLostStolenCriteriaInfo.cardIssuableClient = \"W\"\n                            ),\n                            Or(\n                              And(\n                                Global.validateLostStolenCriteriaInfo.cardFee <> 0.00,\n                                Global.validateLostStolenCriteriaInfo.cardFee <> null,\n                                Global.validateLostStolenCriteriaInfo.cardFee <> \" \"\n                              ),\n                              Global.validateLostStolenCriteriaInfo.cardIssuableClient <> \"N\"\n                            )\n                          ),\n                          true,\n                          false\n                        )\n                      actions:\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.cardReplacementVariables.statusReasonCode\n                          value: A5\n\n                  elseActions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.cardReplacementVariables.statusReasonCode\n                      value: AB\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: aa1539_CancelLostStolenCard_DB_DA\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_aa1531_AskCardReplacementReason_DM = \"something_else\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.globalVariables.transferAllowed\n                      value: true\n\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.eventLog\n                      value: EBT223\n\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/aa1531_out_01.wav\\\">I ll transfer you to customer service</audio>\"\n\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: transferHandler_CS\n```", "timestamp": 1749543672.8660429, "content_hash": "c931b7bdbf8d2ad87aa8277039db9ca5"}