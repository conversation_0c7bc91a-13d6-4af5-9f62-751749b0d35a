{"response": "```yaml\n- kind: Question\n  id: bm0230_MaxBalanceMenu_DM\n  displayName: bm0230_MaxBalanceMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bm0230_MaxBalanceMenu_DM_noinput_1.wav\\\">[Custom audio: bm0230_MaxBalanceMenu_DM_noinput_1]</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/bm0230_MaxBalanceMenu_DM_nomatch_2.wav\\\">[Custom audio: bm0230_MaxBalanceMenu_DM_nomatch_2]</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.bm0230_MaxBalanceMenu_DM_reco\n  prompt:\n    speak:\n      - \"[Custom audio: bm0230_MaxBalanceMenu_initial]\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: mainmenu\n          displayName: mainmenu\n        - id: operator\n          displayName: operator\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/bm0230_MaxBalanceMenu_DM_noinput_1.wav\\\">[Custom audio: bm0230_MaxBalanceMenu_DM_noinput_1]</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_bm0230_MaxBalanceMenu_DM\n  value: =Text(Global.bm0230_MaxBalanceMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_bm0230_MaxBalanceMenu_DM = \"mainmenu\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferDestination\n          value: attPrepaid\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M01_MainMenu2.dvxml#ma1334_InitiateMainMenu_DS\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_bm0230_MaxBalanceMenu_DM = \"operator\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.lastTransferState\n              value: bm0230_MaxBalanceMenu_DM\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.transferReason\n              value: AGENT_REQUEST\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.M09_MainMenu.dvxml#ma9210_NeedTransferType_DS\n\n    elseActions:\n      - kind: ConditionGroup\n        id: conditionGroup_REPLACE_THIS\n        conditions:\n          - id: conditionItem_REPLACE_THIS\n            condition: =If(Global.Var_bm0230_MaxBalanceMenu_DM = \"repeat\", true, false)\n            actions:\n              - kind: GotoAction\n                id: goto_REPLACE_THIS\n                actionId: bm0230_MaxBalanceMenu_DM\n```\n\n---\n\n**Notes on the conversion:**\n\n- **Prompt/Audio:** The `<prompt type=\"custom\">` with a `className` is represented as `[Custom audio: ...]` in the YAML, as there is no direct audio file. If you have a mapping from className to audio file, you can replace `[Custom audio: ...]` with the actual audio tag.\n- **unrecognizedPrompt:** Both nomatchprompts are custom audio, so both are included as custom audio references.\n- **inputTimeoutResponse:** Only one noinput prompt, also custom audio.\n- **entity:** All three command actions (`mainmenu`, `operator`, `repeat`) are included as closed list items.\n- **SetVariable:** For each label, a SetVariable is created for the relevant session-mapping.\n- **BeginDialog/GotoAction:** For `mainmenu` and `operator`, the next is a dialog, so `BeginDialog` is used. For `repeat`, it's a DM, so `GotoAction` is used.\n- **ConditionGroup:** Nested for each label, following the pattern in your examples.\n- **REPLACE_THIS:** All ids are left as `REPLACE_THIS` as per your instruction.\n\nIf you have a mapping for the custom audio className to actual audio files, you can update the `[Custom audio: ...]` placeholders accordingly.", "timestamp": 1749469741.479519, "content_hash": "6304f51f52d9fad38eea68432d99fdd5"}