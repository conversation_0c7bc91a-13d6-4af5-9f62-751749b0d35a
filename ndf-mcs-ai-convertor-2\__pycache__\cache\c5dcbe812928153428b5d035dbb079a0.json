{"response": "```yaml\n- kind: Question\n  id: sc0101_VestaDisambig_DM\n  displayName: sc0101_VestaDisambig_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/sc0101_ini_01.wav\\\">To better assist you please select from the following For help with autopay, debit card or credit card press 1 Otherwise, press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/sc0101_ini_01.wav\\\">To better assist you please select from the following For help with autopay, debit card or credit card press 1 Otherwise, press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.sc0101_VestaDisambig_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.fromCustomerService = true || Global.fromCustomerService = \"true\",\n            \"To get you to the right place, please select one of the following options For help with autopay, debit card or credit card press 1 Otherwise, press 2\",\n            true,\n            \"To better assist you please select from the following For help with autopay, debit card or credit card press 1 Otherwise, press 2\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: other\n          displayName: other\n        - id: payment\n          displayName: payment\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/sc0101_ini_01.wav\\\">To better assist you please select from the following For help with autopay, debit card or credit card press 1 Otherwise, press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/sc0101_ini_01.wav\\\">To better assist you please select from the following For help with autopay, debit card or credit card press 1 Otherwise, press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_sc0101_VestaDisambig_DM\n  value: =Text(Global.sc0101_VestaDisambig_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_sc0101_VestaDisambig_DM = \"other\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferDestination\n          value: attPrepaid\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: sc0102_CheckTransfer_JDA_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_sc0101_VestaDisambig_DM = \"payment\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.transferDestination\n              value: vesta\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: sc0102_CheckTransfer_JDA_DA\n\n    # Set operatorAtVestaDisambig after both actions\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.operatorAtVestaDisambig\n  value: 0\n```", "timestamp": 1749472212.397942, "content_hash": "c5dcbe812928153428b5d035dbb079a0"}