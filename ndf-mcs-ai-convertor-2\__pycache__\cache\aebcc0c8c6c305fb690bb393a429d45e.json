{"response": "```yaml\n- kind: Question\n  id: EP1305_HearPlanDetailsYN_DM\n  displayName: EP1305_HearPlanDetailsYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/EP1305_nm1_01.wav\\\">Do you want to hear the details for your new plan? </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/EP1305_nm2_01.wav\\\">If you want to hear the details of your new plan, say 'yes' or press 1 If you'd just like to move on, say 'no' or press 2 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/EP1305_nm2_01.wav\\\">If you want to hear the details of your new plan, say 'yes' or press 1 If you'd just like to move on, say 'no' or press 2 </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.EP1305_HearPlanDetailsYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/EP1305_ini_01.wav\\\">Would you like to hear those plan details again?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/EP1305_nm1_01.wav\\\">Do you want to hear the details for your new plan? </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/EP1305_nm2_01.wav\\\">If you want to hear the details of your new plan, say 'yes' or press 1 If you'd just like to move on, say 'no' or press 2 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/EP1305_nm2_01.wav\\\">If you want to hear the details of your new plan, say 'yes' or press 1 If you'd just like to move on, say 'no' or press 2 </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_EP1305_HearPlanDetailsYN_DM\n  value: =Text(Global.EP1305_HearPlanDetailsYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_EP1305_HearPlanDetailsYN_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.playDetailsEnteringFrom\n          value: EP1305\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: EP1310_PlayDetailsAgainYN_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_EP1305_HearPlanDetailsYN_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/EP1305_out_01.wav\\\">Alright</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n```", "timestamp": 1749527966.1587956, "content_hash": "aebcc0c8c6c305fb690bb393a429d45e"}