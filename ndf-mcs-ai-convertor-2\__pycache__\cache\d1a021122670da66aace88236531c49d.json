{"response": "```yaml\n- kind: Question\n  id: SE2130_AskMakeChanges_DM\n  displayName: SE2130_AskMakeChanges_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SE2130_nm1_01.wav\\\">These settings apply to all lines on your account</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SE2130_nm2_01.wav\\\">To make changes to your current account restrictions say 'yes' or press 1  or say 'no' or press 2  To hear your current restriction again say 'repeat that' or press 3</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SE2130_nm2_01.wav\\\">To make changes to your current account restrictions say 'yes' or press 1  or say 'no' or press 2  To hear your current restriction again say 'repeat that' or press 3</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.SE2130_AskMakeChanges_DM_reco\n  prompt:\n    speak:\n      - \"{Switch(\n            true,\n            Global.visited_SE2130 = false,\n            \\\"Would you like to make changes here?\\\",\n            Global.visited_SE2130 = true,\n            \\\"These settings apply to all lines on your account\\\"\n        )}\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: yes\n          displayName: yes\n        - id: no\n          displayName: no\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/SE2130_ni1_01.wav\\\">To hear your account restrictions again say 'repeat that'  Otherwise, would you like to make changes?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SE2130_nm2_01.wav\\\">To make changes to your current account restrictions say 'yes' or press 1  or say 'no' or press 2  To hear your current restriction again say 'repeat that' or press 3</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SE2130_nm2_01.wav\\\">To make changes to your current account restrictions say 'yes' or press 1  or say 'no' or press 2  To hear your current restriction again say 'repeat that' or press 3</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.simSwapRestricted\n  value: \"GlobalVars.simSwapRestricted != undefined ? GlobalVars.simSwapRestricted : false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.portOutRestricted\n  value: \"GlobalVars.portOutRestricted != undefined ? GlobalVars.portOutRestricted : false\"\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_SE2130_AskMakeChanges_DM\n  value: =Text(Global.SE2130_AskMakeChanges_DM_reco)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.visited_SE2130\n  value: true\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_SE2130_AskMakeChanges_DM = \"yes\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.simSwapRestricted = false && Global.portOutRestricted = false, true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: SE2140_WhatToChangeBothAllowed_DM\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.simSwapRestricted = true && Global.portOutRestricted = true, true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: SE2156_PlayUnblockInfo_DM\n\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.simSwapRestricted = false && Global.portOutRestricted = true, true, false)\n                      actions:\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: SE2150_WhatToChangeMix2_DM\n\n                  elseActions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: SE2145_WhatToChangeMix1_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_SE2130_AskMakeChanges_DM = \"no\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/SE2130_out_01.wav\\\">Alright</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_SE2130_AskMakeChanges_DM = \"repeat\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.repeat_at_SE2130\n                  value: true\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: SE2120_PlayCurrentSettings_PP\n```", "timestamp": 1749529368.6913688, "content_hash": "d1a021122670da66aace88236531c49d"}