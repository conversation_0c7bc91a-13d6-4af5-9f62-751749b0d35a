{"response": "```yaml\n- kind: Question\n  id: pp0126_FollowUpMenu_DM\n  displayName: pp0126_FollowUpMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.multiline = true || Global.multiline = \"true\",\n              \"You can say add a line or press 1, remove a line or press 2 For other account changes say other or press 3\",\n              true,\n              \"You can say add a line or press 1 Or for other account changes say other or press 2\"\n          )\n        }\n      - |\n        {Switch(\n              true,\n              Global.multiline = true || Global.multiline = \"true\",\n              \"If youd like to add a line to your account, press 1 To remove a line from your account, press 2 For other account changes, press 3\",\n              true,\n              \"If youd like to add a line to your account, press 1  For other account changes, press 2\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.pp0126_FollowUpMenu_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.multiline = true || Global.multiline = \"true\",\n              \"You can say add a line or remove a line For other account changes say Other\",\n              true,\n              \"You can say add a line or for other account changes say Other\"\n          )\n        }\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: add_line\n          displayName: add_line\n        - id: remove_line\n          displayName: remove_line\n        - id: other\n          displayName: other\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.multiline = true || Global.multiline = \"true\",\n                \"You can say add a line or press 1, remove a line or press 2 For other account changes say other or press 3\",\n                true,\n                \"You can say add a line or press 1 Or for other account changes say other or press 2\"\n            )\n          }\n        - |\n          {Switch(\n                true,\n                Global.multiline = true || Global.multiline = \"true\",\n                \"If youd like to add a line to your account, press 1 To remove a line from your account, press 2 For other account changes, press 3\",\n                true,\n                \"If youd like to add a line to your account, press 1  For other account changes, press 2\"\n            )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_pp0126_FollowUpMenu_DM\n  value: =Text(Global.pp0126_FollowUpMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pp0126_FollowUpMenu_DM = \"add_line\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.nextCallerIntent\n          value: add_line\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M01_MainMenu2_Dialog\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_pp0126_FollowUpMenu_DM = \"remove_line\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.nextCallerIntent\n              value: remove_line\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.M01_MainMenu2_Dialog\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_pp0126_FollowUpMenu_DM = \"other\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.fromPlansAndPackages\n                  value: true\n                - kind: BeginDialog\n                  id: begin_REPLACE_THIS\n                  dialog: topic.C01_AccountChanges_Dialog\n\n# Session-mapping for grammars\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.multiline = \"true\" || Global.multiline = true, true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_grammar1\n          value: pp0126_FollowUpMenu_DM.jsp?allowedOptions=add_line^remove_line^other&\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_dtmfgrammar1\n          value: pp0126_FollowUpMenu_DM_dtmf.jsp?allowedOptions=add_line^remove_line^other&\n\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.collection_grammar1\n      value: pp0126_FollowUpMenu_DM.jsp?allowedOptions=add_line^other&\n\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.collection_dtmfgrammar1\n      value: pp0126_FollowUpMenu_DM_dtmf.jsp?allowedOptions=add_line^other&\n```\n", "timestamp": 1749471592.1433258, "content_hash": "7a3705a5b15df0a886155e2dcd0c7ac9"}