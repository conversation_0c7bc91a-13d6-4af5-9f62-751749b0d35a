{"response": "```yaml\n- kind: Question\n  id: cd0210_AskSIMFits_DM\n  displayName: cd0210_AskSIMFits_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I missed that</audio>\"\n      - |\n        {Switch(\n              true,\n              Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n              \"Does your existing SIM card *fit* in your new phone? Just say Yes or No\",\n\n              Global.accountType = \"dataOnly\" || Global.accountType = \"whp\",\n              \"Does your existing SIM card *fit* in your new device? Just say Yes or No\"\n          )\n        }\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">Sorry</audio>\"\n      - |\n        {Switch(\n              true,\n              Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n              \"If your existing SIM card fits in your new phone, say Yes or press 1 If not, say No or press 2\",\n\n              Global.accountType = \"dataOnly\" || Global.accountType = \"whp\",\n              \"If your existing SIM card fits in your new device, say Yes or press 1 If not, say No or press 2\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.cd0210_AskSIMFits_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n              \"Does your SIM card *fit* in your replacement phone?\",\n\n              Global.accountType = \"dataOnly\" || Global.accountType = \"whp\",\n              \"Does your SIM card *fit* in your replacement device?\"\n          )\n        }\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n                \"Does your existing SIM card *fit* in your new phone? Just say Yes or No\",\n\n                Global.accountType = \"dataOnly\" || Global.accountType = \"whp\",\n                \"Does your existing SIM card *fit* in your new device? Just say Yes or No\"\n            )\n          }\n        - |\n          {Switch(\n                true,\n                Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n                \"If your existing SIM card fits in your new phone, say Yes or press 1 If not, say No or press 2\",\n\n                Global.accountType = \"dataOnly\" || Global.accountType = \"whp\",\n                \"If your existing SIM card fits in your new device, say Yes or press 1 If not, say No or press 2\"\n            )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_cd0210_AskSIMFits_DM\n  value: =Text(Global.cd0210_AskSIMFits_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_cd0210_AskSIMFits_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/cd0210_out_01.wav\\\">Okay</audio>\"\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.oldSIMSwap\n          value: true\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: cd0215_CheckSIMDeviceType_JDA_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_cd0210_AskSIMFits_DM = \"false\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: cd0220_NeedsNewSIMWrap_DM\n```", "timestamp": 1749470491.713869, "content_hash": "85683a065a4b7a7894a15e6718442c47"}