{"response": "```yaml\n- kind: Question\n  id: aa2015_BalanceWrapUp_DM\n  displayName: aa2015_BalanceWrapUp_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa2015_nm1_01.wav\\\">Sorry</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.aa2015_BalanceWrapUp_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa2015_ini_01.wav\\\">To hear that again, say,  Repeat That  If you re done feel free to hang-up And to go the main menu say  Main Menu </audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: repeat_that\n          displayName: repeat_that\n        - id: main_menu\n          displayName: main_menu\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/aa2015_ni1_01.wav\\\">5 ms silence</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa2015_BalanceWrapUp_DM\n  value: =Text(Global.aa2015_BalanceWrapUp_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa2015_BalanceWrapUp_DM = \"repeat_that\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aa2015_out_01.wav\\\">Again</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa2005_Balance_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa2015_BalanceWrapUp_DM = \"main_menu\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.nextStep\n              value: main_menu\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/aa2015_out_02.wav\\\">Sure</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa1015_NextStepHandling_JDA_DA\n```", "timestamp": 1749556756.1597517, "content_hash": "4efbef3c616e066189370381a09b39bb"}