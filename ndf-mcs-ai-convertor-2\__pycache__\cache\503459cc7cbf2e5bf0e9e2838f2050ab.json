{"response": "```yaml\n- kind: Question\n  id: br0125_PaymentDelayed_DM\n  displayName: br0125_PaymentDelayed_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/br0125_ni1_01.wav\\\">I cant enroll you in AutoPay right now, because there is a payment still pending on your account To hear that again, say Repeat or press 1 For something else, say Main Menu or press star Or, if you dont need help with anything else, just hang up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/br0125_nm2_01.wav\\\">I cant enroll you in AutoPay now, because a payment is still pending on your account To hear that information again, press 1 Or if youre all done here, press star to return to the main menu, or go ahead and hang up</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.br0125_PaymentDelayed_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/br0125_ini_01.wav\\\">A payment is still pending so I cant enroll you in AutoPay right now</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_400ms.wav\\\">silence 400ms</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/br0125_ini_02.wav\\\">Say Repeat Or if youre done, say Main Menu or just hang up</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: mainmenu\n          displayName: mainmenu\n        - id: operator\n          displayName: operator\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/br0125_ni1_01.wav\\\">I cant enroll you in AutoPay right now, because there is a payment still pending on your account To hear that again, say Repeat or press 1 For something else, say Main Menu or press star Or, if you dont need help with anything else, just hang up</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_br0125_PaymentDelayed_DM\n  value: =Text(Global.br0125_PaymentDelayed_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_br0125_PaymentDelayed_DM = \"mainmenu\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferDestination\n          value: attPrepaid\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M01_MainMenu2_Dialog\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_br0125_PaymentDelayed_DM = \"operator\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.lastTransferState\n              value: br0125_PaymentDelayed_DM\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.transferReason\n              value: AGENT_REQUEST\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.M09_MainMenu_Dialog\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_br0125_PaymentDelayed_DM = \"repeat\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: br0125_PaymentDelayed_DM\n```", "timestamp": 1749469865.283323, "content_hash": "503459cc7cbf2e5bf0e9e2838f2050ab"}