{"response": "```yaml\n- kind: Question\n  id: ma1330_MoreOptions_DM\n  displayName: ma1330_MoreOptions_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I missed that</audio>\"\n      - |\n        {Switch(\n              true,\n              Global.accountType = \"dataOnly\" || Global.accountType = \"whp\",\n              \"Say Account Changes or press 1, Lost Device or press 2, or Pin Reset or press 3, Another Account or press 4, Technical Support or press 5, or Go Back or press 6\",\n              true,\n              \"Say Account Changes or press 1, Lost Device or press 2, or Pin Reset or press 3, Another Account or press 4, Technical Support or press 5, or Go Back or press 6\"\n          )\n        }\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">Sorry</audio>\"\n      - |\n        {Switch(\n              true,\n              Global.accountType = \"dataOnly\" || Global.accountType = \"whp\",\n              \"To make changes to your account, press 1 If your device was lost or stolen, or you found a device you already reported, press 2 For pin reset, press 3 For help with a different A T and T account, press 4 For technical support, press 5 Or to go back to the Main Menu, press 6\",\n              true,\n              \"To make changes to your account, press 1 If your device was lost or stolen, or you found a device you already reported, press 2 For pin reset, press 3 For help with a different A T and T account, press 4 For technical support, press 5 Or to go back to the Main Menu, press 6\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.ma1330_MoreOptions_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.accountType = \"dataOnly\" || Global.accountType = \"whp\",\n              \"More Options Say Account Changes, Lost Device, Pin Reset, Another Account, Technical Support, or Go Back\",\n              true,\n              \"More Options Say Account Changes, Lost Phone, Pin Reset, Another Account, Technical Support, or Go Back\"\n          )\n        }\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: tech_support\n          displayName: tech_support\n        - id: lost_stolen\n          displayName: lost_stolen\n        - id: account_changes\n          displayName: account_changes\n        - id: other_account\n          displayName: other_account\n        - id: go_back\n          displayName: go_back\n        - id: balance_payments\n          displayName: balance_payments\n        - id: activate_device\n          displayName: activate_device\n        - id: account_info\n          displayName: account_info\n        - id: auto_refill\n          displayName: auto_refill\n        - id: change_plan\n          displayName: change_plan\n        - id: plans_and_packages\n          displayName: plans_and_packages\n        - id: data\n          displayName: data\n        - id: mobile_protection\n          displayName: mobile_protection\n        - id: pin_reset\n          displayName: pin_reset\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.accountType = \"dataOnly\" || Global.accountType = \"whp\",\n                \"Say Account Changes or press 1, Lost Device or press 2, or Pin Reset or press 3, Another Account or press 4, Technical Support or press 5, or Go Back or press 6\",\n                true,\n                \"Say Account Changes or press 1, Lost Device or press 2, or Pin Reset or press 3, Another Account or press 4, Technical Support or press 5, or Go Back or press 6\"\n            )\n          }\n        - |\n          {Switch(\n                true,\n                Global.accountType = \"dataOnly\" || Global.accountType = \"whp\",\n                \"To make changes to your account, press 1 If your device was lost or stolen, or you found a device you already reported, press 2 For pin reset, press 3 For help with a different A T and T account, press 4 For technical support, press 5 Or to go back to the Main Menu, press 6\",\n                true,\n                \"To make changes to your account, press 1 If your device was lost or stolen, or you found a device you already reported, press 2 For pin reset, press 3 For help with a different A T and T account, press 4 For technical support, press 5 Or to go back to the Main Menu, press 6\"\n            )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ma1330_MoreOptions_DM\n  value: =Text(Global.ma1330_MoreOptions_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma1330_MoreOptions_DM = \"tech_support\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/ma1330_out_01.wav\\\">Alright</audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: techSupport\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ma1335_MainMenuRouting_SD\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma1330_MoreOptions_DM = \"lost_stolen\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/ma1330_out_01.wav\\\">Alright</audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: lostStolen\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ma1335_MainMenuRouting_SD\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma1330_MoreOptions_DM = \"account_changes\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/ma1330_out_01.wav\\\">Alright</audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: accountChanges\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ma1335_MainMenuRouting_SD\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma1330_MoreOptions_DM = \"other_account\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/ma1330_out_01.wav\\\">Alright</audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: otherAccount\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ma1335_MainMenuRouting_SD\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma1330_MoreOptions_DM = \"go_back\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ma1334_InitiateMainMenu_JDA_DA\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma1330_MoreOptions_DM = \"balance_payments\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ma1340_BalancePaymentsMenu_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma1330_MoreOptions_DM = \"activate_device\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: activate_device\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ma1370_CallActivateDevice_SD\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma1330_MoreOptions_DM = \"account_info\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/ma1330_out_01.wav\\\">Alright</audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: accountInfo\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.playProactiveInfo\n          value: false\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ma1335_MainMenuRouting_SD\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma1330_MoreOptions_DM = \"auto_refill\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/ma1330_out_01.wav\\\">Alright</audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: autoRefill\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ma1335_MainMenuRouting_SD\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma1330_MoreOptions_DM = \"change_plan\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/ma1330_out_01.wav\\\">Alright</audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: changePlan\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ma1335_MainMenuRouting_SD\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma1330_MoreOptions_DM = \"plans_and_packages\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/ma1330_out_01.wav\\\">Alright</audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: plansAndPackages\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ma1335_MainMenuRouting_SD\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma1330_MoreOptions_DM = \"data\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/ma1330_out_01.wav\\\">Alright</audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: data\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ma1335_MainMenuRouting_SD\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma1330_MoreOptions_DM = \"mobile_protection\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: mobileProtection\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ma1360_MainMenuDeviceProtection_SD\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma1330_MoreOptions_DM = \"pin_reset\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/ma1330_out_01.wav\\\">Alright</audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: pin_reset\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ma1335_MainMenuRouting_SD\n```", "timestamp": **********.521741, "content_hash": "c674842c95493973289aa0d2e9261850"}