{"response": "```yaml\n- kind: Question\n  id: TEST_ANIEntry_DM\n  displayName: TEST_ANIEntry_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/TestAniPrompt1.wav\\\">Please enter the TRN to use for testing Or enter a 2 digit code for the application you want 0 1 for 6 1 1 app 0 2 for Star 2 2 8 0 3 for 8 8 8 Care 0 4 for Star 99 Care 0 5 for D S G app 0 6 for N R H app 0 7 for R C C app 0 8 for Star 228 partial 0 9 for Star 289 1 0 for Fi Serv 1 1 for Test Call Minutes 1 3 for 6 1 1 and 2 2 8 Entry English 1 4 for 6 1 1 and 2 2 8 Entry Spanish 1 5 for 8 8 8 data collection 1 6 for Star 9 9 English 1 7 for Star 9 9 Spanish 2 0 for 6 1 1 GSM 2 1 for 611 Data Collection 2 2     for OMNI 9 0 for 4 9 0 9 1 for 4 9 1 26 for VR</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TestAniPrompt1.wav\\\">Please enter the TRN to use for testing Or enter a 2 digit code for the application you want 0 1 for 6 1 1 app 0 2 for Star 2 2 8 0 3 for 8 8 8 Care 0 4 for Star 99 Care 0 5 for D S G app 0 6 for N R H app 0 7 for R C C app 0 8 for Star 228 partial 0 9 for Star 289 1 0 for Fi Serv 1 1 for Test Call Minutes 1 3 for 6 1 1 and 2 2 8 Entry English 1 4 for 6 1 1 and 2 2 8 Entry Spanish 1 5 for 8 8 8 data collection 1 6 for Star 9 9 English 1 7 for Star 9 9 Spanish 2 0 for 6 1 1 GSM 2 1 for 611 Data Collection 2 2     for OMNI 9 0 for 4 9 0 9 1 for 4 9 1 26 for VR</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.TEST_ANIEntry_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/TestAniPrompt1.wav\\\">Please enter the TRN to use for testing Or enter a 2 digit code for the application you want 0 1 for 6 1 1 app 0 2 for Star 2 2 8 0 3 for 8 8 8 Care 0 4 for Star 99 Care 0 5 for D S G app 0 6 for N R H app 0 7 for R C C app 0 8 for Star 228 partial 0 9 for Star 289 1 0 for Fi Serv 1 1 for Test Call Minutes 1 3 for 6 1 1 and 2 2 8 Entry English 1 4 for 6 1 1 and 2 2 8 Entry Spanish 1 5 for 8 8 8 data collection 1 6 for Star 9 9 English 1 7 for Star 9 9 Spanish 2 0 for 6 1 1 GSM 2 1 for 611 Data Collection 2 2     for OMNI 9 0 for 4 9 0 9 1 for 4 9 1 26 for VR</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: 228\n          displayName: 228\n        - id: 228Part\n          displayName: 228Part\n        - id: 99\n          displayName: 99\n        - id: DSG\n          displayName: DSG\n        - id: RCC\n          displayName: RCC\n        - id: NRH\n          displayName: NRH\n        - id: 611\n          displayName: 611\n        - id: 888\n          displayName: 888\n        - id: 289\n          displayName: 289\n        - id: FiServ\n          displayName: FiServ\n        - id: TestCallMin\n          displayName: TestCallMin\n        - id: 490\n          displayName: 490\n        - id: 491\n          displayName: 491\n        - id: 611_228_Entry_English\n          displayName: 611_228_Entry_English\n        - id: 611_228_Entry_Spanish\n          displayName: 611_228_Entry_Spanish\n        - id: 888_data_collection\n          displayName: 888_data_collection\n        - id: Star_99_English\n          displayName: Star_99_English\n        - id: Star_99_Spanish\n          displayName: Star_99_Spanish\n        - id: 611_GSM\n          displayName: 611_GSM\n        - id: 611_Data_Collection\n          displayName: 611_Data_Collection\n        - id: OMNI\n          displayName: OMNI\n        - id: WNP\n          displayName: WNP\n        - id: PWR\n          displayName: PWR\n        - id: PC\n          displayName: PC\n        - id: VR\n          displayName: VR\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/TestAniPrompt1.wav\\\">Please enter the TRN to use for testing Or enter a 2 digit code for the application you want 0 1 for 6 1 1 app 0 2 for Star 2 2 8 0 3 for 8 8 8 Care 0 4 for Star 99 Care 0 5 for D S G app 0 6 for N R H app 0 7 for R C C app 0 8 for Star 228 partial 0 9 for Star 289 1 0 for Fi Serv 1 1 for Test Call Minutes 1 3 for 6 1 1 and 2 2 8 Entry English 1 4 for 6 1 1 and 2 2 8 Entry Spanish 1 5 for 8 8 8 data collection 1 6 for Star 9 9 English 1 7 for Star 9 9 Spanish 2 0 for 6 1 1 GSM 2 1 for 611 Data Collection 2 2     for OMNI 9 0 for 4 9 0 9 1 for 4 9 1 26 for VR</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/TestAniPrompt1.wav\\\">Please enter the TRN to use for testing Or enter a 2 digit code for the application you want 0 1 for 6 1 1 app 0 2 for Star 2 2 8 0 3 for 8 8 8 Care 0 4 for Star 99 Care 0 5 for D S G app 0 6 for N R H app 0 7 for R C C app 0 8 for Star 228 partial 0 9 for Star 289 1 0 for Fi Serv 1 1 for Test Call Minutes 1 3 for 6 1 1 and 2 2 8 Entry English 1 4 for 6 1 1 and 2 2 8 Entry Spanish 1 5 for 8 8 8 data collection 1 6 for Star 9 9 English 1 7 for Star 9 9 Spanish 2 0 for 6 1 1 GSM 2 1 for 611 Data Collection 2 2     for OMNI 9 0 for 4 9 0 9 1 for 4 9 1 26 for VR</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_TEST_ANIEntry_DM\n  value: =Text(Global.TEST_ANIEntry_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TEST_ANIEntry_DM = \"228\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TEST_ANIEntry_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TEST_ANIEntry_DM = \"228Part\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TEST_ANIEntry_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TEST_ANIEntry_DM = \"99\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TEST_ANIEntry_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TEST_ANIEntry_DM = \"DSG\", true, false)\n      actions:\n        # DSG returns dnis ani, no GotoAction\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TEST_ANIEntry_DM = \"RCC\", true, false)\n      actions:\n        # RCC returns dnis ani, no GotoAction\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TEST_ANIEntry_DM = \"NRH\", true, false)\n      actions:\n        # NRH returns dnis ani, no GotoAction\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TEST_ANIEntry_DM = \"611\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TEST_ANIEntry_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TEST_ANIEntry_DM = \"888\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TEST_ANIEntry_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TEST_ANIEntry_DM = \"289\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TEST_ANIEntry_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TEST_ANIEntry_DM = \"FiServ\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TEST_ANIEntry_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TEST_ANIEntry_DM = \"TestCallMin\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TEST_ANIEntry_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TEST_ANIEntry_DM = \"490\", true, false)\n      actions:\n        # 490 returns dnis ani, no GotoAction\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TEST_ANIEntry_DM = \"491\", true, false)\n      actions:\n        # 491 returns dnis ani, no GotoAction\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TEST_ANIEntry_DM = \"611_228_Entry_English\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TEST_ANIEntry_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TEST_ANIEntry_DM = \"611_228_Entry_Spanish\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TEST_ANIEntry_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TEST_ANIEntry_DM = \"888_data_collection\", true, false)\n      actions:\n        # 888_data_collection returns dnis ani, no GotoAction\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TEST_ANIEntry_DM = \"Star_99_English\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TEST_ANIEntry_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TEST_ANIEntry_DM = \"Star_99_Spanish\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TEST_ANIEntry_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TEST_ANIEntry_DM = \"611_GSM\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TEST_ANIEntry_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TEST_ANIEntry_DM = \"611_Data_Collection\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TEST_ANIEntry_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TEST_ANIEntry_DM = \"OMNI\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TEST_ANIEntry_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TEST_ANIEntry_DM = \"WNP\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TEST_ANIEntry_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TEST_ANIEntry_DM = \"PWR\", true, false)\n      actions:\n        # PWR returns dnis ani, no GotoAction\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TEST_ANIEntry_DM = \"PC\", true, false)\n      actions:\n        # PC returns dnis ani, no GotoAction\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TEST_ANIEntry_DM = \"VR\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TEST_ANIEntry_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TEST_ANIEntry_DM = \"default\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.ani\n          value: TEST_ANIEntry_DM.returnvalue\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.trn\n          value: TEST_ANIEntry_DM.returnvalue\n        # default returns dnis ani, no GotoAction\n```", "timestamp": 1749529689.6368804, "content_hash": "eb3e9cb74399c4b22f0bc1f8675eea23"}