{"response": "```yaml\n- kind: Question\n  id: PL1010_AskLanguage_DM\n  displayName: PL1010_AskLanguage_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.PL1010_AskLanguage_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/PL1010_ini_01.wav\\\">Para continuar en español, marque '9'</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: Spanish\n          displayName: Spanish\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_PL1010_AskLanguage_DM\n  value: =Text(Global.PL1010_AskLanguage_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_PL1010_AskLanguage_DM = \"Spanish\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.language\n          value: es-US\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.PromotionCenterLine_Dialog.dvxml#PL1015_PlayRebateInfo_PP\n```", "timestamp": 1749529112.6958282, "content_hash": "ae4d606cfbc9d9138e25c627dacf002e"}