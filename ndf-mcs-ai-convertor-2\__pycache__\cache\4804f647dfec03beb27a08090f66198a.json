{"response": "```yaml\n- kind: Question\n  id: AI1000_ReadyToWriteNumberYN_DM\n  displayName: AI1000_ReadyToWriteNumberYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AI1000_nm1_01.wav\\\">If you have a pen and paper handy to write down your account information, say 'yes' If not, say 'no' and I'll wait while you get something to write with</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AI1000_nm2_01.wav\\\">Say  yes  or press one, or  no  or press two  Do you have a pen and paper handy?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AI1000_nm3_01.wav\\\">If you re ready to copy down your new account details, say  yes  or press one  If you d like me to wait, say  no  or press two</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.AI1000_ReadyToWriteNumberYN_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            // Case 1: acceptedAPNSMS = true && acceptedAcctDetsSMS = true\n            (Global.acceptedAPNSMS = true && Global.acceptedAcctDetsSMS = true) && (Global.APNSMSSuccess = true && Global.acctDetsSMSSuccess = true),\n            [\n                \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/AI1000_ini_03.wav\\\">Okay, your new account's ready! </audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/AI1000_ini_07.wav\\\">You'll get the text messages about the APN settings and your security details in a few minutes</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/AI1000_ini_06.wav\\\">For now</audio>\",\n                \"{Switch(true, Global.npi_flag = true, \\\"<audio src=\\\\\\\"AUDIO_LOCATION/AI1000_ini_02.wav\\\\\\\">I've got your account details ready Are you ready to write them down?</audio>\\\", true, \\\"<audio src=\\\\\\\"AUDIO_LOCATION/AI1000_ini_01.wav\\\\\\\">I've got your new phone number  Are you ready to write it down?</audio>\\\")}\"\n            ],\n\n            (Global.acceptedAPNSMS = true && Global.acceptedAcctDetsSMS = true) && !(Global.APNSMSSuccess = true && Global.acctDetsSMSSuccess = true),\n            [\n                \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/AI1000_ini_03.wav\\\">Okay, your new account's ready! </audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/AI1000_ini_08.wav\\\">I wasn't able to send *all* of your text messages, so make sure you check metro by t dash mobile dot com </audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/AI1000_ini_06.wav\\\">For now</audio>\",\n                \"{Switch(true, Global.npi_flag = true, \\\"<audio src=\\\\\\\"AUDIO_LOCATION/AI1000_ini_02.wav\\\\\\\">I've got your account details ready Are you ready to write them down?</audio>\\\", true, \\\"<audio src=\\\\\\\"AUDIO_LOCATION/AI1000_ini_01.wav\\\\\\\">I've got your new phone number  Are you ready to write it down?</audio>\\\")}\"\n            ],\n\n            // Case 2: acceptedAPNSMS = true\n            Global.acceptedAPNSMS = true && Global.APNSMSSuccess = true,\n            [\n                \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/AI1000_ini_03.wav\\\">Okay, your new account's ready! </audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/AI1000_ini_04.wav\\\">You'll get the text message about APN settings in a few minutes</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/AI1000_ini_06.wav\\\">For now</audio>\",\n                \"{Switch(true, Global.npi_flag = true, \\\"<audio src=\\\\\\\"AUDIO_LOCATION/AI1000_ini_02.wav\\\\\\\">I've got your account details ready Are you ready to write them down?</audio>\\\", true, \\\"<audio src=\\\\\\\"AUDIO_LOCATION/AI1000_ini_01.wav\\\\\\\">I've got your new phone number  Are you ready to write it down?</audio>\\\")}\"\n            ],\n\n            Global.acceptedAPNSMS = true && Global.APNSMSSuccess = false,\n            [\n                \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/AI1000_ini_03.wav\\\">Okay, your new account's ready! </audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/AI1000_ini_05.wav\\\">I couldn't text you about the APN settings though, so please check metro by t dash mobile dot com</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/AI1000_ini_06.wav\\\">For now</audio>\",\n                \"{Switch(true, Global.npi_flag = true, \\\"<audio src=\\\\\\\"AUDIO_LOCATION/AI1000_ini_02.wav\\\\\\\">I've got your account details ready Are you ready to write them down?</audio>\\\", true, \\\"<audio src=\\\\\\\"AUDIO_LOCATION/AI1000_ini_01.wav\\\\\\\">I've got your new phone number  Are you ready to write it down?</audio>\\\")}\"\n            ],\n\n            // Case 3: acceptedAcctDetsSMS = true\n            Global.acceptedAcctDetsSMS = true && Global.acctDetsSMSSuccess = true,\n            [\n                \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/AI1000_ini_03.wav\\\">Okay, your new account's ready! </audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/AI1000_ini_09.wav\\\">You'll get the text message about your security details in a few minutes </audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/AI1000_ini_06.wav\\\">For now</audio>\",\n                \"{Switch(true, Global.npi_flag = true, \\\"<audio src=\\\\\\\"AUDIO_LOCATION/AI1000_ini_02.wav\\\\\\\">I've got your account details ready Are you ready to write them down?</audio>\\\", true, \\\"<audio src=\\\\\\\"AUDIO_LOCATION/AI1000_ini_01.wav\\\\\\\">I've got your new phone number  Are you ready to write it down?</audio>\\\")}\"\n            ],\n\n            Global.acceptedAcctDetsSMS = true && Global.acctDetsSMSSuccess = false,\n            [\n                \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/AI1000_ini_03.wav\\\">Okay, your new account's ready! </audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/AI1000_ini_10.wav\\\">I couldn't text you about the your security details, so please check metro by t dash mobile dot com </audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/AI1000_ini_06.wav\\\">For now</audio>\",\n                \"{Switch(true, Global.npi_flag = true, \\\"<audio src=\\\\\\\"AUDIO_LOCATION/AI1000_ini_02.wav\\\\\\\">I've got your account details ready Are you ready to write them down?</audio>\\\", true, \\\"<audio src=\\\\\\\"AUDIO_LOCATION/AI1000_ini_01.wav\\\\\\\">I've got your new phone number  Are you ready to write it down?</audio>\\\")}\"\n            ],\n\n            // Default case\n            [\n                \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/AI1000_ini_03.wav\\\">Okay, your new account's ready! </audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/AI1000_ini_06.wav\\\">For now</audio>\",\n                \"{Switch(true, Global.npi_flag = true, \\\"<audio src=\\\\\\\"AUDIO_LOCATION/AI1000_ini_02.wav\\\\\\\">I've got your account details ready Are you ready to write them down?</audio>\\\", true, \\\"<audio src=\\\\\\\"AUDIO_LOCATION/AI1000_ini_01.wav\\\\\\\">I've got your new phone number  Are you ready to write it down?</audio>\\\")}\"\n            ]\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/AI1000_nm1_01.wav\\\">If you have a pen and paper handy to write down your account information, say 'yes' If not, say 'no' and I'll wait while you get something to write with</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AI1000_nm2_01.wav\\\">Say  yes  or press one, or  no  or press two  Do you have a pen and paper handy?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AI1000_nm3_01.wav\\\">If you re ready to copy down your new account details, say  yes  or press one  If you d like me to wait, say  no  or press two</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.npi_flag\n  value: GlobalVars.npi_flag\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.callType\n  value: GlobalVars.callType\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.acceptedAPNSMS\n  value: \"GlobalVars.acceptedAPNSMS != undefined ? GlobalVars.acceptedAPNSMS : false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.APNSMSSuccess\n  value: \"GlobalVars.APNSMSSuccess != undefined ? GlobalVars.APNSMSSuccess : false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.acceptedAcctDetsSMS\n  value: \"GlobalVars.acceptedAcctDetsSMS != undefined ? GlobalVars.acceptedAcctDetsSMS : false \"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.acctDetsSMSSuccess\n  value: \"GlobalVars.acctDetsSMSSuccess != undefined ? GlobalVars.acctDetsSMSSuccess : false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.operatorRequest\n  value: false\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_AI1000_ReadyToWriteNumberYN_DM\n  value: =Text(Global.AI1000_ReadyToWriteNumberYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AI1000_ReadyToWriteNumberYN_DM = \"yes\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_600ms.wav\\\"></audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: AI1015_PhoneNumber_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_AI1000_ReadyToWriteNumberYN_DM = \"no\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/AI1000_out_01.wav\\\">No problem, I'll wait while you get set up!</audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_600ms.wav\\\"></audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: AI1010_NewNumberWait_DM\n```", "timestamp": 1749527289.5097578, "content_hash": "4804f647dfec03beb27a08090f66198a"}