{"response": "```yaml\n- kind: Question\n  id: ai0115_AskIMEI_DM\n  displayName: ai0115_AskIMEI_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I missed that</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ai0115_ni1_01.wav\\\">Say or enter the 15 digits of the IMEI number Or say I Cant Find It or press 1 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">Sorry</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ai0115_ni2_01.wav\\\">Please enter the full 15-digit IMEI number Or if you dont see a number like that, press 1 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ai0115_nm3_01.wav\\\">I still didn't get that If you have the 15-digit IMEI number from that sticker, go ahead and enter it now If not, just press 1 </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ai0115_AskIMEI_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ai0115_ini_01.wav\\\">Read me the full 15-digit IMEI number from that sticker </audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: cant_find\n          displayName: cant_find\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ai0115_ni1_01.wav\\\">Say or enter the 15 digits of the IMEI number Or say I Cant Find It or press 1 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ai0115_ni2_01.wav\\\">Please enter the full 15-digit IMEI number Or if you dont see a number like that, press 1 </audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ai0115_AskIMEI_DM\n  value: =Text(Global.ai0115_AskIMEI_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ai0115_AskIMEI_DM = \"cant_find\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/ai0115_out_02.wav\\\">That's OK Let's try something different </audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.foundIMEI\n          value: false\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ai0210_IsAccountType_JDA_DA\n\n  elseActions:\n    - kind: SendActivity\n      id: sendActivity_REPLACE_THIS\n      activity:\n        speak:\n          - \"<audio src=\\\"AUDIO_LOCATION/ai0115_out_01.wav\\\">Thanks</audio>\"\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.imei\n      value: ai0115_AskIMEI_DM.returnvalue\n    - kind: BeginDialog\n      id: begin_REPLACE_THIS\n      dialog: topic.A04_IMEI_03.dvxml\n```", "timestamp": **********.4496439, "content_hash": "4b836c5d84b1aa2a2e48cddce39d74ae"}