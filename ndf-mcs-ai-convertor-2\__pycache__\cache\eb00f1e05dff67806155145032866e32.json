{"response": "```yaml\n- kind: Question\n  id: SH1020_SuspendedOptions_DM\n  displayName: SH1020_SuspendedOptions_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.SH1020_SuspendedOptions_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            // Case 1\n            (Global.aniMatch = false) && (Global.isLoggedIn = false || Global.isLoggedIn = \"false\") && (Global.extensionAllowed = true),\n            \"<audio src=\\\"AUDIO_LOCATION/SH1020_ini_02.wav\\\">To hear the balance and make a payment, say 'pay now' Or say 'get an extension', 'change my rate plan',  or 'autopay' </audio>\",\n\n            // Case 2\n            (Global.aniMatch = false) && (Global.isLoggedIn = false || Global.isLoggedIn = \"false\") && (Global.extensionAllowed = false),\n            \"<audio src=\\\"AUDIO_LOCATION/SH1020_ini_05.wav\\\">To hear the balance and make a payment, say 'pay now' Or say 'change my rate plan',  or 'autopay'</audio>\",\n\n            // Case 3\n            (!(Global.aniMatch = false && (Global.isLoggedIn = false || Global.isLoggedIn = \"false\"))) && (Global.extensionAllowed = true),\n            \"<audio src=\\\"AUDIO_LOCATION/SH1020_ini_01.wav\\\">Please say 'pay now', 'get an extension', 'change my rate plan',  or 'autopay' </audio>\",\n\n            // Case 4\n            (!(Global.aniMatch = false && (Global.isLoggedIn = false || Global.isLoggedIn = \"false\"))) && (Global.extensionAllowed = false),\n            \"<audio src=\\\"AUDIO_LOCATION/SH1020_ini_07.wav\\\">Please say 'pay now', 'change rate plan',  or 'autopay' </audio>\",\n\n            // Case 5\n            ((Global.aniMatch = true && Global.switchLinesSuccess = false) && (Global.eligibleForBillCycleReset = true || Global.eligibleForBillCycleReset = \"true\")),\n            \"<audio src=\\\"AUDIO_LOCATION/SH1020_ini_03.wav\\\">You can also say 'change my due date', or 'switch lines' </audio>\",\n\n            // Case 6\n            ((Global.aniMatch = true && Global.switchLinesSuccess = false) && (Global.eligibleForBillCycleReset <> true && Global.eligibleForBillCycleReset <> \"true\")),\n            \"<audio src=\\\"AUDIO_LOCATION/SH1020_ini_04.wav\\\">You can also say 'switch lines'</audio>\",\n\n            // Case 7\n            (!(Global.aniMatch = true && Global.switchLinesSuccess = false) && (Global.eligibleForBillCycleReset = true || Global.eligibleForBillCycleReset = \"true\")),\n            \"<audio src=\\\"AUDIO_LOCATION/SH1020_ini_06.wav\\\">You can also say 'change my due date'</audio>\",\n\n            // Case 8\n            (!(Global.aniMatch = true && Global.switchLinesSuccess = false) && (Global.eligibleForBillCycleReset <> true && Global.eligibleForBillCycleReset <> \"true\")),\n            \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: make-payment\n          displayName: make-payment\n        - id: change-plan\n          displayName: change-plan\n        - id: vague-autopay_sh\n          displayName: vague-autopay_sh\n        - id: switch-lines\n          displayName: switch-lines\n        - id: request-extension\n          displayName: request-extension\n        - id: change-payment_date\n          displayName: change-payment_date\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n              true,\n              (Global.aniMatch = false) && (Global.isLoggedIn = false || Global.isLoggedIn = \"false\") && (Global.extensionAllowed = true),\n              \"<audio src=\\\"AUDIO_LOCATION/SH1020_ini_02.wav\\\">To hear the balance and make a payment, say 'pay now' Or say 'get an extension', 'change my rate plan',  or 'autopay' </audio>\",\n\n              (Global.aniMatch = false) && (Global.isLoggedIn = false || Global.isLoggedIn = \"false\") && (Global.extensionAllowed = false),\n              \"<audio src=\\\"AUDIO_LOCATION/SH1020_ini_05.wav\\\">To hear the balance and make a payment, say 'pay now' Or say 'change my rate plan',  or 'autopay'</audio>\",\n\n              (!(Global.aniMatch = false && (Global.isLoggedIn = false || Global.isLoggedIn = \"false\"))) && (Global.extensionAllowed = true),\n              \"<audio src=\\\"AUDIO_LOCATION/SH1020_ini_01.wav\\\">Please say 'pay now', 'get an extension', 'change my rate plan',  or 'autopay' </audio>\",\n\n              (!(Global.aniMatch = false && (Global.isLoggedIn = false || Global.isLoggedIn = \"false\"))) && (Global.extensionAllowed = false),\n              \"<audio src=\\\"AUDIO_LOCATION/SH1020_ini_07.wav\\\">Please say 'pay now', 'change rate plan',  or 'autopay' </audio>\",\n\n              ((Global.aniMatch = true && Global.switchLinesSuccess = false) && (Global.eligibleForBillCycleReset = true || Global.eligibleForBillCycleReset = \"true\")),\n              \"<audio src=\\\"AUDIO_LOCATION/SH1020_ini_03.wav\\\">You can also say 'change my due date', or 'switch lines' </audio>\",\n\n              ((Global.aniMatch = true && Global.switchLinesSuccess = false) && (Global.eligibleForBillCycleReset <> true && Global.eligibleForBillCycleReset <> \"true\")),\n              \"<audio src=\\\"AUDIO_LOCATION/SH1020_ini_04.wav\\\">You can also say 'switch lines'</audio>\",\n\n              (!(Global.aniMatch = true && Global.switchLinesSuccess = false) && (Global.eligibleForBillCycleReset = true || Global.eligibleForBillCycleReset = \"true\")),\n              \"<audio src=\\\"AUDIO_LOCATION/SH1020_ini_06.wav\\\">You can also say 'change my due date'</audio>\",\n\n              (!(Global.aniMatch = true && Global.switchLinesSuccess = false) && (Global.eligibleForBillCycleReset <> true && Global.eligibleForBillCycleReset <> \"true\")),\n              \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n          )}\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - |\n          {Switch(\n              true,\n              Global.extensionAllowed = true,\n              \"<audio src=\\\"AUDIO_LOCATION/SH1020_nm2_01.wav\\\">Please say 'pay now' or press 1 'Get an extension' or press 2 'Change my rate plan' or press 3 or 'Autopay' - 4 </audio>\",\n              Global.extensionAllowed = false,\n              \"<audio src=\\\"AUDIO_LOCATION/SH1020_nm2_02.wav\\\">Please say 'pay now' or press 1 'Change rate plan' or press 2 or 'Autopay' - 3 </audio>\"\n          )}\n        - |\n          {Switch(\n              true,\n              ((Global.aniMatch = true && Global.switchLinesSuccess = false) && (Global.eligibleForBillCycleReset = true || Global.eligibleForBillCycleReset = \"true\") && (Global.extensionAllowed = true)),\n              \"<audio src=\\\"AUDIO_LOCATION/SH1020_nm2_03.wav\\\">You can also say change my due date or press 5, or switch account or press 6</audio>\",\n              ((Global.aniMatch = true && Global.switchLinesSuccess = false) && (Global.eligibleForBillCycleReset = true || Global.eligibleForBillCycleReset = \"true\") && (Global.extensionAllowed = false)),\n              \"<audio src=\\\"AUDIO_LOCATION/SH1020_nm2_05.wav\\\">You can also say 'change my due date' or press 4, or 'switch lines' or press 5 </audio>\",\n              ((Global.aniMatch = true && Global.switchLinesSuccess = false) && (Global.eligibleForBillCycleReset <> true && Global.eligibleForBillCycleReset <> \"true\") && (Global.extensionAllowed = true)),\n              \"<audio src=\\\"AUDIO_LOCATION/SH1020_nm2_04.wav\\\">You can also say switch account, or press 4</audio>\",\n              ((Global.aniMatch = true && Global.switchLinesSuccess = false) && (Global.eligibleForBillCycleReset <> true && Global.eligibleForBillCycleReset <> \"true\") && (Global.extensionAllowed = false)),\n              \"<audio src=\\\"AUDIO_LOCATION/SH1020_nm2_07.wav\\\">You can also say 'switch lines', or press 4</audio>\",\n              (Global.aniMatch <> true && (Global.eligibleForBillCycleReset = true || Global.eligibleForBillCycleReset = \"true\") && (Global.extensionAllowed = true)),\n              \"<audio src=\\\"AUDIO_LOCATION/SH1020_nm2_06.wav\\\">You can also say change my due date or press 5</audio>\",\n              (Global.aniMatch <> true && (Global.eligibleForBillCycleReset = true || Global.eligibleForBillCycleReset = \"true\") && (Global.extensionAllowed = false)),\n              \"<audio src=\\\"AUDIO_LOCATION/SH1020_nm2_08.wav\\\">You can also say 'change my due date' or press 4 </audio>\"\n          )}\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - |\n          {Switch(\n              true,\n              Global.extensionAllowed = true,\n              \"<audio src=\\\"AUDIO_LOCATION/SH1020_nm2_01.wav\\\">Please say 'pay now' or press 1 'Get an extension' or press 2 'Change my rate plan' or press 3 or 'Autopay' - 4 </audio>\",\n              Global.extensionAllowed = false,\n              \"<audio src=\\\"AUDIO_LOCATION/SH1020_nm2_02.wav\\\">Please say 'pay now' or press 1 'Change rate plan' or press 2 or 'Autopay' - 3 </audio>\"\n          )}\n        - |\n          {Switch(\n              true,\n              ((Global.aniMatch = true && Global.switchLinesSuccess = false) && (Global.eligibleForBillCycleReset = true || Global.eligibleForBillCycleReset = \"true\") && (Global.extensionAllowed = true)),\n              \"<audio src=\\\"AUDIO_LOCATION/SH1020_nm2_03.wav\\\">You can also say change my due date or press 5, or switch account or press 6</audio>\",\n              ((Global.aniMatch = true && Global.switchLinesSuccess = false) && (Global.eligibleForBillCycleReset = true || Global.eligibleForBillCycleReset = \"true\") && (Global.extensionAllowed = false)),\n              \"<audio src=\\\"AUDIO_LOCATION/SH1020_nm2_05.wav\\\">You can also say 'change my due date' or press 4, or 'switch lines' or press 5 </audio>\",\n              ((Global.aniMatch = true && Global.switchLinesSuccess = false) && (Global.eligibleForBillCycleReset <> true && Global.eligibleForBillCycleReset <> \"true\") && (Global.extensionAllowed = true)),\n              \"<audio src=\\\"AUDIO_LOCATION/SH1020_nm2_04.wav\\\">You can also say switch account, or press 4</audio>\",\n              ((Global.aniMatch = true && Global.switchLinesSuccess = false) && (Global.eligibleForBillCycleReset <> true && Global.eligibleForBillCycleReset <> \"true\") && (Global.extensionAllowed = false)),\n              \"<audio src=\\\"AUDIO_LOCATION/SH1020_nm2_07.wav\\\">You can also say 'switch lines', or press 4</audio>\",\n              (Global.aniMatch <> true && (Global.eligibleForBillCycleReset = true || Global.eligibleForBillCycleReset = \"true\") && (Global.extensionAllowed = true)),\n              \"<audio src=\\\"AUDIO_LOCATION/SH1020_nm2_06.wav\\\">You can also say change my due date or press 5</audio>\",\n              (Global.aniMatch <> true && (Global.eligibleForBillCycleReset = true || Global.eligibleForBillCycleReset = \"true\") && (Global.extensionAllowed = false)),\n              \"<audio src=\\\"AUDIO_LOCATION/SH1020_nm2_08.wav\\\">You can also say 'change my due date' or press 4 </audio>\"\n          )}\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.isAutopayEnabled\n  value: GlobalVars.GetAccountDetails.isAutopayEnabled\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.extensionAllowed\n  value: \"GlobalVars.extensionAllowed == undefined ? false : GlobalVars.extensionAllowed\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.allowedResponses\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.allowedResponsesDtmf\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.isLoggedIn\n  value: GlobalVars.loggedIn\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.aniMatch\n  value: GlobalVars.aniMatch\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.switchLinesSuccess\n  value: GlobalVars.switchLinesSuccess\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_grammar1\n  value: SH1020_SuspendedOptions_DM.jsp\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_dtmfgrammar1\n  value: SH1020_SuspendedOptions_DM_dtmf.jsp\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_SH1020_SuspendedOptions_DM\n  value: =Text(Global.SH1020_SuspendedOptions_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_SH1020_SuspendedOptions_DM = \"make-payment\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/SH1020_out_01.wav\\\">Alright</audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: make_pmt\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.acceptPayByPhone\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.paymentsEntryPoint\n          value: careSuspended\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: SH1010_MakePayment_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_SH1020_SuspendedOptions_DM = \"change-plan\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.callType\n              value: change_plan\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.cti_Intent\n              value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.suspendedRatePlanChange\n              value: true\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.securityRequired\n              value: true\n\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.SuspendedHandlingExtension_Main.dvxml#SH1101_CheckContext_DS\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_SH1020_SuspendedOptions_DM = \"vague-autopay_sh\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.tag\n                  value: vague-autopay\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: SH1029_CheckHasAutopay_JDA\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_SH1020_SuspendedOptions_DM = \"switch-lines\", true, false)\n                  actions:\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/SH1020_out_05.wav\\\">Sure</audio>\"\n\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.switchLinesEntryPoint\n                      value: suspended\n\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: SH1505_SwitchLines_SD\n\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.Var_SH1020_SuspendedOptions_DM = \"request-extension\", true, false)\n                      actions:\n                        - kind: ConditionGroup\n                          id: conditionGroup_REPLACE_THIS\n                          conditions:\n                            - id: conditionItem_REPLACE_THIS\n                              condition: =If(Global.extensionAllowed = true, true, false)\n                              actions:\n                                - kind: SendActivity\n                                  id: sendActivity_REPLACE_THIS\n                                  activity:\n                                    speak:\n                                      - \"<audio src=\\\"AUDIO_LOCATION/SH1020_out_03.wav\\\">Sure</audio>\"\n                                      - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n\n                                - kind: SetVariable\n                                  id: setVariable_REPLACE_THIS\n                                  variable: Global.GlobalVars.callType\n                                  value: extension\n\n                                - kind: SetVariable\n                                  id: setVariable_REPLACE_THIS\n                                  variable: Global.GlobalVars.cti_Intent\n                                  value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n                                - kind: BeginDialog\n                                  id: begin_REPLACE_THIS\n                                  dialog: topic.SuspendedHandlingExtension_Main.dvxml#SH1301_CheckContext_DS\n\n                          elseActions:\n                            - kind: SendActivity\n                              id: sendActivity_REPLACE_THIS\n                              activity:\n                                speak:\n                                  - \"<audio src=\\\"AUDIO_LOCATION/SH1020_out_02.wav\\\">I'm sorry, your account's not eligible for an extension right now You can reduce your payment due now by changing your plan and your add-ons in the myMetro app You'll also be able to make a payment there when you're ready If you DON'T need anything else, you can hang up Otherwise </audio>\"\n\n                            - kind: GotoAction\n                              id: goto_REPLACE_THIS\n                              actionId: SH1020_SuspendedOptions_DM\n\n                  elseActions:\n                    - kind: ConditionGroup\n                      id: conditionGroup_REPLACE_THIS\n                      conditions:\n                        - id: conditionItem_REPLACE_THIS\n                          condition: =If(Global.Var_SH1020_SuspendedOptions_DM = \"change-payment_date\", true, false)\n                          actions:\n                            - kind: SetVariable\n                              id: setVariable_REPLACE_THIS\n                              variable: Global.GlobalVars.callType\n                              value: billcyclereset\n\n                            - kind: SetVariable\n                              id: setVariable_REPLACE_THIS\n                              variable: Global.GlobalVars.cti_Intent\n                              value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n                            - kind: SetVariable\n                              id: setVariable_REPLACE_THIS\n                              variable: Global.GlobalVars.paymentsEntryPoint\n                              value: careSuspended\n\n                            - kind: SendActivity\n                              id: sendActivity_REPLACE_THIS\n                              activity:\n                                speak:\n                                  - \"<audio src=\\\"AUDIO_LOCATION/SH1020_out_04.wav\\\">Sure!</audio>\"\n\n                            - kind: GotoAction\n                              id: goto_REPLACE_THIS\n                              actionId: SH1405_BillCyclereset_SD\n```", "timestamp": 1749529699.366201, "content_hash": "eb00f1e05dff67806155145032866e32"}