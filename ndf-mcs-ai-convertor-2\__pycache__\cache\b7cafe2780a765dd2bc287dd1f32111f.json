{"response": "```yaml\n- kind: Question\n  id: AU1010_AskSetUpAutoPay_DM\n  displayName: AU1010_AskSetUpAutoPay_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AU1010_nm1_01.wav\\\">Would you like to set up autopay?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AU1010_nm2_01.wav\\\">To set up autopay say 'yes' or press 1  or say 'no' or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AU1010_nm2_01.wav\\\">To set up autopay say 'yes' or press 1  or say 'no' or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.AU1010_AskSetUpAutoPay_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/AU1010_ini_01.wav\\\">Would you like to set up autopay now?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/AU1010_nm1_01.wav\\\">Would you like to set up autopay?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AU1010_nm2_01.wav\\\">To set up autopay say 'yes' or press 1  or say 'no' or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AU1010_nm2_01.wav\\\">To set up autopay say 'yes' or press 1  or say 'no' or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_AU1010_AskSetUpAutoPay_DM\n  value: =Text(Global.AU1010_AskSetUpAutoPay_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AU1010_AskSetUpAutoPay_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.tag\n          value: setup-autopay\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: AU1302_CheckNextDueDate_JDA_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_AU1010_AskSetUpAutoPay_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/AU1010_out_01.wav\\\">Ok</audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n```", "timestamp": 1749558048.4066572, "content_hash": "b7cafe2780a765dd2bc287dd1f32111f"}