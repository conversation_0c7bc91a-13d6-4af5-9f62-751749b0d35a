{"response": "```yaml\n- kind: Question\n  id: st1045_InstructionsWrapMenu_DM\n  displayName: st1045_InstructionsWrapMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/st1045_ni1_01.wav\\\">To hear those instructions again, say Repeat or press 1 If you're done, just hang up Or if you need help with something else, say Main Menu or press star</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/st1045_nm2_01.wav\\\">If you need those instructions again, press 1 If you have what you need, go ahead and hang up Or if you *do* need help with something else, press star</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.st1045_InstructionsWrapMenu_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n\n            // Case 1: intent = \"techSupportData\" && (deviceType = \"featurephone\" || deviceType = \"smartphone\" || deviceType = \"tablet\")\n            Global.intent = \"techSupportData\" && (Global.deviceType = \"featurephone\" || Global.deviceType = \"smartphone\" || Global.deviceType = \"tablet\"),\n            \"<audio src=\\\"AUDIO_LOCATION/st1045_ini_01.wav\\\">You may be able to reset your data connection by doing the following First, go to your settings and turn Wi Fi off Next, turn Airplane Mode on You should see a small image of a plane on the top of your screen after you do that Next, wait about 15 seconds, and turn Airplane Mode back off Finally, wait another 15 seconds, and then try going to a website If the website loads, then your internet is working  If that doesn't work, please call us back while you have access to your device</audio>\",\n\n            // Case 2: accountType = \"gophone\" || accountType = \"spareOne\"\n            Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n            \"<audio src=\\\"AUDIO_LOCATION/st1045_ini_02.wav\\\">You may be able to fix the problem by turning off the power on that phone, waiting for about 30 seconds, and then turning the power back on Go ahead and try that, and if you're still having problems, please call us back while you have access to your phone</audio>\",\n\n            // Case 3: !(accountType = \"gophone\" || accountType = \"spareOne\")\n            !(Global.accountType = \"gophone\" || Global.accountType = \"spareOne\"),\n            \"<audio src=\\\"AUDIO_LOCATION/st1045_ini_03.wav\\\">You may be able to fix the problem by turning off the power on that device, waiting for about 30 seconds, and then turning the power back on Go ahead and try that, and if you're still having problems, please call us back while you have access to your device</audio>\",\n\n            // Default: silence and wrap up\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\"></audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/st1045_ini_04.wav\\\">To hear that again, say Repeat, or if you're done, feel free to hang up For anything else, say Main Menu</audio>\"\n            ]\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: repeat\n          displayName: repeat\n        - id: mainmenu\n          displayName: mainmenu\n        - id: operator\n          displayName: operator\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/st1045_ni1_01.wav\\\">To hear those instructions again, say Repeat or press 1 If you're done, just hang up Or if you need help with something else, say Main Menu or press star</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_st1045_InstructionsWrapMenu_DM\n  value: =Text(Global.st1045_InstructionsWrapMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_st1045_InstructionsWrapMenu_DM = \"repeat\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: st1045_InstructionsWrapMenu_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_st1045_InstructionsWrapMenu_DM = \"mainmenu\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferDestination\n          value: attPrepaid\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M01_MainMenu2_Dialog\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_st1045_InstructionsWrapMenu_DM = \"operator\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferReason\n          value: AGENT_REQUEST\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M09_MainMenu_Dialog\n```", "timestamp": 1749472277.9910967, "content_hash": "55bea61fd6baa75f49dd27fcf02189a6"}