{"response": "```yaml\n- kind: Question\n  id: TF1610_ChooseOtherLine_DM\n  displayName: TF1610_ChooseOtherLine_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TF1610_nm1_01.wav\\\">Starting with the area code, tell me another number on your account that I can text If none of the other phones are handy, say 'cancel'</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TF1610_nm2_01.wav\\\">Please say or enter another 10-digit phone number from your account It you can't currently receive texts on any of them, say 'cancel' or press 1 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TF1610_nm2_01.wav\\\">Please say or enter another 10-digit phone number from your account It you can't currently receive texts on any of them, say 'cancel' or press 1 </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.TF1610_ChooseOtherLine_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/TF1610_ini_01.wav\\\">Tell me the 10-digit Metro number I should text, or say cancel</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: default\n          displayName: default\n        - id: cancel\n          displayName: cancel\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/TF1610_nm1_01.wav\\\">Starting with the area code, tell me another number on your account that I can text If none of the other phones are handy, say 'cancel'</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/TF1610_nm2_01.wav\\\">Please say or enter another 10-digit phone number from your account It you can't currently receive texts on any of them, say 'cancel' or press 1 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/TF1610_nm2_01.wav\\\">Please say or enter another 10-digit phone number from your account It you can't currently receive texts on any of them, say 'cancel' or press 1 </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_TF1610_ChooseOtherLine_DM\n  value: =Text(Global.TF1610_ChooseOtherLine_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TF1610_ChooseOtherLine_DM = \"default\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.OTPAltPhone\n          value: TF1610_ChooseOtherLine_DM.returnvalue\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TF1615_CheckInOnAccount_JDA_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_TF1610_ChooseOtherLine_DM = \"cancel\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.fromMultilineFallBack\n              value: true\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: TF1114_CheckNoPhoneConfig_JDA_DA\n```", "timestamp": **********.6953514, "content_hash": "94e2a67d7213cbf9fa2966293554f762"}