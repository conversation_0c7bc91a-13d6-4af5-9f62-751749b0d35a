{"response": "```yaml\n- kind: Question\n  id: IH1125_TeachLanguage_DM\n  displayName: IH1125_TeachLanguage_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.IH1125_TeachLanguage_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/IH1125_ini_01.wav\\\">To continue in English, press 9</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: default\n          displayName: default\n        - id: spanish\n          displayName: spanish\n        - id: operator\n          displayName: operator\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/IH1125_ini_01.wav\\\">To continue in English, press 9</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_IH1125_TeachLanguage_DM\n  value: =Text(Global.IH1125_TeachLanguage_DM_reco)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.langAskedAlready\n  value: true\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_IH1125_TeachLanguage_DM = \"default\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.langAskedAlready\n          value: true\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: IH1002_CheckBCSConfigSuccess_JDA_DA\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_IH1125_TeachLanguage_DM = \"spanish\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.TransferTag\n          value: Customer_Support_Spanish\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.langAskedAlready\n          value: true\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.language = \"en-US\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.language\n                  value: es-US\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.language = \"es-US\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.language\n                      value: en-US\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.GlobalVars.aniMatch = true, true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: IH1115_SetLanguage_DB_DA\n\n          elseActions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: IH1118_CheckTFN_JDA_DA\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_IH1125_TeachLanguage_DM = \"operator\", true, false)\n      actions: []\n```", "timestamp": 1749558298.661331, "content_hash": "8a6a8a22f22c1e260d9cb6879deb8d1e"}