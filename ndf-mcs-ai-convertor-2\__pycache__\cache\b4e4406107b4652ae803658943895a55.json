{"response": "```yaml\n- kind: Question\n  id: bp0115_AskPDOF_DM\n  displayName: bp0115_AskPDOF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bp0115_AskPDOF_DM_noinput_1\\\">(custom audio: com.nuance.att.application.audio.bp0115_AskPDOF_DM_noinput_1)</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/bp0115_AskPDOF_DM_noinput_2\\\">(custom audio: com.nuance.att.application.audio.bp0115_AskPDOF_DM_noinput_2)</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.bp0115_AskPDOF_DM_reco\n  prompt:\n    speak:\n      - \"(custom audio: com.nuance.att.application.audio.bp0115_AskPDOF_DM_initial)\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"(custom audio: com.nuance.att.application.audio.bp0115_AskPDOF_DM_noinput_1)\"\n        - \"(custom audio: com.nuance.att.application.audio.bp0115_AskPDOF_DM_noinput_2)\"\n    defaultValueMissingAction: Escalate\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.GetNextQuickRefillPDOF.returnCode = 0, true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.nextPdof\n          value: GetNextQuickRefillPDOF.pdofCounter\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_bp0115_AskPDOF_DM\n  value: =Text(Global.bp0115_AskPDOF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_bp0115_AskPDOF_DM = \"no\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.pdofCounter\n          value: -1\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.isNewPdof\n          value: true\n\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.B04_GetPaymentDevice_02.dvxml\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_bp0115_AskPDOF_DM = \"yes\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.changeCard = true, true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.changeCard\n                      value: false\n\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.pdofCounter\n                      value: Global.nextPdof\n\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.autoRefillChange = true, true, false)\n                  actions:\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/bp0115_out_01.wav\\\">Alright</audio>\"\n\n              elseActions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/bp0115_out_02.wav\\\">Thanks</audio>\"\n\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.B04_GetPaymentDevice_02.dvxml\n```", "timestamp": 1749469926.572932, "content_hash": "b4e4406107b4652ae803658943895a55"}