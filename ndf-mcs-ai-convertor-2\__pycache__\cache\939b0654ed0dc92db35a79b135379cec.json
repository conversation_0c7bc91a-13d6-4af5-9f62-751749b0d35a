{"response": "```yaml\n- kind: Question\n  id: EP1205_ConfirmNewPlan_DM\n  displayName: EP1205_ConfirmNewPlan_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/EP1205_nm1_01.wav\\\">Please say 'yes' or 'no'  You wanted </audio>\"\n      - \"{Global.plan}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/EP1205_nm1_03.wav\\\">Is that right? </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/EP1205_nm2_01.wav\\\">Please say 'yes' or press 1, or say 'no' or press 2  You wanted </audio>\"\n      - \"{Global.plan}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/EP1205_nm2_03.wav\\\">Did I get that right? </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/EP1205_nm2_01.wav\\\">Please say 'yes' or press 1, or say 'no' or press 2  You wanted </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/EP1205_nm3_01.wav\\\">If you wanted </audio>\"\n      - \"{Global.plan}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/EP1205_nm3_03.wav\\\">say 'yes' or press 1  If not, say 'no' or press 2 </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.EP1205_ConfirmNewPlan_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/EP1205_ini_01.wav\\\">Just to be sure, that's </audio>{Global.plan}<audio src=\\\"AUDIO_LOCATION/EP1205_ini_02.wav\\\">Is that right? </audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/EP1205_nm1_01.wav\\\">Please say 'yes' or 'no'  You wanted </audio>{Global.plan}<audio src=\\\"AUDIO_LOCATION/EP1205_nm1_03.wav\\\">Is that right? </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/EP1205_nm2_01.wav\\\">Please say 'yes' or press 1, or say 'no' or press 2  You wanted </audio>{Global.plan}<audio src=\\\"AUDIO_LOCATION/EP1205_nm2_03.wav\\\">Did I get that right? </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/EP1205_nm2_01.wav\\\">Please say 'yes' or press 1, or say 'no' or press 2  You wanted </audio>{Global.plan}<audio src=\\\"AUDIO_LOCATION/EP1205_nm2_03.wav\\\">Did I get that right? </audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.plan\n  value: GlobalVars.selectedPlan\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.audioMessageKey\n  value: care_configurable_rate_plan_downgrade_audio\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_EP1205_ConfirmNewPlan_DM\n  value: =Text(Global.EP1205_ConfirmNewPlan_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_EP1205_ConfirmNewPlan_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/EP1205_out_01.wav\\\">Great! </audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.newRatePlan\n          value: plan\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.newPlanSOC\n          value: plan\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: EP1225_DetectConflictingOffers_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_EP1205_ConfirmNewPlan_DM = \"false\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.noToConfirmPlan < 2, true, false)\n                  actions:\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/EP1205_out_02.wav\\\">Sorry about that </audio>\"\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.noToConfirmPlan\n                      value: noToConfirmPlan + 1\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: EP1110_ChooseNewPlanShort_DM\n\n              elseActions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/EP1205_out_03.wav\\\">I'm sorry for this trouble </audio>\"\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: EP1020_CallTransfer_SD\n```", "timestamp": 1749528095.1723804, "content_hash": "939b0654ed0dc92db35a79b135379cec"}