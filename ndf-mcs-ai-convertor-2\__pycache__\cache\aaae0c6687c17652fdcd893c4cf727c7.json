{"response": "```yaml\n- kind: Question\n  id: aa1050_PinSelectDTMF_DM\n  displayName: aa1050_PinSelectDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - aa1050_PinSelectDTMF_DM_initial\n      - aa1050_PinSelectDTMF_DM_initial\n\n  alwaysPrompt: true\n  variable: Global.aa1050_PinSelectDTMF_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.validationCriteriaVariables.validationSuccessful <> true && Global.childCareBalance.pinTechnique <> \"PS5\",\n            \"You will need to select a PIN If you would like to do that now, press 1 To select the PIN on another call, please press 2\",\n            Global.validationCriteriaVariables.validationSuccessful <> true && Global.childCareBalance.pinTechnique = \"PS5\",\n            \"You will need to select a PIN If you would like to do that now, press 1 To do it later, press 2\",\n            Global.validationCriteriaVariables.validationSuccessful = true,\n            \"You will need to select a PIN If you would like to do that now, press 1 To do it later, press 2\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: change_pin\n          displayName: change_pin\n        - id: later\n          displayName: later\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - aa1050_PinSelectDTMF_DM_initial\n        - aa1050_PinSelectDTMF_DM_initial\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa1050_PinSelectDTMF_DM\n  value: =Text(Global.aa1050_PinSelectDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa1050_PinSelectDTMF_DM = \"change_pin\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.currentTask\n          value: SetPin\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.validationCriteriaVariables.validationSuccessful = true, true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.globalVariables.nextStep\n                  value: select_pin\n\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/aa1050_out_01.wav\\\">Okay</audio>\"\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: return\n\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.nextStep\n              value: validate_criteria\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa1050_PinSelectDTMF_DM = \"later\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.dnisInfo.callType = \"WICC\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.globalVariables.transferAllowed\n                  value: false\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: aa9805_ProcessTransfer_PP\n\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.nextStep\n              value: balance_inquiry\n\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/aa1050_out_01.wav\\\">Okay</audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: return\n```", "timestamp": 1749458496.4894567, "content_hash": "aaae0c6687c17652fdcd893c4cf727c7"}