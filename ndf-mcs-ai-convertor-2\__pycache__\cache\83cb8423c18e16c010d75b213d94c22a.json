{"response": "```yaml\n- kind: Question\n  id: aa5032_PinSetSuccessWrapUp_DM\n  displayName: aa5032_PinSetSuccessWrapUp_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"\"\n\n  alwaysPrompt: true\n  variable: Global.aa5032_PinSetSuccessWrapUp_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa5032_ini_01.wav\\\">If you would like to hear your balance now, press 1If you're done here, just hang upOr for more options, press 2</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: lookup_balance\n          displayName: lookup_balance\n        - id: more_options\n          displayName: more_options\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa5032_PinSetSuccessWrapUp_DM\n  value: =Text(Global.aa5032_PinSetSuccessWrapUp_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa5032_PinSetSuccessWrapUp_DM = \"lookup_balance\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.nextStep\n          value: lookup_balance\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: return\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa5032_PinSetSuccessWrapUp_DM = \"more_options\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: handleMainMenu_CS\n```", "timestamp": 1749543737.4714456, "content_hash": "83cb8423c18e16c010d75b213d94c22a"}