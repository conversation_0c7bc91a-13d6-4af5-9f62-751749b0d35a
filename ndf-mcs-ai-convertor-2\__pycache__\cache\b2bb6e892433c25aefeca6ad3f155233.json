{"response": "```yaml\n- kind: Question\n  id: aa4161_ReportFraudWrapUpDTMF_DM\n  displayName: aa4161_ReportFraudWrapUpDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"\"\n\n  alwaysPrompt: true\n  variable: Global.aa4161_ReportFraudWrapUpDTMF_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa4161_ini_01.wav\\\">To hear that again, Press 1If you're experiencing a problem with your card, Press 2For other options, Press 3Or if you're done here, hang up</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: repeat\n          displayName: repeat\n\n        - id: card_problem\n          displayName: card_problem\n\n        - id: other_options\n          displayName: other_options\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa4161_ReportFraudWrapUpDTMF_DM\n  value: =Text(Global.aa4161_ReportFraudWrapUpDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa4161_ReportFraudWrapUpDTMF_DM = \"repeat\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa4160_ReportFraudPlayout_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa4161_ReportFraudWrapUpDTMF_DM = \"card_problem\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.transferAllowed\n              value: true\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.transferReason\n              value: cardProblem\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa9805_ProcessTransfer_PP\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_aa4161_ReportFraudWrapUpDTMF_DM = \"other_options\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: aa4105_OtherOptionsMenuDTMF_DM\n```", "timestamp": 1749543998.7671223, "content_hash": "b2bb6e892433c25aefeca6ad3f155233"}