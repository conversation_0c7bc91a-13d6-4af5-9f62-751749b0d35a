{"response": "```yaml\n- kind: Question\n  id: sa0105_ActivateMenu_DM\n  displayName: sa0105_ActivateMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/sa0105_ni1_01.wav\\\">You can say switch device or press 1, add a line or press 2, open a new account or press 3, port number or press 4, replace sim or press 5 Or, reactivate or press 6</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/sa0105_ni2_01.wav\\\">To switch a device, press 1 To add a line, press 2 To open a new account, press 3 To port a number, press 4 To replace a sim, press 5 Or to reactivate a suspended account, press 6</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.sa0105_ActivateMenu_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/sa0105_ini_01.wav\\\">Which of the following do you need help with Say switch device, add a line , open a new account, port number, replace sim, or to reactivate an inactive or suspended account say reactivate</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: add_line\n          displayName: add_line\n        - id: open_account\n          displayName: open_account\n        - id: port_number\n          displayName: port_number\n        - id: reactivate\n          displayName: reactivate\n        - id: replace_sim\n          displayName: replace_sim\n        - id: switch_device\n          displayName: switch_device\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/sa0105_ni1_01.wav\\\">You can say switch device or press 1, add a line or press 2, open a new account or press 3, port number or press 4, replace sim or press 5 Or, reactivate or press 6</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/sa0105_ni2_01.wav\\\">To switch a device, press 1 To add a line, press 2 To open a new account, press 3 To port a number, press 4 To replace a sim, press 5 Or to reactivate a suspended account, press 6</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_sa0105_ActivateMenu_DM\n  value: =Text(Global.sa0105_ActivateMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_sa0105_ActivateMenu_DM = \"add_line\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.nextCallerIntent\n          value: add_line\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M01_MainMenu2_Dialog\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_sa0105_ActivateMenu_DM = \"open_account\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.callType\n              value: activations\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.comingFromActivationSD\n              value: true\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.M01_MainMenu2_Dialog\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_sa0105_ActivateMenu_DM = \"port_number\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.nextCallerIntent\n                  value: port_in\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.byPassPinCollection\n                  value: true\n                - kind: BeginDialog\n                  id: begin_REPLACE_THIS\n                  dialog: topic.M09_MainMenu_Dialog\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_sa0105_ActivateMenu_DM = \"reactivate\", true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: sa0115_ReactivateMenu_DM\n\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.Var_sa0105_ActivateMenu_DM = \"replace_sim\", true, false)\n                      actions:\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: sa0205_ConfirmSim_DM\n\n                  elseActions:\n                    - kind: ConditionGroup\n                      id: conditionGroup_REPLACE_THIS\n                      conditions:\n                        - id: conditionItem_REPLACE_THIS\n                          condition: =If(Global.Var_sa0105_ActivateMenu_DM = \"switch_device\", true, false)\n                          actions:\n                            - kind: GotoAction\n                              id: goto_REPLACE_THIS\n                              actionId: sa0110_CallSwitchDevice_SD\n```", "timestamp": 1749472284.6225057, "content_hash": "84d17d1af6a809a908fce42f54978100"}