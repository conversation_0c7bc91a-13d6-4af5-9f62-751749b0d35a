{"response": "```yaml\n- kind: Question\n  id: aa2588a_BBCollectPinDTMF_DM\n  displayName: aa2588a_BBCollectPinDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa2588a_nm1_01.wav\\\">Sorry, let's try that again  Please enter your 4 digit pin now</audio>\"\n      - |\n        {Switch(\n              true,\n              Global.dnisInfo.dtmfOnlyFlag = true,\n              [\n                \"<audio src=\\\"AUDIO_LOCATION/aa2588a_nm2_01.wav\\\">Sorry, I still didn't get that</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/aa2588a_nm2_02.wav\\\">Please enter your pin</audio>\"\n              ]\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.aa2588a_BBCollectPinDTMF_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa2588a_ini_01.wav\\\">Please enter your 4-digit PIN</audio>\"\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/aa2588a_ni1_01.wav\\\">Sorry, let's try that again  Please enter your 4 digit pin now</audio>\"\n        - |\n          {Switch(\n                true,\n                Global.dnisInfo.dtmfOnlyFlag = true,\n                [\n                  \"<audio src=\\\"AUDIO_LOCATION/aa2588a_ni2_02.wav\\\">Sorry, I still didn't get that</audio>\",\n                  \"<audio src=\\\"AUDIO_LOCATION/aa2588a_ni2_03.wav\\\">Please enter your pin</audio>\"\n                ],\n                true,\n                \"<audio src=\\\"AUDIO_LOCATION/aa2588a_ni2_04.wav\\\">Sorry, I still didn't get anything Let s try it another way</audio>\"\n            )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.validationCriteriaVariables.collectPin\n  value: aa2588a_BBCollectPinDTMF_DM.returnvalue\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: aa2589_BBValidatePin_DB_DA\n```", "timestamp": 1749544331.608747, "content_hash": "cc6460653feb855cb0b34fbcfcb75ce4"}