{"response": "```yaml\n- kind: Question\n  id: aa2591_AltAuthValWrapUpDTMF_DM\n  displayName: aa2591_AltAuthValWrapUpDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"\"\n\n  alwaysPrompt: true\n  variable: Global.aa2591_AltAuthValWrapUpDTMF_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa2591_ini_01.wav\\\">To hear that again, press 1 If you are done, feel free to hang up</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa2591_AltAuthValWrapUpDTMF_DM\n  value: =Text(Global.aa2591_AltAuthValWrapUpDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa2591_AltAuthValWrapUpDTMF_DM = \"repeat\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aa2591_out_01.wav\\\">Again</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa2584_CardReported_PP\n```", "timestamp": 1749544362.3354514, "content_hash": "f844d0965e5e6e0103636c51cb5d1613"}