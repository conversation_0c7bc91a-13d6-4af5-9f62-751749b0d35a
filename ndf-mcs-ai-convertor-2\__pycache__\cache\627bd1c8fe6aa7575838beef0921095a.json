{"response": "```yaml\n- kind: Question\n  id: EP1310_PlayDetailsAgainYN_DM\n  displayName: EP1310_PlayDetailsAgainYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/EP1310_nm1_01.wav\\\">Do you want to hear the details for your new plan again? </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/EP1310_nm2_01.wav\\\">If you want to hear the details of your new plan once more, say yes or press 1 If you d just like to move on, say no or press 2 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/EP1310_nm2_01.wav\\\">If you want to hear the details of your new plan once more, say yes or press 1 If you d just like to move on, say no or press 2 </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.EP1310_PlayDetailsAgainYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/EP1310_ini_03.wav\\\">All right, here are the details for your plan </audio>{Global.plan}<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio><audio src=\\\"AUDIO_LOCATION/EP1310_ini_08.wav\\\">Would you like to hear those details again? </audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/EP1310_nm1_01.wav\\\">Do you want to hear the details for your new plan again? </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/EP1310_nm2_01.wav\\\">If you want to hear the details of your new plan once more, say yes or press 1 If you d just like to move on, say no or press 2 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/EP1310_nm2_01.wav\\\">If you want to hear the details of your new plan once more, say yes or press 1 If you d just like to move on, say no or press 2 </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_EP1310_PlayDetailsAgainYN_DM\n  value: =Text(Global.EP1310_PlayDetailsAgainYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_EP1310_PlayDetailsAgainYN_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.playDetailsEnteringFrom\n          value: EP1310\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: EP1310_PlayDetailsAgainYN_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_EP1310_PlayDetailsAgainYN_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/EP1310_out_01.wav\\\">Okay </audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n```", "timestamp": 1749528029.7331553, "content_hash": "627bd1c8fe6aa7575838beef0921095a"}