{"response": "```yaml\n- kind: Question\n  id: AI1010_NewNumberWait_DM\n  displayName: AI1010_NewNumberWait_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.AI1010_NewNumberWait_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/AI1010_ini_02.wav\\\">No problem  I'll wait  Once you re ready to write down your account information, say continue or press 1  When you re ready, say continue, or press one  At any time, you can say Continue, or press one  If you re ready to hear your account information, say Continue, or press one   If you re ready,  say continue, or press one  You can say Continue, or press one when you re ready  Hmmm, I seem to be having some trouble Let s move on </audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: ready\n          displayName: ready\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.callType\n  value: GlobalVars.callType\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_AI1010_NewNumberWait_DM\n  value: =Text(Global.AI1010_NewNumberWait_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AI1010_NewNumberWait_DM = \"ready\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/AI1010_out_01.wav\\\">Let s continue</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: AI1015_PhoneNumber_PP\n```\n**Notes on the conversion:**\n- Only the `<success>` tag with label \"ready\" is mapped to the ClosedListEntity and ConditionGroup.\n- The `<audio>` under `<action label=\"ready\">` is mapped to a `SendActivity` before the `GotoAction` as per example 7.\n- No `<nomatchprompts>` or `<noinputprompts>` are present, so `unrecognizedPrompt` and `inputTimeoutResponse` are empty.\n- The `entity` is a ClosedListEntity with only \"ready\" as the option.\n- The `SetVariable` for `callType` is included as per the `<session-mapping>`.\n- The `SetVariable` for `Var_AI1010_NewNumberWait_DM` is included as per the pattern for multi-label states.\n- The `ConditionGroup` checks for \"ready\" and then plays the audio and goes to the next state.\n- The `<command>` and `<catch>` tags are ignored as per instructions.", "timestamp": 1749527341.8531766, "content_hash": "e3a1a2c84f92d04cd731fcda3f05456a"}