{"response": "```yaml\n- kind: Question\n  id: SH1312_PayNowYN_DM\n  displayName: SH1312_PayNowYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - |\n        {Switch(\n              true,\n              Global.extensionEntryPoint = \"payment_amount\",\n              \"<audio src=\\\"AUDIO_LOCATION/SH1312_ini_01.wav\\\">Would you like to continue with your payment?</audio>\",\n              true,\n              \"<audio src=\\\"AUDIO_LOCATION/SH1312_ini_02.wav\\\">Would you like to make a payment now?</audio>\"\n          )\n        }\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SH1312_nm2_01.wav\\\">Would you like to make a payment now? Say 'yes' or press 1, or say 'no' or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SH1312_nm2_01.wav\\\">Would you like to make a payment now? Say 'yes' or press 1, or say 'no' or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.SH1312_PayNowYN_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.extensionEntryPoint = \"payment_amount\",\n              \"<audio src=\\\"AUDIO_LOCATION/SH1312_ini_01.wav\\\">Would you like to continue with your payment?</audio>\",\n              true,\n              \"<audio src=\\\"AUDIO_LOCATION/SH1312_ini_02.wav\\\">Would you like to make a payment now?</audio>\"\n          )\n        }\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.extensionEntryPoint = \"payment_amount\",\n                \"<audio src=\\\"AUDIO_LOCATION/SH1312_ini_01.wav\\\">Would you like to continue with your payment?</audio>\",\n                true,\n                \"<audio src=\\\"AUDIO_LOCATION/SH1312_ini_02.wav\\\">Would you like to make a payment now?</audio>\"\n            )\n          }\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SH1312_nm2_01.wav\\\">Would you like to make a payment now? Say 'yes' or press 1, or say 'no' or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SH1312_nm2_01.wav\\\">Would you like to make a payment now? Say 'yes' or press 1, or say 'no' or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_SH1312_PayNowYN_DM\n  value: =Text(Global.SH1312_PayNowYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_SH1312_PayNowYN_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/SH1312_out_01.wav\\\">Great</audio>\"\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: make_pmt\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.acceptPayByPhone\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.paymentsEntryPoint\n          value: careSuspended\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: SH1313_MakePayment_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_SH1312_PayNowYN_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/SH1312_out_02.wav\\\">No problem</audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: SH1315_PaymentMethods_PP\n```", "timestamp": 1749529744.0296366, "content_hash": "c4e0ea6b848cf466c516b5d14084de24"}