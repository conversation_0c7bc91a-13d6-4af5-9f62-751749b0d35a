{"response": "```yaml\n- kind: Question\n  id: bm0725_AskPaymentAmount_DM\n  displayName: bm0725_AskPaymentAmount_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/com.nuance.att.application.audio.bm0725_AskPaymentAmount_DM_noinput_1\\\"> </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/com.nuance.att.application.audio.bm0725_AskPaymentAmount_DM_noinput_2\\\"> </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.bm0725_AskPaymentAmount_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/com.nuance.att.application.audio.bm0725_AskPaymentAmount_initial\\\"> </audio>\"\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/com.nuance.att.application.audio.bm0725_AskPaymentAmount_DM_noinput_1\\\"> </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/com.nuance.att.application.audio.bm0725_AskPaymentAmount_DM_noinput_2\\\"> </audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.refillAmount\n  value: bm0725_AskPaymentAmount_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.chargeAmount\n  value: bm0725_AskPaymentAmount_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.amount\n  value: bm0725_AskPaymentAmount_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.dataPPURefill\n  value: false\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: bm0730_IsValidAmount_JDA\n```", "timestamp": 1749469932.0817883, "content_hash": "c10f8a4e53f81041fb9059dce17a7adb"}