{"response": "```yaml\n- kind: Question\n  id: MW1070_ChooseFromCardList_DM\n  displayName: MW1070_ChooseFromCardList_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1070_nm1_04.wav\\\">Here's the list again </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1070_nm1_02.wav\\\">When you hear the one you d like to use as your primary card just say or enter the last 4 digits  Or say Add new card</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1070_nm1_05.wav\\\">Please Say or enter the last four digits for the card you'd like to remove</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1070_nm1_03.wav\\\">Please Ssay or enter the last four digits for the card you d like to remove</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1070_nm1_04.wav\\\">Here's the list again </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1070_nm1_02.wav\\\">When you hear the one you d like to use as your primary card just say or enter the last 4 digits  Or say Add new card</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1070_nm1_05.wav\\\">Please Say or enter the last four digits for the card you'd like to remove</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1070_nm1_03.wav\\\">Please Ssay or enter the last four digits for the card you d like to remove</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.MW1070_ChooseFromCardList_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.manageCardTask = \"removeCard\",\n            \"Here s what you have saved  When you hear the card you d like to remove just tell me the last 4 digits\",\n            Global.manageCardTask = \"updateExp\",\n            \"Here s what you have saved   When you hear the card you d like to update just tell me the last 4 digits\",\n            Global.manageCardTask = \"changePrimaryCard\",\n            \"Here s what you have saved  When you hear the card you d like to use as your primary just tell me the last 4 digits  Or say Add new card\",\n            !(Global.manageCardTask = \"changePrimaryCard\" || Global.manageCardTask = \"updateExp\" || Global.manageCardTask = \"removeCard\"),\n            \"Here are the cards you have saved When you hear the one you d like to use as your primary payment card just tell me the last 4 digits  Or say Add a new card\",\n            Global.manageCardTask = \"changePrimaryCard\" || Global.tag = \"setup-autopay\",\n            \"Just say or enter the four digits for the card you want to use today  You can also say   repeat  or  add a new card \",\n            !(Global.manageCardTask = \"changePrimaryCard\" || Global.tag = \"setup-autopay\"),\n            \"Just say or enter the four digits for the card you re calling about  You can also say  repeat \",\n            \"Please choose a card.\"\n        )}\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1070_nm1_04.wav\\\">Here's the list again </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1070_nm1_02.wav\\\">When you hear the one you d like to use as your primary card just say or enter the last 4 digits  Or say Add new card</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1070_nm1_05.wav\\\">Please Say or enter the last four digits for the card you'd like to remove</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1070_nm1_03.wav\\\">Please Ssay or enter the last four digits for the card you d like to remove</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1070_nm1_04.wav\\\">Here's the list again </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1070_nm1_02.wav\\\">When you hear the one you d like to use as your primary card just say or enter the last 4 digits  Or say Add new card</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1070_nm1_05.wav\\\">Please Say or enter the last four digits for the card you'd like to remove</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1070_nm1_03.wav\\\">Please Ssay or enter the last four digits for the card you d like to remove</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.manageCardTask\n  value: GlobalVars.manageCardTask\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.paymentOptions\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.isAutopayCard\n  value: false\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.tag\n  value: GlobalVars.tag\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MW1070_ChooseFromCardList_DM = \"add-new_card\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/MW1070_out_02.wav\\\">Ok, let s add a new card</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: MW1200_AskDebitOrCredit_DM\n\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.GlobalVars.paymentOptionId\n      value: MW1070_ChooseFromCardList_DM.returnvalue\n\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.PaymentTable.ACTIVATION_STATUS\n      value: 106\n\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.manageCardTask = \"removeCard\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.isAutopayCard = true, true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: MW1066_RemoveAutopayCardMoreCards_DM\n              elseActions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: MW1601_DeleteSPMItem_DB_DA\n\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.manageCardTask = \"updateExp\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: MW1300_GetExpirationDate_DM\n\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.manageCardTask = \"changePrimaryCard\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.GlobalVars.isAutopayEnrolled = true, true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.autopay\n                      value: true\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: MW1600_UpdateSPMItem_DB_DA\n              elseActions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.defaultPaymentMethod\n                  value: true\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: MW1600_UpdateSPMItem_DB_DA\n\n      elseActions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/MW1070_out_01.wav\\\">Thanks</audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.autopay\n          value: true\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: MW1600_UpdateSPMItem_DB_DA\n```", "timestamp": 1749556997.1374793, "content_hash": "8b15635829db81f8b4381407dd3a1a8a"}