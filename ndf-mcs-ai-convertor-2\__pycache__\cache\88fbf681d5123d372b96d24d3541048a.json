{"response": "```yaml\n- kind: Question\n  id: aa1053_ExpiredCardWrapUpDTMF_DM\n  displayName: aa1053_ExpiredCardWrapUpDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - aa1053_ExpiredCardWrapUpDTMF_DM_initial\n      - aa1053_ExpiredCardWrapUpDTMF_DM_initial\n\n  alwaysPrompt: true\n  variable: Global.aa1053_ExpiredCardWrapUpDTMF_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa1053_ini_01.wav\\\">This card is expired and cannot be used</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/aa1053_ini_02.wav\\\">To hear that again, press 1 To speak with a Customer Service Representative, press 2 If you are done, feel free to hang up</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - aa1053_ExpiredCardWrapUpDTMF_DM_initial\n        - aa1053_ExpiredCardWrapUpDTMF_DM_initial\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa1053_ExpiredCardWrapUpDTMF_DM\n  value: =Text(Global.aa1053_ExpiredCardWrapUpDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa1053_ExpiredCardWrapUpDTMF_DM = \"repeat\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aa1053_out_01.wav\\\">Again</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa1053_ExpiredCardWrapUpDTMF_DM\n```", "timestamp": 1749458525.3823516, "content_hash": "88fbf681d5123d372b96d24d3541048a"}