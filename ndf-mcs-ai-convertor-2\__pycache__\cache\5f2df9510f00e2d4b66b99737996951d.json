{"response": "```yaml\n- kind: Question\n  id: bc0210_ConfirmPaymentDetails_DM\n  displayName: bc0210_ConfirmPaymentDetails_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bc0210_ConfirmPaymentDetails_DM_noinput_1.wav\\\">[custom audio: com.nuance.att.application.audio.bc0210_ConfirmPaymentDetails_DM_noinput_1]</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/bc0210_ConfirmPaymentDetails_DM_noinput_2.wav\\\">[custom audio: com.nuance.att.application.audio.bc0210_ConfirmPaymentDetails_DM_noinput_2]</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.bc0210_ConfirmPaymentDetails_DM_reco\n  prompt:\n    speak:\n      - \"[custom audio: com.nuance.att.application.audio.bc0210_ConfirmPaymentDetails_initial]\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/bc0210_ConfirmPaymentDetails_DM_noinput_1.wav\\\">[custom audio: com.nuance.att.application.audio.bc0210_ConfirmPaymentDetails_DM_noinput_1]</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/bc0210_ConfirmPaymentDetails_DM_noinput_2.wav\\\">[custom audio: com.nuance.att.application.audio.bc0210_ConfirmPaymentDetails_DM_noinput_2]</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.returnCode\n  value: GetPDOFEmailAddress.returnCode\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.returnCode = 0, true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.pdofEmailAddress\n          value: GetPDOFEmailAddress.pdofEmailAddress\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.pdofID\n          value: GetPDOFEmailAddress.pdofID\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_bc0210_ConfirmPaymentDetails_DM\n  value: =Text(Global.bc0210_ConfirmPaymentDetails_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_bc0210_ConfirmPaymentDetails_DM = \"cancel\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.intent = \"refillPlusAutoPay\", true, false)\n              actions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/bc0210_out_01.wav\\\">Okay I will cancel your payment and your Auto Pay enrollment</audio>\"\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: getReturnLink()\n          elseActions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/bc0210_out_02.wav\\\">Okay Ive canceled your payment</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_bc0210_ConfirmPaymentDetails_DM = \"continue\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.intent = \"refillPlusAutoPay\", true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: bc0220_TermsAndConditions_DM\n              elseActions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: bc0215_SavePaymentDevice_JDA_DA\n```", "timestamp": 1749470116.2060134, "content_hash": "5f2df9510f00e2d4b66b99737996951d"}