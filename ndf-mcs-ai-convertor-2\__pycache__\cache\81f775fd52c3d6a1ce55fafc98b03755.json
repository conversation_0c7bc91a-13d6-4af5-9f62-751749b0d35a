{"response": "```yaml\n- kind: Question\n  id: aa1040_GetCardDTMF_DM\n  displayName: aa1040_GetCardDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.stateDeliversByMail = true && Global.cardLength = 19,\n            \"For security purposes we do need to validate all callers as card holders So please enter your 19 digit card number If you just need a moment to find the card, press 1 Or if your card is missing, lost, stolen, or damaged, press 2 And at any time press * to start over\",\n            Global.stateDeliversByMail = true && Global.cardLength <> 19,\n            \"For security purposes we do need to validate all callers as card holders So please enter your 16 digit card number If you just need a moment to find the card, press 1 Or if your card is missing, lost, stolen, or damaged, press 2 And at any time press * to start over\",\n            Global.stateDeliversByMail <> true && Global.cardLength = 19,\n            \"For security purposes we do need to validate all callers as card holders So please enter your 19 digit card number If you just need a moment to find the card, press 1 Or if your card has been lost, stolen, or damaged, press 2 And at any time press * to start over\",\n            Global.stateDeliversByMail <> true && Global.cardLength <> 19,\n            \"For security purposes we do need to validate all callers as card holders So please enter your 16 digit card number If you just need a moment to find the card, press 1 Or if your card has been lost, stolen, or damaged, press 2 And at any time press * to start over\",\n            Global.altAuthValFlag = true,\n            \"…Or to access your account another way say access my account or press 3\",\n            true,\n            \"… And at any time press * to start over\"\n        )\n        }\n      - |\n        {Switch(\n            true,\n            Global.dtmfOnlyFlag = true,\n            \"Sorry, I didn t get that\",\n            true,\n            \"\"\n        )\n        }\n\n  alwaysPrompt: true\n  variable: Global.aa1040_GetCardDTMF_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.cardCollectionPromptDtmfOn = true && Global.cardCollectionPromptDtmf <> null && Global.cardCollectionPromptDtmf <> \"\",\n            \"{Global.cardCollectionPromptDtmf}\",\n            Global.cardCollectionPromptDtmfOn = true && Global.cardLength = 19,\n            [\n                \"Please enter your 19 digit\",\n                \"{Global.terminologyEbtCard}\",\n                \"card number Or if you need a moment to find it, press 1 If you make a mistake press * to start over\"\n            ],\n            Global.cardCollectionPromptDtmfOn = true,\n            [\n                \"Please enter your 16 digit\",\n                \"{Global.terminologyEbtCard}\",\n                \"card number Or if you need a moment to find it, press 1 If you make a mistake press * to start over\"\n            ],\n            Global.cardReplacementFlag = true,\n            [\n                \"silence_500ms\",\n                \"If your card is missing, lost, stolen, or damaged, press 2\"\n            ],\n            Global.altAuthValFlag = true,\n            \"To access your account another way, press 3\"\n        )\n        }\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: access_account\n          displayName: access_account\n        - id: hold_on\n          displayName: hold_on\n        - id: missing_card\n          displayName: missing_card\n        - id: start_over\n          displayName: start_over\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n              true,\n              Global.stateDeliversByMail = true && Global.cardLength = 19,\n              \"For security purposes we do need to validate all callers as card holders So please enter your 19 digit card number If you just need a moment to find the card, press 1 Or if your card is missing, lost, stolen, or damaged, press 2\",\n              Global.stateDeliversByMail = true && Global.cardLength <> 19,\n              \"For security purposes we do need to validate all callers as card holders So please enter your 16 digit card number If you just need a moment to find the card, press 1 Or if your card has been lost, stolen, or damaged, press 2\",\n              Global.stateDeliversByMail <> true && Global.cardLength = 19,\n              \"For security purposes we do need to validate all callers as card holders So please enter your 19 digit card number If you just need a moment to find the card, press 1 Or if your card has been lost, stolen, or damaged, press 2\",\n              Global.stateDeliversByMail <> true && Global.cardLength <> 19,\n              \"For security purposes we do need to validate all callers as card holders So please enter your 16 digit card number If you just need a moment to find the card, press 1 Or if your card is missing, lost, stolen, or damaged, press 2\",\n              Global.altAuthValFlag = true,\n              \"To access your account another way, press 3\",\n              true,\n              \"… And at any time press * to start over\"\n          )\n          }\n        - |\n          {Switch(\n              true,\n              Global.dtmfOnlyFlag = true,\n              \"Sorry, I didn t get that\",\n              true,\n              \"\"\n          )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa1040_GetCardDTMF_DM\n  value: =Text(Global.aa1040_GetCardDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa1040_GetCardDTMF_DM = \"access_account\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa1057_AltAuthenticationNeedAdditionalInfo_PP\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa1040_GetCardDTMF_DM = \"hold_on\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa1099_HoldOn_SD\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa1040_GetCardDTMF_DM = \"missing_card\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.cardReplacementFlag = true, true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.globalVariables.currentTask\n                  value: cardReplacement\n\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.collectLostStolenCardNumber = true, true, false)\n                      actions:\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: aa1060_CollectLostStolenCardNumberDTMF_DM\n\n                  elseActions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: aa1065_LostStolenNeedAdditionalInfo_PP\n\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.callType = \"TX\", true, false)\n                      actions:\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: aa1055_ConfirmCardMissingDTMF_DM\n\n                  elseActions:\n                    - kind: ConditionGroup\n                      id: conditionGroup_REPLACE_THIS\n                      conditions:\n                        - id: conditionItem_REPLACE_THIS\n                          condition: =If(Global.callType = \"WV\", true, false)\n                          actions:\n                            - kind: GotoAction\n                              id: goto_REPLACE_THIS\n                              actionId: aa1055_ConfirmCardMissingDTMF_DM\n\n                      elseActions:\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: aa1055_ConfirmCardMissingDTMF_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa1040_GetCardDTMF_DM = \"start_over\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa1040_GetCardDTMF_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa1040_GetCardDTMF_DM = \"default\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.cardInfoVariables.collectedCardNumber\n          value: Global.aa1040_GetCardDTMF_DM.returnvalue\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.dakotasFlag = true, true, false)\n              actions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.cardNumber7thDigitPosition = \"5\", true, false)\n                      actions:\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.globalVariables.whichDakota\n                          value: ND\n\n                  elseActions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.globalVariables.whichDakota\n                      value: SD\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.callType = \"WICC\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: aa1044_CCBalanceInquiry_DB_DA\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa1043_BalanceInquiry_DB_DA\n```\n**Note:**  \n- All ids like `setVariable_REPLACE_THIS`, `conditionGroup_REPLACE_THIS`, `conditionItem_REPLACE_THIS`, and `goto_REPLACE_THIS` are placeholders as per your instruction.  \n- All variable references are prefixed with `Global.` as per your rules.  \n- All prompt and audio references are converted to PowerFx/Switch format as per your memorized YAML and instructions.  \n- The entity list is built from the action labels in `<success>`.  \n- The nested conditions and session-mapping are converted to SetVariable and ConditionGroup as per your rules.  \n- If you need the audio file references in `<audio src=\"AUDIO_LOCATION/...\">` format, let me know and I can adjust accordingly.", "timestamp": 1749458407.337387, "content_hash": "81f775fd52c3d6a1ce55fafc98b03755"}