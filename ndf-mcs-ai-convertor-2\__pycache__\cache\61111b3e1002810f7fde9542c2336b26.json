{"response": "```yaml\n- kind: Question\n  id: TT1026_PowerCycleTipBrokenDevice_DM\n  displayName: TT1026_PowerCycleTipBrokenDevice_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/TT1026_nm1_01.wav\\\">Sorry, please say 'repeat' or 'continue'</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.TT1026_PowerCycleTipBrokenDevice_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/TT1026_ini_01.wav\\\">After you end this call, turn your device off and back on again using the power button on the top or side of the device When the device is back on, check if your problem's been fixed</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TT1026_ini_02.wav\\\">You can say 'repeat that' When you're ready for the next tip, say 'continue'</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: repeat\n          displayName: repeat\n        - id: continue\n          displayName: continue\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/TT1026_ni1_01.wav\\\">Please say 'repeat' or 'continue'</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_TT1026_PowerCycleTipBrokenDevice_DM\n  value: =Text(Global.TT1026_PowerCycleTipBrokenDevice_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TT1026_PowerCycleTipBrokenDevice_DM = \"repeat\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TT1026_PowerCycleTipBrokenDevice_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_TT1026_PowerCycleTipBrokenDevice_DM = \"continue\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: TT1030_WiFiTipOfferYN_DM\n```", "timestamp": 1749530364.725061, "content_hash": "61111b3e1002810f7fde9542c2336b26"}