{"response": "```yaml\n- kind: Question\n  id: MM1035_MyAccount_DM\n  displayName: MM1035_MyAccount_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - |\n        {Switch(\n              true,\n              Global.hasHighSpeedData = \"FromRatePlan\" || Global.hasHighSpeedData = \"FromFeature\",\n              \"Please say 'My plan', 'My features', or 'Data usage' You can also say 'My balance,' or  'Reset my voicemail PIN,' 'Change my phone number,'  or 'Add a line' \",\n              true,\n              \"To hear or to change your current set-up, say 'My plan' or 'My features'  You can also say 'My balance,' or  'Reset my voicemail PIN,' 'Change my phone number,'  or 'Add a line'\"\n          )\n        }\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - |\n        {Switch(\n              true,\n              Global.hasHighSpeedData = \"FromRatePlan\" || Global.hasHighSpeedData = \"FromFeature\",\n              \"You can say 'My plan' or press 1; 'My features' or press 2; 'Data Usage', 3; 'My balance,' 4; 'Reset my voicemail PIN' - 5; 'Change my phone number' - 6  Or 'Add a line' - 7 \",\n              true,\n              \"You can say 'My plan' or press 1; 'My features' or press 2; 'My balance,' 3; 'Reset my voicemail PIN' - 4; 'Change my phone number' - 5  Or 'Add a line' - 6 \"\n          )\n        }\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - |\n        {Switch(\n              true,\n              Global.hasHighSpeedData = \"FromRatePlan\" || Global.hasHighSpeedData = \"FromFeature\",\n              \"You can say 'My plan' or press 1; 'My features' or press 2; 'Data Usage', 3; 'My balance,' 4; 'Reset my voicemail PIN' - 5; 'Change my phone number' - 6  Or 'Add a line' - 7 \",\n              true,\n              \"You can say 'My plan' or press 1; 'My features' or press 2; 'My balance,' 3; 'Reset my voicemail PIN' - 4; 'Change my phone number' - 5  Or 'Add a line' - 6 \"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.MM1035_MyAccount_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/MM1035_ini_03.wav\\\">To manage your set-up, say 'my plan' or 'my features' </audio>\"\n      - |\n        {Switch(\n              true,\n              Global.hasHighSpeedData = \"FromRatePlan\" || Global.hasHighSpeedData = \"FromFeature\",\n              \"<audio src=\\\"AUDIO_LOCATION/MM1035_ini_01.wav\\\">For data info and options, say 'data usage' </audio>\",\n              \"\"\n          )\n        }\n      - \"<audio src=\\\"AUDIO_LOCATION/MM1035_ini_02.wav\\\">You can also say 'my balance,' 'reset my voicemail PIN,' 'change my phone number,' or 'add a line' </audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: acct_bal\n          displayName: acct_bal\n        - id: add_line\n          displayName: add_line\n        - id: main_menu\n          displayName: main_menu\n        - id: plan_details\n          displayName: plan_details\n        - id: mdn_change\n          displayName: mdn_change\n        - id: my_features\n          displayName: my_features\n        - id: reset_pin\n          displayName: reset_pin\n        - id: make_pmt\n          displayName: make_pmt\n        - id: data_usage\n          displayName: data_usage\n        - id: tmomoney\n          displayName: tmomoney\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.hasHighSpeedData = \"FromRatePlan\" || Global.hasHighSpeedData = \"FromFeature\",\n                \"Please say 'My plan', 'My features', or 'Data usage' You can also say 'My balance,' or  'Reset my voicemail PIN,' 'Change my phone number,'  or 'Add a line' \",\n                true,\n                \"To hear or to change your current set-up, say 'My plan' or 'My features'  You can also say 'My balance,' or  'Reset my voicemail PIN,' 'Change my phone number,'  or 'Add a line'\"\n            )\n          }\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - |\n          {Switch(\n                true,\n                Global.hasHighSpeedData = \"FromRatePlan\" || Global.hasHighSpeedData = \"FromFeature\",\n                \"You can say 'My plan' or press 1; 'My features' or press 2; 'Data Usage', 3; 'My balance,' 4; 'Reset my voicemail PIN' - 5; 'Change my phone number' - 6  Or 'Add a line' - 7 \",\n                true,\n                \"You can say 'My plan' or press 1; 'My features' or press 2; 'My balance,' 3; 'Reset my voicemail PIN' - 4; 'Change my phone number' - 5  Or 'Add a line' - 6 \"\n            )\n          }\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - |\n          {Switch(\n                true,\n                Global.hasHighSpeedData = \"FromRatePlan\" || Global.hasHighSpeedData = \"FromFeature\",\n                \"You can say 'My plan' or press 1; 'My features' or press 2; 'Data Usage', 3; 'My balance,' 4; 'Reset my voicemail PIN' - 5; 'Change my phone number' - 6  Or 'Add a line' - 7 \",\n                true,\n                \"You can say 'My plan' or press 1; 'My features' or press 2; 'My balance,' 3; 'Reset my voicemail PIN' - 4; 'Change my phone number' - 5  Or 'Add a line' - 6 \"\n            )\n          }\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.callType\n  value: GlobalVars.callType\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.hasHighSpeedData\n  value: GlobalVars.GetAccountDetails.hasHighSpeedData\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.offerDataUsage\n  value: \"(hasHighSpeedData=='No')?false:true\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_grammar1\n  value: \"'MM1035_MyAccount_DM.jsp?stateVar='+offerDataUsage\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_dtmfgrammar1\n  value: \"'MM1035_MyAccount_DM_dtmf.jsp?stateVar='+offerDataUsage\"\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_MM1035_MyAccount_DM\n  value: =Text(Global.MM1035_MyAccount_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MM1035_MyAccount_DM = \"acct_bal\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(GlobalVars.aniMatch = true && GlobalVars.switchLinesSuccess = false, true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.securityRequired\n                  value: false\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.securityRequired\n              value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.needMDN\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.mainMenuChoice\n          value: acct_bal\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: acct_bal\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MM1035_MyAccount_DM = \"add_line\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.securityRequired\n          value: false\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.needMDN\n          value: false\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: add_line\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.mainMenuChoice\n          value: add_line\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MM1035_MyAccount_DM = \"main_menu\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.securityRequired\n          value: false\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.needMDN\n          value: false\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: undefined\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.mainMenuChoice\n          value: undefined\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: MM1030_MainMenu_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MM1035_MyAccount_DM = \"plan_details\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.securityRequired\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.needMDN\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.mainMenuChoice\n          value: plan_details\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: plan_details\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.ratePlanAction\n          value: undefined\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MM1035_MyAccount_DM = \"mdn_change\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.mdnChangeVars.eventTypeGMT\n          value: getEventTime()\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.mdnChangeVars.status\n          value: incomplete\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.mdnChangeVars.eventType\n          value: mdn_change\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: mdn_change\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.mainMenuChoice\n          value: mdn_change\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.securityRequired\n          value: false\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.needMDN\n          value: false\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.billableTask\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.ActivationTable.ACTIVATION_TYPE\n          value: 5\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.ActivationTable.ACTIVATION_STARTED\n          value: getGMTTime()\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.ActivationTable.ACTIVATION_STATUS\n          value: 47\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.ActivationTable.ERROR_TEXT\n          value: FAILURE OR HANG UP\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.GlobalVars.GetAccountDetails, true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.paymentInfoRequired\n                  value: true\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.paymentInfoRequired\n              value: false\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MM1035_MyAccount_DM = \"my_features\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.securityRequired\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.needMDN\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.mainMenuChoice\n          value: my_features\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: my_features\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MM1035_MyAccount_DM = \"reset_pin\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.securityRequired\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.needMDN\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.mainMenuChoice\n          value: reset_pin\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: reset_pin\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: MM1036_MetricsPreLoginVoicemailPIN_JDA_DA\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MM1035_MyAccount_DM = \"make_pmt\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: make_pmt\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.securityRequired\n          value: false\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.needMDN\n          value: false\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.mainMenuChoice\n          value: make_pmt\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MM1035_MyAccount_DM = \"data_usage\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: dataUsage\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.mainMenuChoice\n          value: dataUsage\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.needMDN\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.securityRequired\n          value: true\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MM1035_MyAccount_DM = \"tmomoney\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: tmomoney\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.tag\n          value: T-MobileMoney\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.needMDN\n          value: false\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.securityRequired\n          value: false\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MM1035_MyAccount_DM = \"default\", true, false)\n      actions:\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.MainMenu_CheckSecurity.dvxml#MM1050_CheckContinueToDestination_DS\n```", "timestamp": **********.6238894, "content_hash": "b7ae9f26e14b4ce951d2748e7174943c"}