{"response": "```yaml\n- kind: Question\n  id: NP0010_GetOSPMDN_DM\n  displayName: NP0010_GetOSPMDN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/NP0010_nm1_01.wav\\\">Please say or enter the full ten digits of the phone number that you want to keep using</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/NP0010_nm2_01.wav\\\">If you want to keep a telephone number you re already using, please start by entering that phone number now on your keypad</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/NP0010_nm3_01.wav\\\">Please enter the full ten-digit telephone number that you d like to port over to Metro</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.NP0010_GetOSPMDN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/NP0010_ini_01.wav\\\">What s that number?</audio>\"\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/NP0010_nm1_01.wav\\\">Please say or enter the full ten digits of the phone number that you want to keep using</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/NP0010_nm2_01.wav\\\">If you want to keep a telephone number you re already using, please start by entering that phone number now on your keypad</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/NP0010_nm3_01.wav\\\">Please enter the full ten-digit telephone number that you d like to port over to Metro</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.NumberPortInVars.portingNumber\n  value: NP0010_GetOSPMDN_DM.returnvalue\n\n- kind: SendActivity\n  id: sendActivity_REPLACE_THIS\n  activity:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/NP0010_out_01.wav\\\">Thanks</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/NP0010_out_03.wav\\\">One moment while I check that</audio>\"\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: NP0015_IsPortInEligible_DB_DA\n```", "timestamp": 1749528928.4708138, "content_hash": "91eada6b64f831b07b1469edf5ac7d2d"}