{"response": "```yaml\n- kind: Question\n  id: AC0095_PaymentDisclaimer_DM\n  displayName: AC0095_PaymentDisclaimer_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AC0095_nm1_01.wav\\\">If youll be able to pay on this call, say continue Otherwise, say find a store</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AC0095_nm2_01.wav\\\">If youll be able to pay your first months charges on this call, say continue or press 1 Otherwise, say find a store or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AC0095_nm3_01.wav\\\">If you have a credit or debit card, or a  Payment PIN to pay for your first month's charges today, press 1 If you'd rather pay cash at Metro location, press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.AC0095_PaymentDisclaimer_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            // 1. Just so you know, in order to open an account, you'll need to pay your first month's charges on this call\n            // 2. If offerPrepaid = true, You can pay with a credit or debit card, or with a  Payment PIN\n            //    else, You can pay with a credit or debit card\n            // 3. If you'll be able to make that payment, say 'continue'  Otherwise, to pay in cash, say 'find a store'\n            [\n              \"Just so you know, in order to open an account, you'll need to pay your first month's charges on this call\",\n              {Switch(\n                true,\n                Global.offerPrepaid = true,\n                \"You can pay with a credit or debit card, or with a  Payment PIN\",\n                \"You can pay with a credit or debit card \"\n              )},\n              \"If you'll be able to make that payment, say 'continue'  Otherwise, to pay in cash, say 'find a store'\"\n            ]\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: continue\n          displayName: continue\n        - id: inquire-store_location\n          displayName: inquire-store_location\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/AC0095_nm1_01.wav\\\">If youll be able to pay on this call, say continue Otherwise, say find a store</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AC0095_nm2_01.wav\\\">If youll be able to pay your first months charges on this call, say continue or press 1 Otherwise, say find a store or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AC0095_nm3_01.wav\\\">If you have a credit or debit card, or a  Payment PIN to pay for your first month's charges today, press 1 If you'd rather pay cash at Metro location, press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.offerPrepaid\n  value: \"GlobalVars.offerPrepaid != undefined ? GlobalVars.offerPrepaid : false\"\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_AC0095_PaymentDisclaimer_DM\n  value: =Text(Global.AC0095_PaymentDisclaimer_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AC0095_PaymentDisclaimer_DM = \"continue\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.ActivationTable.ACTIVATION_TYPE\n          value: 2\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.ActivationTable.ACTIVATION_STARTED\n          value: new Date()\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.ActivationTable.ACTIVATION_STARTED\n          value: getGMTTime()\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.ActivationTable.ACTIVATION_STATUS\n          value: 41\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.activationVars.eventTypeGMT\n          value: getEventTime()\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.activationVars.status\n          value: incomplete\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.activationVars.eventType\n          value: Activation\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: AC1000_ActivationsRouting_JDA_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_AC0095_PaymentDisclaimer_DM = \"inquire-store_location\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.storeLocatorReason\n              value: sign up\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: AC0096_StoreLocator_SD\n```", "timestamp": **********.2875307, "content_hash": "f3ddab8fce4778d8957d895569433a3c"}