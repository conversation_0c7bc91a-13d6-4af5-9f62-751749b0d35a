{"response": "```yaml\n- kind: Question\n  id: bm1315_OfferAutoRefill_DM\n  displayName: bm1315_OfferAutoRefill_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bm1315_ni1_01.wav\\\">Would you like to get started with Auto Pay? Please say Yes or No</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/bm1315_ni2_01.wav\\\">If you d like to start setting up Auto Pay on your account, press 1 If you *don t* want to, press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.bm1315_OfferAutoRefill_DM_reco\n  prompt:\n    speak:\n      - \"{CustomAudio: com.nuance.att.application.audio.bm1315_OfferAutoRefill_initial}\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/bm1315_ni1_01.wav\\\">Would you like to get started with Auto Pay? Please say Yes or No</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/bm1315_ni2_01.wav\\\">If you d like to start setting up Auto Pay on your account, press 1 If you *don t* want to, press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_bm1315_OfferAutoRefill_DM\n  value: =Text(Global.bm1315_OfferAutoRefill_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_bm1315_OfferAutoRefill_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: autoRefill\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/bm1315_out_01.wav\\\">Alright</audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: bm1320_PostChargeAutoRefill_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.callType = \"activations\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.isIOMEnabled = \"true\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: bm1240_PlayRefillIOM_SD\n\n          elseActions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: bm1245_RefillCompleteWrapMenu_DM\n```\n**Notes on the conversion:**\n- **unrecognizedPrompt** and **inputTimeoutResponse**: Both use the two noinput/nomatch prompt audio files as per the XML.\n- **prompt**: Since the initial prompt is a custom audio class, it's represented as `{CustomAudio: com.nuance.att.application.audio.bm1315_OfferAutoRefill_initial}`.\n- **entity**: Set to `BooleanPrebuiltEntity` because the success actions are for \"true\" and \"false\".\n- **SetVariable**: For the variable mapping as per instruction 11.\n- **ConditionGroup**: Handles the \"true\" and \"false\" branches, with nested conditions for the \"false\" branch as per the XML logic.\n- **SendActivity**: Used to play the \"Alright\" audio after setting the intent for the \"true\" branch.\n- **GotoAction**: Used for all navigation actions.\n- All ids are set to `REPLACE_THIS` as per instruction for placeholder ids.\n- All variable references are prefixed with `Global.` as per instructions.\n- All audio references use the `<audio src=\"AUDIO_LOCATION/...\">...</audio>` format.\n- The \"command\" actions are not included as per instruction 22 (ignore `<command>` and `<event>` tags).", "timestamp": 1749470053.78392, "content_hash": "97559c496c33ee7af1f70bf5c59b8fdc"}