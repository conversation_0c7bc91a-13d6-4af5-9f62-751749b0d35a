{"response": "```yaml\n- kind: Question\n  id: TS1215_MobileTipsWrapMenu_DM\n  displayName: TS1215_MobileTipsWrapMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TS1215_ini_01.wav\\\">Say repeat that, or I ve already tried If you re all set, you can just hang up </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TS1215_nm2_01.wav\\\">To hear those tips again, say repeat or press 1 Or say I ve already tried or press 2 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TS1215_nm2_01.wav\\\">To hear those tips again, say repeat or press 1 Or say I ve already tried or press 2 </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.TS1215_MobileTipsWrapMenu_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/TS1215_ini_01.wav\\\">Say repeat that, or I ve already tried If you re all set, you can just hang up </audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: repeat\n          displayName: repeat\n\n        - id: tried\n          displayName: tried\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/TS1215_ini_01.wav\\\">Say repeat that, or I ve already tried If you re all set, you can just hang up </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/TS1215_nm2_01.wav\\\">To hear those tips again, say repeat or press 1 Or say I ve already tried or press 2 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/TS1215_nm2_01.wav\\\">To hear those tips again, say repeat or press 1 Or say I ve already tried or press 2 </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_TS1215_MobileTipsWrapMenu_DM\n  value: =Text(Global.TS1215_MobileTipsWrapMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TS1215_MobileTipsWrapMenu_DM = \"repeat\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TS1210_PlayMobileTips_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_TS1215_MobileTipsWrapMenu_DM = \"tried\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/TS1215_out_01.wav\\\">Okay, our tech support team will take it from here!</audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: TS1010_TechSupportTransfer_SD\n```", "timestamp": 1749529677.7745645, "content_hash": "c96ba12cd5f8d7705b91614bba087fef"}