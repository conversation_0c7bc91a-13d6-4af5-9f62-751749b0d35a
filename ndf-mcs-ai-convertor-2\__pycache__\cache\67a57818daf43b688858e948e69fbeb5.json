{"response": "```yaml\n- kind: Question\n  id: SW1205_GetNewLinePIN_DM\n  displayName: SW1205_GetNewLinePIN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - |\n        {Switch(\n              true,\n              Global.switchLinesPINAttempts = 0,\n              \"<audio src=\\\"AUDIO_LOCATION/SW1205_nm1_01.wav\\\">Please enter the account PIN for the account you want to work with If it's the same as the account you're *calling in* on, say 'same one' You can also say 'I don't know it' </audio>\",\n              true,\n              \"<audio src=\\\"AUDIO_LOCATION/SW1205_nm1_02.wav\\\">Please enter the account PIN for the account you want to work with If it's the same as the account you're *calling in* on, say 'same one' To correct the phone number you entered, say 'go back' You can also say 'I don't know it' </audio>\"\n          )\n        }\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - |\n        {Switch(\n              true,\n              Global.accountPinToggleOn = true,\n              \"<audio src=\\\"AUDIO_LOCATION/SW1205_nm2_01.wav\\\">Please enter the 6-to-15-digit account PIN for the phone number you just gave me </audio>\",\n              true,\n              \"<audio src=\\\"AUDIO_LOCATION/SW1205_nm2_02.wav\\\">Please enter the 8-digit account PIN for the phone number you just gave me  </audio>\"\n          )\n        }\n      - \"<audio src=\\\"AUDIO_LOCATION/SW1205_nm2_03.wav\\\">If it's the same as the account you're *calling in* on, press 1 If you think you need to *correct* the phone number you gave me, press 2 If you *don't know* the account PIN for this account, press star</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - |\n        {Switch(\n              true,\n              Global.accountPinToggleOn = true,\n              \"<audio src=\\\"AUDIO_LOCATION/SW1205_nm2_01.wav\\\">Please enter the 6-to-15-digit account PIN for the phone number you just gave me </audio>\",\n              true,\n              \"<audio src=\\\"AUDIO_LOCATION/SW1205_nm2_02.wav\\\">Please enter the 8-digit account PIN for the phone number you just gave me  </audio>\"\n          )\n        }\n      - \"<audio src=\\\"AUDIO_LOCATION/SW1205_nm2_03.wav\\\">If it's the same as the account you're *calling in* on, press 1 If you think you need to *correct* the phone number you gave me, press 2 If you *don't know* the account PIN for this account, press star</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.SW1205_GetNewLinePIN_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.switchLinesPINAttempts = 0,\n              \"<audio src=\\\"AUDIO_LOCATION/SW1205_ini_01.wav\\\">Now, the account PIN for *that* number You can say 'it's the same one' Or if it's a different PIN, please enter it now </audio>\",\n              Global.switchLinesPINAttempts = 1,\n              \"<audio src=\\\"AUDIO_LOCATION/SW1205_ini_02.wav\\\">You can say 'that's wrong' or enter a different account PIN</audio>\",\n              Global.lastPinTry = true,\n              \"<audio src=\\\"AUDIO_LOCATION/SW1205_ini_03.wav\\\">Let's try one last time Please enter the PIN for this line</audio>\",\n              true,\n              \"<audio src=\\\"AUDIO_LOCATION/SW1205_ini_04.wav\\\">Please enter the Pin for THIS line</audio>\"\n          )\n        }\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: default\n          displayName: default\n        - id: dont_know\n          displayName: dont_know\n        - id: same_one\n          displayName: same_one\n        - id: incorrect_MDN\n          displayName: incorrect_MDN\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.switchLinesPINAttempts = 0,\n                \"<audio src=\\\"AUDIO_LOCATION/SW1205_nm1_01.wav\\\">Please enter the account PIN for the account you want to work with If it's the same as the account you're *calling in* on, say 'same one' You can also say 'I don't know it' </audio>\",\n                true,\n                \"<audio src=\\\"AUDIO_LOCATION/SW1205_nm1_02.wav\\\">Please enter the account PIN for the account you want to work with If it's the same as the account you're *calling in* on, say 'same one' To correct the phone number you entered, say 'go back' You can also say 'I don't know it' </audio>\"\n            )\n          }\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - |\n          {Switch(\n                true,\n                Global.accountPinToggleOn = true,\n                \"<audio src=\\\"AUDIO_LOCATION/SW1205_nm2_01.wav\\\">Please enter the 6-to-15-digit account PIN for the phone number you just gave me </audio>\",\n                true,\n                \"<audio src=\\\"AUDIO_LOCATION/SW1205_nm2_02.wav\\\">Please enter the 8-digit account PIN for the phone number you just gave me  </audio>\"\n            )\n          }\n        - \"<audio src=\\\"AUDIO_LOCATION/SW1205_nm2_03.wav\\\">If it's the same as the account you're *calling in* on, press 1 If you think you need to *correct* the phone number you gave me, press 2 If you *don't know* the account PIN for this account, press star</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - |\n          {Switch(\n                true,\n                Global.accountPinToggleOn = true,\n                \"<audio src=\\\"AUDIO_LOCATION/SW1205_nm2_01.wav\\\">Please enter the 6-to-15-digit account PIN for the phone number you just gave me </audio>\",\n                true,\n                \"<audio src=\\\"AUDIO_LOCATION/SW1205_nm2_02.wav\\\">Please enter the 8-digit account PIN for the phone number you just gave me  </audio>\"\n            )\n          }\n        - \"<audio src=\\\"AUDIO_LOCATION/SW1205_nm2_03.wav\\\">If it's the same as the account you're *calling in* on, press 1 If you think you need to *correct* the phone number you gave me, press 2 If you *don't know* the account PIN for this account, press star</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.requestedMDNChangeSwitchLines\n  value: GlobalVars.requestedMDNChangeSwitchLines\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.switchLinesPINAttempts\n  value: GlobalVars.switchLinesPINAttempts\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.lastPinTry\n  value: GlobalVars.lastPinTry\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.accountPinToggleOn\n  value: GlobalVars.GetBCSParameters.accountPinToggleOn\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_SW1205_GetNewLinePIN_DM\n  value: =Text(Global.SW1205_GetNewLinePIN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_SW1205_GetNewLinePIN_DM = \"default\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.newLine_PIN\n          value: SW1205_GetNewLinePIN_DM.returnvalue\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.verificationType\n          value: pin\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.verificationValue\n          value: SW1205_GetNewLinePIN_DM.returnvalue\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.collectedPINNewLine\n          value: GlobalVars.newLine_PIN\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_PIN\n          value: GlobalVars.newLine_PIN\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: SW1209_Authenticate_DB_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_SW1205_GetNewLinePIN_DM = \"dont_know\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/SW1205_out_02.wav\\\">No problem</audio>\"\n\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.lastPinTry = true, true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.dontKnowPIN\n                      value: true\n\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: SW1220_Transfer_SD\n\n              elseActions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: SW1225_GoToAccountPinReset_SD\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_SW1205_GetNewLinePIN_DM = \"same_one\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.collectedPINANI\n                  value: GlobalVars.verificationValue\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.collectedPINNewLine\n                  value: GlobalVars.collectedPINANI\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.verificationType\n                  value: pin\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.verificationValue\n                  value: GlobalVars.collectedPINANI\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.cti_PIN\n                  value: GlobalVars.collectedPINANI\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: SW1209_Authenticate_DB_DA\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_SW1205_GetNewLinePIN_DM = \"incorrect_MDN\", true, false)\n                  actions:\n                    - kind: ConditionGroup\n                      id: conditionGroup_REPLACE_THIS\n                      conditions:\n                        - id: conditionItem_REPLACE_THIS\n                          condition: =If(Global.requestedMDNChangeSwitchLines = true, true, false)\n                          actions:\n                            - kind: BeginDialog\n                              id: begin_REPLACE_THIS\n                              dialog: topic.SwitchLines_Security.dvxml#SW1505_SwitchNotAllowedReason_DS\n\n                      elseActions:\n                        - kind: SendActivity\n                          id: sendActivity_REPLACE_THIS\n                          activity:\n                            speak:\n                              - \"<audio src=\\\"AUDIO_LOCATION/SW1205_out_01.wav\\\">Ok, let's get the right one </audio>\"\n\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.GlobalVars.requestedMDNChangeSwitchLines\n                          value: true\n\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.GlobalVars.switchLinesMDNAttempts\n                          value: 0\n\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: SW1105_GetNewLineMDN_DM\n```", "timestamp": 1749529687.7911162, "content_hash": "67a57818daf43b688858e948e69fbeb5"}