{"response": "```yaml\n- kind: Question\n  id: aa1047_CardLostStolenWrapUpDTMF_DM\n  displayName: aa1047_CardLostStolenWrapUpDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - aa1047_CardLostStolenWrapUpDTMF_DM_initial\n      - aa1047_CardLostStolenWrapUpDTMF_DM_initial\n\n  alwaysPrompt: true\n  variable: Global.aa1047_CardLostStolenWrapUpDTMF_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.dnisInfo.lsdAnotherCardOption = true && Global.dnisInfo.lsdOfferAgent = false,\n            \"To hear that again, press 1 To enter a different card, press 2 If you are done, feel free to hang up\",\n\n            Global.dnisInfo.lsdAnotherCardOption = true && Global.dnisInfo.lsdOfferAgent = true,\n            \"To hear that again, press 1To enter a different card, press 2To speak with an agent, press 3 If you are done, feel free to hang up\",\n\n            Global.dnisInfo.lsdAnotherCardOption = false && Global.dnisInfo.lsdOfferAgent = false,\n            \"To hear that again, press 1If you are done, feel free to hang up\",\n\n            \"To hear that again, press 1, To speak with a Customer Service Representative, press 2 If you are done, feel free to hang up\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: repeat\n          displayName: repeat\n        - id: different_card\n          displayName: different_card\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - aa1047_CardLostStolenWrapUpDTMF_DM_initial\n        - aa1047_CardLostStolenWrapUpDTMF_DM_initial\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa1047_CardLostStolenWrapUpDTMF_DM\n  value: =Text(Global.aa1047_CardLostStolenWrapUpDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa1047_CardLostStolenWrapUpDTMF_DM = \"repeat\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aa1047_out_01.wav\\\">Again</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa1045_CardReported_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa1047_CardLostStolenWrapUpDTMF_DM = \"different_card\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.cardInfoVariables.collectedCardNumber\n              value: ''\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.nextStep\n              value: get_card_info\n```", "timestamp": 1749458465.9443672, "content_hash": "a19fc3abab253a919b0f9a3a16014379"}