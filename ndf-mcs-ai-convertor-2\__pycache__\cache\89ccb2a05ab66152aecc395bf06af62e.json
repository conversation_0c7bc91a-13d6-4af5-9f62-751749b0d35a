{"response": "```yaml\n- kind: Question\n  id: MP1010_SinglePlanYN_DM\n  displayName: MP1010_SinglePlanYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MP1010_nm1_01.wav\\\">If you d like to get that plan, say  yes  Otherwise, say  no </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MP1010_nm2_01.wav\\\">I ll give you those details again</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MP1010_nm2_13.wav\\\">To activate your phone with that plan, say  yes  or press one Otherwise, say  no  or press two</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MP1010_nm2_01.wav\\\">I ll give you those details again</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MP1010_nm2_13.wav\\\">To activate your phone with that plan, say  yes  or press one Otherwise, say  no  or press two</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.MP1010_SinglePlanYN_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.reentry_MP1010_SinglePlanYN_DM = true,\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/MP1010_ree_01.wav\\\">Unfortunately, that s the only plan I have available right now If you don t want to activate your phone with that plan, feel free to hang up now Otherwise, I ll read the details for that plan again</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/MP1010_ini_02.wav\\\">Let s set up your monthly plan Here s the one we have for your phone</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\",\n              \"{Global.availablePlan}\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/MP1010_ree_03.wav\\\">I just need to know whether you want that plan or not If you do, say yes or press one If not, you can hang up</audio>\"\n            ],\n            Global.reentry_MP1010_SinglePlanYN_DM = false,\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/MP1010_ini_02.wav\\\">Let s set up your monthly plan Here s the one we have for your phone</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\",\n              \"{Global.availablePlan}\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/MP1010_ini_15.wav\\\">So, would you like to go with that plan?</audio>\"\n            ],\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/MP1010_ini_02.wav\\\">Let s set up your monthly plan Here s the one we have for your phone</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\",\n              \"{Global.availablePlan}\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/MP1010_ini_15.wav\\\">So, would you like to go with that plan?</audio>\"\n            ]\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/MP1010_nm1_01.wav\\\">If you d like to get that plan, say  yes  Otherwise, say  no </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MP1010_nm2_01.wav\\\">I ll give you those details again</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MP1010_nm2_13.wav\\\">To activate your phone with that plan, say  yes  or press one Otherwise, say  no  or press two</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MP1010_nm2_01.wav\\\">I ll give you those details again</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MP1010_nm2_13.wav\\\">To activate your phone with that plan, say  yes  or press one Otherwise, say  no  or press two</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.availablePlan\n  value: getSingleRatePlan(GlobalVars.ratePlans)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.planType\n  value: none\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.callType\n  value: GlobalVars.callType\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.onlyOneOrAmbiguous_MP1050\n  value: false\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.availablePlansArray\n  value: new Object()\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.availablePlansArray[0]\n  value: new Object()\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_MP1010_SinglePlanYN_DM\n  value: =Text(Global.MP1010_SinglePlanYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MP1010_SinglePlanYN_DM = \"yes\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.onlyOneOrAmbiguous_MP1050\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.selectedPlan\n          value: availablePlan\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.availablePlansArray[0].soc\n          value: availablePlan\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.availablePlansArray[0].price\n          value: GlobalVars.ratePlans[0].price\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.choseInvalidPlan\n          value: false\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: MP1065_AssignMonthlyPlan_JDA\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MP1010_SinglePlanYN_DM = \"no\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.noToOnlyPlan_MP1010_SinglePlanYN_DM < 2, true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.noToOnlyPlan_MP1010_SinglePlanYN_DM\n                  value: noToOnlyPlan_MP1010_SinglePlanYN_DM + 1\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.reentry_MP1010_SinglePlanYN_DM\n                  value: true\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: MP1010_SinglePlanYN_DM\n```", "timestamp": 1749558430.8813336, "content_hash": "89ccb2a05ab66152aecc395bf06af62e"}