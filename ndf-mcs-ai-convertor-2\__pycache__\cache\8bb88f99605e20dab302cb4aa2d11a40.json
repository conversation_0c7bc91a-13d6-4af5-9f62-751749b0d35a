{"response": "```yaml\n- kind: Question\n  id: pp0325_AskPlanPackageCategory_DM\n  displayName: pp0325_AskPlanPackageCategory_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"Say Yes or press 1, or say No or press 2\"\n      - \"Say Yes or press 1, or say No or press 2\"\n\n  alwaysPrompt: true\n  variable: Global.pp0325_AskPlanPackageCategory_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.pp0325_nonOptionCount = 0 && Global.pp0325_nonOptionCountForDecoy = 0 && Global.pp0325_saidMultiline = false,\n            \"[custom: com.nuance.att.application.audio.pp0325_AskPlanPackageCategory_DM_initial]\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: none_of_them\n          displayName: none_of_them\n        - id: decoy_data_package\n          displayName: decoy_data_package\n        - id: multi_line\n          displayName: multi_line\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"[custom: com.nuance.att.application.audio.pp0325_AskPlanPackageCategory_DM_noinput_1]\"\n        - \"[custom: com.nuance.att.application.audio.pp0325_AskPlanPackageCategory_DM_noinput_2]\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.returnCode\n  value: GetAvailablePlansPackagesList.returnCode\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.returnCode = 0, true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_dtmfgrammar1\n          value: =Text('pp0325_AskPlanPackageCategory_DM_dtmf.jsp' + Global.GetAvailablePlansPackagesList.planPackageListStringDtmf)\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_grammar1\n          value: =Text('pp0325_AskPlanPackageCategory_DM.jsp' + Global.GetAvailablePlansPackagesList.planPackageListStringVoice)\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_pp0325_AskPlanPackageCategory_DM\n  value: =Text(Global.pp0325_AskPlanPackageCategory_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pp0325_AskPlanPackageCategory_DM = \"none_of_them\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.pp0325_saidMultiline\n          value: false\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.pp0325_nonOptionCount\n          value: =Text(Global.pp0325_nonOptionCount + 1)\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.pp0325_nonOptionCount = 1, true, false)\n              actions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/pp0325_out_01.wav\\\">These are all the ones currently available for your device Let's go through them again </audio>\"\n          elseActions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/pp0325_out_02.wav\\\">Sorry, these are all the plans and add-ons for your device If there's anything else I can help you with say 'Main Menu', otherwise, simply hang-up </audio>\"\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pp0325_AskPlanPackageCategory_DM = \"decoy_data_package\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.pp0325_saidMultiline\n          value: false\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.pp0325_nonOptionCountForDecoy\n          value: =Text(Global.pp0325_nonOptionCountForDecoy + 1)\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.pp0325_nonOptionCountForDecoy = 1, true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: pp0325_decoy_data_package_recoOption_CS\n          elseActions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/pp0325_out_08.wav\\\">Sorry, these are all the plans and add-ons for your device If there's anything else I can help you with say 'Main Menu', otherwise, simply hang-up</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: pp0325_AskPlanPackageCategory_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pp0325_AskPlanPackageCategory_DM = \"multi_line\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.pp0325_saidMultiline\n          value: true\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/pp0325_out_03.wav\\\">Okay</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: pp0325_AskPlanPackageCategory_DM_Multiline_JDA\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pp0325_AskPlanPackageCategory_DM = \"default\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.fromPlansAndPackages\n          value: true\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.planPackageName\n          value: =Text(Global.pp0325_AskPlanPackageCategory_DM.returnvalue)\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: pp0325_AskPlanPackageCategory_DM_JDA\n```", "timestamp": 1749471469.2188241, "content_hash": "8bb88f99605e20dab302cb4aa2d11a40"}