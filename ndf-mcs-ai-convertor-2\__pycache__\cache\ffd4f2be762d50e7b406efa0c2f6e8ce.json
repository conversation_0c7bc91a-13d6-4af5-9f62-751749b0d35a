{"response": "```yaml\n- kind: Question\n  id: ai0220_AskIMEIGophoneFallback2_DM\n  displayName: ai0220_AskIMEIGophoneFallback2_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I missed that</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ai0220_ni1_01.wav\\\">Say or enter the 15 digits of the IMEI number You can skip any numbers that come after a slash If the IMEI number wasnt shown on your new phones screen, say It Didnt Work or press 1 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">Sorry</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ai0220_ni2_01.wav\\\">Please enter the 15 digits of the IMEI number on the keypad of the phone youre calling from You can skip any numbers that come after a slash Or if the IMEI number wasnt shown on your new phones screen, press 1</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ai0220_nm3_01.wav\\\">I still didnt get that If you see the IMEI number on your new phones screen, go ahead and enter it on the phone youre calling from, skipping any numbers that follow a slash If you dont see the IMEI number, just press 1</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ai0220_AskIMEIGophoneFallback2_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ai0220_ini_01.wav\\\">Read me that 15-digit IMEI number You can skip any numbers that come after a slash </audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: doesnt_work\n          displayName: doesnt_work\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ai0220_ni1_01.wav\\\">Say or enter the 15 digits of the IMEI number You can skip any numbers that come after a slash If the IMEI number wasnt shown on your new phones screen, say It Didnt Work or press 1 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ai0220_ni2_01.wav\\\">Please enter the 15 digits of the IMEI number on the keypad of the phone youre calling from You can skip any numbers that come after a slash Or if the IMEI number wasnt shown on your new phones screen, press 1</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ai0220_AskIMEIGophoneFallback2_DM\n  value: =Text(Global.ai0220_AskIMEIGophoneFallback2_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ai0220_AskIMEIGophoneFallback2_DM = \"doesnt_work\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferReason\n          value: BUSINESS_RULE\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferPrompt\n          value: default\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M09_MainMenu_Dialog\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ai0220_AskIMEIGophoneFallback2_DM = \"default\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/ai0220_out_01.wav\\\">Thanks </audio>\"\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.imei\n              value: ai0220_AskIMEIGophoneFallback2_DM.returnvalue\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.A04_IMEI_03_Dialog\n```", "timestamp": 1749469614.4095616, "content_hash": "ffd4f2be762d50e7b406efa0c2f6e8ce"}