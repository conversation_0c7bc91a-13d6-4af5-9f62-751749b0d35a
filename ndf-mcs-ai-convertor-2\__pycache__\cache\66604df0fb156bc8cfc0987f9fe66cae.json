{"response": "```yaml\n- kind: Question\n  id: bm0715_AskQuickRefill_DM\n  displayName: bm0715_AskQuickRefill_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"com.nuance.att.application.audio.bm0715_AskQuickRefill_DM_noinput_1\"\n      - \"com.nuance.att.application.audio.bm0715_AskQuickRefill_DM_noinput_2\"\n\n  alwaysPrompt: true\n  variable: Global.bm0715_AskQuickRefill_DM_reco\n  prompt:\n    speak:\n      - \"{CustomAudio: com.nuance.att.application.audio.bm0715_AskQuickRefill_initial}\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"com.nuance.att.application.audio.bm0715_AskQuickRefill_DM_noinput_1\"\n        - \"com.nuance.att.application.audio.bm0715_AskQuickRefill_DM_noinput_2\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_bm0715_AskQuickRefill_DM\n  value: =Text(Global.bm0715_AskQuickRefill_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_bm0715_AskQuickRefill_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.chargeAmount\n          value: preferredAmount\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.amount\n          value: preferredAmount\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.pdofCounter\n          value: quickRefillPDOFCounter\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/bm0715_out_01.wav\\\">Great</audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: bm0815_AddMoneyVestaProcess_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_bm0715_AskQuickRefill_DM = \"false\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.chargeAmount\n              value: preferredAmount\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.amount\n              value: preferredAmount\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.pdofCounter\n              value: quickRefillPDOFCounter\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: bm0717_AskWhichChange_DM\n```\n\n---\n\n**Explanation of the YAML:**\n\n- **kind: Question**: Always set as per instruction.\n- **id/displayName**: Set to `bm0715_AskQuickRefill_DM` as per the dm-state id.\n- **unrecognizedPrompt**: Since nomatchprompts are custom audio class references, their class names are used as plain text (as per your pattern for custom audio).\n- **prompt**: The initial prompt is a custom audio class, so it's referenced as `{CustomAudio: ...}`.\n- **entity**: BooleanPrebuiltEntity, since the success actions are for true/false.\n- **voiceInputSettings**: Both noinputprompts are custom audio class names, so they're listed as plain text.\n- **SetVariable**: For the variable mapping as per the instructions.\n- **ConditionGroup**: For each label (true/false), the correct actions are mapped, including session-mapping and audio (SendActivity for the audio prompt).\n- **GotoAction**: For the next state as per the next attribute.\n\n**Note:**  \n- The `{CustomAudio: ...}` notation is used to indicate a custom audio class reference, as the XML does not provide a text or audio file, only a className. If you want a different notation, adjust accordingly.\n- All ids for SetVariable, ConditionGroup, etc., are set to `REPLACE_THIS` as per your placeholder instruction.\n- All session-mapping expr values are mapped as variable assignments.\n- The audio prompt under the \"true\" branch is included as a SendActivity with the audio file and text.\n- No nomatch/noinput if/else logic is present, so the prompts are listed as plain text.\n", "timestamp": 1749469803.3863566, "content_hash": "66604df0fb156bc8cfc0987f9fe66cae"}