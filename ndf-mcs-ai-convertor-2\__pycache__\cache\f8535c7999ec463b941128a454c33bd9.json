{"response": "```yaml\n- kind: Question\n  id: IU1055_ActivationInformation_DM\n  displayName: IU1055_ActivationInformation_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/IU1055_ini_01.wav\\\">Great You should have received an email at the address you provided when you placed your order This will contain your order ID A separate email will provide simple steps to activate your device If you have your order ID please go online at at metro by tmobile com / activate to complete your activation At the top of the page select phones and activations and find the option to activate at the bottom Do you still have questions about activation?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/IU1055_nm2_01.wav\\\">You can say 'repeat that' or press 7  Otherwise you can simply hang up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/IU1055_nm2_01.wav\\\">You can say 'repeat that' or press 7  Otherwise you can simply hang up</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.IU1055_ActivationInformation_DM_reco\n  prompt:\n    speak:\n      - \"{Switch(         true,         Global.IU1055saidOperator <> true,         \\\"Great You should have received an email at the address you provided when you placed your order This will contain your order ID A separate email will provide simple steps to activate your device If you have your order ID please go online at at metro by tmobile com / activate to complete your activation At the top of the page select phones and activations and find the option to activate at the bottom Do you still have questions about activation?\\\"     )}\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/IU1055_ini_01.wav\\\">Great You should have received an email at the address you provided when you placed your order This will contain your order ID A separate email will provide simple steps to activate your device If you have your order ID please go online at at metro by tmobile com / activate to complete your activation At the top of the page select phones and activations and find the option to activate at the bottom Do you still have questions about activation?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/IU1055_nm2_01.wav\\\">You can say 'repeat that' or press 7  Otherwise you can simply hang up</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/IU1055_nm2_01.wav\\\">You can say 'repeat that' or press 7  Otherwise you can simply hang up</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.requestOperatorUnidentified\n  value: GlobalVars.requestOperatorUnidentified\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.IU1055saidOperator\n  value: GlobalVars.IU1055saidOperator\n```", "timestamp": 1749530318.066338, "content_hash": "f8535c7999ec463b941128a454c33bd9"}