{"response": "```yaml\n- kind: Question\n  id: NP1015_AskCustomerCarrierorDealer_DM\n  displayName: NP1015_AskCustomerCarrierorDealer_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/NP1015_nm1_01.wav\\\">Which are you Please say 'customer','carrier'or'dealer'</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/NP1015_nm2_01.wav\\\">Please say 'I'm a customer' or press 1'dealer' or press 2 'employee' or press 3</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/NP1015_nm2_01.wav\\\">Please say 'I'm a customer' or press 1'dealer' or press 2 'employee' or press 3</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.NP1015_AskCustomerCarrierorDealer_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/NP1015_ini_01.wav\\\">Now, tell me, are you a customer, carrier, or a dealer?</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: customer\n          displayName: customer\n        - id: dealer\n          displayName: dealer\n        - id: carrier\n          displayName: carrier\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/NP1015_nm1_01.wav\\\">Which are you Please say 'customer','carrier'or'dealer'</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/NP1015_nm2_01.wav\\\">Please say 'I'm a customer' or press 1'dealer' or press 2 'employee' or press 3</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/NP1015_nm2_01.wav\\\">Please say 'I'm a customer' or press 1'dealer' or press 2 'employee' or press 3</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_NP1015_AskCustomerCarrierorDealer_DM\n  value: =Text(Global.NP1015_AskCustomerCarrierorDealer_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_NP1015_AskCustomerCarrierorDealer_DM = \"customer\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.language = \"en-US\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.TransferTag\n                  value: WNP_Customer_English\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.TransferTag\n              value: WNP_Customer_Spanish\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: NP1020_GoToTransfer_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_NP1015_AskCustomerCarrierorDealer_DM = \"dealer\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.language = \"en-US\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.TransferTag\n                      value: WNP_Dealer_English\n              elseActions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.TransferTag\n                  value: WNP_Dealer_Spanish\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: NP1020_GoToTransfer_SD\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_NP1015_AskCustomerCarrierorDealer_DM = \"carrier\", true, false)\n              actions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.language = \"en-US\", true, false)\n                      actions:\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.TransferTag\n                          value: WNP_Carrier_English\n                  elseActions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.TransferTag\n                      value: WNP_Carrier_Spanish\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: NP1020_GoToTransfer_SD\n```", "timestamp": 1749528968.807628, "content_hash": "57ff4452fca0f26d8acb251708f3aa20"}