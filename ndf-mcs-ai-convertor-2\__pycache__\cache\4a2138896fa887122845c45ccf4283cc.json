{"response": "```yaml\n- kind: Question\n  id: aa3022_WICCBalanceWrapUpMultProvidersDTMF_DM\n  displayName: aa3022_WICCBalanceWrapUpMultProvidersDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - aa3022_WICCBalanceWrapUpMultProvidersDTMF_DM_initial\n      - aa3022_WICCBalanceWrapUpMultProvidersDTMF_DM_initial\n\n  alwaysPrompt: true\n  variable: Global.aa3022_WICCBalanceWrapUpMultProvidersDTMF_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.childInfo.totalProviders = Global.wiccBalanceVariables.providerCount,\n            \"That was the last provider in the list\",\n            \"To hear that again, press 1 For the next benefit, press 2 To make a payment for this child and this provider, press 3 To go to the main menu, press 4 Or if you re done, simply hang up\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: repeat\n          displayName: repeat\n        - id: next_benefit\n          displayName: next_benefit\n        - id: make_payment\n          displayName: make_payment\n        - id: main_menu\n          displayName: main_menu\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa3022_WICCBalanceWrapUpMultProvidersDTMF_DM\n  value: =Text(Global.aa3022_WICCBalanceWrapUpMultProvidersDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa3022_WICCBalanceWrapUpMultProvidersDTMF_DM = \"repeat\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa3015_WICCBalance1ChildMultProv_PP\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa3022_WICCBalanceWrapUpMultProvidersDTMF_DM = \"next_benefit\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.childInfo.totalProviders = Global.wiccBalanceVariables.providerCount, true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.wiccBalanceVariables.providerCount\n                  value: 0\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: aa3015_WICCBalance1ChildMultProv_PP\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/aa3022_out_01.wav\\\">Okay, let s start with the first Provider again</audio>\"\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.wiccBalanceVariables.providerCount\n              value: wiccBalanceVariables.providerCount + 1\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa3015_WICCBalance1ChildMultProv_PP\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa3022_WICCBalanceWrapUpMultProvidersDTMF_DM = \"make_payment\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.nextStep\n          value: make_payment_wicc\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: return\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa3022_WICCBalanceWrapUpMultProvidersDTMF_DM = \"main_menu\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: handleMainMenu_CS\n```", "timestamp": **********.0931087, "content_hash": "4a2138896fa887122845c45ccf4283cc"}