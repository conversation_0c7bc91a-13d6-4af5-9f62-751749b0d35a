{"response": "```yaml\n- kind: Question\n  id: SE2140_WhatToChangeBothAllowed_DM\n  displayName: SE2140_WhatToChangeBothAllowed_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SE2140_nm1_01.wav\\\">Which action would you like to retrict? say 'mobile number transfers', 'sim changes' or 'both of them'</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SE2140_nm2_01.wav\\\">You can say 'mobile number transfer' or press 1  'sim changes' or press 2'both of them' or press 3</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SE2140_nm2_01.wav\\\">You can say 'mobile number transfer' or press 1  'sim changes' or press 2'both of them' or press 3</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.SE2140_WhatToChangeBothAllowed_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/SE2140_ini_01.wav\\\">Which action would you like to restrict? say 'mobile number transfers, 'sim changes' or 'both of them'</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: sim_changes\n          displayName: sim_changes\n        - id: port_outs\n          displayName: port_outs\n        - id: both_of_them\n          displayName: both_of_them\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/SE2140_nm1_01.wav\\\">Which action would you like to retrict? say 'mobile number transfers', 'sim changes' or 'both of them'</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SE2140_nm2_01.wav\\\">You can say 'mobile number transfer' or press 1  'sim changes' or press 2'both of them' or press 3</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SE2140_nm2_01.wav\\\">You can say 'mobile number transfer' or press 1  'sim changes' or press 2'both of them' or press 3</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_SE2140_WhatToChangeBothAllowed_DM\n  value: =Text(Global.SE2140_WhatToChangeBothAllowed_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_SE2140_WhatToChangeBothAllowed_DM = \"sim_changes\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.activityCode\n          value: BSS\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.blockingInd\n          value: Y\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: SE2155_PlayUpdateTransition_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_SE2140_WhatToChangeBothAllowed_DM = \"port_outs\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.activityCode\n              value: BPO\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.blockingInd\n              value: Y\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: SE2155_PlayUpdateTransition_PP\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_SE2140_WhatToChangeBothAllowed_DM = \"both_of_them\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.activityCode\n                  value: BSS\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.blockingInd\n                  value: Y\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.changedBothSP\n                  value: true\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.changedBoth\n                  value: true\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: SE2142_SetVariablesForBoth_JDA\n```", "timestamp": 1749529429.430904, "content_hash": "c29d78a4f658b3ada102e8e3efe72d64"}