{"response": "```yaml\n- kind: Question\n  id: MW1400_GetBillingZipCode_DM\n  displayName: MW1400_GetBillingZipCode_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1400_nm1_01.wav\\\">What s the zip code again?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1400_nm2_01.wav\\\">Please enter the billing zip code for the card</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1400_nm2_01.wav\\\">Please enter the billing zip code for the card</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.MW1400_GetBillingZipCode_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1400_ini_01.wav\\\">And finally, what s the billing zip code for the card?</audio>\"\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1400_ni1_01.wav\\\">What s the billing zip code where you get the bill for this card?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1400_nm2_01.wav\\\">Please enter the billing zip code for the card</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1400_nm2_01.wav\\\">Please enter the billing zip code for the card</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.bankCardZip\n  value: MW1400_GetBillingZipCode_DM.returnvalue\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: MW1405_PlayTransition_PP\n```", "timestamp": **********.2420661, "content_hash": "bf274c8278946d33253c45c6ab12cb16"}