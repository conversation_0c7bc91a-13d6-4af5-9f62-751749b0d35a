{"response": "```yaml\n- kind: Question\n  id: aa6056_CCCollectAmount_DM\n  displayName: aa6056_CCCollectAmount_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa6056_ini_01.wav\\\">How much would you like to pay?</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.aa6056_CCCollectAmount_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa6056_ini_01.wav\\\">How much would you like to pay?</audio>\"\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/aa6056_ini_01.wav\\\">How much would you like to pay?</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.childCareInfoVariables.paymentAmount\n  value: aa6056_CCCollectAmount_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.globalVariables.cameFrom\n  value: aa6056\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(parseFloat(Global.childCareInfoVariables.paymentAmount) > parseFloat(Global.ccProviderInfo.providerBalance), true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |\n                =If(Global.globalVariables.amountCounter = 2, true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.globalVariables.transferAllowed\n                  value: true\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: transferHandler_CS\n\n          elseActions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa6060_CCInsufficientFunds_PP\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.amountCounter\n              value: \"Global.globalVariables.amountCounter + 1\"\n\n  elseActions:\n    - kind: GotoAction\n      id: goto_REPLACE_THIS\n      actionId: aa6058_CheckAuthenticatedPin_DB_DA\n\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.ccApplyPaymentInfo.paymentAmount\n      value: Global.childCareInfoVariables.paymentAmount\n\n    - kind: SendActivity\n      id: sendActivity_REPLACE_THIS\n      activity:\n        speak:\n          - \"<audio src=\\\"AUDIO_LOCATION/aa6056_out_01.wav\\\">Got it [audio icon] Going forward please use your telephone keypad to use our automated system</audio>\"\n```", "timestamp": 1749543977.8480933, "content_hash": "94897c0a4ccfde2a47eb76a6e936481e"}