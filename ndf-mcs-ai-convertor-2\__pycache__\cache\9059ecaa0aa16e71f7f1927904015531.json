{"response": "```yaml\n- kind: Question\n  id: st0815_DataPPURefill_DM\n  displayName: st0815_DataPPURefill_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"com.nuance.att.application.audio.st0815_DataPPURefill_DM_noinput1\"\n      - \"com.nuance.att.application.audio.st0815_DataPPURefill_DM_noinput2\"\n\n  alwaysPrompt: true\n  variable: Global.st0815_DataPPURefill_DM_reco\n  prompt:\n    speak:\n      - \"com.nuance.att.application.audio.st0815_DataPPURefill_DM_initial\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: refill\n          displayName: refill\n        - id: change_plan\n          displayName: change_plan\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"com.nuance.att.application.audio.st0815_DataPPURefill_DM_noinput1\"\n        - \"com.nuance.att.application.audio.st0815_DataPPURefill_DM_noinput2\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_st0815_DataPPURefill_DM\n  value: =Text(Global.st0815_DataPPURefill_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_st0815_DataPPURefill_DM = \"refill\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: refillAccount\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.dataPPURefill\n          value: true\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.S02_TechSupport_06_Dialog\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_st0815_DataPPURefill_DM = \"change_plan\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.intent\n              value: changePlan\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.S02_TechSupport_06_Dialog\n```", "timestamp": **********.6971529, "content_hash": "9059ecaa0aa16e71f7f1927904015531"}