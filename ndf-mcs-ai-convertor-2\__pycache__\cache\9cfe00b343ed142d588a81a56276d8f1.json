{"response": "```yaml\n- kind: Question\n  id: BC1435_AskCreditorDebit_DM\n  displayName: BC1435_AskCreditorDebit_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1435_nm1_01.wav\\\">Please say how you would like to process your payment credit or debit</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1435_nm2_01.wav\\\">Please say 'credit, or press 1, or say 'debit' or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1435_nm3_01.wav\\\">To pay with credit, press 1 For debit, press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.BC1435_AskCreditorDebit_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1435_ini_01.wav\\\">How would you like to process your payment today? Please say 'credit' or 'debit'</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: credit\n          displayName: credit\n        - id: debit\n          displayName: debit\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1435_nm1_01.wav\\\">Please say how you would like to process your payment credit or debit</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1435_nm2_01.wav\\\">Please say 'credit, or press 1, or say 'debit' or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1435_nm3_01.wav\\\">To pay with credit, press 1 For debit, press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_BC1435_AskCreditorDebit_DM\n  value: =Text(Global.BC1435_AskCreditorDebit_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_BC1435_AskCreditorDebit_DM = \"credit\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.preferredPaymentMethod\n          value: credit\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: BC1415_ConfirmBankCardDetailsYN_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_BC1435_AskCreditorDebit_DM = \"debit\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.preferredPaymentMethod\n              value: debit\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: BC1415_ConfirmBankCardDetailsYN_DM\n```", "timestamp": **********.3833609, "content_hash": "9cfe00b343ed142d588a81a56276d8f1"}