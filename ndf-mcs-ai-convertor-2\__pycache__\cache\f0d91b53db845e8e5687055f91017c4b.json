{"response": "```yaml\n- kind: Question\n  id: ai0218_IMEIVisibleWait_DM\n  displayName: ai0218_IMEIVisibleWait_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ai0218_ni1_01.wav\\\">Does your screen show a 15-digit number? Say Yes or No Or if you need more time, say Hold On or press 3 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ai0218_ni2_01.wav\\\">If you see the 15-digit IMEI number on the screen of your new phone, say Yes or press 1 If not, say No or press 2 Or if you need more time to enter the code and get the IMEI number, say Hold On or press 3 </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ai0218_IMEIVisibleWait_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ai0218_ini_01.wav\\\">If you need more time, say Hold On Otherwise, does your screen show a 15-digit number? </audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: yes\n          displayName: yes\n        - id: no\n          displayName: no\n        - id: hold_on\n          displayName: hold_on\n        - id: ready\n          displayName: ready\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ai0218_ni1_01.wav\\\">Does your screen show a 15-digit number? Say Yes or No Or if you need more time, say Hold On or press 3 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ai0218_ni2_01.wav\\\">If you see the 15-digit IMEI number on the screen of your new phone, say Yes or press 1 If not, say No or press 2 Or if you need more time to enter the code and get the IMEI number, say Hold On or press 3 </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ai0218_IMEIVisibleWait_DM\n  value: =Text(Global.ai0218_IMEIVisibleWait_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ai0218_IMEIVisibleWait_DM = \"yes\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/ai0218_out_01.wav\\\">Great </audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\"></audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ai0220_AskIMEIGophoneFallback2_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ai0218_IMEIVisibleWait_DM = \"no\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.transferReason\n              value: BUSINESS_RULE\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.transferPrompt\n              value: default\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.M09_MainMenu_Dialog\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_ai0218_IMEIVisibleWait_DM = \"hold_on\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.from_ai0218\n                  value: true\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/ai0218_out_02.wav\\\">Sure </audio>\"\n                      - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\"></audio>\"\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: ai0217_EnterCodeWait_DM\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_ai0218_IMEIVisibleWait_DM = \"ready\", true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: ai0218_IMEIVisibleWait_DM\n```", "timestamp": 1749469568.9041286, "content_hash": "f0d91b53db845e8e5687055f91017c4b"}