{"response": "```yaml\n- kind: Question\n  id: LP1030_SuspendLineYN_DM\n  displayName: LP1030_SuspendLineYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/LP1030_nm1_01.wav\\\">Just say yes or no</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/LP1030_nm2_01.wav\\\">If you would like to temporarily suspend service to your phone line, say yes Otherwise, say no</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/LP1030_nm2_01.wav\\\">If you would like to temporarily suspend service to your phone line, say yes Otherwise, say no</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.LP1030_SuspendLineYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/LP1030_ini_01.wav\\\">To make sure no one can use your phone  in the meantime, and to avoid paying for the days you cant use it, you can temporarily suspend that line </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/LP1030_ini_02.wav\\\">Would you like to do that now</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/LP1030_nm1_01.wav\\\">Just say yes or no</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/LP1030_nm2_01.wav\\\">If you would like to temporarily suspend service to your phone line, say yes Otherwise, say no</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/LP1030_nm2_01.wav\\\">If you would like to temporarily suspend service to your phone line, say yes Otherwise, say no</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_LP1030_SuspendLineYN_DM\n  value: =Text(Global.LP1030_SuspendLineYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_LP1030_SuspendLineYN_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.playTransferMessage\n          value: true\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/LP1030_out_01.wav\\\">Okay</audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: LP1010_GoToCallTransfer_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_LP1030_SuspendLineYN_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/LP1030_out_02.wav\\\">No problem</audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: LP1015_GoToGoodbye_SD\n```", "timestamp": 1749528655.6767867, "content_hash": "c1c0e94e006cd712e007d9bf032ff0ca"}