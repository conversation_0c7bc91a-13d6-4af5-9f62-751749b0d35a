{"response": "```yaml\n- kind: Question\n  id: ES1220_CollectIMEI_DM\n  displayName: ES1220_CollectIMEI_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ES1220_nm1_01.wav\\\">Please enter the 15 digit I M E I for your phone You can skip any numbers that come after a slash To hear how you can find the number again, say  instructions  </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ES1220_nm2_01.wav\\\">If you have the 15 digit IMEI number for your new phone, please enter it now on your keypad Skip any numbers that come after a slash To repeat the instructions to find the number, press star </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ES1220_nm2_01.wav\\\">If you have the 15 digit IMEI number for your new phone, please enter it now on your keypad Skip any numbers that come after a slash To repeat the instructions to find the number, press star </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ES1220_CollectIMEI_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.enteringFrom = \"ES1225_IMEIPassChecksum_DS\",\n            \"That does not look like a valid number Let s try one more time to be sure If you have two different numbers, look for the one labeled I M E I To hear the instructions to  find  the number, press star Otherwise, go ahead and enter it again \",\n            \"If you see a slash followed by two numbers after your serial number, you dont need to enter those So, what s the 15 digit I M E I number? You can say or enter it \"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: instructions\n          displayName: instructions\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ES1220_nm1_01.wav\\\">Please enter the 15 digit I M E I for your phone You can skip any numbers that come after a slash To hear how you can find the number again, say  instructions  </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ES1220_nm2_01.wav\\\">If you have the 15 digit IMEI number for your new phone, please enter it now on your keypad Skip any numbers that come after a slash To repeat the instructions to find the number, press star </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ES1220_nm2_01.wav\\\">If you have the 15 digit IMEI number for your new phone, please enter it now on your keypad Skip any numbers that come after a slash To repeat the instructions to find the number, press star </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ES1220_CollectIMEI_DM\n  value: =Text(Global.ES1220_CollectIMEI_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ES1220_CollectIMEI_DM = \"instructions\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/ES1220_out_01.wav\\\">Sure, instructions </audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ES1210_FindIMEIInstructions_PP\n\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.GlobalVars.imeiSerialNumber\n      value: ES1220_CollectIMEI_DM.returnvalue\n\n    - kind: GotoAction\n      id: goto_REPLACE_THIS\n      actionId: ES1225_IMEIPassChecksum_JDA\n```", "timestamp": 1749528100.6697156, "content_hash": "66322fbfbe18085e0446a66884bb1db0"}