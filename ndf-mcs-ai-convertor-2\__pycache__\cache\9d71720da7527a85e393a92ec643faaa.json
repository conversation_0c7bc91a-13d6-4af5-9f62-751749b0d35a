{"response": "```yaml\n- kind: Question\n  id: cp0305_AskSetAccountPIN_DM\n  displayName: cp0305_AskSetAccountPIN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/cp0305_ni1_01.wav\\\">You'll need either your prepaid device or access to an email address that's been on file for at least 30 days to set your account PIN Do you want to go ahead and do that now? Please say yes or no</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/cp0305_ni2_01.wav\\\">If you want to set your account PIN, say yes or press 1 Otherwise say no or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.cp0305_AskSetAccountPIN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/cp0305_ini_01.wav\\\">For your security, you'll need to set an account PIN To do that, you'll need either your prepaid device or access to an email address that's been on file for at least 30 days</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/cp0305_ini_02.wav\\\">Are you ready to do that now?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/cp0305_ni1_01.wav\\\">You'll need either your prepaid device or access to an email address that's been on file for at least 30 days to set your account PIN Do you want to go ahead and do that now? Please say yes or no</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/cp0305_ni2_01.wav\\\">If you want to set your account PIN, say yes or press 1 Otherwise say no or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_cp0305_AskSetAccountPIN_DM\n  value: =Text(Global.cp0305_AskSetAccountPIN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_cp0305_AskSetAccountPIN_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.setNewPINLandline\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_commandgrammar\n          value: GlobalCommands.grxml?SWI_vars.operator_cnf_mode=NEVER\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_dtmfcommandgrammar\n          value: GlobalCommands_dtmf.grxml?SWI_vars.operator_cnf_mode=NEVER\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/cp0305_out_01.wav\\\">Okay</audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: cp0120_AskAccountZipCode_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_cp0305_AskSetAccountPIN_DM = \"false\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.transferDestination\n              value: attPrepaid\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.lastTransferState\n              value: cp0305_AskSetAccountPIN_DM\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.transferReason\n              value: BUSINESS_RULE\n\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.M09_MainMenu.dvxml#ma9210_NeedTransferType_DS\n```", "timestamp": **********.0359342, "content_hash": "9d71720da7527a85e393a92ec643faaa"}