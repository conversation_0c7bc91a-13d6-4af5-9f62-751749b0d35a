{"response": "```yaml\n- kind: Question\n  id: aa1075_OfflineWrapUpDTMF_DM\n  displayName: aa1075_OfflineWrapUpDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"\"\n\n  alwaysPrompt: true\n  variable: Global.aa1075_OfflineWrapUpDTMF_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa1075_ini_01.wav\\\">If you would you like to speak to a customer service representative, press 1 If you re done, feel free to hang up</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: operator\n          displayName: operator\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa1075_OfflineWrapUpDTMF_DM\n  value: =Text(Global.aa1075_OfflineWrapUpDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa1075_OfflineWrapUpDTMF_DM = \"operator\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.transferReason\n          value: accountOffline\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.transferAllowed\n          value: true\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/gl_operator_01.wav\\\">Let me transfer you to a customer service representative</audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa9805_ProcessTransfer_PP\n```", "timestamp": **********.7952824, "content_hash": "af45dcc9d2023ac5a60cfd77afb8326e"}