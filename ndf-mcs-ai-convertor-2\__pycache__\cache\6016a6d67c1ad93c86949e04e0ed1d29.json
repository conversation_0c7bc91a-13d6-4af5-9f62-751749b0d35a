{"response": "```yaml\n- kind: Question\n  id: aa6040_CCFinalSixDigitsOfPIDDTMF_DM\n  displayName: aa6040_CCFinalSixDigitsOfPIDDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa6040_nm2_01.wav\\\">Sorry, I still didn t get that</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/aa6040_CCFinalSixDigitsOfPIDDTMF_DM_initial.wav\\\"></audio>\"\n\n  alwaysPrompt: true\n  variable: Global.aa6040_CCFinalSixDigitsOfPIDDTMF_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n\n            Global.childCareInfoVariables.collectedFirstLetter = \"a\",\n            \"Ok, now still using just your keypad, please enter the six digits following the  A \",\n\n            Global.childCareInfoVariables.collectedFirstLetter = \"b\",\n            \"Ok, now still using just your keypad, please enter the six digits following the  B \",\n\n            Global.childCareInfoVariables.collectedFirstLetter = \"c\",\n            \"Ok, now still using just your keypad, please enter the six digits following the  C \",\n\n            Global.childCareInfoVariables.collectedFirstLetter = \"d\",\n            \"Ok, now still using just your keypad, please enter the six digits following the  D \",\n\n            Global.childCareInfoVariables.collectedFirstLetter = \"e\",\n            \"Ok, now still using just your keypad, please enter the six digits following the  E \",\n\n            Global.childCareInfoVariables.collectedFirstLetter = \"f\",\n            \"Ok, now still using just your keypad, please enter the six digits following the  F \",\n\n            Global.childCareInfoVariables.collectedFirstLetter = \"g\",\n            \"Ok, now still using just your keypad, please enter the six digits following the  G \",\n\n            \"Ok, now still using just your keypad, please enter the six digits following the  H \"\n        )}\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/aa6040_ni2_01.wav\\\">Sorry, I still didn t get anything</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/aa6040_CCFinalSixDigitsOfPIDDTMF_DM_initial.wav\\\"></audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.globalVariables.cameFrom\n  value: aa6040\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.childCareInfoVariables.collectedPidSixDigits\n  value: aa6040_CCFinalSixDigitsOfPIDDTMF_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.childCareInfoVariables.collectedProviderId\n  value: childCareInfoVariables.collectedFirstLetter.concat(childCareInfoVariables.collectedPidSixDigits)\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: aa6033_CheckSumPass_MW_DA\n```", "timestamp": **********.9267673, "content_hash": "6016a6d67c1ad93c86949e04e0ed1d29"}