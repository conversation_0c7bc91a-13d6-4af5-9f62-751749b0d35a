{"response": "```yaml\n- kind: Question\n  id: BC1005_GetCardNumber_DM\n  displayName: BC1005_GetCardNumber_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1005_nm1_01.wav\\\">Please enter your card number now, or say 'more info'</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1005_nm2_01.wav\\\">If you have your card ready, please enter the number using your keypad If you have more questions, say 'more info' or press star</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1005_nm3_01.wav\\\">Please enter your card number now For more information, press star</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.BC1005_GetCardNumber_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            // Main prompt\n            true,\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/BC1005_ini_05.wav\\\">Now I ll get your card details and I ll read them back to you You can say or enter each of them If I get something wrong, you can say that s not right otherwise we ll move on</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\",\n              \"{Switch( true, Global.preferredPaymentMethod = \\\"credit\\\", \\\"<audio src=\\\\\\\"AUDIO_LOCATION/BC1005_ini_02.wav\\\\\\\">First, what's your credit card number?</audio>\\\", Global.preferredPaymentMethod <> \\\"credit\\\", \\\"<audio src=\\\\\\\"AUDIO_LOCATION/BC1005_ini_03.wav\\\\\\\">First, what's your debit card number?</audio>\\\")}\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n            ]\n        )}\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1005_nm1_01.wav\\\">Please enter your card number now, or say 'more info'</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1005_nm2_01.wav\\\">If you have your card ready, please enter the number using your keypad If you have more questions, say 'more info' or press star</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1005_nm3_01.wav\\\">Please enter your card number now For more information, press star</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.saidOperator\n  value: \"GlobalVars.saidOperatorBC1005?GlobalVars.saidOperatorBC1005:false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.preferredPaymentMethod\n  value: GlobalVars.preferredPaymentMethod\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.failedChecksum\n  value: GlobalVars.failedChecksum\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.disconfirmedDetails\n  value: GlobalVars.disconfirmedDetails\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.correctAllDetails\n  value: GlobalVars.correctAllDetails\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.callType\n  value: GlobalVars.callType\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: GlobalVars.saidOperatorBC1005\n  value: false\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: GlobalVars.bankCardNumber\n  value: BC1005_GetCardNumber_DM.returnvalue\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(GlobalVars.bankCardNumber.substring(0,2) = \"34\" || GlobalVars.bankCardNumber.substring(0,2) = \"37\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: GlobalVars.cardTypeAmex\n          value: true\n\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: GlobalVars.cardTypeAmex\n      value: false\n\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: |\n            =If(GlobalVars.disconfirmedDetails = true && GlobalVars.correctAllDetails = false, true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: |\n            =If(GlobalVars.bankCardNumber.substring(0,2) = \"65\" || GlobalVars.bankCardNumber.substring(0,4) = \"6011\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: GlobalVars.tryOtherCardReason\n              value: failed_checksum\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.unsupportedCardIssuer\n              value: discover\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: BC1015_UseOtherCardYN_DM\n\n      elseActions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/BC1005_out_01.wav\\\">Thanks</audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: BC1010_VerifyChecksum_JDA\n```", "timestamp": **********.236656, "content_hash": "a499e56b824a8cae82bea5ca46ed6872"}