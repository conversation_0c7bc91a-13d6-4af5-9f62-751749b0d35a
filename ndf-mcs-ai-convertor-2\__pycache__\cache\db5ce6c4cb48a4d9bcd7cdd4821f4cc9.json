{"response": "```yaml\n- kind: Question\n  id: MP1050_ConfirmRatePlan_DM\n  displayName: MP1050_ConfirmRatePlan_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MP1050_nm1_01.wav\\\">Please say  yes  or  no  You wanted</audio>{Global.selectedPlan}<audio src=\\\"AUDIO_LOCATION/MP1050_nm1_02.wav\\\">is that right?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MP1050_nm2_01.wav\\\">Please say  yes  or press 1, or say  no  or press 2  You wanted</audio>{Global.selectedPlan}<audio src=\\\"AUDIO_LOCATION/MP1050_nm2_02.wav\\\">Is that right?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MP1050_nm3_01.wav\\\">If you wanted</audio>{Global.selectedPlan}<audio src=\\\"AUDIO_LOCATION/MP1050_nm3_02.wav\\\">say  yes  or press 1  If not, say  no  or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.MP1050_ConfirmRatePlan_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.onlyOneOrAmbiguous_MP1050 = true,\n            \"<audio src=\\\"AUDIO_LOCATION/MP1050_ini_01.wav\\\">So, just to reconfirm, you ll activate your phone with</audio>{Global.selectedPlan}<audio src=\\\"AUDIO_LOCATION/MP1050_ini_03.wav\\\">Is that right?</audio>\",\n            Global.onlyOneOrAmbiguous_MP1050 = false,\n            \"<audio src=\\\"AUDIO_LOCATION/MP1050_ini_02.wav\\\">Just to be sure, that s</audio>{Global.selectedPlan}<audio src=\\\"AUDIO_LOCATION/MP1050_ini_03.wav\\\">Is that right?</audio>\",\n            \"<audio src=\\\"AUDIO_LOCATION/MP1050_ini_01.wav\\\">So, just to reconfirm, you ll activate your phone with</audio>{Global.selectedPlan}<audio src=\\\"AUDIO_LOCATION/MP1050_ini_03.wav\\\">Is that right?</audio>\"\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/MP1050_nm1_01.wav\\\">Please say  yes  or  no  You wanted</audio>{Global.selectedPlan}<audio src=\\\"AUDIO_LOCATION/MP1050_nm1_02.wav\\\">is that right?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MP1050_nm2_01.wav\\\">Please say  yes  or press 1, or say  no  or press 2  You wanted</audio>{Global.selectedPlan}<audio src=\\\"AUDIO_LOCATION/MP1050_nm2_02.wav\\\">Is that right?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MP1050_nm3_01.wav\\\">If you wanted</audio>{Global.selectedPlan}<audio src=\\\"AUDIO_LOCATION/MP1050_nm3_02.wav\\\">say  yes  or press 1  If not, say  no  or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.onlyOneOrAmbiguous_MP1050\n  value: GlobalVars.onlyOneOrAmbiguous_MP1050\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.selectedPlan\n  value: GlobalVars.selectedPlan\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_MP1050_ConfirmRatePlan_DM\n  value: =Text(Global.MP1050_ConfirmRatePlan_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MP1050_ConfirmRatePlan_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/MP1050_out_02.wav\\\">Great</audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: MP1065_AssignMonthlyPlan_JDA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_MP1050_ConfirmRatePlan_DM = \"false\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.GlobalVars.noToConfirm_MP1050 < 2, true, false)\n                  actions:\n                    - kind: ConditionGroup\n                      id: conditionGroup_REPLACE_THIS\n                      conditions:\n                        - id: conditionItem_REPLACE_THIS\n                          condition: =If(numberOfRatePlans(Global.GlobalVars.ratePlans) > 1, true, false)\n                          actions:\n                            - kind: SendActivity\n                              id: sendActivity_REPLACE_THIS\n                              activity:\n                                speak:\n                                  - \"<audio src=\\\"AUDIO_LOCATION/MP1050_out_04.wav\\\">Sorry about that</audio>\"\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.noToConfirm_MP1050\n                      value: GlobalVars.noToConfirm_MP1050 + 1\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: MP1060_CheckNumPlansAvail_JDA\n\n              elseActions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/MP1050_out_05.wav\\\">I m sorry for this trouble</audio>\"\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.activationResult\n                  value: 'transfer'\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: getReturnLink()\n```", "timestamp": 1749528871.6482732, "content_hash": "db5ce6c4cb48a4d9bcd7cdd4821f4cc9"}