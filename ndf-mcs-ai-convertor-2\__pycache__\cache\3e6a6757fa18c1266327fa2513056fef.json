{"response": "```yaml\n- kind: Question\n  id: ma1355_MultilineInfoWrap_DM\n  displayName: ma1355_MultilineInfoWrap_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ma1355_ni1_01.wav\\\">Say Repeat or press 1 Or if you dont need any more help, just hang up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ma1355_ni1_01.wav\\\">Say Repeat or press 1 Or if you dont need any more help, just hang up</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ma1355_MultilineInfoWrap_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ma1355_ni1_01.wav\\\">Say Repeat or press 1 Or if you dont need any more help, just hang up</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: operator\n          displayName: operator\n        - id: mainmenu\n          displayName: mainmenu\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ma1355_ni1_01.wav\\\">Say Repeat or press 1 Or if you dont need any more help, just hang up</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ma1355_MultilineInfoWrap_DM\n  value: =Text(Global.ma1355_MultilineInfoWrap_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma1355_MultilineInfoWrap_DM = \"operator\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.lastTransferState\n          value: ma1355_MultilineInfoWrap_DM\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferReason\n          value: AGENT_REQUEST\n\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M09_MainMenu_Dialog\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ma1355_MultilineInfoWrap_DM = \"mainmenu\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: ma1334_InitiateMainMenu_JDA_DA\n```", "timestamp": 1749471526.192376, "content_hash": "3e6a6757fa18c1266327fa2513056fef"}