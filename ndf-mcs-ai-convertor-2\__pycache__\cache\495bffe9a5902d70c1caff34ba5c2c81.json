{"response": "```yaml\n- kind: Question\n  id: aa1049_Confirm4Digits_DM\n  displayName: aa1049_Confirm4Digits_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - aa1049_Confirm4Digits_DM_initial\n      - aa1049_Confirm4Digits_DM_initial\n\n  alwaysPrompt: true\n  variable: Global.aa1049_Confirm4Digits_DM_reco\n  prompt:\n    speak:\n      - \"Let me be sure, I got {Global.cardInfoVariables.collectedCardNumLastFour} Is that correct?\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - aa1049_Confirm4Digits_DM_initial\n        - aa1049_Confirm4Digits_DM_initial\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa1049_Confirm4Digits_DM\n  value: =Text(Global.aa1049_Confirm4Digits_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa1049_Confirm4Digits_DM = \"yes\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.checkCardsMatchInfo.searchForCardHolder = true, true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: aa1085_GetCurrentCardInfo_DB_DA\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.globalVariables.currentTask = \"cardReplacement\", true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: aa1054_LostStolenNeedAdditionalInfo_PP\n\n              elseActions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: aa1031_FullCardInputMode_JDA\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/aa1049_out_03.wav\\\">Sorry Let s try getting your card number another way</audio>\"\n\n    # else for \"yes\"\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa1049_Confirm4Digits_DM = \"no\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.fourDigitEntryCounter\n              value: globalVariables.fourDigitEntryCounter + 1\n\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.globalVariables.fourDigitEntryCounter = 1, true, false)\n                  actions:\n                    - kind: ConditionGroup\n                      id: conditionGroup_REPLACE_THIS\n                      conditions:\n                        - id: conditionItem_REPLACE_THIS\n                          condition: =If(Global.globalVariables.currentTask = \"cardReplacement\", true, false)\n                          actions:\n                            - kind: GotoAction\n                              id: goto_REPLACE_THIS\n                              actionId: aa1052_CollectLostStolenLastFourDTMF_DM\n                            - kind: SendActivity\n                              id: sendActivity_REPLACE_THIS\n                              activity:\n                                speak:\n                                  - \"<audio src=\\\"AUDIO_LOCATION/aa1049_out_01.wav\\\">Alright, let s try again</audio>\"\n\n                      elseActions:\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: aa1042_GetCardNumberLastFour_DM\n                        - kind: SendActivity\n                          id: sendActivity_REPLACE_THIS\n                          activity:\n                            speak:\n                              - \"<audio src=\\\"AUDIO_LOCATION/aa1049_out_04.wav\\\">Alright, let s try again</audio>\"\n\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.globalVariables.currentTask = \"cardReplacement\", true, false)\n                      actions:\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: aa1054_LostStolenNeedAdditionalInfo_PP\n\n                  elseActions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: aa1031_FullCardInputMode_JDA\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/aa1049_out_02.wav\\\">Sorry Let s try getting your card number another way</audio>\"\n```", "timestamp": 1749458487.3059425, "content_hash": "495bffe9a5902d70c1caff34ba5c2c81"}