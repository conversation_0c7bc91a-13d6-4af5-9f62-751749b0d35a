{"response": "```yaml\n- kind: Question\n  id: ma2448_esimActivationFinalStep_DM\n  displayName: ma2448_esimActivationFinalStep_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.ma2448_esimActivationFinalStep_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ma2448_ini_02.wav\\\">Finally, get the ESIM activation card from your package and point your camera at the QR code Your phone will prompt you through the remaining steps to download your eSIM When youre ready say continue</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/percolation_20s.wav\\\">20 seconds of hold music</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ma2448_ini_03.wav\\\">Remember, get the ESIM activation card from your package and point your camera at the QR code Your phone will prompt you through the remaining steps to download your eSIM When youre ready say continue You can also say Im having issues</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/percolation_20s.wav\\\">20 seconds of hold music</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ma2448_ini_03.wav\\\">Remember, get the ESIM activation card from your package and point your camera at the QR code Your phone will prompt you through the remaining steps to download your eSIM When youre ready say continue You can also say Im having issues</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/percolation_20s.wav\\\">20 seconds of hold music</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ma2448_ini_03.wav\\\">Remember, get the ESIM activation card from your package and point your camera at the QR code Your phone will prompt you through the remaining steps to download your eSIM When youre ready say continue You can also say Im having issues</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/percolation_20s.wav\\\">20 seconds of hold music</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ma2448_ini_03.wav\\\">Remember, get the ESIM activation card from your package and point your camera at the QR code Your phone will prompt you through the remaining steps to download your eSIM When youre ready say continue You can also say Im having issues</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/percolation_20s.wav\\\">20 seconds of hold music</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ma2448_ini_03.wav\\\">Remember, get the ESIM activation card from your package and point your camera at the QR code Your phone will prompt you through the remaining steps to download your eSIM When youre ready say continue You can also say Im having issues</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/percolation_20s.wav\\\">20 seconds of hold music</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ma2448_ini_03.wav\\\">Remember, get the ESIM activation card from your package and point your camera at the QR code Your phone will prompt you through the remaining steps to download your eSIM When youre ready say continue You can also say Im having issues</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/percolation_20s.wav\\\">20 seconds of hold music</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ma2448_ini_03.wav\\\">Remember, get the ESIM activation card from your package and point your camera at the QR code Your phone will prompt you through the remaining steps to download your eSIM When youre ready say continue You can also say Im having issues</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/percolation_20s.wav\\\">20 seconds of hold music</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ma2448_ini_03.wav\\\">Remember, get the ESIM activation card from your package and point your camera at the QR code Your phone will prompt you through the remaining steps to download your eSIM When youre ready say continue You can also say Im having issues</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/percolation_20s.wav\\\">20 seconds of hold music</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: continue\n          displayName: continue\n        - id: having_issues\n          displayName: having_issues\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ma2448_ni1_01.wav\\\">Sorry, Im having trouble understanding you For more information, go to att dot com slash e SIM</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\"></audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ma2448_esimActivationFinalStep_DM\n  value: =Text(Global.ma2448_esimActivationFinalStep_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma2448_esimActivationFinalStep_DM = \"continue\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ma2450_eSimActivationCompleted_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ma2448_esimActivationFinalStep_DM = \"having_issues\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/ma2448_out_01.wav\\\">Im sorry to hear that</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: M09_MainMenu.dvxml\n```", "timestamp": 1749471591.6593058, "content_hash": "adb3af0fd2e1b40a8a88cc874028c0c1"}