{"response": "```yaml\n- kind: Question\n  id: bc0420_AskContactNumber_DM\n  displayName: bc0420_AskContactNumber_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"com.nuance.att.application.audio.bc0420_AskContactNumber_DM_noinput_1\"\n      - \"com.nuance.att.application.audio.bc0420_AskContactNumber_DM_noinput_2\"\n\n  alwaysPrompt: true\n  variable: Global.bc0420_AskContactNumber_DM_reco\n  prompt:\n    speak:\n      - \"{Audio: com.nuance.att.application.audio.bc0420_AskContactNumber_initial}\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"com.nuance.att.application.audio.bc0420_AskContactNumber_DM_noinput_1\"\n        - \"com.nuance.att.application.audio.bc0420_AskContactNumber_DM_noinput_2\"\n    defaultValueMissingAction: Escalate\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.frombc0420 = true && Global.optionChosenbc0420 = \"no\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_dtmfgrammar1\n          value: 'bc0420_AskContactNumber_DM_dtmf.grxml?SWI_vars.disallow=yes^no&'\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_grammar1\n          value: 'bc0420_AskContactNumber_DM.grxml?SWI_vars.disallow=yes^no&'\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: |\n            =If(Global.frombc0415 = true, true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.collection_dtmfgrammar1\n              value: 'bc0420_AskContactNumber_DM_dtmf.grxml?SWI_vars.disallow=phone_number&'\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.collection_grammar1\n              value: 'bc0420_AskContactNumber_DM.grxml?SWI_vars.disallow=phone_number&'\n\n      elseActions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_dtmfgrammar1\n          value: 'bc0420_AskContactNumber_DM_dtmf.grxml?SWI_vars.disallow=none&'\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_grammar1\n          value: 'bc0420_AskContactNumber_DM.grxml?SWI_vars.disallow=none&'\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_bc0420_AskContactNumber_DM\n  value: =Text(Global.bc0420_AskContactNumber_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_bc0420_AskContactNumber_DM = \"yes\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.contactPhoneNumber\n          value: bankPhoneNumber\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/bc0420_out_02.wav\\\">Thanks</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: bc0425_StoreContact_DB_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_bc0420_AskContactNumber_DM = \"no\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.frombc0415\n              value: false\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.frombc0420\n              value: true\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.optionChosenbc0420\n              value: no\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: bc0420_AskContactNumber_DM\n\n      elseActions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.contactPhoneNumber\n          value: bc0420_AskContactNumber_DM.returnvalue\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/bc0420_out_01.wav\\\">Thanks</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: bc0425_StoreContact_DB_DA\n```\n**Notes:**\n- The `prompt` and `unrecognizedPrompt` use the className as a string, as the XML only provides a custom class reference, not a text or audio file.\n- The `SendActivity` is used to play the \"Thanks\" audio after success actions, as per the pattern in Example 7.\n- All `SetVariable` and `GotoAction` ids are set to `setVariable_REPLACE_THIS` and `goto_REPLACE_THIS` as per your placeholder instructions.\n- The nested `if/elseif/else` for grammar selection is implemented as nested `ConditionGroup` and `elseActions`.\n- The `entity` is set to `BooleanPrebuiltEntity` because the success actions are for \"yes\" and \"no\".\n- The `variable` is set to `Global.bc0420_AskContactNumber_DM_reco` as per your instructions.\n- The `inputTimeoutResponse` and `unrecognizedPrompt` use the className as a string, as there is no text or audiofile in those custom prompts. If you want to use a different format for custom className prompts, please specify.", "timestamp": 1749470179.5291176, "content_hash": "dfc9c651887d2b5546a983999d99bc83"}