{"response": "```yaml\n- kind: Question\n  id: aa6090_CCDefaultWrapUpDTMF_DM\n  displayName: aa6090_CCDefaultWrapUpDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa6090_ini_01.wav\\\">To hear that again, press 1 To go to the main menu, press 2 Or if you re done, simply hang up</audio>\"\n      - |\n        {Switch(\n              true,\n              Global.dnisInfo.dtmfOnlyFlag = true,\n              \"Sorry, I still didn t get that\",\n          \n              true,\n              \"Sorry, I still didn t get that Let s try it another way\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.aa6090_CCDefaultWrapUpDTMF_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa6090_ini_01.wav\\\">To hear that again, press 1 To go to the main menu, press 2 Or if you re done, simply hang up</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: repeat\n          displayName: repeat\n        - id: main_menu\n          displayName: main_menu\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa6090_CCDefaultWrapUpDTMF_DM\n  value: =Text(Global.aa6090_CCDefaultWrapUpDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa6090_CCDefaultWrapUpDTMF_DM = \"repeat\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.globalVariables.cameFrom = \"aa6020\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.globalVariables.cameFrom\n                  value: ''\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: aa6020_CCMustHavePinSelected_PP\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.globalVariables.cameFrom = \"aa6031\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.globalVariables.cameFrom\n                      value: ''\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: aa6031_CCMustGetPIDFromProvider_PP\n\n              elseActions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: aa6075_CCPaymentSuccessful_PP\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa6090_CCDefaultWrapUpDTMF_DM = \"main_menu\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.nextStep\n          value: main_menu\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa1015_NextStepHandling_JDA\n```", "timestamp": **********.7478771, "content_hash": "a50770bc05373567bcd762870c8bb6c6"}