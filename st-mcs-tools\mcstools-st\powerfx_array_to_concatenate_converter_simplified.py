#!/usr/bin/env python3
"""
PowerFX Array to Concatenate Converter
=====================================
Converts PowerFX arrays to Concatenate() expressions in YAML files.
Example: ["a", "b"] → Concatenate("a", "b")
"""

import os
import re
from pathlib import Path
from ruamel.yaml import YAML

class PowerFXConcatenateConverter:
    def __init__(self):
        # Configure YAML parser
        self.yaml = YAML()
        self.yaml.preserve_quotes = True
        self.yaml.width = 4096

        # Array patterns (simplified)
        self.array_pattern = re.compile(
            r'\[\s*("[^"]*"(?:\s*,\s*"[^"]*")*)\s*\]',
            re.DOTALL
        )
        
        # Mixed arrays (variables + strings)
        self.mixed_array_pattern = re.compile(
            r'\[\s*((?:"[^"]*"|[^",\]]+)(?:\s*,\s*(?:"[^"]*"|[^",\]]+))*)\s*\]',
            re.DOTALL
        )

    def convert_array(self, array_str: str) -> str:
        """Convert array string to Concatenate() call"""
        inner = array_str[1:-1].strip()  # Remove brackets
        
        # Split elements (handling quotes and variables)
        elements = []
        current = ""
        in_quotes = False
        
        for char in inner:
            if char == '"' and not (current.endswith("\\")):
                in_quotes = not in_quotes
            elif char == "," and not in_quotes:
                elements.append(current.strip())
                current = ""
                continue
            
            current += char
        
        if current.strip():  # Add last element
            elements.append(current.strip())

        # Format as Concatenate()
        return f"Concatenate({', '.join(elements)})"

    def process_file(self, file_path: Path):
        """Process a single YAML file"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Convert all arrays (simple + mixed)
        modified = False
        for pattern in [self.array_pattern, self.mixed_array_pattern]:
            new_content = pattern.sub(
                lambda m: self.convert_array(m.group()), 
                content
            )
            if new_content != content:
                modified = True
                content = new_content

        if modified:
            # Write backup first
            backup = file_path.with_suffix(file_path.suffix + '.bak')
            os.replace(file_path, backup)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)

            print(f"Processed: {file_path} (backup: {backup})")

    def process_directory(self, directory: Path):
        """Process all YAML files in a directory"""
        for path in directory.glob("*.y*ml"):
            if path.suffix in ('.yml', '.yaml'):
                try:
                    self.process_file(path)
                except Exception as e:
                    print(f"Error processing {path}: {str(e)}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser()
    parser.add_argument("path", help="Directory or file to process")
    args = parser.parse_args()

    converter = PowerFXConcatenateConverter()
    path = Path(args.path)
    
    if path.is_dir():
        converter.process_directory(path)
    elif path.exists():
        converter.process_file(path)
    else:
        print(f"Path doesn't exist: {path}")