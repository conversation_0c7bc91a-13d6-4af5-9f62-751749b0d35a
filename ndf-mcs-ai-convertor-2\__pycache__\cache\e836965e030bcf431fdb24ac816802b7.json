{"response": "```yaml\n- kind: Question\n  id: DH1415_AcceptTermsYN_DM\n  displayName: DH1415_AcceptTermsYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DH1415_nm1_01.wav\\\">You can read our terms and conditions of service on metro by T dash mobile dot com slash terms Do you accept them? </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DH1415_nm2_01.wav\\\">If you accept our terms and conditions of service, say 'yes' or press 1 Otherwise, say 'no' or press 2  </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DH1415_nm2_01.wav\\\">If you accept our terms and conditions of service, say 'yes' or press 1 Otherwise, say 'no' or press 2  </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.DH1415_AcceptTermsYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/DH1415_ini_01.wav\\\">First, do you accept our terms and conditions of service? They're on metro by T dash mobile dot com slash terms</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/DH1415_nm1_01.wav\\\">You can read our terms and conditions of service on metro by T dash mobile dot com slash terms Do you accept them? </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DH1415_nm2_01.wav\\\">If you accept our terms and conditions of service, say 'yes' or press 1 Otherwise, say 'no' or press 2  </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DH1415_nm2_01.wav\\\">If you accept our terms and conditions of service, say 'yes' or press 1 Otherwise, say 'no' or press 2  </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_DH1415_AcceptTermsYN_DM\n  value: =Text(Global.DH1415_AcceptTermsYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_DH1415_AcceptTermsYN_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/DH1415_out_01.wav\\\">Thanks</audio>\"\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.activationEntryPoint\n          value: care\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: activate\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: DH1420_Activation_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_DH1415_AcceptTermsYN_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/DH1415_out_02.wav\\\">No problem You can read them and then set up an account at metro by t dash mobile dot com </audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: DH1315_Goodbye_SD\n```", "timestamp": **********.9803848, "content_hash": "e836965e030bcf431fdb24ac816802b7"}