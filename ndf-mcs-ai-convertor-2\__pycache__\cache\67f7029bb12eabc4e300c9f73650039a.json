{"response": "```yaml\n- kind: Question\n  id: LG1405_DSGCollectMDN_DM\n  displayName: LG1405_DSGCollectMDN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/LG1405_nm1_01.wav\\\">Please enter the phone number for the account you want to work with, including the area code </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/LG1405_nm2_01.wav\\\">Please enter the phone number for the account you want to work with, including the area code </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/LG1405_nm2_01.wav\\\">Please enter the phone number for the account you want to work with, including the area code </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.LG1405_DSGCollectMDN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/LG1405_ini_01.wav\\\">First, please enter the 10-digit phone number </audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items: []\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/LG1405_nm1_01.wav\\\">Please enter the phone number for the account you want to work with, including the area code </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/LG1405_nm2_01.wav\\\">Please enter the phone number for the account you want to work with, including the area code </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/LG1405_nm2_01.wav\\\">Please enter the phone number for the account you want to work with, including the area code </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.accountPinToggleOn\n  value: GlobalVars.GetBCSParameters.accountPinToggleOn\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.MDN\n  value: LG1405_DSGCollectMDN_DM.returnvalue\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: LG1410_DSGGetAccountDetails_DB_DA\n```", "timestamp": **********.5385206, "content_hash": "67f7029bb12eabc4e300c9f73650039a"}