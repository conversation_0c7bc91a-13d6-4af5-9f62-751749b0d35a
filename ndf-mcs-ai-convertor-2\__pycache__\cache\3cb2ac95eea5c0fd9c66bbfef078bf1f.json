{"response": "```yaml\n- kind: Question\n  id: pr0425_StillChangePlan_DM\n  displayName: pr0425_StillChangePlan_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I missed that</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/pr0425_ni1_01.wav\\\">To continue with your plan change, say Yes To cancel, say No</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">Sorry</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/pr0425_ni1_01.wav\\\">To continue with your plan change, say Yes To cancel, say No</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.pr0425_StillChangePlan_DM_reco\n  prompt:\n    speak:\n      - \"{CustomAudio: com.nuance.att.application.audio.pr0425_StillChangePlan_DM_initial}\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/pr0425_ni1_01.wav\\\">To continue with your plan change, say Yes To cancel, say No</audio>\"\n        - \"{CustomAudio: com.nuance.att.application.audio.pr0425_StillChangePlan_DM_noinput_2}\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.requiredBalance\n  value: \"\"\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_pr0425_StillChangePlan_DM\n  value: =Text(Global.pr0425_StillChangePlan_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pr0425_StillChangePlan_DM = \"true\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: pr0430_HaveAutoRefillStatus_JDA_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_pr0425_StillChangePlan_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/pr0425_out_01.wav\\\">Alright, Ill cancel your plan change</audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\"></audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: pr0440_BasicChangePlanWrap_DM\n```", "timestamp": 1749471783.1836312, "content_hash": "3cb2ac95eea5c0fd9c66bbfef078bf1f"}