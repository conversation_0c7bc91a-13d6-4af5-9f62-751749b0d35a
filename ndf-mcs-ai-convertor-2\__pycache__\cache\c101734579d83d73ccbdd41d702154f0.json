{"response": "```yaml\n- kind: Question\n  id: aw0110_AskWirelessNumber_DM\n  displayName: aw0110_AskWirelessNumber_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aw0110_ni1_01.wav\\\">Please enter your 10-digit wireless number, starting with the area code </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/aw0110_ni2_01.wav\\\">Using your phone keypad, please enter the 10 digit wireless number for your A T and T Prepaid account</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.aw0110_AskWirelessNumber_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.numMINAttempts = 0,\n            \"Please give me that wireless number, starting with the area code\",\n            \"Please give me your 10 digit wireless number again\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: dont_have\n          displayName: dont_have\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/aw0110_ni1_01.wav\\\">Please enter your 10-digit wireless number, starting with the area code </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/aw0110_ni2_01.wav\\\">Using your phone keypad, please enter the 10 digit wireless number for your A T and T Prepaid account</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aw0110_AskWirelessNumber_DM\n  value: =Text(Global.aw0110_AskWirelessNumber_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.callType = \"611\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_commandgrammar\n          value: 'GlobalCommands.grxml?SWI_vars.allow=mainmenu'\n\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.collection_commandgrammar\n      value: 'GlobalCommands.grxml'\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.Var_aw0110_AskWirelessNumber_DM = \"dont_have\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aw0110_out_01.wav\\\">Alright, let's try your SIM number instead </audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: getReturnLink()\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: |\n            =If(Global.Var_aw0110_AskWirelessNumber_DM = \"default\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.page_subscriberNumber\n              value: subscriberNumber\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.page_subscriberNumberPlayback\n              value: aw0110_AskWirelessNumber_DM.returnvalue\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.subscriberNumber\n              value: aw0110_AskWirelessNumber_DM.returnvalue\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.numMINAttempts\n              value: numMINAttempts + 1\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aw0115_CheckDBMIN_DB_DA\n```", "timestamp": 1749469492.4661055, "content_hash": "c101734579d83d73ccbdd41d702154f0"}