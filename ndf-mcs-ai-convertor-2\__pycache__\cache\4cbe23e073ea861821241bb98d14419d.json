{"response": "```yaml\n- kind: Question\n  id: TT1005_UsingDeviceWithProblemYN_DM\n  displayName: TT1005_UsingDeviceWithProblemYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TT1005_nm1_01.wav\\\">Are you calling on the device you're having trouble with?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TT1005_nm2_01.wav\\\">If you're calling on the device you're having trouble with, say 'yes' or press 1 If you're not, say 'no' or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.TT1005_UsingDeviceWithProblemYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/TT1005_ini_01.wav\\\">Are you calling on the device you're having trouble with?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/TT1005_ni1_01.wav\\\">Are you calling on the device you're having trouble with?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/TT1005_ni2_01.wav\\\">If you're calling on the device you're having trouble with, say 'yes' or press 1 If you're not, say 'no' or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_TT1005_UsingDeviceWithProblemYN_DM\n  value: =Text(Global.TT1005_UsingDeviceWithProblemYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TT1005_UsingDeviceWithProblemYN_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.usingBrokenDevice\n          value: yes\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TT1010_CallOnOtherDevice_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_TT1005_UsingDeviceWithProblemYN_DM = \"false\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.usingBrokenDevice\n              value: no\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: TT1015_OnNetworkTip_DM\n```", "timestamp": 1749530329.7634854, "content_hash": "4cbe23e073ea861821241bb98d14419d"}