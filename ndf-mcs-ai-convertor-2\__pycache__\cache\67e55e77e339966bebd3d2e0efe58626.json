{"response": "```yaml\n- kind: Question\n  id: SQ1050_ConfirmStreetNameYN_DM\n  displayName: SQ1050_ConfirmStreetNameYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SQ1050_nm1_01.wav\\\">Please say  yes  or  no   I recorded</audio>\"\n      - \"{Global.streetNameFilename}\"\n      - \"{Global.spellStreetFilename}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SQ1050_nm1_05.wav\\\">Is that right?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SQ1050_nm2_01.wav\\\">Please say  yes  or press one or  no  or press two  I recorded</audio>\"\n      - \"{Global.streetNameFilename}\"\n      - \"{Global.spellStreetFilename}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SQ1050_nm2_05.wav\\\">Is that right?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SQ1050_nm3_01.wav\\\">If the street name is</audio>\"\n      - \"{Global.streetNameFilename}\"\n      - \"{Global.spellStreetFilename}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SQ1050_nm3_05.wav\\\">Say  yes  or press one To re-record, say  no  or press two</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.SQ1050_ConfirmStreetNameYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/SQ1050_ini_01.wav\\\">I recorded</audio>\"\n      - \"{Global.streetNameFilename}\"\n      - \"{Global.spellStreetFilename}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SQ1050_ini_05.wav\\\">Is that right?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/SQ1050_nm1_01.wav\\\">Please say  yes  or  no   I recorded</audio>\"\n        - \"{Global.streetNameFilename}\"\n        - \"{Global.spellStreetFilename}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SQ1050_nm1_05.wav\\\">Is that right?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SQ1050_nm2_01.wav\\\">Please say  yes  or press one or  no  or press two  I recorded</audio>\"\n        - \"{Global.streetNameFilename}\"\n        - \"{Global.spellStreetFilename}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SQ1050_nm2_05.wav\\\">Is that right?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SQ1050_nm3_01.wav\\\">If the street name is</audio>\"\n        - \"{Global.streetNameFilename}\"\n        - \"{Global.spellStreetFilename}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SQ1050_nm3_05.wav\\\">Say  yes  or press one To re-record, say  no  or press two</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.streetNameFilename\n  value: \"'' + GlobalVars.streetNameFilename\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.spellStreetFilename\n  value: \"'' + GlobalVars.spellStreetFilename\"\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_SQ1050_ConfirmStreetNameYN_DM\n  value: =Text(Global.SQ1050_ConfirmStreetNameYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_SQ1050_ConfirmStreetNameYN_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.securityQuestion\n          value: street\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/SQ1050_out_01.wav\\\">Thanks, someone will listen to that too and update your account</audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: SQ1055_SubmitStreetNameToTranscription_DB_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.securityQuestionStNameAttempts < 1, true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/SQ1050_out_02.wav\\\">My mistake  Let s try one more time</audio>\"\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.securityQuestionStNameAttempts\n              value: Global.securityQuestionStNameAttempts + 1\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: SQ1030_RecordStreetName_DM\n\n      elseActions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/SQ1050_out_03.wav\\\">My mistake again</audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: getReturnLink()\n```", "timestamp": 1749529618.1042528, "content_hash": "67e55e77e339966bebd3d2e0efe58626"}