{"response": "```yaml\n- kind: Question\n  id: cv0150_ContinueVmailReset_DM\n  displayName: cv0150_ContinueVmailReset_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/cv0150_ni1_01.wav\\\">Do you want to continue with your voicemail password reset? Just say Yes or No </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/cv0150_ni2_01.wav\\\">If you want to continue with your voicemail password reset, say Yes or press 1 Otherwise, say No or press 2 </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.cv0150_ContinueVmailReset_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/cv0150_ini_01.wav\\\">Just so you know, after I reset your voicemail password, you will need to set up your voicemail again and create a new password before you can receive new messages However, your existing messages will still be there Do you still want me to reset your password?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/cv0150_ni1_01.wav\\\">Do you want to continue with your voicemail password reset? Just say Yes or No </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/cv0150_ni2_01.wav\\\">If you want to continue with your voicemail password reset, say Yes or press 1 Otherwise, say No or press 2 </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_cv0150_ContinueVmailReset_DM\n  value: =Text(Global.cv0150_ContinueVmailReset_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_cv0150_ContinueVmailReset_DM = \"true\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: cv0210_ResetVMPassword_DB_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_cv0150_ContinueVmailReset_DM = \"false\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: cv0230_VmailPasswordWrap_DM\n```", "timestamp": 1749470488.1650212, "content_hash": "ef029256f989ea12b98501517c8ba618"}