{"response": "```yaml\n- kind: Question\n  id: sa0205_ConfirmSim_DM\n  displayName: sa0205_ConfirmSim_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/sa0205_ni1_01.wav\\\">Youd like to replace a SIM card or Esim in a device that was previously activted on the att network, correct? Please say Yes or No</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/sa0205_ni2_01.wav\\\">If youd like to replace a Sim card or E-sim in a device that was previously activated on the a t and t network, press 1 Otherwise, press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.sa0205_ConfirmSim_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/sa0205_ini_01.wav\\\">Just to confirm, youd like to replace a SIM card or E-sim in a device that was previously activted on the a t and t network, correct?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/sa0205_ni1_01.wav\\\">Youd like to replace a SIM card or Esim in a device that was previously activted on the att network, correct? Please say Yes or No</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/sa0205_ni2_01.wav\\\">If youd like to replace a Sim card or E-sim in a device that was previously activated on the a t and t network, press 1 Otherwise, press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_sa0205_ConfirmSim_DM\n  value: =Text(Global.sa0205_ConfirmSim_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_sa0205_ConfirmSim_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.bypassPinReset\n          value: true\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: sa0210_SimCardAvailable_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_sa0205_ConfirmSim_DM = \"false\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: sa0105_ActivateMenu_DM\n```", "timestamp": 1749472397.9888504, "content_hash": "777752e8ef1d90266e38be7f8ea57061"}