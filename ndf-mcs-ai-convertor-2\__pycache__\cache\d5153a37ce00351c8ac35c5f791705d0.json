{"response": "```yaml\n- kind: Question\n  id: UA1205_NewCustomerInfoYN_DM\n  displayName: UA1205_NewCustomerInfoYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/UA1205_nm1_01.wav\\\">Would you like to hear that again?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/UA1205_nm2_01.wav\\\">To hear that information again, say 'repeat' or press 1If you are done, you can simply hang up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/UA1205_nm2_01.wav\\\">To hear that information again, say 'repeat' or press 1If you are done, you can simply hang up</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.UA1205_NewCustomerInfoYN_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n\n            // Case 1: UA1205_operator_counter = 1\n            Global.UA1205_operator_counter = 1,\n            [\n                \"Im sorry, I cant transfer you right now\",\n                \"test\",\n                \"Would you like to hear that information again?\"\n            ],\n\n            // Case 2: UA1205_operator_counter = 2\n            Global.UA1205_operator_counter = 2,\n            [\n                \"Let me try to help you here one more time\",\n                \"test\",\n                \"To hear the information again, say 'repeat that' or press 1If you are done, you can simply hang up\"\n            ],\n\n            // Default Case\n            [\n                \"If you have a Metro  phone ready to activate, just dial *228 from that phone, and we ll help you set up your account and choose a plan That s *-2-2-8\",\n                \"test\",\n                \"Otherwise  You can buy a phone online at metrobyt-mobilecom, from one of our stores, or from any authorized Metro dealer You can also use an unlocked cellphone from another carrier We use the nationwide, 4G-LTE T-Mobile network so you can download, stream and upload faster than ever from coast-to-coast And our plans are simple and straightforward Get one low price with taxes and regulatory fees included There are no hidden fees and no annual contract\",\n                \"test\",\n                \"You can find a map of our locations, and details about our plans, at metrobyt-mobilecom\",\n                \"test\",\n                \"Would you like to hear that again?\"\n            ]\n        )\n        }\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/UA1205_nm1_01.wav\\\">Would you like to hear that again?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/UA1205_nm2_01.wav\\\">To hear that information again, say 'repeat' or press 1If you are done, you can simply hang up</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/UA1205_nm2_01.wav\\\">To hear that information again, say 'repeat' or press 1If you are done, you can simply hang up</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_UA1205_NewCustomerInfoYN_DM\n  value: =Text(Global.UA1205_NewCustomerInfoYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_UA1205_NewCustomerInfoYN_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/UA1205_out_01.wav\\\">Sure</audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: UA1205_NewCustomerInfoYN_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_UA1205_NewCustomerInfoYN_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/UA1205_out_02.wav\\\">Alright</audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: UA1010_Goodbye_SD\n```", "timestamp": 1749530160.0103316, "content_hash": "d5153a37ce00351c8ac35c5f791705d0"}