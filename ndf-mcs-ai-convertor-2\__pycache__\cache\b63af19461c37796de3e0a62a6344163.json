{"response": "```yaml\n- kind: Question\n  id: MS1005_OfferSecuritySMSYN_DM\n  displayName: MS1005_OfferSecuritySMSYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MS1005_nm1_01.wav\\\">I see we are still missing some of your security details Can I text you a link to fill them out online? </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.MS1005_OfferSecuritySMSYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/MS1005_ini_01.wav\\\">I see we are still missing some of your security details You can fill them out at metrobyt-mobilecom, under My Account Can I text you a link? </audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/MS1005_nm1_01.wav\\\">I see we are still missing some of your security details Can I text you a link to fill them out online? </audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_MS1005_OfferSecuritySMSYN_DM\n  value: =Text(Global.MS1005_OfferSecuritySMSYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MS1005_OfferSecuritySMSYN_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.campaignId\n          value: GlobalVars.GetBCSParameters.acct_dets_campaign_id\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.languageCode\n          value: language == 'en-US' ? 'en' : 'es'\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: MS1010_SendSecuritySMS_DB_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_MS1005_OfferSecuritySMSYN_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/MS1005_out_01.wav\\\">Alright, moving on! </audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n```", "timestamp": 1749528741.8540602, "content_hash": "b63af19461c37796de3e0a62a6344163"}