{"response": "```yaml\n- kind: Question\n  id: da0215_DataHandlingWrapMenu_DM\n  displayName: da0215_DataHandlingWrapMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I missed that</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/da0215_ni1_01.wav\\\">If you need more help with data, say Tech Support or press 1 If you're done, just hang up Or if you need help with something else, say Main Menu or press star </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">Sorry</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/da0215_nm2_01.wav\\\">If your question has been answered and you don't need any more help, you can hang up If you do need help with something else, press star</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.da0215_DataHandlingWrapMenu_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/da0215_ini_01.wav\\\">For more help with data, say Tech Support Or if that's all you needed, go ahead and hang up For anything else, say Main Menu</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: tech_support\n          displayName: tech_support\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/da0215_ni1_01.wav\\\">If you need more help with data, say Tech Support or press 1 If you're done, just hang up Or if you need help with something else, say Main Menu or press star </audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_da0215_DataHandlingWrapMenu_DM\n  value: =Text(Global.da0215_DataHandlingWrapMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_da0215_DataHandlingWrapMenu_DM = \"tech_support\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: M09_MainMenu.dvxml\n```", "timestamp": 1749470674.3677583, "content_hash": "f94569ff806569caa6a800cc6ad8f4f3"}