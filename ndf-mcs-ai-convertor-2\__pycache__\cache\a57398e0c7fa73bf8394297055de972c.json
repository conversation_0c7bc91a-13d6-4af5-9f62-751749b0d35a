{"response": "```yaml\n- kind: Question\n  id: ES1105_ConfirmANIAccountYN_DM\n  displayName: ES1105_ConfirmANIAccountYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ES1105_nm1_01.wav\\\">I have your phone number as</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ES1105_nm1_02.wav\\\">Do you want to switch the phone on this account? </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ES1105_nm1_01.wav\\\">I have your phone number as</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ES1105_nm1_02.wav\\\">Do you want to switch the phone on this account? </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ES1105_nm1_01.wav\\\">I have your phone number as</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ES1105_nm1_02.wav\\\">Do you want to switch the phone on this account? </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ES1105_ConfirmANIAccountYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ES1105_ini_01.wav\\\">Just to confirm, you want to replace the phone you're calling on now, right? </audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ES1105_nm1_01.wav\\\">I have your phone number as</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ES1105_nm1_02.wav\\\">Do you want to switch the phone on this account? </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ES1105_nm1_01.wav\\\">I have your phone number as</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ES1105_nm1_02.wav\\\">Do you want to switch the phone on this account? </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ES1105_nm1_01.wav\\\">I have your phone number as</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ES1105_nm1_02.wav\\\">Do you want to switch the phone on this account? </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.ANI\n  value: GlobalVars.trn\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ES1105_ConfirmANIAccountYN_DM\n  value: =Text(Global.ES1105_ConfirmANIAccountYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ES1105_ConfirmANIAccountYN_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/ES1105_out_02.wav\\\">Got it</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ES1120_CheckFutureDatedRequest_JDA_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ES1105_ConfirmANIAccountYN_DM = \"false\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.esnSetupIssue\n              value: differentaccount\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.switchLinesEntryPoint\n              value: esn_swap\n\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/ES1105_out_01.wav\\\">Okay </audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: ES1115_SwitchLines_SD\n```", "timestamp": **********.3674684, "content_hash": "a57398e0c7fa73bf8394297055de972c"}