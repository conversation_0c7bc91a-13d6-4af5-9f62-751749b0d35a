{"response": "```yaml\n- kind: Question\n  id: aa2565_CaseNumberDTMF_DM\n  displayName: aa2565_CaseNumberDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.validationCriteriaVariables.currentValidationType = \"C10\",\n              \"Please enter your 10 digit case number now\",\n\n              Global.validationCriteriaVariables.currentValidationType = \"C08\",\n              \"Please enter your eight digit case number, including the leading zeros\",\n\n              true,\n              \"Please enter your 9 digit case number now\"\n          )\n        }\n      - |\n        {Switch(\n              true,\n              Global.dnisInfo.dtmfOnlyFlag = true,\n              \"Sorry, I still didn t get that\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.aa2565_CaseNumberDTMF_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.validationCriteriaVariables.currentValidationType = \"C10\",\n              \"Please enter your 10 digit case number now\",\n\n              Global.validationCriteriaVariables.currentValidationType = \"C08\",\n              \"Please enter your eight digit case number, including the leading zeros\",\n\n              true,\n              \"Please enter your 9 digit case number now\"\n          )\n        }\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.validationCriteriaVariables.currentValidationType = \"C10\",\n                \"Please enter your 10 digit case number now\",\n\n                Global.validationCriteriaVariables.currentValidationType = \"C08\",\n                \"Please enter your eight digit case number, including the leading zeros\",\n\n                true,\n                \"Please enter your 9 digit case number now\"\n            )\n          }\n        - |\n          {Switch(\n                true,\n                Global.dnisInfo.dtmfOnlyFlag = true,\n                \"Sorry, I still didn t get that\"\n            )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.result\n  value: aa2565_CaseNumberDTMF_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.case\n  value: aa2565_CaseNumberDTMF_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.validationCriteriaVariables.validationValue\n  value: validationCriteriaVariables.validationValue + ';' + result\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.validationCriteriaVariables.validationType\n  value: validationCriteriaVariables.validationType + ';' + validationCriteriaVariables.currentValidationType\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.globalVariables.currentTask = \"cardReplacement\" || Global.globalVariables.currentTask = \"altAuthVal\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa2510_ValidationDecision_JDA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: |\n            =If(Global.result = Global.validationCriteriaVariables.currentValidationData, true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa2510_ValidationDecision_JDA\n\n      elseActions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.validationCriteriaVariables.validationDataMismatch\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.validationCriteriaVariables.validationDataMismatchCaseNum\n          value: true\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa2510_ValidationDecision_JDA\n```", "timestamp": 1749544230.0263276, "content_hash": "8d472c754d94ff0e792d59a70a4e99bd"}