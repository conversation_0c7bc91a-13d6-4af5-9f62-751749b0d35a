{"response": "```yaml\n- kind: Question\n  id: PC1010_GetPrepaidCardPIN_DM\n  displayName: PC1010_GetPrepaidCardPIN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"{Switch(\n            true,\n            Global.prepaidPinToggleOn = true,\n            \\\"What's the 10- or 17-digit PIN on your card or receipt? Or say 'help me find it' \\\",\n            Global.prepaidPinToggleOn <> true,\n            \\\"What's the 10 digit PIN on your card or receipt? Or say 'help me find it' \\\"\n        )}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"{Switch(\n            true,\n            Global.prepaidPinToggleOn = true,\n            \\\"Please enter the 10- or 17-digit PIN on your prepaid card, or on your receipt If you need help finding it, press star\\\",\n            Global.prepaidPinToggleOn <> true,\n            \\\"Please enter the 10-digit PIN on your prepaid card, or on your receipt If you need help finding it, press star \\\"\n        )}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"{Switch(\n            true,\n            Global.prepaidPinToggleOn = true,\n            \\\"Please enter the 10- or 17-digit PIN on your prepaid card, or on your receipt If you need help finding it, press star\\\",\n            Global.prepaidPinToggleOn <> true,\n            \\\"Please enter the 10-digit PIN on your prepaid card, or on your receipt If you need help finding it, press star\\\"\n        )}\"\n\n  alwaysPrompt: true\n  variable: Global.PC1010_GetPrepaidCardPIN_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.prepaidPinToggleOn = true,\n            \"Now, What's the 10 or 17 digit PIN on your card or receipt? Or say 'help me find it' \",\n            Global.prepaidPinToggleOn <> true,\n            \"Now, What's the 10-digit PIN on your card or receipt? Or say 'help me find it' \"\n        )}\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"{Switch(\n              true,\n              Global.prepaidPinToggleOn = true,\n              \\\"What's the 10- or 17-digit PIN on your card or receipt? Or say 'help me find it' \\\",\n              Global.prepaidPinToggleOn <> true,\n              \\\"What's the 10 digit PIN on your card or receipt? Or say 'help me find it' \\\"\n          )}\"\n        - \"{Switch(\n              true,\n              Global.prepaidPinToggleOn = true,\n              \\\"Please enter the 10- or 17-digit PIN on your prepaid card, or on your receipt If you need help finding it, press star\\\",\n              Global.prepaidPinToggleOn <> true,\n              \\\"Please enter the 10-digit PIN on your prepaid card, or on your receipt If you need help finding it, press star \\\"\n          )}\"\n        - \"{Switch(\n              true,\n              Global.prepaidPinToggleOn = true,\n              \\\"Please enter the 10- or 17-digit PIN on your prepaid card, or on your receipt If you need help finding it, press star\\\",\n              Global.prepaidPinToggleOn <> true,\n              \\\"Please enter the 10-digit PIN on your prepaid card, or on your receipt If you need help finding it, press star\\\"\n          )}\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.fromFindPINWait\n  value: GlobalVars.fromFindPINWait\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.numberPINPayments\n  value: GlobalVars.numberPINPayments\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.errorCode\n  value: GlobalVars.errorCode\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.prepaidPINErrorCounter\n  value: GlobalVars.prepaidPINErrorCounter\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.prepaidPinToggleOn\n  value: GlobalVars.GetBCSParameters.prepaidPinToggleOn\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_PC1010_GetPrepaidCardPIN_DM\n  value: =Text(Global.PC1010_GetPrepaidCardPIN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_PC1010_GetPrepaidCardPIN_DM = \"help\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/PC1010_out_01.wav\\\">Sure</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: PC2005_PlayPINLocation_PP\n\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.GlobalVars.fromFindPINWait\n      value: false\n\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.GlobalVars.voucherNumber\n      value: PC1010_GetPrepaidCardPIN_DM.returnvalue\n\n    - kind: GotoAction\n      id: goto_REPLACE_THIS\n      actionId: PC1015_ValidatePrepaidPIN_DB_DA\n```", "timestamp": 1749529094.2646112, "content_hash": "a902d0bf63e8debe397bb7388d333691"}