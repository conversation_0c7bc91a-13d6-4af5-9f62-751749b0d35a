{"response": "```yaml\n- kind: Question\n  id: aa1095_CardActivationSuccessMenu_DM\n  displayName: aa1095_CardActivationSuccessMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"\"\n\n  alwaysPrompt: true\n  variable: Global.aa1095_CardActivationSuccessMenu_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa1095_ini_01.wav\\\">The card has been activated, but for you to be able to use it, you will need to select a PIN If you would like to do that now, press 1 If not, press 2</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: now\n          displayName: now\n        - id: later\n          displayName: later\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa1095_CardActivationSuccessMenu_DM\n  value: =Text(Global.aa1095_CardActivationSuccessMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa1095_CardActivationSuccessMenu_DM = \"now\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.nextStep\n          value: select_pin\n        # The script tag is ignored as per instructions\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: return\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa1095_CardActivationSuccessMenu_DM = \"later\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.nextStep\n              value: balance_inquiry\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: return\n```", "timestamp": 1749458586.4991202, "content_hash": "8d0f2e82dc772cfa07b17e70b9309d10"}