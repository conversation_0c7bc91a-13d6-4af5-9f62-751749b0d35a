{"response": "```yaml\n- kind: Question\n  id: MW1065_RemoveAutopayCard_DM\n  displayName: MW1065_RemoveAutopayCard_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1065_nm1_01.wav\\\">If you remove this card, your autopay will deactivate  please say  replace the card  or  cancel autopay ?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1065_nm1_02.wav\\\">If you remove this card, your autopay will be deactivated and you would lose your autopay discount  Would you rather 'replace the card' or 'cancel autopay'? </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1065_nm2_01.wav\\\">Please say  replace the card  or press 1   Cancel autopay  or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MW1065_nm2_01.wav\\\">Please say  replace the card  or press 1   Cancel autopay  or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.MW1065_RemoveAutopayCard_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.isAutopayEligPlanExists <> true,\n            \"If you remove this card, your autopay will deactivate  Would you rather  replace the card  or  cancel autopay ?\",\n            Global.isAutopayEligPlanExists = true,\n            \"If you remove this card, your autopay will be deactivated and you would lose your autopay discount  Would you rather replace the card or cancel autopay? \",\n            \"If you remove this card, your autopay will deactivate  Would you rather  replace the card  or  cancel autopay ?\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: replace-card\n          displayName: replace-card\n        - id: cancel-autopay\n          displayName: cancel-autopay\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1065_ni1_01.wav\\\">You have one card saved and it is used  for your automatic payments If you remove this card, your autopay will deactivate  Would you rather  replace the card  or  cancel autopay ?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1065_nm2_01.wav\\\">Please say  replace the card  or press 1   Cancel autopay  or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MW1065_nm2_01.wav\\\">Please say  replace the card  or press 1   Cancel autopay  or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.isAutopayEligPlanExists\n  value: GlobalVars.GetAccountDetails.isAutopayEligPlanExists\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.paymentOptionId\n  value: GlobalVars.GetWalletItems.walletItems[0].paymentOptionId\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.numDaysBtwPayAndCurrentDate\n  value: GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.numDaysBtwPayAndCurrentDate:0\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_MW1065_RemoveAutopayCard_DM\n  value: =Text(Global.MW1065_RemoveAutopayCard_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MW1065_RemoveAutopayCard_DM = \"replace-card\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.manageCardTask\n          value: ReplaceAutopayCard\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/MW1065_out_01.wav\\\">Ok, we ll need to remove this card from your account before we can add a new card One Moment</audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: MW1601_DeleteSPMItem_DB_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_MW1065_RemoveAutopayCard_DM = \"cancel-autopay\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.tag\n              value: cancel-autopay\n\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.numDaysBtwPayAndCurrentDate <= 7, true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: MW1099_GoToTransfer_SD\n\n              elseActions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.autopay\n                  value: false\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: MW1601_DeleteSPMItem_DB_DA\n```", "timestamp": 1749556970.445598, "content_hash": "3975aa81716cd5e027c1f9d9a3561167"}