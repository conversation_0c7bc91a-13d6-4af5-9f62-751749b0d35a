{"response": "```yaml\n- kind: Question\n  id: SE2150_WhatToChangeMix2_DM\n  displayName: SE2150_WhatToChangeMix2_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SE2150_nm1_01.wav\\\">You can say 'restrict sim changes' or 'allow mobile number transfers'</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SE2150_nm2_01.wav\\\">Please say 'restrict sim changes' or press 1 'allow mobile number transfers' or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SE2150_nm2_01.wav\\\">Please say 'restrict sim changes' or press 1 'allow mobile number transfers' or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.SE2150_WhatToChangeMix2_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/SE2150_ini_01.wav\\\">Your choices are 'restrict sim changes' or 'allow mobile number transfers' Which would you like?</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: block_sim_changes\n          displayName: block_sim_changes\n\n        - id: allow_port_outs\n          displayName: allow_port_outs\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/SE2150_nm1_01.wav\\\">You can say 'restrict sim changes' or 'allow mobile number transfers'</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SE2150_nm2_01.wav\\\">Please say 'restrict sim changes' or press 1 'allow mobile number transfers' or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SE2150_nm2_01.wav\\\">Please say 'restrict sim changes' or press 1 'allow mobile number transfers' or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_SE2150_WhatToChangeMix2_DM\n  value: =Text(Global.SE2150_WhatToChangeMix2_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_SE2150_WhatToChangeMix2_DM = \"block_sim_changes\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.activityCode\n          value: BSS\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.blockingInd\n          value: Y\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: SE2155_PlayUpdateTransition_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_SE2150_WhatToChangeMix2_DM = \"allow_port_outs\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.activityCode\n              value: BPO\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.blockingInd\n              value: N\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: SE2156_PlayUnblockInfo_DM\n```", "timestamp": 1749529499.4776547, "content_hash": "a1a13d27aa146281b3e8afda38dba106"}