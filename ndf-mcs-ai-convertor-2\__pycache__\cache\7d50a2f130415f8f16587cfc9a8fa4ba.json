{"response": "```yaml\n- kind: Question\n  id: dc0315_OfferPackageToPPU_DM\n  displayName: dc0315_OfferPackageToPPU_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/dc0315_ni1_01.wav\\\">Do you want to hear about our data add ons? Just say Yes or No</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/dc0315_ni2_01.wav\\\">If you want to hear about data add ons, say Yes or press 1 If not, say No or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.dc0315_OfferPackageToPPU_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/dc0315_ini_01.wav\\\">With your current plan, you can access the internet using data at pay per use rates, or with a data add on Would you like to buy a data add on?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/dc0315_ni1_01.wav\\\">Do you want to hear about our data add ons? Just say Yes or No</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/dc0315_ni2_01.wav\\\">If you want to hear about data add ons, say Yes or press 1 If not, say No or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_dc0315_OfferPackageToPPU_DM\n  value: =Text(Global.dc0315_OfferPackageToPPU_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_dc0315_OfferPackageToPPU_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: addPackages\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.packageDestination\n          value: data\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: dc0420_DataAddPackages_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_dc0315_OfferPackageToPPU_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/dc0315_out_01.wav\\\">Alright</audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: dc0320_LowBalancePPUDataOnly_JDA\n```", "timestamp": 1749470681.476602, "content_hash": "7d50a2f130415f8f16587cfc9a8fa4ba"}