{"response": "```yaml\n- kind: Question\n  id: aa1048_Confirm4DigitsDTMF_DM\n  displayName: aa1048_Confirm4DigitsDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"\"\n      - \"<audio src=\\\"AUDIO_LOCATION/aa1048_nm2_01.wav\\\">Sorry, I still didn t get that</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.aa1048_Confirm4DigitsDTMF_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa1048_ini_01.wav\\\">Let me be sure, I got</audio> {Global.cardInfoVariables.collectedCardNumLastFour} <audio src=\\\"AUDIO_LOCATION/aa1048_ini_02.wav\\\">If that s correct, press 1 otherwise press 2</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"\"\n        - \"<audio src=\\\"AUDIO_LOCATION/aa1048_ni2_01.wav\\\">Sorry, I still didn t get that</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa1048_Confirm4DigitsDTMF_DM\n  value: =Text(Global.aa1048_Confirm4DigitsDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa1048_Confirm4DigitsDTMF_DM = \"yes\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.checkCardsMatchInfo.searchForCardHolder = true, true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: aa1085_GetCurrentCardInfo_DB_DA\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.globalVariables.currentTask = \"cardReplacement\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: aa1054_LostStolenNeedAdditionalInfo_PP\n          elseActions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa1040_GetCardDTMF_DM\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/aa1048_out_03.wav\\\">Sorry Let s try getting your card number another way</audio>\"\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa1048_Confirm4DigitsDTMF_DM = \"no\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.fourDigitEntryCounter\n              value: globalVariables.fourDigitEntryCounter + 1\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.globalVariables.fourDigitEntryCounter = 1, true, false)\n                  actions:\n                    - kind: ConditionGroup\n                      id: conditionGroup_REPLACE_THIS\n                      conditions:\n                        - id: conditionItem_REPLACE_THIS\n                          condition: =If(Global.globalVariables.currentTask = \"cardReplacement\", true, false)\n                          actions:\n                            - kind: GotoAction\n                              id: goto_REPLACE_THIS\n                              actionId: aa1052_CollectLostStolenLastFourDTMF_DM\n                            - kind: SendActivity\n                              id: sendActivity_REPLACE_THIS\n                              activity:\n                                speak:\n                                  - \"<audio src=\\\"AUDIO_LOCATION/aa1048_out_01.wav\\\">Alright, let s try again</audio>\"\n                      elseActions:\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: aa1041_GetCardNumberLastFourDTMF_DM\n                        - kind: SendActivity\n                          id: sendActivity_REPLACE_THIS\n                          activity:\n                            speak:\n                              - \"<audio src=\\\"AUDIO_LOCATION/aa1048_out_04.wav\\\">Alright, let s try again</audio>\"\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.globalVariables.currentTask = \"cardReplacement\", true, false)\n                      actions:\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: aa1054_LostStolenNeedAdditionalInfo_PP\n                  elseActions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: aa1040_GetCardDTMF_DM\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/aa1048_out_02.wav\\\">Sorry Let s try getting your card number another way</audio>\"\n```", "timestamp": 1749458476.6891944, "content_hash": "58460a765111f073ff5d036eba6ec83c"}