{"response": "```yaml\n- kind: Question\n  id: sc0143_OfferChatAgentOnly_DM\n  displayName: sc0143_OfferChatAgentOnly_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/sc0143_ini_01.wav\\\">Please note, our call center is currently closed If youd like to receive a text message thatll connect you with a chat agent, press 1 To go back to the main menu and continue using our automated system, press 2 Otherwise, you can call us back during business hours or go online to  a t t dot com slash my prepaid</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/sc0143_ini_01.wav\\\">Please note, our call center is currently closed If youd like to receive a text message thatll connect you with a chat agent, press 1 To go back to the main menu and continue using our automated system, press 2 Otherwise, you can call us back during business hours or go online to  a t t dot com slash my prepaid</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.sc0143_OfferChatAgentOnly_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/sc0143_ini_01.wav\\\">Please note, our call center is currently closed If youd like to receive a text message thatll connect you with a chat agent, press 1 To go back to the main menu and continue using our automated system, press 2 Otherwise, you can call us back during business hours or go online to  a t t dot com slash my prepaid</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: chat_agent\n          displayName: chat_agent\n        - id: mainmenu\n          displayName: mainmenu\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/sc0143_ini_01.wav\\\">Please note, our call center is currently closed If youd like to receive a text message thatll connect you with a chat agent, press 1 To go back to the main menu and continue using our automated system, press 2 Otherwise, you can call us back during business hours or go online to  a t t dot com slash my prepaid</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/sc0143_ini_01.wav\\\">Please note, our call center is currently closed If youd like to receive a text message thatll connect you with a chat agent, press 1 To go back to the main menu and continue using our automated system, press 2 Otherwise, you can call us back during business hours or go online to  a t t dot com slash my prepaid</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_sc0143_OfferChatAgentOnly_DM\n  value: =Text(Global.sc0143_OfferChatAgentOnly_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_sc0143_OfferChatAgentOnly_DM = \"chat_agent\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/sc0143_out_01.wav\\\">Alright, I'll send you the text If you do not receive this text message after a few minutes, please call us back</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: sc0148_ChatAgentSendSMS_DB_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_sc0143_OfferChatAgentOnly_DM = \"mainmenu\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.lastIntent\n              value: intent\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.intent\n              value: ''\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.callerIntent\n              value: ''\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.transferDestinationEBB\n              value: 'false'\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.M01_MainMenu2.dvxml#ma1334_InitiateMainMenu_DS\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.operatorAtOfferAgentOptions\n  value: 0\n```", "timestamp": 1749472275.3308604, "content_hash": "5faed315abd8b274b29f0c705922234f"}