{"response": "```yaml\n- kind: Question\n  id: st0915_AskAddData_DM\n  displayName: st0915_AskAddData_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"com.nuance.att.application.audio.st0915_AskAddData_DM_noinput1\"\n      - \"com.nuance.att.application.audio.st0915_AskAddData_DM_noinput2\"\n\n  alwaysPrompt: true\n  variable: Global.st0915_AskAddData_DM_reco\n  prompt:\n    speak:\n      - \"com.nuance.att.application.audio.st0915_AskAddData_DM_initial\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: add_packages\n          displayName: add_packages\n        - id: change_plan\n          displayName: change_plan\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"com.nuance.att.application.audio.st0915_AskAddData_DM_noinput1\"\n        - \"com.nuance.att.application.audio.st0915_AskAddData_DM_noinput2\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_st0915_AskAddData_DM\n  value: =Text(Global.st0915_AskAddData_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_st0915_AskAddData_DM = \"add_packages\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: addPackages\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.packageDestination\n          value: data\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: st0920_DataAddPackages_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_st0915_AskAddData_DM = \"change_plan\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.intent\n              value: changePlan\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: S02_TechSupport_06.dvxml\n```", "timestamp": 1749472093.5498426, "content_hash": "48d1b935afcf6b43fbb3d6f77a784934"}