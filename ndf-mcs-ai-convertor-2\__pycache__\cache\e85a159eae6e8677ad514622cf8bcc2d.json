{"response": "```yaml\n- kind: Question\n  id: NP0050_CheckAcctNumAlphasYN_DM\n  displayName: NP0050_CheckAcctNumAlphasYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/NP0050_nm1_01.wav\\\">I just need to check if the account number for your old provider has any letters in it If it does, say  yes  Otherwise, say  no  pause If you don t know it, say  I don t know it </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/NP0050_nm2_01.wav\\\">If the account number from your old telephone company has any letters in it, say  yes  or press 1 If not, say  no  or press 2 pause If you don t know it, say  I don t know it , or press 3</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/NP0050_nm2_01.wav\\\">If the account number from your old telephone company has any letters in it, say  yes  or press 1 If not, say  no  or press 2 pause If you don t know it, say  I don t know it , or press 3</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.NP0050_CheckAcctNumAlphasYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/NP0050_ini_01.wav\\\">Now, does your old account number have any letters in it?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/NP0050_nm1_01.wav\\\">I just need to check if the account number for your old provider has any letters in it If it does, say  yes  Otherwise, say  no  pause If you don t know it, say  I don t know it </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/NP0050_nm2_01.wav\\\">If the account number from your old telephone company has any letters in it, say  yes  or press 1 If not, say  no  or press 2 pause If you don t know it, say  I don t know it , or press 3</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/NP0050_nm2_01.wav\\\">If the account number from your old telephone company has any letters in it, say  yes  or press 1 If not, say  no  or press 2 pause If you don t know it, say  I don t know it , or press 3</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_NP0050_CheckAcctNumAlphasYN_DM\n  value: =Text(Global.NP0050_CheckAcctNumAlphasYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_NP0050_CheckAcctNumAlphasYN_DM = \"no\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: NP0055_GetOSPAccountNum_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_NP0050_CheckAcctNumAlphasYN_DM = \"yes\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.NumberPortInVars.npi_fail\n              value: acctnum_alphas\n\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.NumberPortIn_4.dvxml\n```", "timestamp": **********.9483488, "content_hash": "e85a159eae6e8677ad514622cf8bcc2d"}