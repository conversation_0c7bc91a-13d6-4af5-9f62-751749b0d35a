{"response": "```yaml\n- kind: Question\n  id: bm0717_AskWhichChange_DM\n  displayName: bm0717_AskWhichChange_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bm0717_ni1_01.wav\\\">What would you like to change? Say the Amount or press 1, the Card or press 2, or Both or press 3 If you don t want to make any changes, say No Changes or press 4</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/bm0717_ni2_01.wav\\\">To change the amount, press 1 To use a different card, press 2 To change the amount *and* the card, press 3 Or, if the amount and card are ok, press 4</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.bm0717_AskWhichChange_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bm0717_ini_01.wav\\\">Which part would you like to change - the amount, the card, or both?</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: amount\n          displayName: amount\n        - id: card\n          displayName: card\n        - id: both\n          displayName: both\n        - id: no_changes\n          displayName: no_changes\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/bm0717_ni1_01.wav\\\">What would you like to change? Say the Amount or press 1, the Card or press 2, or Both or press 3 If you don t want to make any changes, say No Changes or press 4</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/bm0717_ni2_01.wav\\\">To change the amount, press 1 To use a different card, press 2 To change the amount *and* the card, press 3 Or, if the amount and card are ok, press 4</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_bm0717_AskWhichChange_DM\n  value: =Text(Global.bm0717_AskWhichChange_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_bm0717_AskWhichChange_DM = \"amount\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.changeAmount\n          value: true\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: bm0725_AskPaymentAmount_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_bm0717_AskWhichChange_DM = \"card\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.changeCard\n              value: true\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.bm0717saidCard\n              value: true\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: RemoveDeniedCardFromSession_DB_call_CS\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_bm0717_AskWhichChange_DM = \"both\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.changeCard\n                  value: true\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/bm0717_out_01.wav\\\">Okay, let s start with the amount</audio>\"\n                      - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\"></audio>\"\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.bm0717saidCard\n                  value: false\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: RemoveDeniedCardFromSession_DB_call_CS\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_bm0717_AskWhichChange_DM = \"no_changes\", true, false)\n                  actions:\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/bm0717_out_02.wav\\\">Alright, no changes</audio>\"\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: bm0815_AddMoneyVestaProcess_SD\n```", "timestamp": 1749469812.8969102, "content_hash": "a7b372cd90857c78cf3acb10773e4293"}