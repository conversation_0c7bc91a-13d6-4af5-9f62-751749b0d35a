{"response": "```yaml\n- kind: Question\n  id: br0323_Play2GWarning_DM\n  displayName: br0323_Play2GWarning_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/br0323_ni1_01.wav\\\">If you want to continue setting up Auto Pay even though A T and T may interrupt, suspend, or cancel your enrollment after the 2 G Network shutdown, say Yes or press 1 If not, say No or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/br0323_ni2_01.wav\\\">Lets go on</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.br0323_Play2GWarning_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/br0323_ini_01.wav\\\">Because of the 2 G Network shutdown, you should not enroll in Auto Pay unless you replace your 2 G phone If you do enroll, A T and T may interrupt, suspend or cancel your Auto Pay enrollment without notice Would you like to continue enrolling?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/br0323_ni1_01.wav\\\">If you want to continue setting up Auto Pay even though A T and T may interrupt, suspend, or cancel your enrollment after the 2 G Network shutdown, say Yes or press 1 If not, say No or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/br0323_ni2_01.wav\\\">Lets go on</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_br0323_Play2GWarning_DM\n  value: =Text(Global.br0323_Play2GWarning_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_br0323_Play2GWarning_DM = \"true\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: br0326_AutoPayEnrollDetails_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_br0323_Play2GWarning_DM = \"false\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: B03_AutoRefill_05.dvxml\n```", "timestamp": 1749469862.6696706, "content_hash": "f63a2757f860a2c9b039f174f1676424"}