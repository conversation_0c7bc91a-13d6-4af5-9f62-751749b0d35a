{"response": "```yaml\n- kind: Question\n  id: bp0230_AskExpirationDate_DM\n  displayName: bp0230_AskExpirationDate_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bp0230_ni1_01.wav\\\">Please say or enter the four-digit expiration date on your card For example zero five sixteen</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/bp0230_ni2_01.wav\\\">Please enter the four-digit expiration date listed on your card  For example, if the date is March 2017, you should enter zero three one seven</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.bp0230_AskExpirationDate_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bp0230_ini_01.wav\\\">whats the 4-digit expiration date?</audio>\"\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/bp0230_ni1_01.wav\\\">Please say or enter the four-digit expiration date on your card For example zero five sixteen</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/bp0230_ni2_01.wav\\\">Please enter the four-digit expiration date listed on your card  For example, if the date is March 2017, you should enter zero three one seven</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.chargeExpirationDate\n  value: bp0230_AskExpirationDate_DM.returnvalue\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: IsValidDate_JDA\n```", "timestamp": 1749470112.3239913, "content_hash": "bfbee821b161fa8dd31ff110bdb7c59c"}