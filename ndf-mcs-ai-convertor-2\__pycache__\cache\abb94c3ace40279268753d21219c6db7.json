{"response": "```yaml\n- kind: Question\n  id: SP1105_MakeExtraPaymentYN_DM\n  displayName: SP1105_MakeExtraPaymentYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SP1105_nm1_02.wav\\\">I see you have automatic payments set up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SP1105_nm1_01.wav\\\">I see you already made a payment yesterday</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SP1105_nm1_03.wav\\\">Did you want to make a payment on top of that</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SP1105_nm2_02.wav\\\">If you want to make a payment on top of the automatic payment, say 'yes' or press 1</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SP1105_nm2_01.wav\\\">If you want to make a payment on top of the payment you made yesterday, say 'yes' or press 1</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SP1105_nm2_03.wav\\\">Otherwise, say 'no' or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SP1105_nm3_01.wav\\\">To make an additional payment, press 1 Otherwise, press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.SP1105_MakeExtraPaymentYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/SP1105_ini_03.wav\\\">Do you want to make a payment on top of that?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/SP1105_nm1_02.wav\\\">I see you have automatic payments set up</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SP1105_nm1_01.wav\\\">I see you already made a payment yesterday</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SP1105_nm1_03.wav\\\">Did you want to make a payment on top of that</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SP1105_nm2_02.wav\\\">If you want to make a payment on top of the automatic payment, say 'yes' or press 1</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SP1105_nm2_01.wav\\\">If you want to make a payment on top of the payment you made yesterday, say 'yes' or press 1</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SP1105_nm2_03.wav\\\">Otherwise, say 'no' or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SP1105_nm3_01.wav\\\">To make an additional payment, press 1 Otherwise, press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.isAutopayEnabled\n  value: GlobalVars.GetAccountDetails.isAutopayEnabled\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.balance\n  value: GlobalVars.GetAccountDetails.balance\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.dueImmediatelyAmount\n  value: parseFloat(GlobalVars.GetAccountDetails.dueImmediatelyAmount)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.balanceAnddueImmediatelyAmount\n  value: (parseFloat(balance) + dueImmediatelyAmount).toString()\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.skipBalance\n  value: GlobalVars.skipBalance\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_SP1105_MakeExtraPaymentYN_DM\n  value: =Text(Global.SP1105_MakeExtraPaymentYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_SP1105_MakeExtraPaymentYN_DM = \"true\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.isAutopayEnabled = true && Global.balanceAnddueImmediatelyAmount = 0, true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.skipBalance\n                  value: true\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/SP1105_out_03.wav\\\">alright</audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: SP1201_ServicePaymentCheckContext_JDA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_SP1105_MakeExtraPaymentYN_DM = \"false\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.callType = \"make_pmt_auto_pay\", true, false)\n                  actions:\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/SP1105_out_04.wav\\\">Ok, please call back later to set up autopay</audio>\"\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.callType\n                      value: goodbye\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: getReturnLink()\n\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.GetAccountDetails && Global.GetAccountDetails.accountStatus = \"suspended\", true, false)\n                      actions:\n                        - kind: SendActivity\n                          id: sendActivity_REPLACE_THIS\n                          activity:\n                            speak:\n                              - \"<audio src=\\\"AUDIO_LOCATION/SP1105_out_02.wav\\\">No problem</audio>\"\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.callType\n                          value: goodbye\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.cti_Intent\n                          value: getctiIntent(Global.GetCTIParameters.ctiIntent,Global.callType, 'callType')\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: getReturnLink()\n\n                  elseActions:\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/SP1105_out_01.wav\\\">Alright If you're done, you can just hang up Otherwise, I'll take you to the main menu</audio>\"\n                          - \"<audio src=\\\"AUDIO_LOCATION/silence_200ms.wav\\\">test</audio>\"\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.callType\n                      value: undefined\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.cti_Intent\n                      value: getctiIntent(Global.GetCTIParameters.ctiIntent,Global.callType, 'callType')\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: getReturnLink()\n```", "timestamp": 1749529433.4325073, "content_hash": "abb94c3ace40279268753d21219c6db7"}