{"response": "```yaml\n- kind: Question\n  id: ma1116_PRVIQuestion_DM\n  displayName: ma1116_PRVIQuestion_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ma1116_nm1_01.wav\\\">Are you calling about the notice you received to change your Puerto Rico or Virgin Islands phone number? Say yes of no</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ma1116_nm2_01.wav\\\">If you are calling about changing your Puerto Rico or Virgin Islands phone number, say yes or press 1 Otherwise, say no or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ma1116_PRVIQuestion_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ma1116_ini_01.wav\\\">If youre calling about the notice you received to change your Puerto Rico or Virgin Islands phone number, say yes Otherwise say no</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ma1116_ni1_01.wav\\\">Sorry Are you calling about the notice you received to change your Puerto Rico or Virgin Islands phone number? Say yes of no</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ma1116_ni2_01.wav\\\">If you are calling about changing your Puerto Rico or Virgin Islands phone number, say yes or press 1 Otherwise, say no or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ma1116_PRVIQuestion_DM\n  value: =Text(Global.ma1116_PRVIQuestion_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma1116_PRVIQuestion_DM = \"true\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.callType = \"611\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.collection_commandgrammar\n                  value: 'GlobalCommands.grxml?SWI_vars.allow=mainmenu'\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.collection_dtmfcommandgrammar\n                  value: 'GlobalCommands_dtmf.grxml?SWI_vars.allow=mainmenu'\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.collection_commandgrammar\n              value: 'GlobalCommands.grxml'\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.collection_dtmfcommandgrammar\n              value: 'GlobalCommands_dtmf.grxml'\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.heardACPInfo\n          value: true\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.S04_CallTransfer_02.dvxml#sc0110_TransferMessage_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ma1116_PRVIQuestion_DM = \"false\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.callType = \"611\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.collection_commandgrammar\n                      value: 'GlobalCommands.grxml?SWI_vars.allow=mainmenu'\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.collection_dtmfcommandgrammar\n                      value: 'GlobalCommands_dtmf.grxml?SWI_vars.allow=mainmenu'\n              elseActions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.collection_commandgrammar\n                  value: 'GlobalCommands.grxml'\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.collection_dtmfcommandgrammar\n                  value: 'GlobalCommands_dtmf.grxml'\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.isIOMEnabled = true, true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: ma1120_IOMInitialize_SD\n              elseActions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: ma1210_CheckAccountStatus_JDA\n```", "timestamp": **********.658414, "content_hash": "e23ba7ddf9dfd831bd320102cfaa7754"}