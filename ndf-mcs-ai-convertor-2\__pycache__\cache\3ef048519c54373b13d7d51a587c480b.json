{"response": "```yaml\n- kind: Question\n  id: LG1010_GetAccountNumber_DM\n  displayName: LG1010_GetAccountNumber_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/LG1010_nm1_01.wav\\\">Please enter your Metro area code and phone number</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/LG1010_nm1_02.wav\\\">Starting with the area code, enter your Metro phone number If you don t have an account yet, you can say  getting started with Metro  You can also say  my phone is missing or damaged </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/LG1010_nm2_01.wav\\\">Starting with the area code, enter your metro phone number using the telephone keypad Or if you ve just bought a Metro phone and need to activate it, say  activate a phone  You can also say  my phone is missing or damaged  or  if you don t have an account yet, you can say  getting started with Metro </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/LG1010_nm2_01.wav\\\">Starting with the area code, enter your metro phone number using the telephone keypad Or if you ve just bought a Metro phone and need to activate it, say  activate a phone  You can also say  my phone is missing or damaged  or  if you don t have an account yet, you can say  getting started with Metro </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.LG1010_GetAccountNumber_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.callType = \"make_pmt\",\n            \"I ll just need the Metro Phone number you want to pay for Say or enter it now\",\n            Global.callType = \"switch_phone\",\n            \"First, what s the Metro  phone number you want to work with?\",\n            \"First, say or enter your Metro Phone number\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: report-phone_lost\n          displayName: report-phone_lost\n        - id: sign_up\n          displayName: sign_up\n        - id: activate-new_account\n          displayName: activate-new_account\n        - id: activate-phone_lg\n          displayName: activate-phone_lg\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/LG1010_nm1_01.wav\\\">Please enter your Metro area code and phone number</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/LG1010_nm1_02.wav\\\">Starting with the area code, enter your Metro phone number If you don t have an account yet, you can say  getting started with Metro  You can also say  my phone is missing or damaged </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/LG1010_nm2_01.wav\\\">Starting with the area code, enter your metro phone number using the telephone keypad Or if you ve just bought a Metro phone and need to activate it, say  activate a phone  You can also say  my phone is missing or damaged  or  if you don t have an account yet, you can say  getting started with Metro </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/LG1010_nm2_01.wav\\\">Starting with the area code, enter your metro phone number using the telephone keypad Or if you ve just bought a Metro phone and need to activate it, say  activate a phone  You can also say  my phone is missing or damaged  or  if you don t have an account yet, you can say  getting started with Metro </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.numTries\n  value: getAccountNumberTries\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.from611orStar99\n  value: \"GlobalVars.serviceDialed == '611' || GlobalVars.serviceDialed == '99'\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.callType\n  value: GlobalVars.callType\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.comingFromLG1010LG1025\n  value: true\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_LG1010_GetAccountNumber_DM\n  value: =Text(Global.LG1010_GetAccountNumber_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_LG1010_GetAccountNumber_DM = \"report-phone_lost\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: lost_phone\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.mainMenuChoice\n          value: lost_phone\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.needMDN\n          value: false\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.securityRequired\n          value: false\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: Login_Security.dvxml\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_LG1010_GetAccountNumber_DM = \"sign_up\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.needMDN\n              value: false\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.securityRequired\n              value: false\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: Login_Security.dvxml\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_LG1010_GetAccountNumber_DM = \"activate-new_account\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.callType\n                  value: new_customer\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.cti_Intent\n                  value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.needMDN\n                  value: false\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.securityRequired\n                  value: false\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.paymentInfoRequired\n                  value: false\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: Login_Security.dvxml\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_LG1010_GetAccountNumber_DM = \"activate-phone_lg\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.callType\n                      value: activate_unknownDevice\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.cti_Intent\n                      value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.mainMenuChoice\n                      value: activate\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.needMDN\n                      value: false\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.securityRequired\n                      value: false\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: Login_Security.dvxml\n\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.Var_LG1010_GetAccountNumber_DM = \"default\", true, false)\n                      actions:\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.getAccountNumberTries\n                          value: getAccountNumberTries + 1\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.GlobalVars.mdn\n                          value: LG1010_GetAccountNumber_DM.returnvalue\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: LG1015_GetAccountDetails_DB_DA\n```", "timestamp": **********.8904197, "content_hash": "3ef048519c54373b13d7d51a587c480b"}