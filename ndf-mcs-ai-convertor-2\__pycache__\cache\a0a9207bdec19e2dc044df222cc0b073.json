{"response": "```yaml\n- kind: Question\n  id: aa5006_PinSelect1_DM\n  displayName: aa5006_PinSelect1_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.aa5006_PinSelect1_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa5006_ini_01.wav\\\">Please say your new 4 digit PIN</audio>\"\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.pinVariables.pin1\n  value: aa5006_PinSelect1_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.pinVariables.pinChangeCounter\n  value: pinVariables.pinChangeCounter + 1\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: aa5011_PinSelect2_DM\n```", "timestamp": 1749543539.7062483, "content_hash": "a0a9207bdec19e2dc044df222cc0b073"}