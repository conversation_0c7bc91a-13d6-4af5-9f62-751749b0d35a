{"response": "```yaml\n- kind: Question\n  id: XC1105_CollectSecurityCode_DM\n  displayName: XC1105_CollectSecurityCode_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.XC1105_CollectSecurityCode_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.lastLoginAttempt = true,\n            \"Sorry, it's not a match  You can always reset your account PIN online at metrobyT-Mobilecom Simply sign into your account , go to My Account and select Profile From there select, PIN/Passcode  Now, please say or enter your pin one more time\",\n            Global.preTransferCodeCollectionErrors = 0 && Global.lastLoginAttempt <> true && Global.accountPinToggleOn = true,\n            \"Now, to save time with an agent, what's your 6-to-15-digit account PIN?\",\n            Global.preTransferCodeCollectionErrors = 0 && Global.lastLoginAttempt <> true && Global.accountPinToggleOn <> true,\n            \"Now, to save time with an agent, what's your 8-digit account PIN?\",\n            Global.preTransferCodeCollectionErrors <> 0 && Global.lastLoginAttempt <> true,\n            \"That didn't match either, Let's try again\"\n        )}\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.tag\n  value: GlobalVars.tag\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.lastLoginAttempt\n  value: GlobalVars.onePinTryRemaining\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.accountPinToggleOn\n  value: GlobalVars.GetBCSParameters.accountPinToggleOn\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.verificationType\n  value: pin\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.verificationValue\n  value: XC1105_CollectSecurityCode_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.cti_PIN\n  value: XC1105_CollectSecurityCode_DM.returnvalue\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: XC1110_Authenticate_DB_DA\n```", "timestamp": **********.286292, "content_hash": "51fd093525542522180cf6dbd37c45da"}