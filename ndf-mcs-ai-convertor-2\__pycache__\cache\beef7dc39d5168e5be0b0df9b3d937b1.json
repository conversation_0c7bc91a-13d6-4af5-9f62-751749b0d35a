{"response": "```yaml\n- kind: Question\n  id: pr0440_BasicChangePlanWrap_DM\n  displayName: pr0440_BasicChangePlanWrap_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I missed that</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/pr0440_ni1_01.wav\\\">If thats all you needed, go ahead and hang up Or for anything else, say Main Menu or press star</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">Sorry</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/pr0440_nm2_01.wav\\\">Ive completed your plan change, so if youre done, you can hang up If you need something else, press star</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.pr0440_BasicChangePlanWrap_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/pr0440_ini_01.wav\\\">If youre done, just hang up For anything else, say Main Menu</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: mainmenu\n          displayName: mainmenu\n        - id: operator\n          displayName: operator\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/pr0440_ni1_01.wav\\\">If thats all you needed, go ahead and hang up Or for anything else, say Main Menu or press star</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_pr0440_BasicChangePlanWrap_DM\n  value: =Text(Global.pr0440_BasicChangePlanWrap_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pr0440_BasicChangePlanWrap_DM = \"mainmenu\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferDestination\n          value: attPrepaid\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M01_MainMenu2_Dialog\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_pr0440_BasicChangePlanWrap_DM = \"operator\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.lastTransferState\n              value: pr0440_BasicChangePlanWrap_DM\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.transferReason\n              value: AGENT_REQUEST\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.M09_MainMenu_Dialog\n\n    elseActions:\n      - kind: ConditionGroup\n        id: conditionGroup_REPLACE_THIS\n        conditions:\n          - id: conditionItem_REPLACE_THIS\n            condition: =If(Global.Var_pr0440_BasicChangePlanWrap_DM = \"repeat\", true, false)\n            actions:\n              - kind: GotoAction\n                id: goto_REPLACE_THIS\n                actionId: pr0440_BasicChangePlanWrap_DM\n```", "timestamp": 1749471837.9593005, "content_hash": "beef7dc39d5168e5be0b0df9b3d937b1"}