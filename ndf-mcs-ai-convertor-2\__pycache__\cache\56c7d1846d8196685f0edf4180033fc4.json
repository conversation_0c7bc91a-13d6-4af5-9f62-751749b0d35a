{"response": "```yaml\n- kind: Question\n  id: st1034_TestSolutionReady_DM\n  displayName: st1034_TestSolutionReady_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n              \"If you're done testing your phone, say I'm Ready or press 1 If not, go ahead and hang up, finish testing your phone, and call us back if you're still having problems with it\",\n              true,\n              \"If you're done testing your device, say I'm Ready or press 1 If not, go ahead and hang up, finish testing your device, and call us back if you're still having problems with it\"\n          )\n        }\n      - |\n        {Switch(\n              true,\n              Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n              \"If you need more time to finish testing your phone, go ahead and hang up, and if you're still having problems, call us back Or, if you're ready, press 1\",\n              true,\n              \"If you need more time to finish testing your device, go ahead and hang up, and if you're still having problems, call us back Or, if you're ready, press 1\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.st1034_TestSolutionReady_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n              \"If you need more time, go ahead and hang up for now, and finish testing your phone If you're still having problems with it, just call us back Otherwise, say I'm Ready\",\n              true,\n              \"If you need more time, go ahead and hang up for now, and finish testing your device If you're still having problems with it, just call us back Otherwise, say I'm Ready\"\n          )\n        }\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: ready\n          displayName: ready\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n                \"If you're done testing your phone, say I'm Ready or press 1 If not, go ahead and hang up, finish testing your phone, and call us back if you're still having problems with it\",\n                true,\n                \"If you're done testing your device, say I'm Ready or press 1 If not, go ahead and hang up, finish testing your device, and call us back if you're still having problems with it\"\n            )\n          }\n        - |\n          {Switch(\n                true,\n                Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n                \"If you need more time to finish testing your phone, go ahead and hang up, and if you're still having problems, call us back Or, if you're ready, press 1\",\n                true,\n                \"If you need more time to finish testing your device, go ahead and hang up, and if you're still having problems, call us back Or, if you're ready, press 1\"\n            )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_st1034_TestSolutionReady_DM\n  value: =Text(Global.st1034_TestSolutionReady_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_st1034_TestSolutionReady_DM = \"ready\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: st1035_AskFixedProblem_DM\n```", "timestamp": **********.8746393, "content_hash": "56c7d1846d8196685f0edf4180033fc4"}