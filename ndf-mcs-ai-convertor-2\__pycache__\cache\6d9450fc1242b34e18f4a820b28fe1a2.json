{"response": "```yaml\n- kind: Question\n  id: ME1005_GetMetroCardPIN_DM\n  displayName: ME1005_GetMetroCardPIN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ME1005_nm1_01.wav\\\">What's the 10-digit PIN for the card? You can also say 'more info'</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ME1005_nm2_01.wav\\\">Please say or enter the 10-digit PIN for the card Or say 'more info' or press star</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ME1005_nm3_01.wav\\\">Using your keypad, enter teh 10-digit PIN for this card If you're not sure where to find it, press star</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ME1005_GetMetroCardPIN_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            // callType != \"activate\"\n            Global.callType <> \"activate\",\n            [\n                \"<audio src=\\\"AUDIO_LOCATION/ME1005_ini_01.wav\\\">Sure</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/ME1005_ini_04.wav\\\">Just so you know, we will add the full value of this card to your account</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/ME1005_ini_02.wav\\\">What's the 10-digit PIN on the card? You can say OR enter it</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/silence_2000ms.wav\\\">test</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/ME1005_ini_03.wav\\\">You can also say 'more info'</audio>\"\n            ],\n            // Default (callType == \"activate\")\n            [\n                \"<audio src=\\\"AUDIO_LOCATION/ME1005_ini_01.wav\\\">Sure</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/ME1005_ini_02.wav\\\">What's the 10-digit PIN on the card? You can say OR enter it</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/silence_2000ms.wav\\\">test</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/ME1005_ini_03.wav\\\">You can also say 'more info'</audio>\"\n            ]\n        )}\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ME1005_nm1_01.wav\\\">What's the 10-digit PIN for the card? You can also say 'more info'</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ME1005_nm2_01.wav\\\">Please say or enter the 10-digit PIN for the card Or say 'more info' or press star</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ME1005_nm3_01.wav\\\">Using your keypad, enter teh 10-digit PIN for this card If you're not sure where to find it, press star</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.saidOperator\n  value: \"GlobalVars.saidOperatorME1005?GlobalVars.saidOperatorME1005:false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.firstInvalidPIN\n  value: GlobalVars.firstInvalidPIN\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.metroCardFail\n  value: GlobalVars.metroCardFail\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.callType\n  value: GlobalVars.callType\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.saidOperatorME1005\n  value: false\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.metroCardPIN\n  value: ME1005_GetMetroCardPIN_DM.returnvalue\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.GlobalVars.metroCardPIN = Global.GlobalVars.firstInvalidPIN, true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.transferReason\n          value: dbfail\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.playTransferMessage\n          value: true\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ME1035_CallTransfer_SD\n\n  elseActions:\n    - kind: GotoAction\n      id: goto_REPLACE_THIS\n      actionId: ME1010_ValidateMetroPaymentCard_DB_DA\n```", "timestamp": 1749528682.7631512, "content_hash": "6d9450fc1242b34e18f4a820b28fe1a2"}