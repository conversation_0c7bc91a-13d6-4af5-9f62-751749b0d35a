#!/usr/bin/env python3
"""
Test script for PowerFX Validator and Fixer
==========================================

This script creates sample YAML files with various PowerFX issues and demonstrates
the validation and fixing capabilities based on Microsoft Learn documentation.
"""

import os
import tempfile
from pathlib import Path
from powerfx_validator_fixer import PowerFXValidatorFixer, setup_logging

def create_test_yaml_files(test_dir: Path):
    """Create sample YAML files with PowerFX issues for testing"""
    
    # Test file 1: Quote and array issues
    test1_content = '''
kind: Topic
displayName: PowerFX Issues Test 1
triggers:
  - kind: UnknownIntent

actions:
  - kind: Question
    id: question1
    variable: Global.userChoice
    prompt:
      kind: Activity
      activity:
        speak:
          - kind: AdaptiveCardResponse
            cardPayload: |
              Switch(
                Global.npi_type = 'cellphone',
                [
                  'You can make calls immediately.',
                  'Incoming calls start within 2 hours.',
                  'Visit a store if issues persist.'
                ],
                Global.npi_type = 'landline',
                [
                  'Your landline service is ready.',
                  'Test your connection now.'
                ],
                ['Default message for unknown type.']
              )
  - kind: SetVariable
    id: setVar1
    variable: Global.testVar
    value: "This has \\"escaped quotes\\" in it"
'''
    
    # Test file 2: Boolean and function issues
    test2_content = '''
kind: Topic
displayName: PowerFX Issues Test 2
triggers:
  - kind: UnknownIntent

actions:
  - kind: Question
    id: question2
    variable: Global.booleanTest
    prompt:
      kind: Activity
      activity:
        speak:
          - kind: AdaptiveCardResponse
            cardPayload: |
              If(
                Global.isActive = True,
                Concat('User is active', 'Welcome back'),
                If(
                  Global.isGuest = FALSE,
                  ['Guest user detected', 'Limited access granted'],
                  'Unknown user status'
                )
              )
  - kind: SetVariable
    id: setVar2
    variable: Global.statusMessage
    value: "Status: \\"Active\\""
'''
    
    # Test file 3: Mixed issues
    test3_content = '''
kind: Topic
displayName: PowerFX Issues Test 3
triggers:
  - kind: UnknownIntent

actions:
  - kind: Question
    id: question3
    variable: Global.mixedTest
    prompt:
      kind: Activity
      activity:
        speak:
          - kind: AdaptiveCardResponse
            cardPayload: |
              Switch(
                Global.userType = 'premium',
                Concat('Premium user', 'Full access'),
                Global.userType = 'standard',
                ['Standard user', 'Limited access'],
                If(Global.isGuest = True, ['Guest access'], 'No access')
              )
  - kind: SetVariable
    id: setVar3
    variable: Global.userInfo
    value: "User: \\"Premium\\""
  - kind: ConditionGroup
    id: conditionGroup1
    conditions:
      - id: condition1
        condition: =If(Global.testCondition = TRUE, true, False)
        actions:
          - kind: SetVariable
            id: nestedSetVar
            variable: Global.nestedVar
            value: "Nested value with \\"quotes\\""
'''
    
    # Write test files
    test_files = [
        ('test_issues_1.yml', test1_content),
        ('test_issues_2.yml', test2_content),
        ('test_issues_3.yml', test3_content)
    ]
    
    for filename, content in test_files:
        file_path = test_dir / filename
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content.strip())
        print(f"Created test file: {file_path}")

def run_validator_fixer_test():
    """Run the PowerFX validator and fixer test"""
    print("PowerFX Validator and Fixer - Test Suite")
    print("=" * 60)
    print("Testing validation and fixing based on Microsoft Learn documentation")
    print("Reference: https://learn.microsoft.com/en-us/power-platform/power-fx/overview")
    print("=" * 60)
    
    # Create temporary directory for test files
    with tempfile.TemporaryDirectory() as temp_dir:
        test_dir = Path(temp_dir)
        print(f"Test directory: {test_dir}")
        
        # Create test files
        print("\n1. Creating test YAML files with PowerFX issues...")
        print("-" * 50)
        create_test_yaml_files(test_dir)
        
        # Show original content with issues
        print("\n2. Original files with PowerFX issues:")
        print("-" * 50)
        for yaml_file in test_dir.glob("*.yml"):
            print(f"\n--- {yaml_file.name} ---")
            with open(yaml_file, 'r', encoding='utf-8') as f:
                content = f.read()
                # Show PowerFX expressions with issues
                lines = content.split('\n')
                in_powerfx = False
                for i, line in enumerate(lines):
                    if any(func in line for func in ['Switch', 'If', 'Concat', 'SetVariable']):
                        in_powerfx = True
                        print(f"Line {i+1}: {line}")
                    elif in_powerfx and (line.strip().startswith('-') or line.strip() == ''):
                        in_powerfx = False
                    elif in_powerfx:
                        print(f"Line {i+1}: {line}")
        
        # Setup logger and validator
        print(f"\n3. Running PowerFX Validator and Fixer...")
        print("-" * 50)
        
        logger = setup_logging(verbose=True)
        validator_fixer = PowerFXValidatorFixer(logger)
        
        # Process the directory
        results = validator_fixer.process_directory(test_dir)
        
        # Show results
        print(f"\n4. Validation and Fixing Results:")
        print("-" * 50)
        print(f"Files found:       {results['files_found']}")
        print(f"Files processed:   {results['files_processed']}")
        print(f"Files modified:    {results['files_modified']}")
        print(f"Issues found:      {results['total_issues']}")
        print(f"Issues fixed:      {results['total_fixes']}")
        print(f"Errors:           {results['errors']}")
        
        # Show fixed content
        if results['files_modified'] > 0:
            print(f"\n5. Fixed files (Microsoft PowerFX compliant):")
            print("-" * 50)
            for yaml_file in test_dir.glob("*.yml"):
                if yaml_file.suffix != '.backup':
                    print(f"\n--- {yaml_file.name} (FIXED) ---")
                    with open(yaml_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        # Show fixed PowerFX expressions
                        lines = content.split('\n')
                        in_powerfx = False
                        for i, line in enumerate(lines):
                            if any(func in line for func in ['Switch', 'If', 'Concatenate', 'SetVariable']):
                                in_powerfx = True
                                print(f"Line {i+1}: {line}")
                            elif in_powerfx and (line.strip().startswith('-') or line.strip() == ''):
                                in_powerfx = False
                            elif in_powerfx:
                                print(f"Line {i+1}: {line}")
        
        # Show backup files
        backup_files = list(test_dir.glob("*.backup"))
        if backup_files:
            print(f"\n6. Backup files created:")
            print("-" * 50)
            for backup_file in backup_files:
                print(f"   📁 {backup_file.name}")
        
        # Show comprehensive statistics
        validator_fixer.print_final_report()
        
        print(f"\n7. Microsoft PowerFX Compliance Summary:")
        print("-" * 50)
        print(f"✅ All fixes applied according to Microsoft Learn documentation")
        print(f"✅ PowerFX expressions now follow Microsoft standards")
        print(f"✅ String quoting: Single quotes → Double quotes")
        print(f"✅ Boolean values: Mixed case → Lowercase")
        print(f"✅ Functions: Concat() → Concatenate() for individual strings")
        print(f"✅ Arrays: [array] → Concatenate() function calls")
        print(f"✅ SetVariable: Removed quotes from values")
        
        print(f"\n8. Test completed!")
        print("   All PowerFX expressions are now Microsoft-compliant.")
        print("   Reference: https://learn.microsoft.com/en-us/power-platform/power-fx/overview")
        
        # Keep files for manual inspection if needed
        input("Press Enter to continue (temp files will be deleted)...")

def run_dry_run_test():
    """Run a dry-run test to show what would be fixed"""
    print("\nDry Run Test - Preview Mode")
    print("=" * 40)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        test_dir = Path(temp_dir)
        
        # Create one test file
        test_content = '''
actions:
  - kind: Question
    prompt:
      activity:
        speak:
          - cardPayload: |
              Switch(
                Global.type = 'phone',
                ['Message 1', 'Message 2'],
                If(Global.active = True, Concat('Active', 'User'), 'Inactive')
              )
'''
        
        test_file = test_dir / "dry_run_test.yml"
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content.strip())
        
        print(f"Created test file: {test_file}")
        
        # Run dry-run
        logger = setup_logging(verbose=False)
        validator_fixer = PowerFXValidatorFixer(logger)
        
        print(f"\nRunning dry-run validation...")
        was_modified = validator_fixer.process_yaml_file(test_file, dry_run=True)
        
        if was_modified:
            print(f"✅ File would be modified with Microsoft PowerFX compliance fixes")
        else:
            print(f"ℹ️  No changes needed - already compliant")
        
        # Show that original file is unchanged
        with open(test_file, 'r', encoding='utf-8') as f:
            final_content = f.read()
        
        if final_content.strip() == test_content.strip():
            print(f"✅ Original file unchanged (dry-run mode)")
        else:
            print(f"❌ ERROR: File was modified in dry-run mode!")

if __name__ == "__main__":
    print("PowerFX Validator and Fixer Test Suite")
    print("=" * 50)
    print("1. Full validation and fixing test")
    print("2. Dry-run test")
    print("=" * 50)
    
    try:
        # Run full test
        run_validator_fixer_test()
        
        # Run dry-run test
        run_dry_run_test()
        
        print(f"\n🎉 All tests completed successfully!")
        print(f"PowerFX Validator and Fixer is working correctly.")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        raise
