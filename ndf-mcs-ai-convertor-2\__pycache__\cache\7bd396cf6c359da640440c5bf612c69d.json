{"response": "```yaml\n- kind: Question\n  id: br0326_AutoPayEnrollDetails_DM\n  displayName: br0326_AutoPayEnrollDetails_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/br0326_nm1_01.wav\\\">Would you like to continue with the Auto Pay setup? Please say Yes or No</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/br0326_nm2_01.wav\\\">To continue with the Auto Pay setup, say Yes or press 1 Otherwise, say No or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.br0326_AutoPayEnrollDetails_DM_reco\n  prompt:\n    speak:\n      - \"{CustomAudio: com.nuance.att.application.audio.br0326_AutoPayEnrollDetails_DM_initial}\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"{CustomAudio: com.nuance.att.application.audio.br0326_AutoPayEnrollDetails_DM_noinput_01}\"\n        - \"{CustomAudio: com.nuance.att.application.audio.br0326_AutoPayEnrollDetails_DM_noinput_02}\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_br0326_AutoPayEnrollDetails_DM\n  value: =Text(Global.br0326_AutoPayEnrollDetails_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_br0326_AutoPayEnrollDetails_DM = \"true\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.isEligibleRTPromo = true && Global.paymentWithAutoPayEnrollAmount > 0, true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.intent\n                  value: refillPlusAutoPay\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/br0326_out_01.wav\\\">Great</audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: br0410_ARGetPaymentDevice_SD\n\n  elseActions:\n    - kind: SendActivity\n      id: sendActivity_REPLACE_THIS\n      activity:\n        speak:\n          - \"<audio src=\\\"AUDIO_LOCATION/br0326_out_02.wav\\\">Okay I won't enroll you in Auto Pay</audio>\"\n          - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\"></audio>\"\n\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.intent = \"refillPlusAutoPay\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.intent\n              value: refillAccount\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.cancelledRefillPlusAutoPay\n              value: true\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: br0335_AutoPayAddMoney_SD\n\n      elseActions:\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.B03_AutoRefill_05.dvxml#br0535_AutoRefillWrap_DM\n```", "timestamp": **********.408065, "content_hash": "7bd396cf6c359da640440c5bf612c69d"}