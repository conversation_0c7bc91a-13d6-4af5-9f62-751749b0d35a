{"response": "```yaml\n- kind: Question\n  id: TT1040_TransferOffer_DM\n  displayName: TT1040_TransferOffer_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TT1040_nm1_01.wav\\\">You can say 'transfer me'Or hang up and call back from another phone</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.TT1040_TransferOffer_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/TT1040_ini_01.wav\\\">I can transfer you to an agent now but the agent may ask you to hang up and call again from another phone You can say 'transfer me anyway' However, I recommend that you hang up and call back from another phone when it's convenient for you When you call back, choose troubleshooting tips from the main menu and then say 'agent'</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: transfer\n          displayName: transfer\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/TT1040_ni1_01.wav\\\">You can say 'transfer me'Or hang up and call back from another phone</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_TT1040_TransferOffer_DM\n  value: =Text(Global.TT1040_TransferOffer_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TT1040_TransferOffer_DM = \"transfer\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.maxRetryTroubleshooting\n          value: false\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TT1045_Transfer_PP\n```", "timestamp": 1749530410.8466148, "content_hash": "47a78b5c2d9eb850b69bc50bd209eeef"}