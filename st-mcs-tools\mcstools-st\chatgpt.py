import re
from ruamel.yaml import <PERSON>AM<PERSON>
from pathlib import Path
from tempfile import NamedTemporaryFile
import shutil

# Compile regex once for performance
SWITCH_PATTERN = re.compile(r'Switch\((.*?)\)', re.DOTALL)

# Initialize YAML processor
yaml = YAML()
yaml.preserve_quotes = True


def escape_single_quotes(text):
    return text.replace("'", "''")


def transform_array_literals(case_result):
    if case_result.startswith("[") and case_result.endswith("]"):
        # Find all individual array items (handles quoted strings or bare words/numbers)
        items = re.findall(r"'([^']*)'|\"([^\"]*)\"|([^,\[\]]+)", case_result[1:-1])
        flat_items = []
        for item in items:
            value = item[0] or item[1] or item[2].strip()
            # If already quoted, preserve outer quotes, just escape inner single quotes
            if (value.startswith("'") and value.endswith("'")) or (value.startswith('"') and value.endswith('"')):
                inner = value[1:-1].replace("'", "''")
                flat_items.append(f"{value[0]}{inner}{value[-1]}")
            elif value.isdigit() or (value.startswith("{") and value.endswith("}")):
                flat_items.append(value)
            else:
                escaped_value = value.replace("'", "''")
                flat_items.append(f"'{escaped_value}'")
        joined = ", ".join(flat_items)
        result = f"Concat({joined})"
        print(f"Transformed array literal: [{case_result}] → {result}")
        return result
    return case_result


def parse_cases(switch_content):
    # Split by commas at top level (not within brackets)
    parts, buf, depth = [], '', 0
    for char in switch_content:
        if char == '[':
            depth += 1
        elif char == ']':
            depth -= 1
        if char == ',' and depth == 0:
            parts.append(buf.strip())
            buf = ''
        else:
            buf += char
    if buf:
        parts.append(buf.strip())

    # Group as pairs (condition, result)
    initial_expr = parts[0]
    cases = list(zip(parts[1::2], parts[2::2]))
    return initial_expr, cases


def rebuild_switch(initial_expr, cases):
    case_strings = []
    for condition, result in cases:
        new_result = transform_array_literals(result)
        case_strings.append(f"{condition}, {new_result}")
    rebuilt = f"Switch({initial_expr}, {', '.join(case_strings)})"
    print(f"Rebuilt Switch: {rebuilt}")
    return rebuilt


def transform_switch_cases(yaml_text):
    switches = SWITCH_PATTERN.findall(yaml_text)
    print(f"Found {len(switches)} Switch statements to process.")
    for switch in switches:
        initial_expr, cases = parse_cases(switch)
        new_switch = rebuild_switch(initial_expr, cases)
        yaml_text = yaml_text.replace(f"Switch({switch})", new_switch)
    return yaml_text


def process_file(input_path, output_path, backup=True):
    with open(input_path, 'r', encoding='utf-8') as f:
        yaml_text = f.read()

    new_yaml_text = transform_switch_cases(yaml_text)

    if backup:
        shutil.copy2(input_path, f"{input_path}.bak")
        print(f"Backup created: {input_path}.bak")

    with NamedTemporaryFile('w', delete=False, encoding='utf-8') as tmp:
        tmp.write(new_yaml_text)
        temp_name = tmp.name

    shutil.move(temp_name, output_path)
    print(f"Updated YAML written to: {output_path}")


# CLI usage
if __name__ == '__main__':
    import sys
    if len(sys.argv) != 3:
        print("Usage: python yaml_switch_transformer.py input.yml output.yml")
        sys.exit(1)

    input_file = Path(sys.argv[1])
    output_file = Path(sys.argv[2])
    process_file(input_file, output_file)
    print(f"Transformation completed: {output_file}")
