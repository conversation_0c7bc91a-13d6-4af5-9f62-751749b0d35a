{"response": "```yaml\n- kind: Question\n  id: br0330_ARCycleChange_DM\n  displayName: br0330_ARCycleChange_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/com.nuance.att.application.audio.br0330_ARCycleChange_DM_nomatch_01\\\"> </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/com.nuance.att.application.audio.br0330_ARCycleChange_DM_nomatch_02\\\"> </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.br0330_ARCycleChange_DM_reco\n  prompt:\n    speak:\n      - \"{CustomAudio: com.nuance.att.application.audio.br0330_ARCycleChange_DM_initial}\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"{CustomAudio: com.nuance.att.application.audio.br0330_ARCycleChange_DM_noinput_01}\"\n        - \"{CustomAudio: com.nuance.att.application.audio.br0330_ARCycleChange_DM_noinput_02}\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_br0330_ARCycleChange_DM\n  value: =Text(Global.br0330_ARCycleChange_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_br0330_ARCycleChange_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.isMigratingAutoPay\n          value: true\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/br0330_out_02.wav\\\">Okay</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: br0410_ARGetPaymentDevice_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_br0330_ARCycleChange_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/br0330_out_01.wav\\\">Okay Your AutoPay settings wont be changed</audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\"></audio>\"\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.B03_AutoRefill_05.dvxml\n```", "timestamp": 1749469925.44664, "content_hash": "c9c19ed71a3dd7ce8e56f4e821100f9b"}