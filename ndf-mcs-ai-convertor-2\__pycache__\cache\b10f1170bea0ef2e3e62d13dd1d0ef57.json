{"response": "```yaml\n- kind: Question\n  id: aa1051_CollectLostStolenLastFour_DM\n  displayName: aa1051_CollectLostStolenLastFour_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa1051_nm1_01.wav\\\">Sorry</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.aa1051_CollectLostStolenLastFour_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa1051_ini_01.wav\\\">If you know your</audio> {Global.dnisInfo.terminologyEbtCard} <audio src=\\\"AUDIO_LOCATION/aa1051_ini_03.wav\\\">card number, please say or enter the last four digits now short pause Otherwise, say  I don t know it  or press 1</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: default\n          displayName: default\n        - id: card_number_not_known\n          displayName: card_number_not_known\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/aa1051_ni1_01.wav\\\">Sorry</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa1051_CollectLostStolenLastFour_DM\n  value: =Text(Global.aa1051_CollectLostStolenLastFour_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa1051_CollectLostStolenLastFour_DM = \"default\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.cardInfoVariables.collectedCardNumLastFour\n          value: aa1051_CollectLostStolenLastFour_DM.returnvalue\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aa1051_out_01.wav\\\">Thank you</audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa1084_CheckCardsForMatch_DB_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa1051_CollectLostStolenLastFour_DM = \"card_number_not_known\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa1054_LostStolenNeedAdditionalInfo_PP\n```", "timestamp": 1749458504.0861158, "content_hash": "b10f1170bea0ef2e3e62d13dd1d0ef57"}