{"response": "```yaml\n- kind: Question\n  id: PC2010_FindPINWaitSBI_DM\n  displayName: PC2010_FindPINWaitSBI_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.PC2010_FindPINWaitSBI_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/PC2010_ini_01.wav\\\">Once you ve found that number, say  continue  or press 1 10sec wait music When you have your  Payment PIN, say  continue  or press 1 Or say  I can t find it  or press 2 10sec wait musicAt any time, say  continue  or press 1 Or say  I can t find it  or press 2 10sec wait musicWhen you have your  Payment PIN, say  continue  or press 1 Or say  I can t find it  or press 2 10sec wait music At any time, say  continue  or press 1 Or say  I can t find it  or press 2 10sec wait music When you have your  Payment PIN, say  continue  or press 1 Or say  I can t find it  or press 2 10sec wait music</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: continue\n          displayName: continue\n        - id: cant_find\n          displayName: cant_find\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_PC2010_FindPINWaitSBI_DM\n  value: =Text(Global.PC2010_FindPINWaitSBI_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_PC2010_FindPINWaitSBI_DM = \"continue\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.fromFindPINWait\n          value: true\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/PC2010_out_01.wav\\\">All right!</audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: PC1010_GetPrepaidCardPIN_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_PC2010_FindPINWaitSBI_DM = \"cant_find\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/PC2010_out_02.wav\\\">No problem</audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: PC2015_UseBankCardYN_DM\n```", "timestamp": **********.7224643, "content_hash": "75bb36424d09eb78460e2a16ab92530b"}