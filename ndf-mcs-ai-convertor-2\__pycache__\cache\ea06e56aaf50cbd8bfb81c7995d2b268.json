{"response": "```yaml\n- kind: Question\n  id: AC1500_CreateSecurityCode_DM\n  displayName: AC1500_CreateSecurityCode_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - |\n        {Switch(\n              true,\n              Global.accountPinToggleOn = true,\n              \"What's the 6-to-15-digit account PIN you want to create? \",\n              true,\n              \"What's the 8-digit account PIN you want to create?  \"\n          )\n        }\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AC1500_nm2_01.wav\\\">Say or enter the account PIN youd like</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - |\n        {Switch(\n              true,\n              Global.accountPinToggleOn = true,\n              \"Say or enter a 6-to-15-digit number to use as your account PIN\",\n              true,\n              \"Say or enter an 8-digit number to use as your account PIN\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.AC1500_CreateSecurityCode_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.accountPinToggleOn = true,\n              \"It needs to be 6-to-15-digits long, and something you can easily remember every time you call or log into the app But it can't be a date, like your birthday - those are too easy for other people to guess! So, what's the 6-to-15-digit PIN you want to use? \",\n              true,\n              \" It needs to be 8-digits long, and something you can easily remember every time you call or log into the app But it can't be a date, like your birthday - those are too easy for other people to guess! So, what's the 6-to-15-digit PIN you want to use? \"\n          )\n        }\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.accountPinToggleOn = true,\n                \"What's the 6-to-15-digit account PIN you want to create? \",\n                true,\n                \"What's the 8-digit account PIN you want to create? \"\n            )\n          }\n        - \"<audio src=\\\"AUDIO_LOCATION/AC1500_ni2_01.wav\\\">Say or enter the account PIN youd like</audio>\"\n        - |\n          {Switch(\n                true,\n                Global.accountPinToggleOn = true,\n                \"Say or enter the 6-to-15-digit number to use as your account PIN\",\n                true,\n                \"Say or enter the 8-digit number to use as your account PIN\"\n            )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.accountPinToggleOn\n  value: GlobalVars.GetBCSParameters.accountPinToggleOn\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.securityPinCode\n  value: AC1500_CreateSecurityCode_DM.returnvalue\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: AC1510_ConfirmSecurityCodeYN_DM\n```", "timestamp": **********.3596094, "content_hash": "ea06e56aaf50cbd8bfb81c7995d2b268"}