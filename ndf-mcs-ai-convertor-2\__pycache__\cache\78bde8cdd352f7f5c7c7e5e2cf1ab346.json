{"response": "```yaml\n- kind: Question\n  id: pf0310_PlayPackageInfo_DM\n  displayName: pf0310_PlayPackageInfo_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/com.nuance.att.application.audio.pf0310_PlayPackageInfo_noinput_1\\\"> </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/com.nuance.att.application.audio.pf0310_PlayPackageInfo_noinput_2\\\"> </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.pf0310_PlayPackageInfo_DM_reco\n  prompt:\n    speak:\n      - \"{CustomAudio: com.nuance.att.application.audio.pf0310_PlayPackageInfo_initial}\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: add_it\n          displayName: add_it\n        - id: other\n          displayName: other\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/com.nuance.att.application.audio.pf0310_PlayPackageInfo_noinput_1\\\"> </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/com.nuance.att.application.audio.pf0310_PlayPackageInfo_noinput_2\\\"> </audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.numPackages > 1 || (Global.fromPlansAndPackages = true && Global.pbNumPlans > 0), true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_dtmfgrammar1\n          value: 'pf0310_PlayPackageInfo_DM_dtmf.grxml'\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_grammar1\n          value: 'pf0310_PlayPackageInfo_DM.grxml'\n\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.collection_dtmfgrammar1\n      value: 'pf0310_PlayPackageInfo_DM_dtmf.grxml?SWI_vars.disallow=other&'\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.collection_grammar1\n      value: 'pf0310_PlayPackageInfo_DM.grxml?SWI_vars.disallow=other&'\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_pf0310_PlayPackageInfo_DM\n  value: =Text(Global.pf0310_PlayPackageInfo_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pf0310_PlayPackageInfo_DM = \"add_it\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.choseOtherPackages\n          value: false\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: pf0410_NeedToResolveConflicts_JDA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_pf0310_PlayPackageInfo_DM = \"other\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.choseOtherPackages\n              value: true\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: pf0315_OtherPackagesInCategory_JDA\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_pf0310_PlayPackageInfo_DM = \"repeat\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: pf0310_PlayPackageInfo_DM\n```", "timestamp": 1749471776.7332778, "content_hash": "78bde8cdd352f7f5c7c7e5e2cf1ab346"}