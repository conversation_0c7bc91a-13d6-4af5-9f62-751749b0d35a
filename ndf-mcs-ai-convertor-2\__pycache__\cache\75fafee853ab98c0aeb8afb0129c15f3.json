{"response": "```yaml\n- kind: Question\n  id: st0425_AskAddMinutes_DM\n  displayName: st0425_AskAddMinutes_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/st0425_ni1_01.wav\\\">Do you want to hear about adding a minute package? Say Yes or No</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/st0425_ni2_01.wav\\\">Youve used up the minutes that are included with your plan until it renews at the end of the day on</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/st0425_ni2_02.wav\\\">If you want to make calls before then, you will need to buy more minutes If you want to hear about that, say Yes or press 1, or if not, say No or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.st0425_AskAddMinutes_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/st0425_ini_01.wav\\\">You cant make calls right now because Youve used up the minutes that come with your plan So if you need to make calls before it renews at the end of the day on</audio> {Global.planRenewDateStr} <audio src=\\\"AUDIO_LOCATION/st0425_ini_02.wav\\\">you will need to add a minute package Would you like to hear more about that?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/st0425_ni1_01.wav\\\">Do you want to hear about adding a minute package? Say Yes or No</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/st0425_ni2_01.wav\\\">Youve used up the minutes that are included with your plan until it renews at the end of the day on</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/st0425_ni2_02.wav\\\">If you want to make calls before then, you will need to buy more minutes If you want to hear about that, say Yes or press 1, or if not, say No or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_st0425_AskAddMinutes_DM\n  value: =Text(Global.st0425_AskAddMinutes_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_st0425_AskAddMinutes_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: addPackages\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.packageDestination\n          value: minutes\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: st0430_CallAddPackages_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_st0425_AskAddMinutes_DM = \"false\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: S02_TechSupport_01.dvxml\n```\n**Notes on the output:**\n- All audio prompts are referenced with `<audio src=\"AUDIO_LOCATION/...\">...</audio>` as per your pattern.\n- The prompt and inputTimeoutResponse combine the audio and variable as per your examples.\n- The entity is set to `BooleanPrebuiltEntity` because the success actions are for \"true\" and \"false\".\n- The SetVariable for the main variable is included before the ConditionGroup.\n- The ConditionGroup checks for \"true\" and \"false\" and sets variables and actions accordingly.\n- Only the `<success>` block is handled for logic; `<command>` is ignored as per your instructions.\n- All ids for SetVariable, ConditionGroup, etc., are set to `REPLACE_THIS` as placeholders for your system to generate unique ids.\n- The prompt for the date uses `{Global.planRenewDateStr}` as a placeholder for the date value.", "timestamp": 1749472027.3419666, "content_hash": "75fafee853ab98c0aeb8afb0129c15f3"}