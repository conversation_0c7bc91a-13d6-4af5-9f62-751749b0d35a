{"response": "```yaml\n- kind: Question\n  id: MP1040_SelectRatePlanMoreInfo_DM\n  displayName: MP1040_SelectRatePlanMoreInfo_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MP1030_nm1_01.wav\\\">Say</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MP1030_nm2_01.wav\\\">Say</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MP1030_nm2_01.wav\\\">Say</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/MP1030_nm3_01.wav\\\">Or say  more information </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.MP1040_SelectRatePlanMoreInfo_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            // Case 1: reentry_MP1040_SelectRatePlanMoreInfo_DM != true && numberOfPlans == 2\n            Global.reentry_MP1040_SelectRatePlanMoreInfo_DM <> true && Global.numberOfPlans = 2,\n            \"<audio src=\\\"AUDIO_LOCATION/MP1040_ini_01.wav\\\">We have two plans to choose from, and the price for each plan includes all taxes and regulatory fees</audio>\",\n\n            // Case 2: reentry_MP1040_SelectRatePlanMoreInfo_DM != true && numberOfPlans > 2\n            Global.reentry_MP1040_SelectRatePlanMoreInfo_DM <> true && Global.numberOfPlans > 2,\n            \"<audio src=\\\"AUDIO_LOCATION/MP1040_ini_04.wav\\\">We have several plans to choose from</audio>\",\n\n            // Case 3: isOnFamilyPlan != true && reentry_MP1040_SelectRatePlanMoreInfo_DM != true && lowestPrice != highestPrice\n            Global.isOnFamilyPlan <> true && Global.reentry_MP1040_SelectRatePlanMoreInfo_DM <> true && Global.lowestPrice <> Global.highestPrice,\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/MP1040_ini_05.wav\\\">They range in price from</audio>\",\n              \"{Global.lowestPrice}\",\n              \"<audio src=\\\"AUDIO_LOCATION/MP1040_ini_07.wav\\\">to</audio>\",\n              \"{Global.highestPrice}\",\n              \"<audio src=\\\"AUDIO_LOCATION/MP1040_ini_09.wav\\\">dollars a month, and the price for each plan includes all taxes and regulatory fees</audio>\"\n            ],\n\n            // Default: Always play the following sequence\n            true,\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/MP1040_ini_10.wav\\\">When you hear the one you want, just say the name of the plan back to me</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\",\n              \"{Global.availablePlansList}\", // Custom dynamic prompt\n              \"<audio src=\\\"AUDIO_LOCATION/MP1040_ini_24.wav\\\">Now, which one would you like -- You can say</audio>\",\n              \"{Global.availablePlansList}\", // Custom dynamic prompt\n              \"<audio src=\\\"AUDIO_LOCATION/MP1040_ini_36.wav\\\">Or you can say  repeat that  to hear those options again</audio>\",\n              // If choseInvalidPlan == true\n              \"{Switch(true, Global.choseInvalidPlan = true, \\\"<audio src=\\\\\\\"AUDIO_LOCATION/MP1040_ini_06.wav\\\\\\\">If none of those are what you wanted, you can say 'different plan'</audio>\\\", \\\"\\\")}\"\n            ]\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: more_info\n          displayName: more_info\n        - id: different_plan\n          displayName: different_plan\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/MP1030_nm1_01.wav\\\">Say</audio>\"\n        - \"{Global.availablePlansList}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MP1030_nm2_01.wav\\\">Say</audio>\"\n        - \"{Global.availablePlansList}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MP1030_nm2_01.wav\\\">Say</audio>\"\n        - \"{Global.availablePlansList}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/MP1030_nm3_01.wav\\\">Or say  more information </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.numberOfPlans\n  value: numberOfRatePlans(GlobalVars.ratePlans)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.lowestPrice\n  value: getLowestRatePlanPrice(GlobalVars.ratePlans)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.highestPrice\n  value: getHighestRatePlanPrice(GlobalVars.ratePlans)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.availablePlansList\n  value: getAllRatePlans(GlobalVars.ratePlans)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.choseInvalidPlan\n  value: GlobalVars.choseInvalidPlan\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.isOnFamilyPlan\n  value: GlobalVars.isOnFamilyPlan\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.planType\n  value: none\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.planGrammarURL\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.planGrammarDtmfURL\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.allowedResponses\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.planGrammarURL\n  value: GlobalVars.GetAvailableRatePlans.RatePlanGrammarURL\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.planGrammarDtmfURL\n  value: GlobalVars.GetAvailableRatePlans.RatePlanDTMFGrammarURL\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.disambigTooManyMatches\n  value: false\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.disambigNoMatches\n  value: false\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_MP1040_SelectRatePlanMoreInfo_DM\n  value: =Text(Global.MP1040_SelectRatePlanMoreInfo_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_MP1040_SelectRatePlanMoreInfo_DM = \"more_info\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/MP1040_hlp_01.wav\\\">Sure I m just going through the details of the plans you can choose from When you hear the one you want, just say the name back to me, and we ll proceed with the activation I ll go through the plan descriptions again now</audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.reentry_MP1040_SelectRatePlanMoreInfo_DM\n          value: true\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: MP1040_SelectRatePlanMoreInfo_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_MP1040_SelectRatePlanMoreInfo_DM = \"different_plan\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.activationResult\n              value: transfer\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_MP1040_SelectRatePlanMoreInfo_DM = \"default\", true, false)\n              actions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.MP1040_SelectRatePlanMoreInfo_DM.nbestresults <> undefined, true, false)\n                      actions:\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.GlobalVars.ratePlanSelectionType\n                          value: MP1040_SelectRatePlanMoreInfo_DM.nbestresults[0].interpretation.selectionType\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.GlobalVars.nbestresults\n                          value: MP1040_SelectRatePlanMoreInfo_DM.nbestresults\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.selectedPlan\n                  value: MP1040_SelectRatePlanMoreInfo_DM.returnvalue\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.choseInvalidPlan\n                  value: undefined\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: MP1042_CheckAmbiguity_JDA_DA\n```", "timestamp": 1749558515.156593, "content_hash": "6b780504bdb5aec6b5e52fdae49d0af3"}