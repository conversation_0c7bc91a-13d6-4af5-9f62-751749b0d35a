{"response": "```yaml\n- kind: Question\n  id: ES1205_IMEITransitionSkipSBI_DM\n  displayName: ES1205_IMEITransitionSkipSBI_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.ES1205_IMEITransitionSkipSBI_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.twoFactorAuthOutcome = \"success\",\n            \"Next, I ll need the serial number, or I-M-E-I, for the phone you re switching to\",\n            Global.switchLinesSuccess = true,\n            \"First, the serial number, or I-M-E-I, for the phone you re switching to\",\n            \"Next, the serial number, or I-M-E-I, for the phone you re switching to\",\n            \"test\",\n            \"If you already *have* the number, press 1 \"\n        )}\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ES1205_ni1_01.wav\\\">Otherwise, here's a neat trick to get it! </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.switchLinesSuccess\n  value: GlobalVars.switchLinesSuccess\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.twoFactorAuthOutcome\n  value: GlobalVars.twoFactorAuthOutcome\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: ES1220_CollectIMEI_DM\n```", "timestamp": 1749528033.9516096, "content_hash": "ef173f10fcf265cd9d4f994c3cfe9588"}