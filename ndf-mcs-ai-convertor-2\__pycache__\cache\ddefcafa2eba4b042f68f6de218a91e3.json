{"response": "```yaml\n- kind: Question\n  id: su0220_UnsuspendWrapMenu_DM\n  displayName: su0220_UnsuspendWrapMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/su0220_ni1_01.wav\\\">If that's all you needed, go ahead and hang up For anything else, say Main Menu</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/su0220_ni2_01.wav\\\">If you don't need any more help, you can hang up If you do need help with something else, press star</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.su0220_UnsuspendWrapMenu_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/su0220_ini_01.wav\\\">If that's all you needed, go ahead and hang up For anything else, say Main Menu</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: mainmenu\n          displayName: mainmenu\n        - id: operator\n          displayName: operator\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/su0220_ni1_01.wav\\\">If that's all you needed, go ahead and hang up For anything else, say Main Menu</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/su0220_ni2_01.wav\\\">If you don't need any more help, you can hang up If you do need help with something else, press star</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_su0220_UnsuspendWrapMenu_DM\n  value: =Text(Global.su0220_UnsuspendWrapMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_su0220_UnsuspendWrapMenu_DM = \"mainmenu\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferDestination\n          value: attPrepaid\n\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M01_MainMenu2_Dialog\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_su0220_UnsuspendWrapMenu_DM = \"operator\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.lastTransferState\n          value: su0220_UnsuspendWrapMenu_DM\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferReason\n          value: AGENT_REQUEST\n\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M09_MainMenu_Dialog\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_su0220_UnsuspendWrapMenu_DM = \"repeat\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: su0220_UnsuspendWrapMenu_DM\n```", "timestamp": 1749472404.7803957, "content_hash": "ddefcafa2eba4b042f68f6de218a91e3"}