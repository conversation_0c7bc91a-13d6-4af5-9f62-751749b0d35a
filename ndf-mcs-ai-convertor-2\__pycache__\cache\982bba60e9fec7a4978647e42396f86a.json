{"response": "```yaml\n- kind: Question\n  id: GC1000_GetSecurityCode_DM\n  displayName: GC1000_GetSecurityCode_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - |\n        {Switch(\n              true,\n              Global.accountPinToggleOn = true,\n              \"Please say or enter your 6-to-15-digit account PIN  If you need to find it, say 'wait a minute' You can also say 'where can I find it\",\n              true,\n              \"Please say or enter your 8-digit account PIN  If you need to find it, say 'wait a minute' You can also say 'where can I find it' \"\n          )\n        }\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - |\n        {Switch(\n              true,\n              Global.accountPinToggleOn = true,\n              \"Please enter your 6-to-15-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you need information on where to look for it, say 'where can I find it'\",\n              true,\n              \"Please enter your 8-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you need information on where to look for it, say 'where can I find it'\"\n          )\n        }\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - |\n        {Switch(\n              true,\n              Global.accountPinToggleOn = true,\n              \"Please enter your 6-to-15-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you need information on where to look for it, say 'where can I find it'\",\n              true,\n              \"Please enter your 8-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you need information on where to look for it, say 'where can I find it'\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.GC1000_GetSecurityCode_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.accountPinToggleOn = true,\n              \"Now, to help you with that, I also need your 6-to-15-digit account PIN  Say or enter it now\",\n              true,\n              \"Now, to help you with that, I also need your 8-digit account PIN  Say or enter it now\"\n          )\n        }\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: dont_know\n          displayName: dont_know\n        - id: wait\n          displayName: wait\n        - id: code_info\n          displayName: code_info\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.accountPinToggleOn = true,\n                \"Please say or enter your 6-to-15-digit account PIN  If you need to find it, say 'wait a minute' You can also say 'where can I find it\",\n                true,\n                \"Please say or enter your 8-digit account PIN  If you need to find it, say 'wait a minute' You can also say 'where can I find it' \"\n            )\n          }\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - |\n          {Switch(\n                true,\n                Global.accountPinToggleOn = true,\n                \"Please enter your 6-to-15-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you need information on where to look for it, say 'where can I find it'\",\n                true,\n                \"Please enter your 8-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you need information on where to look for it, say 'where can I find it'\"\n            )\n          }\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - |\n          {Switch(\n                true,\n                Global.accountPinToggleOn = true,\n                \"Please enter your 6-to-15-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you need information on where to look for it, say 'where can I find it'\",\n                true,\n                \"Please enter your 8-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you need information on where to look for it, say 'where can I find it'\"\n            )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.reentry\n  value: \"GlobalVars.GC1000_visited ? GlobalVars.GC1000_visited : false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.FromGC1010\n  value: \"GlobalVars.FromGC1010 ? GlobalVars.FromGC1010 : false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.callType\n  value: GlobalVars.callType\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.askSQEntry\n  value: GlobalVars.askSQEntry\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_maxnoinputs\n  value: 3\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.heardCodeInfo\n  value: GlobalVars.heardCodeInfo\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.lastPinTry\n  value: GlobalVars.lastPinTry\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.saidOperator\n  value: \"GlobalVars.saidOperatorGC1000?GlobalVars.saidOperatorGC1000:false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.continue228TaskAfterPayment\n  value: GlobalVars.continue228TaskAfterPayment\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.accountPinToggleOn\n  value: GlobalVars.GetBCSParameters.accountPinToggleOn\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.heardCodeInfo, true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_maxnoinputs\n          value: 0\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.GC1000_visited\n  value: true\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.FromGC1010\n  value: false\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_GC1000_GetSecurityCode_DM\n  value: =Text(Global.GC1000_GetSecurityCode_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_GC1000_GetSecurityCode_DM = \"dont_know\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.lastPinTry = true, true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.dontKnowPIN\n                  value: true\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: GC1090_GoToTransfer_SD\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.heardCodeInfo\n              value: false\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.transferReason\n              value: getPIN.dontKnow\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: GC1085_GoToAccountPinReset_SD\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_GC1000_GetSecurityCode_DM = \"wait\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/GC1000_out_02.wav\\\">No problem I'll wait</audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.heardCodeInfo\n          value: false\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: GC1010_AskLoginWait_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_GC1000_GetSecurityCode_DM = \"code_info\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.heardCodeInfo\n          value: true\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: GC1000_GetSecurityCode_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_GC1000_GetSecurityCode_DM = \"default\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GetSecurityCodeVars.securityCode\n          value: GC1000_GetSecurityCode_DM.returnvalue\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.heardCodeInfo\n          value: false\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.verificationType\n          value: pin\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.verificationValue\n          value: GC1000_GetSecurityCode_DM.returnvalue\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_PIN\n          value: GC1000_GetSecurityCode_DM.returnvalue\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: GC1029_Authenticate_DB_DA\n```", "timestamp": 1749528355.8814049, "content_hash": "982bba60e9fec7a4978647e42396f86a"}