{"response": "```yaml\n- kind: Question\n  id: aa4105_OtherOptionsMenuDTMF_DM\n  displayName: aa4105_OtherOptionsMenuDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"{Switch( true, true, \\\"{com.nuance.fis.audio.aa4105_OtherOptionsMenuDTMF_DM_Render}\\\")}\"\n      - \"{Switch( true, true, \\\"{com.nuance.fis.audio.aa4105_OtherOptionsMenuDTMF_DM_Render}\\\")}\"\n\n  alwaysPrompt: true\n  variable: Global.aa4105_OtherOptionsMenuDTMF_DM_reco\n  prompt:\n    speak:\n      - \"{com.nuance.fis.audio.aa4105_OtherOptionsMenuDTMF_DM_Render}\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: benefit_access\n          displayName: benefit_access\n        - id: card_issues\n          displayName: card_issues\n        - id: overview\n          displayName: overview\n        - id: retailerFees\n          displayName: retailerFees\n        - id: secondaryCard\n          displayName: secondaryCard\n        - id: reportFraud\n          displayName: reportFraud\n        - id: something_else\n          displayName: something_else\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"{Switch( true, true, \\\"{com.nuance.fis.audio.aa4105_OtherOptionsMenuDTMF_DM_Render}\\\")}\"\n        - \"{Switch( true, true, \\\"{com.nuance.fis.audio.aa4105_OtherOptionsMenuDTMF_DM_Render}\\\")}\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa4105_OtherOptionsMenuDTMF_DM\n  value: =Text(Global.aa4105_OtherOptionsMenuDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa4105_OtherOptionsMenuDTMF_DM = \"benefit_access\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa4110_BenefitsAccessPlayout_PP\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa4105_OtherOptionsMenuDTMF_DM = \"card_issues\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.transferReason\n          value: pin\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.transferAllowed\n          value: true\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.dnisInfo.cardPinIssuesWrapMenu = true, true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: aa4140_CardPinIssuesPlayout_PP\n\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.dnisInfo.specialTransferMessageCardPinIssuesOn = true, true, false)\n              actions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"{Global.dnisInfo.specialTransferMessageCardPinIssues}\"\n\n          elseActions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/aa4105_out_01.wav\\\">I ll find someone who can help with enquiry pertaining to card or pin</audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: transferHandler_CS\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa4105_OtherOptionsMenuDTMF_DM = \"overview\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aa4105_out_02.wav\\\">Sure</audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa4120_EBTOverViewPlayout_PP\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa4105_OtherOptionsMenuDTMF_DM = \"retailerFees\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa4150_RetailFeesPlayout_PP\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa4105_OtherOptionsMenuDTMF_DM = \"secondaryCard\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa4170_SecondaryCardFeesPlayout_PP\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa4105_OtherOptionsMenuDTMF_DM = \"reportFraud\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa4160_ReportFraudPlayout_PP\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa4105_OtherOptionsMenuDTMF_DM = \"something_else\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aa4105_out_03.wav\\\">Okay</audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa4004_GetMainMenuOptions_DB_DA\n```", "timestamp": 1749543607.0882926, "content_hash": "3fb0eaeb29257c6b8a89f5ec03ec46e8"}