{"response": "```yaml\n- kind: Question\n  id: PC2015_UseBankCardYN_DM\n  displayName: PC2015_UseBankCardYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/PC2015_ini_01.wav\\\">Would you like to pay with a bank card instead?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/PC2015_nm2_01.wav\\\">Do you wanna make your payment with a bank card instead? Say  yes  or press 1 or  no  or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/PC2015_nm2_01.wav\\\">Do you wanna make your payment with a bank card instead? Say  yes  or press 1 or  no  or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.PC2015_UseBankCardYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/PC2015_ini_01.wav\\\">Would you like to pay with a bank card instead?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/PC2015_ini_01.wav\\\">Would you like to pay with a bank card instead?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/PC2015_nm2_01.wav\\\">Do you wanna make your payment with a bank card instead? Say  yes  or press 1 or  no  or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/PC2015_nm2_01.wav\\\">Do you wanna make your payment with a bank card instead? Say  yes  or press 1 or  no  or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_PC2015_UseBankCardYN_DM\n  value: =Text(Global.PC2015_UseBankCardYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_PC2015_UseBankCardYN_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.payingWithPrepaid\n          value: false\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.offerPrepaid\n          value: false\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.skipBalance\n          value: true\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: getReturnLink()\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_PC2015_UseBankCardYN_DM = \"false\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.payingWithPrepaid\n              value: false\n\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.GlobalVars.callType = \"activate\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.activationResult\n                      value: transfer\n\n              elseActions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.callType\n                  value: undefined\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n```", "timestamp": 1749529178.3932931, "content_hash": "4563b153f380d83ffc8a23e4c33a7993"}