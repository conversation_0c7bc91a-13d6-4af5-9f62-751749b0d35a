{"response": "```yaml\n- kind: Question\n  id: cn0430_ChangeNumberWrap_DM\n  displayName: cn0430_ChangeNumberWrap_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I missed that</audio>\"\n      - |\n        {Switch(\n              true,\n              Global.autoRefillStatus = \"enrolled\" && Global.balanceAmount = 0,\n              \"Say Help With Voicemail or press 1, Make a Payment or press 2, or Auto Pay or press 3\",\n              Global.autoRefillStatus = \"enrolled\",\n              \"Say Help With Voicemail or press 1 or AutoPay or press 2\",\n              Global.balanceAmount = 0,\n              \"Say Help With Voicemail or press 1, or Make a Payment or press 2\",\n              \"Say Help With Voicemail or press 1\"\n          )\n        }\n      - \"<audio src=\\\"AUDIO_LOCATION/cn0430_ni1_05.wav\\\">If you need help with anything else, say Main Menu or press star Or if youre all done, go ahead and hang up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">Sorry</audio>\"\n      - |\n        {Switch(\n              true,\n              Global.autoRefillStatus = \"enrolled\" && Global.balanceAmount = 0,\n              \"For help setting up voicemail with your new number, press 1 To make a payment to your account, press 2 To re-enroll in Auto Pay, press 3\",\n              Global.autoRefillStatus = \"enrolled\",\n              \"For help setting up voicemail with your new number, press 1 To re-enroll in AutoPay, press 2\",\n              Global.balanceAmount = 0,\n              \"For help setting up voicemail with your new number, press 1 To make a payment to your account, press 2\",\n              \"For help setting up voicemail with your new number, press 1\"\n          )\n        }\n      - \"<audio src=\\\"AUDIO_LOCATION/cn0430_ni2_05.wav\\\">For anything else, press star, or if youre all done, just hang up</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.cn0430_ChangeNumberWrap_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/cn0430_ini_01.wav\\\">If youre done, just hang up</audio>\"\n      - |\n        {Switch(\n              true,\n              Global.autoRefillStatus = \"enrolled\" && Global.balanceAmount = 0,\n              \"You can also say Help With Voicemail, Make a Payment, or Auto Pay\",\n              Global.autoRefillStatus = \"enrolled\",\n              \"You can also say Help With Voicemail or AutoPay\",\n              Global.balanceAmount = 0,\n              \"You can also say Help With Voicemail, or Make a Payment\",\n              \"You can also say Help With Voicemail\"\n          )\n        }\n      - \"<audio src=\\\"AUDIO_LOCATION/cn0430_ini_06.wav\\\">Or for anything else, say Main Menu</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: voicemail\n          displayName: voicemail\n        - id: auto_refill\n          displayName: auto_refill\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.autoRefillStatus = \"enrolled\" && Global.balanceAmount = 0,\n                \"Say Help With Voicemail or press 1, Make a Payment or press 2, or Auto Pay or press 3\",\n                Global.autoRefillStatus = \"enrolled\",\n                \"Say Help With Voicemail or press 1 or AutoPay or press 2\",\n                Global.balanceAmount = 0,\n                \"Say Help With Voicemail or press 1, or Make a Payment or press 2\",\n                \"Say Help With Voicemail or press 1\"\n            )\n          }\n        - \"<audio src=\\\"AUDIO_LOCATION/cn0430_ni1_05.wav\\\">If you need help with anything else, say Main Menu or press star Or if youre all done, go ahead and hang up</audio>\"\n        - |\n          {Switch(\n                true,\n                Global.autoRefillStatus = \"enrolled\" && Global.balanceAmount = 0,\n                \"For help setting up voicemail with your new number, press 1 To make a payment to your account, press 2 To re-enroll in Auto Pay, press 3\",\n                Global.autoRefillStatus = \"enrolled\",\n                \"For help setting up voicemail with your new number, press 1 To re-enroll in AutoPay, press 2\",\n                Global.balanceAmount = 0,\n                \"For help setting up voicemail with your new number, press 1 To make a payment to your account, press 2\",\n                \"For help setting up voicemail with your new number, press 1\"\n            )\n          }\n        - \"<audio src=\\\"AUDIO_LOCATION/cn0430_ni2_05.wav\\\">For anything else, press star, or if youre all done, just hang up</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.allowedOptions\n  value: voicemail\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.balanceAmount = 0, true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.allowedOptions\n          value: =allowedOptions+'^refill'\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.autoRefillStatus = \"enrolled\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.allowedOptions\n          value: =allowedOptions+'^auto_refill'\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_dtmfgrammar1\n  value: = 'cn0430_ChangeNumberWrap_DM_dtmf.jsp?allowedOptions='+allowedOptions+'&'\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_grammar1\n  value: = 'cn0430_ChangeNumberWrap_DM.jsp?allowedOptions='+allowedOptions+'&'\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_cn0430_ChangeNumberWrap_DM\n  value: =Text(Global.cn0430_ChangeNumberWrap_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_cn0430_ChangeNumberWrap_DM = \"voicemail\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: techSupportVoicemail\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: cn0435_ChangeNumberVoicemail_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_cn0430_ChangeNumberWrap_DM = \"auto_refill\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.intent\n              value: autoRefill\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: cn0440_ChangeNumberAutoRefill_SD\n```", "timestamp": 1749470369.1241386, "content_hash": "fea1bb69b83c697d20ef17b80fe7fa4c"}