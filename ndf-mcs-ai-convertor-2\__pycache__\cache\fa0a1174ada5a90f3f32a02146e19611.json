{"response": "```yaml\n- kind: Question\n  id: lo0150_RCIA2ndSlot_DM\n  displayName: lo0150_RCIA2ndSlot_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.lo0150_RCIA2ndSlot_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/lo0150_ini_01.wav\\\">I wont be able to help you in our automated system without your wireless number, so Ill need to connect you to a representative But first, to improve our service, wed like to understand how to better, so in a few words, please tell me why youre calling</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/lo0150_ini_01.wav\\\">I wont be able to help you in our automated system without your wireless number, so Ill need to connect you to a representative But first, to improve our service, wed like to understand how to better, so in a few words, please tell me why youre calling</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_lo0150_RCIA2ndSlot_DM\n  value: =Text(Global.lo0150_RCIA2ndSlot_DM_reco)\n\n- kind: SendActivity\n  id: sendActivity_REPLACE_THIS\n  activity:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/lo0150_out_01.wav\\\">Sorry, lets try this another way</audio>\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.transferReason\n  value: BUSINESS_RULE\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.transferPrompt\n  value: default\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.transferDestination\n  value: attPrepaid\n\n- kind: BeginDialog\n  id: begin_REPLACE_THIS\n  dialog: topic.M09_MainMenu_Dialog\n```", "timestamp": 1749471338.8920348, "content_hash": "fa0a1174ada5a90f3f32a02146e19611"}