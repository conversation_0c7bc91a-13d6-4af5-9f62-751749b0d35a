{"response": "```yaml\n- kind: Question\n  id: aa2021_WICCBalanceWrapUpDTMF_DM\n  displayName: aa2021_WICCBalanceWrapUpDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa2021_nm1_01.wav\\\">Sorry</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/aa2021_nm2_01.wav\\\">Sorry, I still didn t get that</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.aa2021_WICCBalanceWrapUpDTMF_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa2021_ini_01.wav\\\">To hear that again, press 1  And if you re done feel free to hang-up</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/aa2021_ni1_01.wav\\\">5 ms silence</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/aa2021_ni2_01.wav\\\">Sorry, I still didn t get anything</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa2021_WICCBalanceWrapUpDTMF_DM\n  value: =Text(Global.aa2021_WICCBalanceWrapUpDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa2021_WICCBalanceWrapUpDTMF_DM = \"repeat\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa2006_WICCNoBenefits_PP\n```", "timestamp": 1749543424.7985752, "content_hash": "de42a9e24fafb66407ddbd578572db5f"}