{"response": "```yaml\n- kind: Question\n  id: la0005_AddLine_DM\n  displayName: la0005_AddLine_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/la0005_ni1_01.wav\\\">Say Repeat or press 1 Or if you dont need any more help, just hang up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/la0005_nm2_01.wav\\\"></audio>\"\n\n  alwaysPrompt: true\n  variable: Global.la0005_AddLine_DM_reco\n  prompt:\n    speak:\n      - \"{CustomAudio: com.nuance.att.application.audio.la0005_AddLine_DM_initial}\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: mainmenu\n          displayName: mainmenu\n        - id: operator\n          displayName: operator\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/la0005_ni1_01.wav\\\">Say Repeat or press 1 Or if you dont need any more help, just hang up</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_la0005_AddLine_DM\n  value: =Text(Global.la0005_AddLine_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_la0005_AddLine_DM = \"mainmenu\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferDestination\n          value: attPrepaid\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M01_MainMenu2_Dialog\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_la0005_AddLine_DM = \"operator\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.lastTransferState\n          value: la0005_AddLine_DM\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferReason\n          value: AGENT_REQUEST\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M09_MainMenu_Dialog\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_la0005_AddLine_DM = \"repeat\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: la0005_AddLine_DM\n```", "timestamp": 1749470550.5604393, "content_hash": "d04b881165889f198796e7316c4cab9e"}