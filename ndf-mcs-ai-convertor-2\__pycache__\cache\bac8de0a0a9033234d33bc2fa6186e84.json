{"response": "```yaml\n- kind: Question\n  id: ai0225_AskIMEIWHPFallback_DM\n  displayName: ai0225_AskIMEIWHPFallback_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ai0225_ni1_01.wav\\\">You should see a sticker on the bottom of your device with some numbers and barcodes printed on it Look for the number labeled as the IMEI Please say or enter it now Or say I Can't Find It or press 1</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ai0225_ni2_01.wav\\\">On the bottom of your device, look for a sticker with a long number printed after the letters IMEI Go ahead and enter that number on the phone keypad Or if you don't see a number like that, press 1</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ai0225_AskIMEIWHPFallback_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.foundIMEI = false,\n            [\n              \"That's OK Let's try something different \",\n              \"\",\n              \"Your IMEI number should be printed on a sticker on the bottom of your device Read me that number, or say I Can't Find It \"\n            ],\n            true,\n            [\n              \"Now I need the IMEI number \",\n              \"\",\n              \"Your IMEI number should be printed on a sticker on the bottom of your device Read me that number, or say I Can't Find It \"\n            ]\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: cant_find\n          displayName: cant_find\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ai0225_ni1_01.wav\\\">You should see a sticker on the bottom of your device with some numbers and barcodes printed on it Look for the number labeled as the IMEI Please say or enter it now Or say I Can't Find It or press 1</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ai0225_ni2_01.wav\\\">On the bottom of your device, look for a sticker with a long number printed after the letters IMEI Go ahead and enter that number on the phone keypad Or if you don't see a number like that, press 1</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ai0225_AskIMEIWHPFallback_DM\n  value: =Text(Global.ai0225_AskIMEIWHPFallback_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ai0225_AskIMEIWHPFallback_DM = \"cant_find\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferReason\n          value: BUSINESS_RULE\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferPrompt\n          value: default\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M09_MainMenu_Dialog\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ai0225_AskIMEIWHPFallback_DM = \"default\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/ai0225_out_01.wav\\\">Thanks </audio>\"\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.imei\n              value: ai0225_AskIMEIWHPFallback_DM.returnvalue\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.A04_IMEI_03_Dialog\n```", "timestamp": 1749469677.7147143, "content_hash": "bac8de0a0a9033234d33bc2fa6186e84"}