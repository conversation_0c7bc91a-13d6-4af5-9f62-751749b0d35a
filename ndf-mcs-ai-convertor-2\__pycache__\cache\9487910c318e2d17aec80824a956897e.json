{"response": "```yaml\n- kind: Question\n  id: su0201_AskCallReasonByRequest_DM\n  displayName: su0201_AskCallReasonByRequest_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/su0201_nm1_01.wav\\\">Just so you know you wont be able to fully manage or access this account while it is suspended You can say unsuspend or main menu</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/su0201_nm2_01.wav\\\">Just so you know you wont be able to fully manage or access this account while it is suspended You can say unsuspend or press one, or main menu and press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.su0201_AskCallReasonByRequest_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/su0201_ini_01.wav\\\">Just so you know you wont be able to fully manage or access this account while it is suspended You can say unsuspend or main menu</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: unsuspend\n          displayName: unsuspend\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/su0201_ni1_01.wav\\\">Just so you know you wont be able to fully manage or access this account while it is suspended You can say unsuspend or main menu</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/su0201_ni2_01.wav\\\">Just so you know you wont be able to fully manage or access this account while it is suspended You can say unsuspend or press one, or main menu and press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_su0201_AskCallReasonByRequest_DM\n  value: =Text(Global.su0201_AskCallReasonByRequest_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_su0201_AskCallReasonByRequest_DM = \"unsuspend\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: su0205_UnsuspendDevice_DB_DA\n```", "timestamp": **********.9895413, "content_hash": "9487910c318e2d17aec80824a956897e"}