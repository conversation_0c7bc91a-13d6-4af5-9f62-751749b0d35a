{"response": "```yaml\n- kind: Question\n  id: AE1005_AnythingElse_DM\n  displayName: AE1005_AnythingElse_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AE1005_nm1_01.wav\\\">Just say yes or no - would you like to do something else now?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AE1005_nm2_01.wav\\\">If you re done, you can just hang up But if you d like to do anything else now, say  yes  or press 1</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AE1005_nm2_01.wav\\\">If you re done, you can just hang up But if you d like to do anything else now, say  yes  or press 1</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.AE1005_AnythingElse_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.numToMatch = 1,\n            \"Is there anything else I can do for you?\",\n            Global.numToMatch = 2,\n            \"Is there anything else I can help you with?\",\n            Global.numToMatch = 3,\n            \"Is there anything else you d like to do?\",\n            \"Anything else you d like to do?\"\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/AE1005_nm1_01.wav\\\">Just say yes or no - would you like to do something else now?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AE1005_nm2_01.wav\\\">If you re done, you can just hang up But if you d like to do anything else now, say  yes  or press 1</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AE1005_nm2_01.wav\\\">If you re done, you can just hang up But if you d like to do anything else now, say  yes  or press 1</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_AE1005_AnythingElse_DM\n  value: =Text(Global.AE1005_AnythingElse_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AE1005_AnythingElse_DM = \"false\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: AE1015_GoToGoodbye_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_AE1005_AnythingElse_DM = \"true\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.fromAnythingElse\n              value: true\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.callType\n              value: undefined\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.cti_Intent\n              value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: AE1006_CheckHaveAccountDetails_JDA\n```", "timestamp": **********.524814, "content_hash": "65f9eb2caa9353ed19601646442739a3"}