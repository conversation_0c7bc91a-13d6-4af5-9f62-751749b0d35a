{"response": "```yaml\n- kind: Question\n  id: st1030_PowercycleReady_DM\n  displayName: st1030_PowercycleReady_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.intent = \"techSupportData\" && (Global.deviceType = \"featurephone\" || Global.deviceType = \"smartphone\" || Global.deviceType = \"tablet\") && Global.matchedANI = true,\n            \"If your mobile data is back on, say I'm Ready or press 1 If not, go ahead and hang up, and call us back if you're still having problems after you've turned mobile data on\",\n            Global.intent = \"techSupportData\" && (Global.deviceType = \"featurephone\" || Global.deviceType = \"smartphone\" || Global.deviceType = \"tablet\") && Global.matchedANI <> true,\n            \"If Airplane Mode is back off, say I'm Ready or press 1 If not, go ahead and hang up, and call us back if you're still having problems after you've turned Airplane Mode off\",\n            Global.intent = \"techSupportData\" && (Global.deviceType = \"featurephone\" || Global.deviceType = \"smartphone\" || Global.deviceType = \"tablet\"),\n            \"\",\n            Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n            \"If your phone is back on, say I am Ready or press 1 If not, go ahead and hang up, and call us back if youre still having problems when your phone comes back on\",\n            \"If your device is back on, say I am Ready or press 1 If not, go ahead and hang up, and call us back if youre still having problems when your device comes back on\"\n        )\n        }\n      - |\n        {Switch(\n            true,\n            Global.intent = \"techSupportData\" && (Global.deviceType = \"featurephone\" || Global.deviceType = \"smartphone\" || Global.deviceType = \"tablet\") && Global.matchedANI = true,\n            \"If you need more time, go ahead and hang up, and if you're still having problems after your mobile data is back on, call us back Or, if you're ready now, press 1\",\n            Global.intent = \"techSupportData\" && (Global.deviceType = \"featurephone\" || Global.deviceType = \"smartphone\" || Global.deviceType = \"tablet\") && Global.matchedANI <> true,\n            \"If you need more time, go ahead and hang up, and if you're still having problems after Airplane Mode is back off, call us back Or, if you're ready now, press 1\",\n            Global.intent = \"techSupportData\" && (Global.deviceType = \"featurephone\" || Global.deviceType = \"smartphone\" || Global.deviceType = \"tablet\"),\n            \"\",\n            Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n            \"If you need more time to finish restarting your phone, go ahead and hang up, and if youre still having problems when your phone comes back on, call us back Or, if youre ready, press 1\",\n            \"If you need more time to finish restarting your device, go ahead and hang up, and if youre still having problems when your device comes back on, call us back Or, if youre ready, press 1\"\n        )\n        }\n\n  alwaysPrompt: true\n  variable: Global.st1030_PowercycleReady_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.intent = \"techSupportData\" && (Global.deviceType = \"featurephone\" || Global.deviceType = \"smartphone\" || Global.deviceType = \"tablet\") && Global.matchedANI = true,\n            \"If you need more time, go ahead and hang up for now If you're still having problems once you've turned your mobile data back on, just call us back Otherwise, say I'm Ready\",\n            Global.intent = \"techSupportData\" && (Global.deviceType = \"featurephone\" || Global.deviceType = \"smartphone\" || Global.deviceType = \"tablet\") && Global.matchedANI <> true,\n            \"If you need more time, go ahead and hang up for now If you're still having problems once you've turned Airplane Mode back off, just call us back Otherwise, say I'm Ready\",\n            Global.intent = \"techSupportData\" && (Global.deviceType = \"featurephone\" || Global.deviceType = \"smartphone\" || Global.deviceType = \"tablet\"),\n            \"\",\n            Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n            \"If you need more time, go ahead and hang up for now If youre still having problems once your phone is back on, just call us back Otherwise, say I am Ready\",\n            \"If you need more time, go ahead and hang up for now If youre still having problems once your device is back on, just call us back Otherwise, say I am Ready\"\n        )\n        }\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: ready\n          displayName: ready\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n              true,\n              Global.intent = \"techSupportData\" && (Global.deviceType = \"featurephone\" || Global.deviceType = \"smartphone\" || Global.deviceType = \"tablet\") && Global.matchedANI = true,\n              \"If your mobile data is back on, say I'm Ready or press 1 If not, go ahead and hang up, and call us back if you're still having problems after you've turned mobile data on\",\n              Global.intent = \"techSupportData\" && (Global.deviceType = \"featurephone\" || Global.deviceType = \"smartphone\" || Global.deviceType = \"tablet\") && Global.matchedANI <> true,\n              \"If Airplane Mode is back off, say I'm Ready or press 1 If not, go ahead and hang up, and call us back if you're still having problems after you've turned Airplane Mode off\",\n              Global.intent = \"techSupportData\" && (Global.deviceType = \"featurephone\" || Global.deviceType = \"smartphone\" || Global.deviceType = \"tablet\"),\n              \"\",\n              Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n              \"If your phone is back on, say I am Ready or press 1 If not, go ahead and hang up, and call us back if youre still having problems when your phone comes back on\",\n              \"If your device is back on, say I am Ready or press 1 If not, go ahead and hang up, and call us back if youre still having problems when your device comes back on\"\n          )\n          }\n        - |\n          {Switch(\n              true,\n              Global.intent = \"techSupportData\" && (Global.deviceType = \"featurephone\" || Global.deviceType = \"smartphone\" || Global.deviceType = \"tablet\") && Global.matchedANI = true,\n              \"If you need more time, go ahead and hang up, and if you're still having problems after your mobile data is back on, call us back Or, if you're ready now, press 1\",\n              Global.intent = \"techSupportData\" && (Global.deviceType = \"featurephone\" || Global.deviceType = \"smartphone\" || Global.deviceType = \"tablet\") && Global.matchedANI <> true,\n              \"If you need more time, go ahead and hang up, and if you're still having problems after Airplane Mode is back off, call us back Or, if you're ready now, press 1\",\n              Global.intent = \"techSupportData\" && (Global.deviceType = \"featurephone\" || Global.deviceType = \"smartphone\" || Global.deviceType = \"tablet\"),\n              \"\",\n              Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n              \"If you need more time to finish restarting your phone, go ahead and hang up, and if youre still having problems when your phone comes back on, call us back Or, if youre ready, press 1\",\n              \"If you need more time to finish restarting your device, go ahead and hang up, and if youre still having problems when your device comes back on, call us back Or, if youre ready, press 1\"\n          )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_st1030_PowercycleReady_DM\n  value: =Text(Global.st1030_PowercycleReady_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_st1030_PowercycleReady_DM = \"ready\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: st1032_TestSolution_PP\n```", "timestamp": 1749472347.861688, "content_hash": "519d6c87889d5a1fb7bcec0a319ad6c0"}