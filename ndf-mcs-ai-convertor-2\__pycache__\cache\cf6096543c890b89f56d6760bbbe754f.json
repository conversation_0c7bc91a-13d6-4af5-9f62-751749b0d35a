{"response": "```yaml\n- kind: Question\n  id: UW1135_DebitOrCredit_DM\n  displayName: UW1135_DebitOrCredit_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/UW1135_nm1_01.wav\\\">Is the card you chose a 'credit' card, or a 'debit' card?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/UW1135_nm2_01.wav\\\">If the card you chose is a credit card, say 'credit' or press 1 Or  say 'debit' or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/UW1135_nm3_01.wav\\\">I need to know the type of card you're using today If the card you chose is a credit card, press 1 If it's a debit card, press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.UW1135_DebitOrCredit_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/UW1135_ini_01.wav\\\">Is this a 'credit' or a 'debit' card?</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: credit\n          displayName: credit\n        - id: debit\n          displayName: debit\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/UW1135_nm1_01.wav\\\">Is the card you chose a 'credit' card, or a 'debit' card?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/UW1135_nm2_01.wav\\\">If the card you chose is a credit card, say 'credit' or press 1 Or  say 'debit' or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/UW1135_nm3_01.wav\\\">I need to know the type of card you're using today If the card you chose is a credit card, press 1 If it's a debit card, press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_UW1135_DebitOrCredit_DM\n  value: =Text(Global.UW1135_DebitOrCredit_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_UW1135_DebitOrCredit_DM = \"credit\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.preferredPaymentMethod\n          value: credit\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.PaymentTable.CARD_TYPE\n          value: credit\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: getReturnLink()\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_UW1135_DebitOrCredit_DM = \"debit\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.preferredPaymentMethod\n              value: debit\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.PaymentTable.CARD_TYPE\n              value: debit\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n```", "timestamp": 1749529976.8068874, "content_hash": "cf6096543c890b89f56d6760bbbe754f"}