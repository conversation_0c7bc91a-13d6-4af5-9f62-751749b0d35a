{"response": "```yaml\n- kind: Question\n  id: as0210_AskIfHaveSIM_DM\n  displayName: as0210_AskIfHaveSIM_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/as0210_ni1_01.wav\\\">If you have your new SIM card number, say I'm Ready or press 1 If not, say Help Me Find It or press 2 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/as0210_ni2_01.wav\\\">If you're ready to give me your SIM card number, press 1 If not, press 2 </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.as0210_AskIfHaveSIM_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/as0210_ini_01.wav\\\">Do you have a SIM card? If you are not sure, say Help Me Find It </audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: help_find\n          displayName: help_find\n        - id: ready\n          displayName: ready\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/as0210_ni1_01.wav\\\">If you have your new SIM card number, say I'm Ready or press 1 If not, say Help Me Find It or press 2 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/as0210_ni2_01.wav\\\">If you're ready to give me your SIM card number, press 1 If not, press 2 </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.callType = \"611\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_commandgrammar\n          value: 'GlobalCommands.grxml?SWI_vars.allow=mainmenu&SWI_vars.operator_cnf_mode=NEVER'\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.collection_commandgrammar\n      value: 'GlobalCommands.grxml?SWI_vars.operator_cnf_mode=NEVER'\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.from_as0111\n  value: false\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_as0210_AskIfHaveSIM_DM\n  value: =Text(Global.as0210_AskIfHaveSIM_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.callType = \"611\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_commandgrammar\n          value: 'GlobalCommands.grxml?SWI_vars.allow=mainmenu'\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.collection_commandgrammar\n      value: 'GlobalCommands.grxml'\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_as0210_AskIfHaveSIM_DM = \"help_find\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: as0225_PlaySIMInfo_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_as0210_AskIfHaveSIM_DM = \"ready\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/as0210_out_01.wav\\\">Great</audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\"></audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: as0220_AskSIMNumberNoSticker_DM\n```", "timestamp": 1749469677.3960667, "content_hash": "d344e6f6334b8ddfd171fff569025e4c"}