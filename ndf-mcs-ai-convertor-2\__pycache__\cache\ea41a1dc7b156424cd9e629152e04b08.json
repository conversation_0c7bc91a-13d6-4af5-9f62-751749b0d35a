{"response": "```yaml\n- kind: Question\n  id: BR1025_AcceptBCRTermsYN_DM\n  displayName: BR1025_AcceptBCRTermsYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BR1025_nm1_02.wav\\\">To change your due date to today would be</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BR1025_nm1_01.wav\\\">Would you like to do that? To hear all the terms again, say repeat that</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BR1025_nm2_01.wav\\\">To change your due date to today would be</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BR1025_nm2_02.wav\\\">To do that now, say yes or press 1 Otherwise, say no or press 2, or to hear all the terms again sat repeat or press 3</audio>\"\n      - |\n        {Switch(\n            true,\n            Global.callType = \"make_pmt\",\n            \"<audio src=\\\"AUDIO_LOCATION/BR1025_nm3_01.wav\\\">I m having trouble understanding Let s go back to your payment</audio>\",\n            true,\n            \"<audio src=\\\"AUDIO_LOCATION/BR1025_nm3_02.wav\\\">Let s try one more time</audio>\"\n        )\n        }\n      - \"<audio src=\\\"AUDIO_LOCATION/BR1025_nm2_01.wav\\\">To change your due date to today would be</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BR1025_nm2_02.wav\\\">To do that now, say yes or press 1 Otherwise, say no or press 2, or to hear all the terms again sat repeat or press 3</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.BR1025_AcceptBCRTermsYN_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.callType = \"make_pmt\",\n            \"Would you like to do that?\",\n            true,\n            \"Are you ready to change your due date?\"\n        )\n        }\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/BR1025_nm1_02.wav\\\">To change your due date to today would be</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BR1025_nm1_01.wav\\\">Would you like to do that? To hear all the terms again, say repeat that</audio>\"\n        - |\n          {Switch(\n              true,\n              Global.callType = \"make_pmt\",\n              \"<audio src=\\\"AUDIO_LOCATION/BR1025_ni2_01.wav\\\">I didn t hear you Let s go back to your payment</audio>\",\n              true,\n              \"<audio src=\\\"AUDIO_LOCATION/BR1025_ni2_02.wav\\\">I didn t hear you</audio>\"\n          )\n          }\n        - \"<audio src=\\\"AUDIO_LOCATION/BR1025_nm2_01.wav\\\">To change your due date to today would be</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BR1025_nm2_02.wav\\\">To do that now, say yes or press 1 Otherwise, say no or press 2, or to hear all the terms again sat repeat or press 3</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BR1025_nm2_01.wav\\\">To change your due date to today would be</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BR1025_nm2_02.wav\\\">To do that now, say yes or press 1 Otherwise, say no or press 2, or to hear all the terms again sat repeat or press 3</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.callType\n  value: GlobalVars.callType\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_maxnoinputs\n  value: \"GlobalVars.callType == 'make_pmt' ? 1 : 3\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_maxnomatches\n  value: \"GlobalVars.callType == 'make_pmt' ? 2 : 3\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.audioMessageKey\n  value: care_bcr_charge_audio\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_BR1025_AcceptBCRTermsYN_DM\n  value: =Text(Global.BR1025_AcceptBCRTermsYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_BR1025_AcceptBCRTermsYN_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.acceptedBCR\n          value: true\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/BR1025_out_01.wav\\\">Great</audio>\"\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.GlobalVars.loggedIn <> true, true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.securityRequired\n                  value: true\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: BR1101_CheckNeedPIN_JDA_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_BR1025_AcceptBCRTermsYN_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/BR1025_out_02.wav\\\">No problem</audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: BR1030_RepeatCurrentDueDate_PP\n```\n**Notes on the output:**\n- All audiofile references are converted to `<audio src=\"AUDIO_LOCATION/filename.wav\">text</audio>` as per your pattern.\n- All if/else logic in prompts and success actions is converted to PowerFx/Switch or ConditionGroup as per your rules.\n- All session-mapping and variable assignments are converted to SetVariable actions.\n- The BooleanPrebuiltEntity is used for entity as the labels are \"true\"/\"false\".\n- The structure and indentation follow your memorized YAML format and examples.\n- All ids for SetVariable, ConditionGroup, etc. are set to `REPLACE_THIS` as per your placeholder convention.", "timestamp": 1749558116.5931559, "content_hash": "ea41a1dc7b156424cd9e629152e04b08"}