#!/usr/bin/env python3
"""
Enhanced PowerFX Array to Concatenate Converter
==============================================

This script processes PowerFX YAML files to transform array arguments in PowerFX formulas 
into single-line Concatenate expressions. It follows the user's specific requirements for
YAML processing, quote handling, and error management.

Key Features:
- Processes YAML files using ruamel.yaml with width=4096 and preserve_quotes=True
- Converts PowerFX arrays to Concatenate() expressions
- Removes quotes from SetVariable entries
- Handles escaped quotes by completely removing them
- Implements retry mechanisms for failed files
- Accepts input from parent input folder (not subfolders)
- Preserves all original YAML structure, comments, and spacing

Usage:
    python powerfx_array_to_concatenate_converter.py [input_folder] [--dry-run] [--verbose]
"""

import os
import re
import sys
import time
import logging
import traceback
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from ruamel.yaml import YAML
from ruamel.yaml.scalarstring import PlainScalarString

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'powerfx_converter_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PowerFXArrayToConcatenateConverter:
    """Enhanced converter for PowerFX arrays to Concatenate expressions"""
    
    def __init__(self):
        # Configure YAML parser with user's preferred settings
        self.yaml_parser = YAML()
        self.yaml_parser.preserve_quotes = True
        self.yaml_parser.width = 4096  # User preference for preventing line wrapping
        self.yaml_parser.indent(mapping=2, sequence=4, offset=2)
        self.yaml_parser.allow_unicode = True
        self.yaml_parser.default_flow_style = False
        
        # PowerFX function patterns - based on Microsoft documentation
        # Functions that commonly use multiple string arguments or array-like structures
        self.powerfx_functions = [
            "Switch", "If", "IfError", "With", "Concatenate",
            "Text", "Value", "IsBlank", "IsEmpty", "And", "Or", "Not",
            "DateAdd", "TimeZoneInformation", "Coalesce", "Notify"
        ]
        
        # Pattern to find PowerFX function calls
        self.powerfx_pattern = re.compile(
            r'\b(' + '|'.join(self.powerfx_functions) + r')\s*\(',
            re.IGNORECASE
        )
        
        # PowerFX array pattern - matches bracket arrays that should be converted to Concatenate
        # Based on Microsoft PowerFX documentation, these are non-standard array syntax
        # that should be converted to proper Concatenate() function calls
        self.array_pattern = re.compile(
            r'\[\s*"[^"]*"(?:\s*,\s*"[^"]*")*\s*\]',
            re.DOTALL | re.MULTILINE
        )

        # Pattern for mixed content arrays (strings + variables/expressions)
        # Example: ["Score: ", Global.score, " points"]
        self.mixed_array_pattern = re.compile(
            r'\[\s*(?:(?:"[^"]*"|[^",\[\]]+)(?:\s*,\s*(?:"[^"]*"|[^",\[\]]+))*)\s*\]',
            re.DOTALL | re.MULTILINE
        )
        
        # Statistics tracking
        self.stats = {
            'files_processed': 0,
            'files_modified': 0,
            'arrays_converted': 0,
            'setvariable_processed': 0,
            'errors': 0,
            'failed_files': []
        }
        
        # Failed files management
        self.failed_files_path = "failed_files.txt"
        
    def detect_powerfx_arrays(self, text: str) -> List[Dict[str, Any]]:
        """
        Detect PowerFX expressions containing array arguments
        
        Args:
            text: String content to analyze
            
        Returns:
            List of dictionaries with PowerFX expression details
        """
        if not isinstance(text, str):
            return []
        
        results = []
        
        # Find all PowerFX function calls
        for match in self.powerfx_pattern.finditer(text):
            func_name = match.group(1)
            start_pos = match.start()
            
            # Extract complete function expression
            expression, end_pos = self._extract_complete_expression(text, start_pos)
            
            if expression:
                # Check for arrays in this expression
                array_matches = list(self.array_pattern.finditer(expression))
                mixed_matches = list(self.mixed_array_pattern.finditer(expression))
                
                # Combine and deduplicate matches
                all_matches = array_matches + [m for m in mixed_matches 
                                             if not any(am.start() <= m.start() < am.end() 
                                                       for am in array_matches)]
                
                if all_matches:
                    results.append({
                        'function': func_name,
                        'expression': expression,
                        'start_pos': start_pos,
                        'end_pos': end_pos,
                        'arrays': [match.group() for match in all_matches],
                        'array_positions': [(m.start(), m.end()) for m in all_matches]
                    })
        
        return results
    
    def _extract_complete_expression(self, text: str, start_pos: int) -> Tuple[Optional[str], int]:
        """
        Extract complete PowerFX expression by matching parentheses
        
        Args:
            text: Full text content
            start_pos: Starting position of function name
            
        Returns:
            Tuple of (complete_expression, end_position)
        """
        # Find function name start
        func_start = start_pos
        while func_start > 0 and text[func_start - 1].isalnum():
            func_start -= 1
        
        # Find opening parenthesis
        paren_pos = start_pos
        while paren_pos < len(text) and text[paren_pos] != '(':
            paren_pos += 1
        
        if paren_pos >= len(text):
            return None, start_pos
        
        # Match parentheses to find expression end
        paren_count = 1
        pos = paren_pos + 1
        
        while pos < len(text) and paren_count > 0:
            if text[pos] == '(':
                paren_count += 1
            elif text[pos] == ')':
                paren_count -= 1
            pos += 1
        
        if paren_count == 0:
            return text[func_start:pos], pos
        
        return None, start_pos
    
    def convert_array_to_concatenate(self, expression: str) -> str:
        """
        Convert array expressions to Concatenate() function calls
        
        Args:
            expression: PowerFX expression containing arrays
            
        Returns:
            Modified expression with arrays converted to Concatenate()
        """
        def replace_array(match):
            array_str = match.group().strip()
            
            # Remove outer brackets
            inner_content = array_str[1:-1].strip()
            
            # Parse array elements - handle both quoted strings and variables
            elements = []
            current_element = ""
            in_quotes = False
            bracket_depth = 0
            
            i = 0
            while i < len(inner_content):
                char = inner_content[i]
                
                if char == '"' and (i == 0 or inner_content[i-1] != '\\'):
                    in_quotes = not in_quotes
                    current_element += char
                elif char == '[' and not in_quotes:
                    bracket_depth += 1
                    current_element += char
                elif char == ']' and not in_quotes:
                    bracket_depth -= 1
                    current_element += char
                elif char == ',' and not in_quotes and bracket_depth == 0:
                    # End of current element
                    element = current_element.strip()
                    if element:
                        elements.append(element)
                    current_element = ""
                else:
                    current_element += char
                
                i += 1
            
            # Add final element
            if current_element.strip():
                elements.append(current_element.strip())
            
            # Process elements for Concatenate
            processed_elements = []
            for element in elements:
                element = element.strip()
                
                # Handle quoted strings
                if element.startswith('"') and element.endswith('"'):
                    # Remove outer quotes and handle escaped quotes
                    content = element[1:-1]
                    # Remove escaped quotes completely (user preference)
                    content = content.replace('\\"', '')
                    processed_elements.append(f'"{content}"')
                else:
                    # Variable or expression - keep as-is
                    processed_elements.append(element)
            
            # Create Concatenate expression per Microsoft PowerFX documentation
            # Concatenate function syntax: Concatenate(String1 [, String2, ...])
            # Reference: https://learn.microsoft.com/en-us/power-platform/power-fx/reference/function-concatenate
            if len(processed_elements) == 0:
                return '""'  # Empty string
            elif len(processed_elements) == 1:
                return processed_elements[0]  # Single element - no need for Concatenate
            else:
                # Use Concatenate function (not Concat) for individual strings
                return f'Concatenate({", ".join(processed_elements)})'
        
        # Replace arrays with Concatenate calls
        # Process mixed arrays first, then simple string arrays
        converted = self.mixed_array_pattern.sub(replace_array, expression)
        converted = self.array_pattern.sub(replace_array, converted)
        
        return converted

    def validate_powerfx_syntax(self, expression: str) -> Tuple[bool, List[str]]:
        """
        Validate PowerFX expression against Microsoft PowerFX standards

        Args:
            expression: PowerFX expression to validate

        Returns:
            Tuple of (is_valid, list_of_issues)
        """
        issues = []

        # Check for deprecated Concat usage (should be Concatenate for individual strings)
        if re.search(r'\bConcat\s*\([^,)]*(?:,[^,)]*){0,2}\)', expression):
            issues.append("Found Concat() usage - consider Concatenate() for individual strings")

        # Check for proper Concatenate syntax
        concatenate_matches = re.findall(r'Concatenate\s*\([^)]+\)', expression)
        for match in concatenate_matches:
            # Validate that Concatenate has at least one argument
            args_content = re.search(r'Concatenate\s*\(([^)]+)\)', match)
            if args_content:
                args = [arg.strip() for arg in args_content.group(1).split(',')]
                if len(args) < 1:
                    issues.append(f"Concatenate function requires at least one argument: {match}")

        # Check for proper string quoting (PowerFX uses double quotes)
        if "'" in expression and '"' in expression:
            issues.append("Mixed quote usage detected - PowerFX prefers double quotes for strings")

        # Check for unsupported array syntax
        if re.search(r'\[[^\]]*\]', expression) and 'Concatenate' not in expression:
            issues.append("Array syntax detected - consider converting to Concatenate() function")

        return len(issues) == 0, issues

    def process_setvariable_quotes(self, value: str) -> str:
        """
        Remove quotes from SetVariable values (user preference)

        Args:
            value: Original value string

        Returns:
            Value with quotes removed
        """
        if isinstance(value, str):
            # Remove outer quotes if present
            if value.startswith('"') and value.endswith('"'):
                return value[1:-1]
            # Remove escaped quotes completely
            return value.replace('\\"', '')
        return value

    def process_yaml_content(self, content: str) -> Tuple[str, bool]:
        """
        Process YAML content to convert PowerFX arrays and handle SetVariable quotes

        Args:
            content: Original YAML content

        Returns:
            Tuple of (modified_content, was_modified)
        """
        modified_content = content
        was_modified = False

        # Step 1: Convert PowerFX arrays to Concatenate expressions
        powerfx_expressions = self.detect_powerfx_arrays(content)

        if powerfx_expressions:
            # Sort by position (reverse order to maintain positions during replacement)
            powerfx_expressions.sort(key=lambda x: x['start_pos'], reverse=True)

            for expr_info in powerfx_expressions:
                original_expr = expr_info['expression']
                converted_expr = self.convert_array_to_concatenate(original_expr)

                if converted_expr != original_expr:
                    start_pos = expr_info['start_pos']
                    end_pos = expr_info['end_pos']

                    modified_content = (modified_content[:start_pos] +
                                      converted_expr +
                                      modified_content[end_pos:])

                    self.stats['arrays_converted'] += len(expr_info['arrays'])
                    was_modified = True

                    # Detailed logging with before/after comparison
                    logger.info(f"PowerFX conversion in position {start_pos}-{end_pos}")
                    logger.info(f"  Function: {expr_info['function']}")
                    logger.info(f"  Arrays found: {len(expr_info['arrays'])}")

                    # Log each array that was converted
                    for i, array_str in enumerate(expr_info['arrays'], 1):
                        logger.info(f"  Array {i}: {array_str}")

                    # Validate against PowerFX standards
                    is_valid, validation_issues = self.validate_powerfx_syntax(converted_expr)
                    if not is_valid:
                        logger.warning(f"  ⚠️  PowerFX validation issues:")
                        for issue in validation_issues:
                            logger.warning(f"    - {issue}")
                    else:
                        logger.info(f"  ✅ PowerFX syntax validation passed")

                    # Show before/after (truncated for readability)
                    if len(original_expr) > 200:
                        logger.debug(f"  BEFORE: {original_expr[:200]}...")
                        logger.debug(f"  AFTER:  {converted_expr[:200]}...")
                    else:
                        logger.debug(f"  BEFORE: {original_expr}")
                        logger.debug(f"  AFTER:  {converted_expr}")

                    logger.info(f"  ✅ Successfully converted {len(expr_info['arrays'])} arrays")

        # Step 2: Process SetVariable entries to remove quotes
        # Use regex to find SetVariable value entries
        setvariable_pattern = re.compile(
            r'(\s+value:\s*)(.*?)(\s*$)',
            re.MULTILINE
        )

        def process_setvariable_value(match):
            prefix = match.group(1)
            value = match.group(2)
            suffix = match.group(3)

            # Check if this is within a SetVariable context
            # Look backwards to find 'kind: SetVariable'
            text_before = modified_content[:match.start()]
            lines_before = text_before.split('\n')

            # Check recent lines for SetVariable
            for line in reversed(lines_before[-10:]):  # Check last 10 lines
                if 'kind: SetVariable' in line:
                    processed_value = self.process_setvariable_quotes(value)
                    if processed_value != value:
                        self.stats['setvariable_processed'] += 1
                        logger.info(f"SetVariable quote processing:")
                        logger.info(f"  BEFORE: {value}")
                        logger.info(f"  AFTER:  {processed_value}")
                        logger.info(f"  ✅ Removed quotes from SetVariable value")
                        return prefix + processed_value + suffix
                    break

            return match.group(0)  # No change

        # Apply SetVariable processing
        setvariable_modified = setvariable_pattern.sub(process_setvariable_value, modified_content)
        if setvariable_modified != modified_content:
            modified_content = setvariable_modified
            was_modified = True

        return modified_content, was_modified

    def process_yaml_file(self, file_path: Path) -> bool:
        """
        Process a single YAML file

        Args:
            file_path: Path to YAML file

        Returns:
            True if file was modified
        """
        try:
            logger.info(f"🔄 Starting processing: {file_path}")

            # Read original content
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()

            file_size = len(original_content)
            logger.info(f"  📄 File size: {file_size} characters")

            # Process content
            logger.info(f"  🔍 Scanning for PowerFX arrays and SetVariable entries...")
            modified_content, was_modified = self.process_yaml_content(original_content)

            if was_modified:
                # Create backup
                backup_path = file_path.with_suffix(file_path.suffix + '.backup')
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(original_content)

                # Write modified content
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(modified_content)

                new_size = len(modified_content)
                size_change = new_size - file_size

                logger.info(f"  💾 File modified and saved")
                logger.info(f"  📁 Backup created: {backup_path}")
                logger.info(f"  📊 Size change: {size_change:+d} characters ({file_size} → {new_size})")
                logger.info(f"✅ Successfully processed: {file_path}")

                self.stats['files_modified'] += 1
                return True
            else:
                logger.info(f"  ℹ️  No changes needed")
                logger.info(f"✅ Completed processing: {file_path}")
                return False

        except Exception as e:
            error_msg = f"Error processing {file_path}: {str(e)}"
            logger.error(error_msg)
            self.stats['errors'] += 1
            self.stats['failed_files'].append(str(file_path))
            self._log_failed_file(str(file_path), error_msg)
            return False

    def _log_failed_file(self, file_path: str, error_msg: str):
        """Log failed file to failed_files.txt"""
        try:
            with open(self.failed_files_path, 'a', encoding='utf-8') as f:
                f.write(f"{file_path}\n")
            logger.info(f"Added {file_path} to failed files list")
        except Exception as e:
            logger.error(f"Failed to log failed file {file_path}: {e}")

    def remove_from_failed_list(self, file_path: str):
        """Remove successfully processed file from failed_files.txt"""
        try:
            if not os.path.exists(self.failed_files_path):
                return

            with open(self.failed_files_path, 'r', encoding='utf-8') as f:
                failed_files = [line.strip() for line in f.readlines() if line.strip()]

            if file_path in failed_files:
                failed_files.remove(file_path)

                with open(self.failed_files_path, 'w', encoding='utf-8') as f:
                    for failed_file in failed_files:
                        f.write(f"{failed_file}\n")

                logger.info(f"Removed {file_path} from failed files list")

        except Exception as e:
            logger.error(f"Failed to remove {file_path} from failed files list: {e}")

    def process_directory(self, directory_path: Path, pattern: str = "*.yml") -> Dict[str, Any]:
        """
        Process all YAML files in directory (parent folder only, not subfolders)

        Args:
            directory_path: Path to directory
            pattern: File pattern to match

        Returns:
            Processing results dictionary
        """
        results = {
            'files_found': 0,
            'files_processed': 0,
            'files_modified': 0,
            'arrays_converted': 0,
            'setvariable_processed': 0,
            'errors': 0,
            'failed_files': []
        }

        try:
            # Find YAML files in parent directory only (user preference)
            yaml_files = []
            for pattern_ext in [pattern, "*.yaml"]:
                yaml_files.extend(list(directory_path.glob(pattern_ext)))

            # Filter to only direct children (no subdirectories)
            yaml_files = [f for f in yaml_files if f.parent == directory_path]

            results['files_found'] = len(yaml_files)
            logger.info(f"📁 Directory scan complete: {directory_path}")
            logger.info(f"📄 Found {len(yaml_files)} YAML files to process")

            if yaml_files:
                logger.info(f"📋 Files to process:")
                for i, file_path in enumerate(yaml_files, 1):
                    logger.info(f"  {i}. {file_path.name}")
            else:
                logger.warning(f"⚠️  No YAML files found in {directory_path}")
                return results

            logger.info(f"\n🚀 Starting batch processing of {len(yaml_files)} files...")

            # Process each file
            for i, file_path in enumerate(yaml_files, 1):
                logger.info(f"\n📋 [{i}/{len(yaml_files)}] Processing: {file_path.name}")
                try:
                    self.stats['files_processed'] += 1
                    was_modified = self.process_yaml_file(file_path)

                    if was_modified:
                        results['files_modified'] += 1
                        # Remove from failed list if it was there
                        self.remove_from_failed_list(str(file_path))

                    results['files_processed'] += 1

                except Exception as e:
                    logger.error(f"Error processing {file_path}: {e}")
                    results['errors'] += 1
                    results['failed_files'].append(str(file_path))

            # Update results with current stats
            results['arrays_converted'] = self.stats['arrays_converted']
            results['setvariable_processed'] = self.stats['setvariable_processed']
            results['errors'] = self.stats['errors']
            results['failed_files'] = self.stats['failed_files']

            # Comprehensive completion logging
            logger.info(f"\n🎉 BATCH PROCESSING COMPLETED")
            logger.info(f"=" * 50)
            logger.info(f"📊 FINAL STATISTICS:")
            logger.info(f"  📄 Files found:           {results['files_found']}")
            logger.info(f"  ✅ Files processed:       {results['files_processed']}")
            logger.info(f"  📝 Files modified:        {results['files_modified']}")
            logger.info(f"  🔄 Arrays converted:      {results['arrays_converted']}")
            logger.info(f"  🏷️  SetVariable processed: {results['setvariable_processed']}")
            logger.info(f"  ❌ Errors encountered:    {results['errors']}")

            if results['failed_files']:
                logger.warning(f"⚠️  FAILED FILES ({len(results['failed_files'])}):")
                for failed_file in results['failed_files']:
                    logger.warning(f"    - {failed_file}")
                logger.info(f"💡 Use --retry-failed to retry failed files")

            if results['files_modified'] > 0:
                logger.info(f"💾 Backup files created with .backup extension")
                logger.info(f"🔄 {results['files_modified']} files successfully modified")

            logger.info(f"=" * 50)

            return results

        except Exception as e:
            logger.error(f"Error processing directory {directory_path}: {e}")
            results['errors'] += 1
            return results

    def retry_failed_files(self) -> Dict[str, Any]:
        """
        Retry processing failed files with sequential processing

        Returns:
            Retry results dictionary
        """
        if not os.path.exists(self.failed_files_path):
            logger.info("No failed files list found")
            return {'retried': 0, 'successful': 0, 'still_failed': 0}

        try:
            with open(self.failed_files_path, 'r', encoding='utf-8') as f:
                failed_files = [line.strip() for line in f.readlines() if line.strip()]

            if not failed_files:
                logger.info("No failed files to retry")
                return {'retried': 0, 'successful': 0, 'still_failed': 0}

            logger.info(f"🔄 Starting retry process for {len(failed_files)} failed files")
            logger.info(f"📋 Failed files to retry:")
            for i, file_path in enumerate(failed_files, 1):
                logger.info(f"  {i}. {file_path}")

            successful_retries = 0
            still_failed = 0

            logger.info(f"\n🚀 Beginning sequential retry processing...")

            for i, file_path in enumerate(failed_files, 1):
                logger.info(f"\n🔄 RETRY [{i}/{len(failed_files)}]: {file_path}")

                if not os.path.exists(file_path):
                    logger.warning(f"  ❌ File not found: {file_path}")
                    still_failed += 1
                    continue

                try:
                    was_modified = self.process_yaml_file(Path(file_path))
                    successful_retries += 1
                    logger.info(f"  ✅ RETRY SUCCESSFUL: {file_path}")

                    # Remove from failed list
                    self.remove_from_failed_list(file_path)

                except Exception as e:
                    logger.error(f"  ❌ RETRY FAILED: {file_path}")
                    logger.error(f"     Error: {e}")
                    still_failed += 1

                # Small delay for supervision
                time.sleep(0.5)

            # Final retry summary
            logger.info(f"\n🎉 RETRY PROCESS COMPLETED")
            logger.info(f"=" * 40)
            logger.info(f"📊 RETRY STATISTICS:")
            logger.info(f"  🔄 Files retried:     {len(failed_files)}")
            logger.info(f"  ✅ Successful:       {successful_retries}")
            logger.info(f"  ❌ Still failed:     {still_failed}")

            if successful_retries > 0:
                logger.info(f"🎉 {successful_retries} files successfully recovered!")
                logger.info(f"💡 These files have been removed from failed_files.txt")

            if still_failed > 0:
                logger.warning(f"⚠️  {still_failed} files still need attention")

            logger.info(f"=" * 40)

            return {
                'retried': len(failed_files),
                'successful': successful_retries,
                'still_failed': still_failed
            }

        except Exception as e:
            logger.error(f"Error during retry process: {e}")
            return {'retried': 0, 'successful': 0, 'still_failed': 0, 'error': str(e)}

    def print_statistics(self):
        """Print comprehensive processing statistics"""
        print("\n" + "=" * 80)
        print("POWERFX ARRAY TO CONCATENATE CONVERSION RESULTS")
        print("=" * 80)
        print(f"Files processed:           {self.stats['files_processed']}")
        print(f"Files modified:            {self.stats['files_modified']}")
        print(f"Arrays converted:          {self.stats['arrays_converted']}")
        print(f"SetVariable entries processed: {self.stats['setvariable_processed']}")
        print(f"Errors encountered:        {self.stats['errors']}")

        if self.stats['failed_files']:
            print(f"\nFailed files ({len(self.stats['failed_files'])}):")
            for failed_file in self.stats['failed_files']:
                print(f"  - {failed_file}")

        print("=" * 80)


def main():
    """Main function for command-line usage"""
    import argparse

    parser = argparse.ArgumentParser(
        description="Convert PowerFX array arguments to Concatenate expressions in YAML files",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python powerfx_array_to_concatenate_converter.py input/
  python powerfx_array_to_concatenate_converter.py input/ --dry-run
  python powerfx_array_to_concatenate_converter.py input/ --retry-failed
  python powerfx_array_to_concatenate_converter.py input/ --verbose

This script processes PowerFX YAML files to:
- Convert array arguments in PowerFX functions to Concatenate() expressions
- Remove quotes from SetVariable entries
- Handle escaped quotes by completely removing them
- Preserve all original YAML formatting and structure
        """
    )

    parser.add_argument(
        'input_path',
        nargs='?',
        default='input',
        help="Path to directory containing YAML files (default: input)"
    )

    parser.add_argument(
        '--dry-run',
        action='store_true',
        help="Show what would be converted without making changes"
    )

    parser.add_argument(
        '--retry-failed',
        action='store_true',
        help="Retry processing files from failed_files.txt"
    )

    parser.add_argument(
        '--verbose',
        action='store_true',
        help="Enable verbose logging"
    )

    parser.add_argument(
        '--pattern',
        default="*.yml",
        help="File pattern to match (default: *.yml)"
    )

    args = parser.parse_args()

    # Configure logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    print("PowerFX Array to Concatenate Converter")
    print("=" * 50)
    print(f"Input path: {args.input_path}")

    if args.dry_run:
        print("DRY RUN MODE - No files will be modified")

    print("=" * 50)

    # Create converter
    converter = PowerFXArrayToConcatenateConverter()

    # Log startup information
    logger.info(f"🚀 PowerFX Array to Concatenate Converter Started")
    logger.info(f"📅 Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"📁 Input path: {args.input_path}")
    logger.info(f"🔍 File pattern: {args.pattern}")
    if args.dry_run:
        logger.info(f"🔍 Mode: DRY RUN (no changes will be made)")
    if args.retry_failed:
        logger.info(f"🔄 Mode: RETRY FAILED FILES")
    logger.info(f"=" * 60)

    try:
        if args.retry_failed:
            # Retry failed files
            print("\n🔄 RETRYING FAILED FILES")
            print("-" * 30)

            retry_results = converter.retry_failed_files()

            print(f"\nRetry Results:")
            print(f"  Files retried:     {retry_results['retried']}")
            print(f"  Successful:        {retry_results['successful']}")
            print(f"  Still failed:      {retry_results['still_failed']}")

            if 'error' in retry_results:
                print(f"  Error: {retry_results['error']}")
                return 1

            converter.print_statistics()
            return 0

        # Process input directory
        input_path = Path(args.input_path)

        if not input_path.exists():
            print(f"❌ ERROR: Input path does not exist: {input_path}")
            return 1

        if not input_path.is_dir():
            print(f"❌ ERROR: Input path is not a directory: {input_path}")
            return 1

        print(f"\n🔍 Processing directory: {input_path}")

        if args.dry_run:
            print("   (DRY RUN - analyzing only)")

        results = converter.process_directory(input_path, args.pattern)

        # Display results
        print(f"\n📊 PROCESSING RESULTS:")
        print(f"   Files found:       {results['files_found']}")
        print(f"   Files processed:   {results['files_processed']}")

        if not args.dry_run:
            print(f"   Files modified:    {results['files_modified']}")

        print(f"   Arrays converted:  {results['arrays_converted']}")
        print(f"   SetVariable processed: {results['setvariable_processed']}")
        print(f"   Errors:           {results['errors']}")

        if results['failed_files']:
            print(f"\n❌ Failed files ({len(results['failed_files'])}):")
            for failed_file in results['failed_files']:
                print(f"     - {failed_file}")
            print(f"\n💡 Use --retry-failed to retry failed files")

        # Show detailed statistics
        converter.print_statistics()

        # Summary message
        if results['arrays_converted'] > 0:
            if args.dry_run:
                print(f"\n✅ SUCCESS: Found {results['arrays_converted']} PowerFX arrays that would be converted")
                print("   Run without --dry-run to apply changes")
            else:
                print(f"\n✅ SUCCESS: Converted {results['arrays_converted']} PowerFX arrays")
                if results['files_modified'] > 0:
                    print(f"   Modified {results['files_modified']} files")
                    print("   Backup files created with .backup extension")
        else:
            print("\n📝 INFO: No PowerFX arrays found to convert")

        if results['errors'] > 0:
            print(f"\n⚠️  WARNING: {results['errors']} errors encountered")
            return 1

        return 0

    except KeyboardInterrupt:
        print("\n⚠️  Interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        print(f"\n❌ ERROR: {e}")
        return 1


if __name__ == "__main__":
    exit(main())

    def process_directory(self, directory_path: Path, pattern: str = "*.yml") -> Dict[str, Any]:
        """
        Process all YAML files in directory (parent folder only, not subfolders)

        Args:
            directory_path: Path to directory
            pattern: File pattern to match

        Returns:
            Processing results dictionary
        """
        results = {
            'files_found': 0,
            'files_processed': 0,
            'files_modified': 0,
            'arrays_converted': 0,
            'setvariable_processed': 0,
            'errors': 0,
            'failed_files': []
        }

        try:
            # Find YAML files in parent directory only (user preference)
            yaml_files = []
            for pattern_ext in [pattern, "*.yaml"]:
                yaml_files.extend(list(directory_path.glob(pattern_ext)))

            # Filter to only direct children (no subdirectories)
            yaml_files = [f for f in yaml_files if f.parent == directory_path]

            results['files_found'] = len(yaml_files)
            logger.info(f"Found {len(yaml_files)} YAML files in {directory_path}")

            if not yaml_files:
                logger.warning(f"No YAML files found in {directory_path}")
                return results

            # Process each file
            for file_path in yaml_files:
                try:
                    self.stats['files_processed'] += 1
                    was_modified = self.process_yaml_file(file_path)

                    if was_modified:
                        results['files_modified'] += 1
                        # Remove from failed list if it was there
                        self.remove_from_failed_list(str(file_path))

                    results['files_processed'] += 1

                except Exception as e:
                    logger.error(f"Error processing {file_path}: {e}")
                    results['errors'] += 1
                    results['failed_files'].append(str(file_path))

            # Update results with current stats
            results['arrays_converted'] = self.stats['arrays_converted']
            results['setvariable_processed'] = self.stats['setvariable_processed']
            results['errors'] = self.stats['errors']
            results['failed_files'] = self.stats['failed_files']

            return results

        except Exception as e:
            logger.error(f"Error processing directory {directory_path}: {e}")
            results['errors'] += 1
            return results

    def retry_failed_files(self) -> Dict[str, Any]:
        """
        Retry processing failed files with sequential processing

        Returns:
            Retry results dictionary
        """
        if not os.path.exists(self.failed_files_path):
            logger.info("No failed files list found")
            return {'retried': 0, 'successful': 0, 'still_failed': 0}

        try:
            with open(self.failed_files_path, 'r', encoding='utf-8') as f:
                failed_files = [line.strip() for line in f.readlines() if line.strip()]

            if not failed_files:
                logger.info("No failed files to retry")
                return {'retried': 0, 'successful': 0, 'still_failed': 0}

            logger.info(f"Retrying {len(failed_files)} failed files")

            successful_retries = 0
            still_failed = 0

            for i, file_path in enumerate(failed_files, 1):
                logger.info(f"Retry [{i}/{len(failed_files)}]: {file_path}")

                if not os.path.exists(file_path):
                    logger.warning(f"File not found: {file_path}")
                    still_failed += 1
                    continue

                try:
                    was_modified = self.process_yaml_file(Path(file_path))
                    successful_retries += 1
                    logger.info(f"Retry successful: {file_path}")

                    # Remove from failed list
                    self.remove_from_failed_list(file_path)

                except Exception as e:
                    logger.error(f"Retry failed for {file_path}: {e}")
                    still_failed += 1

                # Small delay for supervision
                time.sleep(0.5)

            return {
                'retried': len(failed_files),
                'successful': successful_retries,
                'still_failed': still_failed
            }

        except Exception as e:
            logger.error(f"Error during retry process: {e}")
            return {'retried': 0, 'successful': 0, 'still_failed': 0, 'error': str(e)}

    def print_statistics(self):
        """Print comprehensive processing statistics"""
        print("\n" + "=" * 80)
        print("POWERFX ARRAY TO CONCATENATE CONVERSION RESULTS")
        print("=" * 80)
        print(f"Files processed:           {self.stats['files_processed']}")
        print(f"Files modified:            {self.stats['files_modified']}")
        print(f"Arrays converted:          {self.stats['arrays_converted']}")
        print(f"SetVariable entries processed: {self.stats['setvariable_processed']}")
        print(f"Errors encountered:        {self.stats['errors']}")

        if self.stats['failed_files']:
            print(f"\nFailed files ({len(self.stats['failed_files'])}):")
            for failed_file in self.stats['failed_files']:
                print(f"  - {failed_file}")

        print("=" * 80)
