{"response": "```yaml\n- kind: Question\n  id: sl0240_LostStolenWrapMenu_DM\n  displayName: sl0240_LostStolenWrapMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/sl0240_ni1_01.wav\\\">If youre done, just hang up Or if you need help with something else, say Main Menu or press star</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/sl0240_nm2_01.wav\\\">If you dont need any more help, you can hang up If you do need help with something else, press star</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.sl0240_LostStolenWrapMenu_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/sl0240_ini_01.wav\\\">If thats all you needed, go ahead and hang up For anything else, say Main Menu</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: mainmenu\n          displayName: mainmenu\n        - id: operator\n          displayName: operator\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/sl0240_ni1_01.wav\\\">If youre done, just hang up Or if you need help with something else, say Main Menu or press star</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_sl0240_LostStolenWrapMenu_DM\n  value: =Text(Global.sl0240_LostStolenWrapMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_sl0240_LostStolenWrapMenu_DM = \"mainmenu\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferDestination\n          value: attPrepaid\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M01_MainMenu2_Dialog\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_sl0240_LostStolenWrapMenu_DM = \"operator\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.transferReason\n              value: AGENT_REQUEST\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.M09_MainMenu_Dialog\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_sl0240_LostStolenWrapMenu_DM = \"repeat\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: sl0240_LostStolenWrapMenu_DM\n```", "timestamp": 1749472088.5468414, "content_hash": "7252029d6d189688537077c531d037c4"}