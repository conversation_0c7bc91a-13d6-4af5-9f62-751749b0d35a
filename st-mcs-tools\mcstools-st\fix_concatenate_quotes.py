from ruamel.yaml import <PERSON><PERSON><PERSON>
import os
import re
from ruamel.yaml.scalarstring import ScalarString
from ruamel.yaml.comments import TaggedScalar
from collections import OrderedDict

def ensure_directory_exists(file_path):
    directory = os.path.dirname(file_path)
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"Created directory: {directory}")

def read_yaml(file_path):
    print(f"\nStarting to read YAML file: {file_path}")
    try:
        yaml = YAML()
        yaml.width = 4096
        yaml.indent(mapping=2, sequence=4, offset=2)
        yaml.preserve_quotes = True
        with open(file_path, 'r') as file:
            data = yaml.load(file)
            print("Successfully read YAML file")
            return data, yaml
    except FileNotFoundError:
        print(f"Error: File {file_path} not found")
        return None, None
    except Exception as e:
        print(f"Error parsing YAML file: {e}")
        return None, None

def write_yaml(data, yaml_instance, file_path):
    print(f"\nStarting to write updated YAML to: {file_path}")
    try:
        if data is None or yaml_instance is None:
            print("Error: No valid data or YAML instance to write")
            return
            
        ensure_directory_exists(file_path)
        
        with open(file_path, 'w', encoding='utf-8') as file:
            yaml_instance.dump(data, file)
        print(f"Successfully wrote updated YAML to {file_path}")
    except Exception as e:
        print(f"Error writing YAML file: {e}")

def fix_concatenate_quotes(value):
    """
    Fix escaped quotes in Concatenate functions by completely removing \"
    This handles PowerFX syntax requirements for MCS
    """
    if not isinstance(value, str):
        return value

    # Check if this is a Concatenate function
    if 'Concatenate(' in value:
        # Remove escaped quotes completely
        # Handle various patterns of escaped quotes
        fixed_value = value

        # Remove \" completely (most common case)
        fixed_value = re.sub(r'\\\"', '', fixed_value)

        # Handle cases where there might be multiple backslashes before quotes
        fixed_value = re.sub(r'\\+\"', '', fixed_value)

        return fixed_value

    return value

def process_concatenate_fixes(data):
    """
    Recursively process the YAML data to fix Concatenate quote issues
    """
    if data is None:
        return data

    changes_made = 0

    def process_item(item):
        nonlocal changes_made

        if isinstance(item, dict):
            for key, value in item.items():
                if key == 'value' and isinstance(value, str) and 'Concatenate(' in value:
                    original_value = value
                    fixed_value = fix_concatenate_quotes(value)
                    if fixed_value != original_value:
                        item[key] = fixed_value
                        changes_made += 1
                        print(f"Fixed Concatenate quotes in: {key}")
                        print(f"  Before: {original_value[:100]}...")
                        print(f"  After:  {fixed_value[:100]}...")
                else:
                    process_item(value)
        elif isinstance(item, list):
            for subitem in item:
                process_item(subitem)

    process_item(data)
    print(f"\nSuccessfully fixed Concatenate quotes. Total changes made: {changes_made}")
    return data

def preprocess_yaml_text(file_path):
    """
    Preprocess the YAML file to fix escaped quotes in Concatenate functions
    before parsing with ruamel.yaml
    """
    print(f"Preprocessing YAML text to fix Concatenate quotes...")

    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()

    # Count original escaped quotes
    original_count = content.count('\\"')

    # Use regex to find and fix all Concatenate functions with escaped quotes
    # This handles both single-line and multi-line cases
    def fix_concatenate_match(match):
        concatenate_content = match.group(0)
        # Remove all escaped quotes from the Concatenate function
        fixed_content = concatenate_content.replace('\\"', '')
        return fixed_content

    # Pattern to match Concatenate functions across multiple lines
    # This regex handles nested parentheses and multi-line content
    concatenate_pattern = r'=Concatenate\([^)]*(?:\([^)]*\)[^)]*)*\)'

    # Apply the fix to all Concatenate functions
    fixed_content = re.sub(concatenate_pattern, fix_concatenate_match, content, flags=re.DOTALL)

    # Also handle any remaining escaped quotes in lines that contain "Concatenate" but weren't caught by regex
    lines = fixed_content.split('\n')
    final_lines = []

    for line in lines:
        if 'Concatenate(' in line and '\\"' in line:
            # Remove remaining escaped quotes from lines containing Concatenate
            fixed_line = line.replace('\\"', '')
            final_lines.append(fixed_line)
        else:
            final_lines.append(line)

    final_content = '\n'.join(final_lines)

    # Count remaining escaped quotes
    final_count = final_content.count('\\"')
    changes_made = original_count - final_count

    print(f"Preprocessed and removed {changes_made} escaped quotes from Concatenate functions")
    print(f"Remaining escaped quotes in file: {final_count}")

    return final_content

def process_yaml_file(input_file, output_file):
    print("\n=== Starting YAML Concatenate Quote Fix Processing ===")

    if not os.path.exists(input_file):
        print(f"Error: Input file '{input_file}' not found")
        return False

    # Preprocess the YAML text to fix escaped quotes
    fixed_yaml_content = preprocess_yaml_text(input_file)

    # Write the preprocessed content to a temporary file
    temp_file = input_file + '.temp'
    with open(temp_file, 'w', encoding='utf-8') as file:
        file.write(fixed_yaml_content)

    try:
        # Read the preprocessed YAML
        data, yaml_instance = read_yaml(temp_file)
        if data is None:
            print("Error: Failed to read preprocessed file")
            return False

        # Write the final output
        write_yaml(data, yaml_instance, output_file)
        print("\n=== YAML Concatenate Quote Fix Processing Complete ===")
        return True

    finally:
        # Clean up temporary file
        if os.path.exists(temp_file):
            os.remove(temp_file)

if __name__ == "__main__":
    # Get the directory containing the script
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # Define input and output files relative to script directory
    # Process the original file with quote removal logic
    input_file = os.path.join(script_dir, 'processed_ternary_operations.yml')
    output_file = os.path.join(script_dir, 'removed_slashes.yml')

    success = process_yaml_file(input_file, output_file)
    exit(0 if success else 1)
