{"response": "```yaml\n- kind: Question\n  id: aa1029a_EmergencyMessageWrapMenuDTMF_DM\n  displayName: aa1029a_EmergencyMessageWrapMenuDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - aa1029a_EmergencyMessageWrapMenuDTMF_DM_initial\n      - aa1029a_EmergencyMessageWrapMenuDTMF_DM_initial\n\n  alwaysPrompt: true\n  variable: Global.aa1029a_EmergencyMessageWrapMenuDTMF_DM_reco\n  prompt:\n    speak:\n      - \"{CustomPrompt: com.nuance.fis.audio.aa1029a_EmergencyMessageWrapMenuDTMF_DM_Render}\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: repeat\n          displayName: repeat\n        - id: transfer_CS\n          displayName: transfer_CS\n        - id: continue_ivr\n          displayName: continue_ivr\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - aa1029a_EmergencyMessageWrapMenuDTMF_DM_initial\n        - aa1029a_EmergencyMessageWrapMenuDTMF_DM_initial\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa1029a_EmergencyMessageWrapMenuDTMF_DM\n  value: =Text(Global.aa1029a_EmergencyMessageWrapMenuDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa1029a_EmergencyMessageWrapMenuDTMF_DM = \"repeat\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa1029_EmergencyMessage_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa1029a_EmergencyMessageWrapMenuDTMF_DM = \"transfer_CS\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.nextStep\n              value: transfer_CS\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.transferReason\n              value: emergencyWrapUp\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.transferAllowed\n              value: true\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: return\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_aa1029a_EmergencyMessageWrapMenuDTMF_DM = \"continue_ivr\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: return\n```", "timestamp": 1749458404.3282738, "content_hash": "d92147bee26c08e789a8ee35d8a36278"}