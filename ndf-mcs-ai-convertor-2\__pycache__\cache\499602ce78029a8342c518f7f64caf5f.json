{"response": "```yaml\n- kind: Question\n  id: bp0210_AskCardNumber_DM\n  displayName: bp0210_AskCardNumber_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I missed that</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/bp0210_ni1_01.wav\\\">Please say the number of the credit or debit card you'd like to use, or enter it, followed by pound</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">Sorry</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/bp0210_ni2_01.wav\\\">Go ahead and enter your credit or debit card number, followed by pound</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/bp0210_nm3_01.wav\\\">Sorry, I still didn't get that Please enter your credit or debit card number, followed by pound </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.bp0210_AskCardNumber_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.fromBp0225 = true,\n            \"Please say or enter another credit or debit card number\",\n\n            Global.intent = \"autoRefill\" || Global.intent = \"refillPlusAutoPay\",\n            \"Please say or enter the credit or debit card number you'd like to use for Auto Pay\",\n\n            \"Please say or enter your credit or debit card number\"\n        )}\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/bp0210_ni1_01.wav\\\">Please say the number of the credit or debit card you'd like to use, or enter it, followed by pound</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/bp0210_ni2_01.wav\\\">Go ahead and enter your credit or debit card number, followed by pound</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.chargeAccountNumber\n  value: bp0210_AskCardNumber_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.isNewPdof\n  value: true\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: bp0215_GetChargePaymentDeviceInfo_DB_DA\n```", "timestamp": **********.897931, "content_hash": "499602ce78029a8342c518f7f64caf5f"}