{"response": "```yaml\n- kind: Question\n  id: AP1005_ACPStart_DM\n  displayName: AP1005_ACPStart_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AP1005_nm1_01.wav\\\">You can say repeat that Or say main menu  If you'd like a link to our ACP information page say, 'send me a text'</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AP1005_nm2_01.wav\\\">You can say repeat that Or say main menu  If you'd like a link to our ACP information page say, 'send me a text'</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AP1005_nm2_01.wav\\\">You can say repeat that Or say main menu  If you'd like a link to our ACP information page say, 'send me a text'</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.AP1005_ACPStart_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/AP1005_ini_01.wav\\\">If you are a customer using the Affordable Connectivity Program, please be aware that the program will be ended by the federal government in April But Metro is here to help We will continue to provide current Metro ACP beneficiaries the full ACP discount through your May bill Then, starting on your June bill date you will receive up to a $15 credit to help with the transition for three months Remember, at Metro you're never locked into a contract, you have control to change your service when you need to  You can say 'repeat that' or 'main menu'  If you'd like a link to our acp information page say 'send me a text'</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: send_text\n          displayName: send_text\n        - id: main_menu\n          displayName: main_menu\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/AP1005_ini_01.wav\\\">If you are a customer using the Affordable Connectivity Program, please be aware that the program will be ended by the federal government in April But Metro is here to help We will continue to provide current Metro ACP beneficiaries the full ACP discount through your May bill Then, starting on your June bill date you will receive up to a $15 credit to help with the transition for three months Remember, at Metro you're never locked into a contract, you have control to change your service when you need to  You can say 'repeat that' or 'main menu'  If you'd like a link to our acp information page say 'send me a text'</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AP1005_nm2_01.wav\\\">You can say repeat that Or say main menu  If you'd like a link to our ACP information page say, 'send me a text'</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AP1005_nm2_01.wav\\\">You can say repeat that Or say main menu  If you'd like a link to our ACP information page say, 'send me a text'</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.saidOperatorAcpAt1005\n  value: GlobalVars.saidOperatorAcpAt1005\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.operatorReqCount_AP1005\n  value: \"GlobalVars.operatorReqCount_AP1005 != undefined ? GlobalVars.operatorReqCount_AP1005 : 0\"\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_AP1005_ACPStart_DM\n  value: =Text(Global.AP1005_ACPStart_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AP1005_ACPStart_DM = \"send_text\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.campaignId\n          value: GlobalVars.GetBCSParameters.acp_info_campaign_id\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: AP1010_SendACPLinks_DB_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_AP1005_ACPStart_DM = \"main_menu\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.acpFlowVisited\n              value: true\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n```", "timestamp": 1749527412.8159282, "content_hash": "40d48b429cb8bbdcd7f8ab006ecbe5d0"}