{"response": "```yaml\n- kind: Question\n  id: aa6530_MultipleTransactionsFoundDTMF_DM\n  displayName: aa6530_MultipleTransactionsFoundDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.transactionCounter = 2,\n              \"At any time you can use the following To repeat a transaction, Press 1 To hear the next set of transactions, Press 2 To stop, Press 3\",\n              true,\n              \"To repeat, press 1 To hear the next transactions, press 2 To stop, press 3\"\n          )\n        }\n      - |\n        {Switch(\n              true,\n              Global.dnisInfo.dtmfOnlyFlag = true,\n              \"Sorry, I still didn t get that\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.aa6530_MultipleTransactionsFoundDTMF_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.transactionCounter = 2,\n              \"At any time you can use the following To repeat a transaction, Press 1 To hear the next set of transactions, Press 2 To stop, Press 3\",\n              true,\n              \"To repeat, press 1 To hear the next transactions, press 2 To stop, press 3\"\n          )\n        }\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: repeat\n          displayName: repeat\n        - id: next\n          displayName: next\n        - id: stop\n          displayName: stop\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.transactionCounter = 2,\n                \"At any time you can use the following To repeat a transaction, Press 1 To hear the next set of transactions, Press 2 To stop, Press 3\",\n                true,\n                \"To repeat, press 1 To hear the next transactions, press 2 To stop, press 3\"\n            )\n          }\n        - |\n          {Switch(\n                true,\n                Global.dnisInfo.dtmfOnlyFlag = true,\n                \"Sorry, I still didn t get that\"\n            )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa6530_MultipleTransactionsFoundDTMF_DM\n  value: =Text(Global.aa6530_MultipleTransactionsFoundDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa6530_MultipleTransactionsFoundDTMF_DM = \"repeat\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa6527_MultipleTransactionsFound_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa6530_MultipleTransactionsFoundDTMF_DM = \"next\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.transactionCounter\n              value: transactionCounter+1\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa6527_MultipleTransactionsFound_PP\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_aa6530_MultipleTransactionsFoundDTMF_DM = \"stop\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: aa6535_TransactionHistoryWrapUpDTMF_DM\n```", "timestamp": 1749543730.2574275, "content_hash": "a4ce4cbd86b7c0f288dcfbd1f029a43e"}