{"response": "```yaml\n- kind: Question\n  id: ma2210_ConfirmContinueActivation_DM\n  displayName: ma2210_ConfirmContinueActivation_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I missed that</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ma2210_ni1_01.wav\\\">Are you calling to finish an activation that you started on the phone earlier? Just say Yes or No</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">Sorry</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ma2210_ni2_01.wav\\\">If you want to finish an activation that you started in an earlier call, say Yes or press 1 If you want to activate a different device, say No or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ma2210_ConfirmContinueActivation_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ma2210_ini_01.wav\\\">I've matched your phone number to an activation in progress Are you calling to finish that activation? </audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ma2210_ni1_01.wav\\\">Are you calling to finish an activation that you started on the phone earlier? Just say Yes or No</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ma2210_ni2_01.wav\\\">If you want to finish an activation that you started in an earlier call, say Yes or press 1 If you want to activate a different device, say No or press 2</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.fromma2210\n  value: true\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.callType = \"611\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_commandgrammar\n          value: 'GlobalCommands.grxml?SWI_vars.allow=mainmenu&SWI_vars.operator_cnf_mode=NEVER'\n\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.collection_commandgrammar\n      value: 'GlobalCommands.grxml?SWI_vars.operator_cnf_mode=NEVER'\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ma2210_ConfirmContinueActivation_DM\n  value: =Text(Global.ma2210_ConfirmContinueActivation_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.callType = \"611\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_commandgrammar\n          value: 'GlobalCommands.grxml?SWI_vars.allow=mainmenu'\n\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.collection_commandgrammar\n      value: 'GlobalCommands.grxml'\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma2210_ConfirmContinueActivation_DM = \"yes\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.verifySIM\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.continueActivation\n          value: true\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ma2215_AskIfHavePhoneNumber_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ma2210_ConfirmContinueActivation_DM = \"no\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.sim\n              value: ''\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.imei\n              value: ''\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.simlast4digit\n              value: \"\"\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.accountType\n              value: shared\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.callerDeviceType\n              value: ''\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.continueActivation\n              value: false\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.activationStatus\n              value: ''\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: ma2115_AskIfReplacingDevice_DM\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_ma2210_ConfirmContinueActivation_DM = \"spanish\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.systemLanguage\n                  value: spanish\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.language\n                  value: es-US\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.nluEnabled\n                  value: false\n\n                - kind: BeginDialog\n                  id: begin_REPLACE_THIS\n                  dialog: topic.M02_MainMenu.dvxml#ma2210_ConfirmContinueActivation_DM\n```", "timestamp": 1749471600.1428645, "content_hash": "fee39ca8d580fac35a681219665aaac3"}