{"response": "```yaml\n- kind: Question\n  id: aa1512_ConfirmContinueCancelCard_DM\n  displayName: aa1512_ConfirmContinueCancelCard_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.aa1512_ConfirmContinueCancelCard_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa1512_ini_01.wav\\\">If you would like to continue to cancel your card, press 1Otherwise, just hang up</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: cancel_card\n          displayName: cancel_card\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa1512_ConfirmContinueCancelCard_DM\n  value: =Text(Global.aa1512_ConfirmContinueCancelCard_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa1512_ConfirmContinueCancelCard_DM = \"cancel_card\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa1520_CardWillBeDeactivatedMsg_PP\n```", "timestamp": 1749543602.1420865, "content_hash": "60e13eaa84789ba3d0077c837523f929"}