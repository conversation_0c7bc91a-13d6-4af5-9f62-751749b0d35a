{"response": "```yaml\n- kind: Question\n  id: ES1725_FindStore_DM\n  displayName: ES1725_FindStore_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ES1725_nm1_01.wav\\\">You can say find a store, or if you re done, just hang up </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ES1725_nm2_01.wav\\\">To find out where you can get help, you can find a map of our corporate stores at metrobyt-mobilecom</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ES1725_FindStore_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n\n            // Case 1: ES1725_operator_counter != 1\n            Global.ES1725_operator_counter <> 1,\n            \"To look for Metro stores right now, say  find a store  You can also find a map of our locations near you at metrobyt-mobilecom If you re done, you can just hang up\",\n\n            // Case 2: ES1725_operator_counter == 1 && (simExpired = true || simExpired = 'true')\n            Global.ES1725_operator_counter = 1 && (Global.simExpired = true || Global.simExpired = \\\"true\\\"),\n            \"I m sorry There s a problem with your SIM card, so our agents would not be able to work with it over the phone You would need to buy a new one at a Metro store or authorized dealer You can say  find a store , or go to metrobyt-mobilecom for a map of our locations If you re done, you can just hang up\",\n\n            // Case 3: ES1725_operator_counter == 1 && !(simExpired = true || simExpired = 'true')\n            Global.ES1725_operator_counter = 1 && !(Global.simExpired = true || Global.simExpired = \\\"true\\\"),\n            \"I m sorry Because of an issue with your phone, our agents would not be able to work with it over the phone You would need to get help *in person* at a Metro store or authorized dealer You can say  find a store , or go to metrobyt-mobilecom for a map of our locations If you re done, you can just hang up\",\n\n            // Default\n            \"To look for Metro stores right now, say  find a store  You can also find a map of our locations near you at metrobyt-mobilecom If you re done, you can just hang up\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: inquire-store_location\n          displayName: inquire-store_location\n        - id: operator\n          displayName: operator\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ES1725_nm1_01.wav\\\">You can say find a store, or if you re done, just hang up </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ES1725_nm2_01.wav\\\">To find out where you can get help, you can find a map of our corporate stores at metrobyt-mobilecom</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.simExpired\n  value: GlobalVars.ValidateDevice.simExpired\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ES1725_FindStore_DM\n  value: =Text(Global.ES1725_FindStore_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ES1725_FindStore_DM = \"inquire-store_location\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.storeLocatorReason\n          value: payment\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ES1730_StoreLocator_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ES1725_FindStore_DM = \"operator\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.ES1725_operator_counter\n              value: ES1725_operator_counter+1\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.ES1725_operator_counter < 2, true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: ES1725_FindStore_DM\n              elseActions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/ES1725_out_01.wav\\\">I m sorry Our agents would not be able to help you To find a map of our locations, please visit us at metrobyt-mobilecom</audio>\"\n                      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: ES1735_Goodbye_SD\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_ES1725_FindStore_DM = \"repeat\", true, false)\n              actions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.enteringFrom = \"ES1715_SIMExpired_PP\", true, false)\n                      actions:\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: ES1715_SIMExpired_PP\n                  elseActions:\n                    - kind: ConditionGroup\n                      id: conditionGroup_REPLACE_THIS\n                      conditions:\n                        - id: conditionItem_REPLACE_THIS\n                          condition: =If(Global.enteringFrom = \"ES1720_DeviceInvalid_PP\", true, false)\n                          actions:\n                            - kind: GotoAction\n                              id: goto_REPLACE_THIS\n                              actionId: ES1720_DeviceInvalid_PP\n                    elseActions:\n                      - kind: GotoAction\n                        id: goto_REPLACE_THIS\n                        actionId: ES1725_FindStore_DM\n```", "timestamp": 1749527978.087978, "content_hash": "73f919b0ea54e7a8dd40f5b7b53854fa"}