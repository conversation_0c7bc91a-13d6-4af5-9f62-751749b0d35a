{"response": "```yaml\n- kind: Question\n  id: AC2220_CollectIMEI_DM\n  displayName: AC2220_CollectIMEI_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AC2220_nm1_01.wav\\\">Please enter the 15-digit I-M-E-I for your phone You can skip any numbers that come after a slash To hear how you can find the number again, say  instructions </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AC2220_nm2_01.wav\\\">If you have the 15-digit IMEI number for your new phone, please enter it now on your keypad Skip any numbers that come after a slash To repeat the instructions to *find* the number, press star</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AC2220_nm2_01.wav\\\">If you have the 15-digit IMEI number for your new phone, please enter it now on your keypad Skip any numbers that come after a slash To repeat the instructions to *find* the number, press star</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.AC2220_CollectIMEI_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.imeiFailChecksum = true,\n            \"That doesn t look like a valid number Let s try one more time to be sure If you have two different numbers, look for the one labeled I-M-E-I To hear the instructions to *find* the number, press star Otherwise, go ahead and enter it again\",\n            \"If you see a slash followed by two digits after the number, I *don t* need those So, what s the 15-digit I-M-E-I? You can say or enter it\"\n        )}\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/AC2220_nm1_01.wav\\\">Please enter the 15-digit I-M-E-I for your phone You can skip any numbers that come after a slash To hear how you can find the number again, say  instructions </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AC2220_nm2_01.wav\\\">If you have the 15-digit IMEI number for your new phone, please enter it now on your keypad Skip any numbers that come after a slash To repeat the instructions to *find* the number, press star</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AC2220_nm2_01.wav\\\">If you have the 15-digit IMEI number for your new phone, please enter it now on your keypad Skip any numbers that come after a slash To repeat the instructions to *find* the number, press star</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.imeiFailChecksum\n  value: \"GlobalVars.imeiFailChecksum != undefined ? GlobalVars.imeiFailChecksum : false\"\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AC2220_CollectIMEI_DM = \"instructions\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/AC2220_out_01.wav\\\">Sure, instructions</audio>\"\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.imeiFailChecksum\n          value: false\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: AC2210_FindIMEIInstructions_PP\n\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.GlobalVars.imeiSerialNumber\n      value: AC2220_CollectIMEI_DM.returnvalue\n\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.GlobalVars.IMEI\n      value: AC2220_CollectIMEI_DM.returnvalue\n\n    - kind: GotoAction\n      id: goto_REPLACE_THIS\n      actionId: AC2225_IMEIPassChecksum_JDA_DA\n```\n**Notes:**\n- All audio references are formatted as `<audio src=\"AUDIO_LOCATION/filename.wav\">text</audio>`.\n- The `prompt` and `repeatprompts` with if/else are converted to a PowerFX-style Switch.\n- All session-mapping and actions are converted to SetVariable and GotoAction as per your rules.\n- The ConditionGroup is created for the \"instructions\" label, with elseActions for the default path.\n- The entity is set as `StringPrebuiltEntity` since there is no closed list or boolean.\n- All ids for SetVariable, GotoAction, etc., are set to `REPLACE_THIS` as per your placeholder rule.\n- Only relevant tags are converted; `<command>` and `<catch>` are ignored as per your instructions.", "timestamp": 1749558757.6054845, "content_hash": "a56f0bf7ad786ecb69228d72d436f1f9"}