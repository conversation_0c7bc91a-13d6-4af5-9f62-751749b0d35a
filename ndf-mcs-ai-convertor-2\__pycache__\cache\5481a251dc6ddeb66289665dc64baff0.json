{"response": "```yaml\n- kind: Question\n  id: aa7506_FreezeCardWrap_DM\n  displayName: aa7506_FreezeCardWrap_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.aa7506_FreezeCardWrap_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa7506_ini_01.wav\\\">If you would like to hear that again, say Repeat That To go to the main menu, say Main Menu And if you are done feel free to hang up</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: main_menu\n          displayName: main_menu\n        - id: repeat_that\n          displayName: repeat_that\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa7506_FreezeCardWrap_DM\n  value: =Text(Global.aa7506_FreezeCardWrap_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa7506_FreezeCardWrap_DM = \"main_menu\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.nextStep\n          value: main_menu\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa1015_NextStepHandling_JDA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa7506_FreezeCardWrap_DM = \"repeat_that\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa7506_FreezeCardWrap_DM\n```", "timestamp": 1749543789.6777582, "content_hash": "5481a251dc6ddeb66289665dc64baff0"}