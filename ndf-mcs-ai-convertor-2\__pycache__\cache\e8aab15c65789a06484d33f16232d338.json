{"response": "```yaml\n- kind: Question\n  id: ES1315_FindICCIDWaitSBI_DM\n  displayName: ES1315_FindICCIDWaitSBI_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.ES1315_FindICCIDWaitSBI_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ES1315_ini_01.wav\\\">I ll wait while you look for it When you have it, say  continue  </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ES1315_ini_02.wav\\\">10 seconds wait music Look for the 19-digit SIM Card number, that ends with the letter F When you have it, say 'continue' Or say 'I can't find it' 10 seconds wait music When you have your SIM card number, say 'continue' or press 1 Or say 'I can't find it' or press 210 seconds wait music When you have your SIM card number, say 'continue' or press 1 Or say 'I can't find it' or press 2 10 seconds wait music When you have your SIM card number, say 'continue' or press 1 Or say 'I can't find it' or press 2 10 seconds wait music When you have your SIM card number, say 'continue' or press 1 Or say 'I can't find it' or press 2 10 seconds wait music When you have your SIM card number, say 'continue' or press 1 Or say 'I can't find it' or press 2 10 seconds wait music When you have your SIM card number, say 'continue' or press 1 Or say 'I can't find it' or press 2 10 seconds wait music When you have your SIM card number, say 'continue' or press 1 Or say 'I can't find it' or press 2 10 seconds wait music We seem to be having some trouble              </audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: continue\n          displayName: continue\n        - id: cant_find\n          displayName: cant_find\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ES1315_FindICCIDWaitSBI_DM\n  value: =Text(Global.ES1315_FindICCIDWaitSBI_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ES1315_FindICCIDWaitSBI_DM = \"continue\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/ES1315_out_01.wav\\\">Okay </audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ES1320_CollectICCID_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ES1315_FindICCIDWaitSBI_DM = \"cant_find\", true, false)\n          actions:\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.CareESNSwap_Conflicts_Main.dvxml#ES1935_Transfer_SD\n```", "timestamp": 1749528165.181744, "content_hash": "e8aab15c65789a06484d33f16232d338"}