{"response": "```yaml\n- kind: Question\n  id: AR1005_OfferSMSLinkForAcctPinUpdate_DM\n  displayName: AR1005_OfferSMSLinkForAcctPinUpdate_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AR1005_nm1_01.wav\\\">You can say repeat that' or To receive a text with the link to our website say 'send me  text' You can also say Main menu</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AR1005_nm2_01.wav\\\">Please say 'send me a text' or press 1  Main Menu or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AR1005_nm2_01.wav\\\">Please say 'send me a text' or press 1  Main Menu or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.AR1005_OfferSMSLinkForAcctPinUpdate_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.saidOperatorArAt1005 <> true || Global.saidRepeatAtAR1005 = true,\n            \"To keep your account secure, only you can make adjustments to your account online  Customer care is unable to reset your account PINYou can reset your account PIN online at metro by t dash mobile dot comSimply sign into your account , go to 'My Account' and select 'Profile'From there select,'PIN/Passcode'To hear that again say, 'Repeat that'  To do something else, say 'main menu'\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: repeat\n          displayName: repeat\n        - id: main-menu\n          displayName: main-menu\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/AR1005_nm1_01.wav\\\">You can say repeat that' or To receive a text with the link to our website say 'send me  text' You can also say Main menu</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AR1005_nm2_01.wav\\\">Please say 'send me a text' or press 1  Main Menu or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AR1005_nm2_01.wav\\\">Please say 'send me a text' or press 1  Main Menu or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.saidOperatorArAt1005\n  value: GlobalVars.saidOperatorArAt1005\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.saidRepeatAtAR1005\n  value: \"GlobalVars.saidRepeatAtAR1005 != undefined ? GlobalVars.saidRepeatAtAR1005 : false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.operatorReqCount_AR1005\n  value: GlobalVars.operatorReqCount_AR1005 != undefined ? GlobalVars.operatorReqCount_AR1005 : 0\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_AR1005_OfferSMSLinkForAcctPinUpdate_DM\n  value: =Text(Global.AR1005_OfferSMSLinkForAcctPinUpdate_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AR1005_OfferSMSLinkForAcctPinUpdate_DM = \"repeat\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.saidRepeatAtAR1005\n          value: true\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: AR1005_OfferSMSLinkForAcctPinUpdate_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_AR1005_OfferSMSLinkForAcctPinUpdate_DM = \"main-menu\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.needMDN\n              value: false\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.cti_Intent\n              value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.callType\n              value: undefined\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.tag\n              value: undefined\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.nluReEntryAfterSelfService\n              value: true\n\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.GlobalVars.enteredFrom = \"PWR\" && Global.GlobalVars.GetAccountDetails = undefined, true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: getReturnLink()\n\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.GlobalVars.GetBCSParameters.care_nlu_enabled = \"true\" && Global.language = \"en-US\", true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: AR1015_NLUMainMenu_SD\n\n              elseActions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: AR1010_MainMenu_SD\n```", "timestamp": **********.7401485, "content_hash": "5f2bfae9a7f443fd21b09b15c8e218e5"}