{"response": "```yaml\n- kind: Question\n  id: pr0230_AskWhichPlan_DM\n  displayName: pr0230_AskWhichPlan_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I missed that</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/pr0230_AskWhichPlan_noinput_01.wav\\\"></audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">Sorry</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/pr0230_AskWhichPlan_noinput_02.wav\\\"></audio>\"\n\n  alwaysPrompt: true\n  variable: Global.pr0230_AskWhichPlan_DM_reco\n  prompt:\n    speak:\n      - \"{CustomAudio: com.nuance.att.application.audio.pr0230_AskWhichPlan_initial}\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: other\n          displayName: other\n        - id: plan_code\n          displayName: plan_code\n        - id: two_dollar_plan\n          displayName: two_dollar_plan\n        - id: multi_line\n          displayName: multi_line\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"{CustomAudio: com.nuance.att.application.audio.pr0230_AskWhichPlan_noinput_01}\"\n        - \"{CustomAudio: com.nuance.att.application.audio.pr0230_AskWhichPlan_noinput_02}\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_commandgrammar\n  value: \"'GlobalCommands.grxml?SWI_vars.allow=mainmenu&SWI_vars.operator_cnf_mode=NEVER'\" # if callType = 611\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_commandgrammar\n  value: \"'GlobalCommands.grxml?SWI_vars.operator_cnf_mode=NEVER'\" # else\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.returnCode\n  value: GetPlanOptionList.returnCode\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.returnCode = 0, true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_dtmfgrammar1\n          value: \"'pr0230_AskWhichPlan_DM_dtmf.jsp'+GetPlanOptionList.planOptionStringDtmf\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_grammar1\n          value: \"'pr0230_AskWhichPlan_DM.jsp'+GetPlanOptionList.planOptionStringVoice\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.planCategoryCode\n          value: GetPlanOptionList.planCategoryCode\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_pr0230_AskWhichPlan_DM\n  value: =Text(Global.pr0230_AskWhichPlan_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pr0230_AskWhichPlan_DM = \"other\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.fromPlansAndPackages = true && Global.numberOfAvailableFeatures > 0, true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: getReturnLink()\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.choseOtherPlans\n              value: true\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: pr0220_AskWhichCategory_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pr0230_AskWhichPlan_DM = \"plan_code\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.lastTransferState\n          value: pr0230_AskWhichPlan_DM\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferReason\n          value: BUSINESS_RULE\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M09_MainMenu.dvxml#ma9210_NeedTransferType_DS\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pr0230_AskWhichPlan_DM = \"two_dollar_plan\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.choseOtherPlans\n          value: true\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: pr0220_AskWhichCategory_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pr0230_AskWhichPlan_DM = \"multi_line\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/pr0230_out_01.wav\\\">Okay</audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/pr0230_out_02.wav\\\">To setup or join a multiline account, log into a t t dot com slash myprepaid Again that's a t t dot com slash myprepaid</audio>\"\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.fromPlansAndPackages = true && Global.numberOfAvailableFeatures > 0, true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: getReturnLink()\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.choseOtherPlans\n              value: true\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: pr0220_AskWhichCategory_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pr0230_AskWhichPlan_DM = \"default\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.planCode\n          value: pr0230_AskWhichPlan_DM.returnvalue\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.choseOtherPlans\n          value: false\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.P02_ChoosePlan_03.dvxml\n\n# Command actions\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pr0230_AskWhichPlan_DM = \"mainmenu\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferDestination\n          value: attPrepaid\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M01_MainMenu2.dvxml#ma1334_InitiateMainMenu_DS\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pr0230_AskWhichPlan_DM = \"operator\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.callType = \"activations\", true, false)\n              actions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/pr0230_operator_01.wav\\\">I cant transfer you until your account is activated, but you can always change your plan later For any other questions, please refer to your activation materials</audio>\"\n                      - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\"></audio>\"\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: pr0230_AskWhichPlan_DM\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.lastTransferState\n              value: pr0230_AskWhichPlan_DM\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.transferReason\n              value: AGENT_REQUEST\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.M09_MainMenu.dvxml#ma9210_NeedTransferType_DS\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pr0230_AskWhichPlan_DM = \"repeat\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: pr0230_AskWhichPlan_DM\n```\n**Notes:**\n- All ids with `_REPLACE_THIS` should be replaced with unique values in actual implementation.\n- `{CustomAudio: ...}` is a placeholder for custom audio prompt logic; if you want to use the actual audio file, replace with the correct `<audio src=...>` markup as in other examples.\n- All `BeginDialog` dialog values are prefixed with `topic.` as per your instructions.\n- All `SetVariable` and `GotoAction`/`BeginDialog` actions are structured as per your memorized YAML and instructions.\n- All `unrecognizedPrompt` and `inputTimeoutResponse` audio references are constructed as per your rules, using the audiofile text and src.\n- All conditions are written in PowerFx style as per your instructions.\n- The entity list is constructed from the action labels in the `<success>` tag.\n- The command actions are mapped as additional `ConditionGroup` for command handling.\n- If you need the YAML to be wrapped in a DialogComponent (as in some examples), you can nest this under a `DialogComponent` as needed.", "timestamp": 1749471660.2436333, "content_hash": "6b862f41b017aa11b3102915bb047a86"}