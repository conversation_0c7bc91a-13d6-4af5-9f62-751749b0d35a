{"response": "```yaml\n- kind: Question\n  id: nlu0100_NLU_Disambiguation_DM\n  displayName: nlu0100_NLU_Disambiguation_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.nlu0100_NLU_Disambiguation_DM_reco\n  prompt:\n    speak:\n      - \"\"\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.disambigTag\n  value: .returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.model.appdata.callerIntent\n  value: disambigTag\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.ssmScore\n  value: '100'\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: getReturnLink()\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.nlu0100_NLU_Disambiguation_DM_reco = \"operator\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.model.appdata.callerIntent\n          value: operator\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.ssmScore\n          value: '100'\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: getReturnLink()\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.nlu0100_NLU_Disambiguation_DM_reco = \"mainmenu\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.model.appdata.callerIntent\n              value: operator_main_menu\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.ssmScore\n              value: '100'\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: ma1325_MainMenu_DM\n```", "timestamp": 1749471401.8395889, "content_hash": "54d1839fb306ac5f6690d63e686811d2"}