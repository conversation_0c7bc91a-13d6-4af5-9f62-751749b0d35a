{"response": "```yaml\n- kind: Question\n  id: DS3010_DealerOrCustomer_DM\n  displayName: DS3010_DealerOrCustomer_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/DS3010_nm1_01.wav\\\">I couldn't find that dealer code Try entering it again </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.DS3010_DealerOrCustomer_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/DS3010_ini_01.wav\\\">You've reached Metro's DSG  As part of this call, and subsequent interactions from this call, we may collect information about you to improve service To learn more, go to metro by T dash Mobile dot com slash privacy If you are a Metro customer, please press Star If you're a Metro Authorized Dealer, please enter your dealer code now </audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: star\n          displayName: star\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/DS3010_ni1_01.wav\\\">Please enter your dealer code now If you are a Metro  Customer, press star</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.playedCCPA\n  value: true\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_DS3010_DealerOrCustomer_DM\n  value: =Text(Global.DS3010_DealerOrCustomer_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_DS3010_DealerOrCustomer_DM = \"star\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: DS3015_PlayCustomerMessage_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_DS3010_DealerOrCustomer_DM = \"default\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.language = \"en-US\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.TransferTag\n                      value: DSGapp_Dealer_English\n              elseActions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.TransferTag\n                  value: DSGapp_Dealer_Spanish\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: DS3011_DSGOptions_DM\n```", "timestamp": 1749528341.0797336, "content_hash": "664fc07fd466bf086a5a576fa94ec8ac"}