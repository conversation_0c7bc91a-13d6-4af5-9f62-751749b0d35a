{"response": "```yaml\n- kind: Question\n  id: IU1210_RepeatFindMDNInfoYN_DM\n  displayName: IU1210_RepeatFindMDNInfoYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/IU1210_nm1_01.wav\\\">Would you like to hear that again? </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/IU1210_nm2_01.wav\\\">To hear about finding your number again, press 1 If youre done, you can just hang up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/IU1210_nm2_01.wav\\\">To hear about finding your number again, press 1 If youre done, you can just hang up</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.IU1210_RepeatFindMDNInfoYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/IU1210_ini_01.wav\\\">Would you like to hear about finding your number again?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/IU1210_nm1_01.wav\\\">Would you like to hear that again? </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/IU1210_nm2_01.wav\\\">To hear about finding your number again, press 1 If youre done, you can just hang up</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/IU1210_nm2_01.wav\\\">To hear about finding your number again, press 1 If youre done, you can just hang up</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_IU1210_RepeatFindMDNInfoYN_DM\n  value: =Text(Global.IU1210_RepeatFindMDNInfoYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_IU1210_RepeatFindMDNInfoYN_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/IU1210_out_01.wav\\\">Sure</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: IU1205_PlayFindMDNInfo_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_IU1210_RepeatFindMDNInfoYN_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/IU1210_out_02.wav\\\">Okay Call us back when you have your number! You can also manage your account with the myMetro app, or at metro by t dash mobile dot com</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: IU1035_Goodbye_SD\n```", "timestamp": **********.731188, "content_hash": "3a6ce40289a25e91120777e437f34a85"}