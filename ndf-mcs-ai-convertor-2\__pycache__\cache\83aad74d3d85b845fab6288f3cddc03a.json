{"response": "```yaml\n- kind: Question\n  id: RP1425_NoOtherPlans_DM\n  displayName: RP1425_NoOtherPlans_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.RP1425_NoOtherPlans_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.RP1425_operator_counter > 0,\n            [\n                \"<audio src=\\\"AUDIO_LOCATION/RP1425_operator_01.wav\\\">I m sorry, I can t transfer you right now</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/RP1425_operator_02.wav\\\">To see if you're eligible for a payment extension, say 'extension' or to hear the plan information again say 'go back' </audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/RP1425_operator_04.wav\\\">If you re done, you can simply hang up</audio>\"\n            ],\n            Global.ratePlans > 1,\n            [\n                \"<audio src=\\\"AUDIO_LOCATION/RP1425_ini_01.wav\\\">Those are the only plans I can offer you right now </audio>\"\n            ],\n            Global.ratePlans = 1,\n            [\n                \"<audio src=\\\"AUDIO_LOCATION/RP1425_ini_02.wav\\\">This is the only plan I can offer you right now  </audio>\"\n            ],\n            Global.extensionAllowed = true && Global.heardDataExceededInfo = false,\n            [\n                \"<audio src=\\\"AUDIO_LOCATION/RP1425_ini_03.wav\\\">To check if you can get a payment extension, say 'extension' or to hear the plan information again say 'go back' </audio>\"\n            ],\n            !(Global.extensionAllowed = true && Global.heardDataExceededInfo = false),\n            [\n                \"<audio src=\\\"AUDIO_LOCATION/RP1425_ini_04.wav\\\">To hear the plan information again say go back  </audio>\"\n            ],\n            true,\n            [\n                \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\",\n                \"<audio src=\\\"AUDIO_LOCATION/RP1425_ini_05.wav\\\">If you re done, you can simply hang up </audio>\"\n            ]\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: request-extension\n          displayName: request-extension\n        - id: go_back\n          displayName: go_back\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n              true,\n              Global.RP1425_operator_counter > 0,\n              [\n                  \"<audio src=\\\"AUDIO_LOCATION/RP1425_operator_01.wav\\\">I m sorry, I can t transfer you right now</audio>\",\n                  \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\",\n                  \"<audio src=\\\"AUDIO_LOCATION/RP1425_operator_02.wav\\\">To see if you're eligible for a payment extension, say 'extension' or to hear the plan information again say 'go back' </audio>\",\n                  \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\",\n                  \"<audio src=\\\"AUDIO_LOCATION/RP1425_operator_04.wav\\\">If you re done, you can simply hang up</audio>\"\n              ],\n              Global.ratePlans > 1,\n              [\n                  \"<audio src=\\\"AUDIO_LOCATION/RP1425_ini_01.wav\\\">Those are the only plans I can offer you right now </audio>\"\n              ],\n              Global.ratePlans = 1,\n              [\n                  \"<audio src=\\\"AUDIO_LOCATION/RP1425_ini_02.wav\\\">This is the only plan I can offer you right now  </audio>\"\n              ],\n              Global.extensionAllowed = true && Global.heardDataExceededInfo = false,\n              [\n                  \"<audio src=\\\"AUDIO_LOCATION/RP1425_ini_03.wav\\\">To check if you can get a payment extension, say 'extension' or to hear the plan information again say 'go back' </audio>\"\n              ],\n              !(Global.extensionAllowed = true && Global.heardDataExceededInfo = false),\n              [\n                  \"<audio src=\\\"AUDIO_LOCATION/RP1425_ini_04.wav\\\">To hear the plan information again say go back  </audio>\"\n              ],\n              true,\n              [\n                  \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\",\n                  \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\",\n                  \"<audio src=\\\"AUDIO_LOCATION/RP1425_ini_05.wav\\\">If you re done, you can simply hang up </audio>\"\n              ]\n          )}\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.extensionAllowed\n  value: \"GlobalVars.extensionAllowed == undefined ? false : GlobalVars.extensionAllowed\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.ratePlans\n  value: \"GlobalVars.ratePlans.length\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.heardDataExceededInfo\n  value: \"GlobalVars.heardDataExceededInfo\"\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_RP1425_NoOtherPlans_DM\n  value: =Text(Global.RP1425_NoOtherPlans_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_RP1425_NoOtherPlans_DM = \"request-extension\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/RP1425_out_01.wav\\\">Sure !</audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: extension\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.tag\n          value: request-extension\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: getReturnLink()\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_RP1425_NoOtherPlans_DM = \"go_back\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.comingFrom = \"RP1415\", true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: RP1415_ListRatePlans_DM\n\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.comingFrom = \"RP1420\", true, false)\n                      actions:\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: RP1420_ListPlanDetails_DM\n\n                  elseActions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: RP1411_OfferOnePlanYN_DM\n```", "timestamp": 1749529371.2901368, "content_hash": "83aad74d3d85b845fab6288f3cddc03a"}