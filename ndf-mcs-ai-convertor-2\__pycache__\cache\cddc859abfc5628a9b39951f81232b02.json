{"response": "```yaml\n- kind: Question\n  id: NL1105_NLUConfirmTagYN_DM\n  displayName: NL1105_NLUConfirmTagYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.NL1105_NLUConfirmTagYN_DM_reco\n  prompt:\n    speak:\n      - \"{Global.playPromptTTS}\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.playPrompt\n  value: GlobalVars.GetTagData.confirmPrompt\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.playPromptTTS\n  value: GlobalVars.GetTagData.confirmTTS\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.playPrompt = undefined, true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.playPrompt\n          value: \"\"\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.playPromptTTS = undefined, true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.playPromptTTS\n          value: \"\"\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_NL1105_NLUConfirmTagYN_DM\n  value: =Text(Global.NL1105_NLUConfirmTagYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_NL1105_NLUConfirmTagYN_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.hasConfirmed\n          value: true\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: NL1110_NLUPlayYesConfirmationExit_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_NL1105_NLUConfirmTagYN_DM = \"false\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.maxAttempts < 2, true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.maxAttempts\n                      value: maxAttempts+1\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: NL1115_NLUPlayNoConfirmationExit_PP\n\n              elseActions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/NL1105_out_01.wav\\\">My mistake  Let s try this a different way</audio>\"\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.tag\n                  value: backoff-menu\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.ssmScore\n                  value: 100\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: NL1010_LookUpTagData_DB_DA\n```", "timestamp": 1749528841.7338927, "content_hash": "cddc859abfc5628a9b39951f81232b02"}