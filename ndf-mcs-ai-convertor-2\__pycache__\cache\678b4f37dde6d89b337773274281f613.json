{"response": "```yaml\n- kind: Question\n  id: IU1115_AskReopenAccountYN_DM\n  displayName: IU1115_AskReopenAccountYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/IU1115_nm1_01.wav\\\">This account is probably closed Would you like to reopen it? </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/IU1115_nm2_01.wav\\\">To reopen this account, press 1 If you'd like to check the phone number again and call us back, you can just hang up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/IU1115_nm2_01.wav\\\">To reopen this account, press 1 If you'd like to check the phone number again and call us back, you can just hang up</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.IU1115_AskReopenAccountYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/IU1115_ini_01.wav\\\">Sorry, Im still not finding it If youre sure youve got the right phone number, then this account was closed because of an overdue payment </audio><audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio><audio src=\\\"AUDIO_LOCATION/IU1115_ini_02.wav\\\">Would you like to reopen it?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/IU1115_ini_01.wav\\\">Sorry, Im still not finding it If youre sure youve got the right phone number, then this account was closed because of an overdue payment </audio><audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio><audio src=\\\"AUDIO_LOCATION/IU1115_ini_02.wav\\\">Would you like to reopen it?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/IU1115_nm2_01.wav\\\">To reopen this account, press 1 If you'd like to check the phone number again and call us back, you can just hang up</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/IU1115_nm2_01.wav\\\">To reopen this account, press 1 If you'd like to check the phone number again and call us back, you can just hang up</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_IU1115_AskReopenAccountYN_DM\n  value: =Text(Global.IU1115_AskReopenAccountYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_IU1115_AskReopenAccountYN_DM = \"yes\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: IU1040_CallTransfer_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_IU1115_AskReopenAccountYN_DM = \"no\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/IU1115_out_01.wav\\\">Okay You can double check the phone number and call us back You can also manage your account with the myMetro app, or at metro by t dash mobile dot com</audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: IU1035_Goodbye_SD\n```", "timestamp": **********.5736613, "content_hash": "678b4f37dde6d89b337773274281f613"}