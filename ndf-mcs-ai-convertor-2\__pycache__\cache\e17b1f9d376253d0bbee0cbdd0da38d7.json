{"response": "```yaml\n- kind: Question\n  id: bm1310_InsufficientRefill_DM\n  displayName: bm1310_InsufficientRefill_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bm1310_ni1_01.wav\\\">To make another payment, say Credit Card or press 1, Debit Card or press 2, or Refill Card or press 3 If you *don t* want to make another payment to your account, say Skip It or press 4</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/bm1310_ni2_01.wav\\\">To pay with a credit card, press 1 To use a debit card, press 2, or to use a refill card, press 3 If you do *not* want to make another payment now, press 4</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.bm1310_InsufficientRefill_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bm1310_ini_01.wav\\\">To make another payment, say Credit Card, Debit Card, or Refill Card Otherwise, say Skip It</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: credit_card\n          displayName: credit_card\n        - id: debit_card\n          displayName: debit_card\n        - id: refill_pin\n          displayName: refill_pin\n        - id: skip_it\n          displayName: skip_it\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/bm1310_ni1_01.wav\\\">To make another payment, say Credit Card or press 1, Debit Card or press 2, or Refill Card or press 3 If you *don t* want to make another payment to your account, say Skip It or press 4</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/bm1310_ni2_01.wav\\\">To pay with a credit card, press 1 To use a debit card, press 2, or to use a refill card, press 3 If you do *not* want to make another payment now, press 4</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_bm1310_InsufficientRefill_DM\n  value: =Text(Global.bm1310_InsufficientRefill_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_bm1310_InsufficientRefill_DM = \"credit_card\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.refillMethod\n          value: credit\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferDestination\n          value: vesta\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.B02_AddMoney_05.dvxml\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_bm1310_InsufficientRefill_DM = \"debit_card\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.refillMethod\n          value: debit\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferDestination\n          value: vesta\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.B02_AddMoney_05.dvxml\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_bm1310_InsufficientRefill_DM = \"refill_pin\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.refillMethod\n          value: refillPIN\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.firstTimeInBm0410\n          value: true\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.B02_AddMoney_03.dvxml\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_bm1310_InsufficientRefill_DM = \"skip_it\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/bm1310_out_01.wav\\\">Okay</audio>\"\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.callType = \"activations\", true, false)\n              actions:\n                - kind: BeginDialog\n                  id: begin_REPLACE_THIS\n                  dialog: topic.getReturnLink()\n          elseActions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: bm1240_PlayRefillIOM_SD\n```", "timestamp": 1749469992.7153673, "content_hash": "e17b1f9d376253d0bbee0cbdd0da38d7"}