{"response": "```yaml\n- kind: Question\n  id: SQ1030_RecordStreetName_DM\n  displayName: SQ1030_RecordStreetName_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SQ1030_nm1_01.wav\\\">Please SAY the name of the street you grew up on, or press 1 to go back to the elementary school question</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SQ1030_nm2_01.wav\\\">If you don t want to answer this question, press 1 to go back to the elementary school question Otherwise, SAY the name of the street you grew up on now</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SQ1030_nm3_01.wav\\\">If you ever forget your security code, you ll be able to access it by answering this security question If you prefer to answer the previous security question, press 1 After the beep, please say the name the street you grew up on</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.SQ1030_RecordStreetName_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/SQ1030_ini_01.wav\\\">After the beep, go ahead and tell me the name of the street</audio>\"\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/SQ1030_ni1_01.wav\\\">Sorry, I didn t hear you Please say the name of the street you grew up on If you prefer to answer the elementary school question, press 1</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SQ1030_ni2_01.wav\\\">Sorry, please say the name of the street you grew up on To answer the elementary school question press 1</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SQ1030_ni3_01.wav\\\">Sorry, please say the name of the street you grew up on To answer the elementary school question press 1</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.otherOptionSelected\n  value: false\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.recordDuration\n  value: audio$.duration\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.recordTermchar\n  value: audio$.termchar\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.recordMaxtime\n  value: audio$.maxtime\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.recordTermchar = \"1\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/SQ1030_out_01.wav\\\">Ok</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: SQ1000_RecordSchoolName_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: |\n            =If(And(Global.recordTermchar <> null , Global.recordTermchar <> \"\" , Global.recordTermchar <> \"#\"), true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: SQ1030_RecordStreetName_DM\n\n      elseActions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.streetNameFilename\n          value: SQ1030_RecordStreetName_DM.returnvalue\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: SQ1040_SpellStreetName_DM\n```", "timestamp": 1749529502.6196787, "content_hash": "a4b8c7cf0b7e6aecf29d787209bdc410"}