{"response": "```yaml\n- kind: Question\n  id: BC1305_UseSameAsAboveYN_DM\n  displayName: BC1305_UseSameAsAboveYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1305_nm1_01.wav\\\">Is the billing zip code for this card</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1305_nm2_01.wav\\\">If the billing zip code for this card the same as the one you gave me earlier, press 1 To enter a different one, press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1305_nm3_01.wav\\\">Do you receive the bills for this card in the zip code</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1305_nm3_02.wav\\\">If yes, press 1 If not, press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.BC1305_UseSameAsAboveYN_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.FromBC1205 = true && Global.voiceOrDtmf = \"voice\",\n            [\n              \"{Global.bankCardCVV}\",\n              \"test\"\n            ],\n            Global.providedZipCode = false,\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/BC1305_ini_01.wav\\\">Last, is the billing zip code for this card the same zip code you gave me earlier?</audio>\",\n              \"test\",\n              \"<audio src=\\\"AUDIO_LOCATION/BC1305_ini_02.wav\\\">Is it also</audio>\",\n              \"{Global.zipCode}\"\n            ]\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: yes\n          displayName: yes\n        - id: no\n          displayName: no\n        - id: thats_not_right\n          displayName: thats_not_right\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1305_nm1_01.wav\\\">Is the billing zip code for this card</audio>\"\n        - \"{Global.zipCode}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1305_nm2_01.wav\\\">If the billing zip code for this card the same as the one you gave me earlier, press 1 To enter a different one, press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1305_nm3_01.wav\\\">Do you receive the bills for this card in the zip code</audio>\"\n        - \"{Global.zipCode}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1305_nm3_02.wav\\\">If yes, press 1 If not, press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.saidOperator\n  value: \"GlobalVars.saidOperatorBC1305?GlobalVars.saidOperatorBC1305:false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.providedZipCode\n  value: GlobalVars.providedZipCode\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.zipCode\n  value: GlobalVars.callerEnteredZipCode\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.bankCardCVV\n  value: GlobalVars.bankCardCVV\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.voiceOrDtmf\n  value: GlobalVars.VerificationCode_voiceOrDtmf\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.FromBC1205\n  value: GlobalVars.FromBC1205\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_BC1305_UseSameAsAboveYN_DM\n  value: =Text(Global.BC1305_UseSameAsAboveYN_DM_reco)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.saidOperatorBC1305\n  value: false\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.FromBC1205\n  value: undefined\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.MDEinBC1305\n  value: false\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_BC1305_UseSameAsAboveYN_DM = \"yes\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.useSameZipCode\n          value: yes\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.providedZipCode\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.bankCardZip\n          value: GlobalVars.callerEnteredZipCode\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: BC1401_CheckContext_JDA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_BC1305_UseSameAsAboveYN_DM = \"no\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.GlobalVars.disconfirmedDetails = true, true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.useSameZipCode\n                      value: disconfirmed\n\n              elseActions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.useSameZipCode\n                  value: no\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: BC1310_GetBillingZipCode_DM\n\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_BC1305_UseSameAsAboveYN_DM = \"thats_not_right\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.implicitConfirmReject\n              value: true\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.bankCardCVV\n              value: undefined\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: BC1205_GetVerificationCode_DM\n```", "timestamp": **********.115655, "content_hash": "f96f62b5878ec011b349c39bdeb8aa95"}