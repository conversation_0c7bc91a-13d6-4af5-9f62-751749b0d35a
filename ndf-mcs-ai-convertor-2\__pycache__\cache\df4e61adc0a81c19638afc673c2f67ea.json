{"response": "```yaml\n- kind: Question\n  id: DU1305_DataUsageWrapMenu_DM\n  displayName: DU1305_DataUsageWrapMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DU1305_ini_01.wav\\\">What would you like to do next? Say 'add more data', or 'data tips'</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_1000ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DU1305_ini_06.wav\\\">If you're done, you can simply hang up </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DU1305_nm2_06.wav\\\">Say 'add more data' or press 1, or 'data tips' or press 2 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DU1305_nm2_06.wav\\\">Say 'add more data' or press 1, or 'data tips' or press 2 </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.DU1305_DataUsageWrapMenu_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/DU1305_ini_01.wav\\\">What would you like to do next? Say 'add more data', or 'data tips'</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_1000ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DU1305_ini_06.wav\\\">If you're done, you can simply hang up </audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: add-data\n          displayName: add-data\n        - id: datatips\n          displayName: datatips\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/DU1305_ini_01.wav\\\">What would you like to do next? Say 'add more data', or 'data tips'</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_1000ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DU1305_ini_06.wav\\\">If you're done, you can simply hang up </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DU1305_nm2_06.wav\\\">Say 'add more data' or press 1, or 'data tips' or press 2 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DU1305_nm2_06.wav\\\">Say 'add more data' or press 1, or 'data tips' or press 2 </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_DU1305_DataUsageWrapMenu_DM\n  value: =Text(Global.DU1305_DataUsageWrapMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_DU1305_DataUsageWrapMenu_DM = \"add-data\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: add-data\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.tag\n          value: buy-data_topup\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: DU1310_AddData_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_DU1305_DataUsageWrapMenu_DM = \"datatips\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: DU1505_DataTipsInfo_PP\n```", "timestamp": 1749528166.9878592, "content_hash": "df4e61adc0a81c19638afc673c2f67ea"}