{"response": "```yaml\n- kind: Question\n  id: ca0162_AccountRetrieve_DM\n  displayName: ca0162_AccountRetrieve_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ca0162_nm1_01.wav\\\">Do you have the mobile phone associated with this number? say yes or no</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ca0162_nm2_01.wav\\\">If you are able to receive a text now, press 1 If you dont have your phone or do not want to receive a text, press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ca0162_AccountRetrieve_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.matchedANI = true || Global.matchedANI = \"true\",\n            \"I can send you a text link with your account number Would you like to receive that text?\",\n            true,\n            \"I can send you a text link with your account number To receive that text, youll need the phone associated with this number and it must be able to receive text messages Are you able to do that now?\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: send_text\n          displayName: send_text\n        - id: no_text\n          displayName: no_text\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ca0162_ni1_01.wav\\\">I didnt hear that</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ca0162_ni1_02.wav\\\">Do you have the mobile phone associated with this number? say yes or no</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ca0162_ni2_01.wav\\\">If you are able to receive a text now, press 1 If you dont have your phone or do not want to receive a text, press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ca0162_AccountRetrieve_DM\n  value: =Text(Global.ca0162_AccountRetrieve_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ca0162_AccountRetrieve_DM = \"send_text\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ca0163_SendAccountSMSInfo_DB_DA\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ca0162_AccountRetrieve_DM = \"no_text\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ca0171_NeedAccountRep_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ca0162_AccountRetrieve_DM = \"repeat\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ca0162_AccountRetrieve_DM\n```", "timestamp": **********.9159229, "content_hash": "f87594bcf59bbd70cd8cfca60f773b12"}