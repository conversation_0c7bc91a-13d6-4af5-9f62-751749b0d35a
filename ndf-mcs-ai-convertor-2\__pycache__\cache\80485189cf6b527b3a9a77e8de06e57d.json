{"response": "```yaml\n- kind: Question\n  id: da0225_DataOptions_DM\n  displayName: da0225_DataOptions_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I missed that</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/da0225_DataOptions_DM_noinput1\\\">[custom audio: com.nuance.att.application.audio.da0225_DataOptions_DM_noinput1]</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">Sorry</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/da0225_DataOptions_DM_noinput2\\\">[custom audio: com.nuance.att.application.audio.da0225_DataOptions_DM_noinput2]</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.da0225_DataOptions_DM_reco\n  prompt:\n    speak:\n      - \"[custom audio: com.nuance.att.application.audio.da0225_DataOptions_DM_initial]\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: add_data\n          displayName: add_data\n        - id: change_plan\n          displayName: change_plan\n        - id: tech_support\n          displayName: tech_support\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"[custom audio: com.nuance.att.application.audio.da0225_DataOptions_DM_noinput1]\"\n        - \"[custom audio: com.nuance.att.application.audio.da0225_DataOptions_DM_noinput2]\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_da0225_DataOptions_DM\n  value: =Text(Global.da0225_DataOptions_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_da0225_DataOptions_DM = \"add_data\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: addPackages\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.packageDestination\n          value: data\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: da0230_DataHandlingAddData_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_da0225_DataOptions_DM = \"change_plan\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.intent\n              value: changePlan\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: da0235_DataHandlingChangePlan_SD\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_da0225_DataOptions_DM = \"tech_support\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.intent\n                  value: techSupportTroubleshoot\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: da0240_DataHandlingTechSupport_SD\n```", "timestamp": 1749470681.5006428, "content_hash": "80485189cf6b527b3a9a77e8de06e57d"}