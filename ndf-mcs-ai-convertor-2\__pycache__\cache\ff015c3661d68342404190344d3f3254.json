{"response": "```yaml\n- kind: Question\n  id: DA1335_AskContinueYN_DM\n  displayName: DA1335_AskContinueYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - |\n        <audio src=\"AUDIO_LOCATION/gl_nm1_01.wav\">I didn't get that </audio>\n      - |\n        {Switch(\n              true,\n              Global.isDataUnlimited = true,\n              \"Do you want to hear about our monthly data add-ons? \",\n              true,\n              \"Do you want to hear about our other plan options? \"\n          )\n        }\n      - |\n        <audio src=\"AUDIO_LOCATION/gl_nm2_01.wav\">I *still* didn't get that </audio>\n      - |\n        {Switch(\n              true,\n              Global.isDataUnlimited = true,\n              \"To hear about our monthly data add-ons, say 'yes' or press 1 To cancel, say 'no' or press 2 \",\n              true,\n              \"To hear about our otehr rate plans, say 'yes' or press 1 To cancel, say 'no' or press 2 \"\n          )\n        }\n      - |\n        <audio src=\"AUDIO_LOCATION/gl_nm3_01.wav\">Let's try one more time </audio>\n      - |\n        {Switch(\n              true,\n              Global.isDataUnlimited = true,\n              \"To hear about our monthly data add-ons, say 'yes' or press 1 To cancel, say 'no' or press 2 \",\n              true,\n              \"To hear about our otehr rate plans, say 'yes' or press 1 To cancel, say 'no' or press 2 \"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.DA1335_AskContinueYN_DM_reco\n  prompt:\n    speak:\n      - |\n        <audio src=\"AUDIO_LOCATION/DA1335_ini_01.wav\">Do you want to go ahead? </audio>\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.isDataUnlimited = true,\n                \"Do you want to hear about our monthly data add-ons? \",\n                true,\n                \"Do you want to hear about our other plan options? \"\n            )\n          }\n        - |\n          <audio src=\"AUDIO_LOCATION/gl_ni2_01.wav\">I didn't hear you </audio>\n        - |\n          {Switch(\n                true,\n                Global.isDataUnlimited = true,\n                \"To hear about our monthly data add-ons, say 'yes' or press 1 To cancel, say 'no' or press 2 \",\n                true,\n                \"To hear about our otehr rate plans, say 'yes' or press 1 To cancel, say 'no' or press 2 \"\n            )\n          }\n        - |\n          <audio src=\"AUDIO_LOCATION/gl_ni3_01.wav\">I still didn't hear</audio>\n        - |\n          {Switch(\n                true,\n                Global.isDataUnlimited = true,\n                \"To hear about our monthly data add-ons, say 'yes' or press 1 To cancel, say 'no' or press 2 \",\n                true,\n                \"To hear about our otehr rate plans, say 'yes' or press 1 To cancel, say 'no' or press 2 \"\n            )\n          }\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.isDataUnlimited\n  value: \"GlobalVars.GetAccountDetails.isDataUnlimited != undefined ? GlobalVars.GetAccountDetails.isDataUnlimited : false\"\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_DA1335_AskContinueYN_DM\n  value: =Text(Global.DA1335_AskContinueYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_DA1335_AskContinueYN_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/DA1335_out_01.wav\\\">Great! </audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: DA1205_RouteToIntent_JDA_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_DA1335_AskContinueYN_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/DA1335_out_02.wav\\\">No problem </audio>\"\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.callType\n              value: undefined\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n```", "timestamp": 1749558426.9455202, "content_hash": "ff015c3661d68342404190344d3f3254"}