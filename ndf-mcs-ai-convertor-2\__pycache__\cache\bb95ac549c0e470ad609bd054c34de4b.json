{"response": "```yaml\n- kind: Question\n  id: aa3021_WICCBalanceNoProviderWrapUpDTMF_DM\n  displayName: aa3021_WICCBalanceNoProviderWrapUpDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.aa3021_WICCBalanceNoProviderWrapUpDTMF_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa3021_ini_01.wav\\\">To hear that again, press 1 For the main menu, press 2 Or if you re done, feel free to hang up</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: main_menu\n          displayName: main_menu\n\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa3021_WICCBalanceNoProviderWrapUpDTMF_DM\n  value: =Text(Global.aa3021_WICCBalanceNoProviderWrapUpDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa3021_WICCBalanceNoProviderWrapUpDTMF_DM = \"main_menu\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aa3021_out_02.wav\\\">Sure</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: handleMainMenu_CS\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa3021_WICCBalanceNoProviderWrapUpDTMF_DM = \"repeat\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/aa3021_out_01.wav\\\">Again</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa3011_WICCBalance1ChildNoProviders_PP\n```", "timestamp": **********.0587811, "content_hash": "bb95ac549c0e470ad609bd054c34de4b"}