{"response": "```yaml\n- kind: Question\n  id: aa4010_MainMenu_DM\n  displayName: aa4010_MainMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa4010_nm1_07.wav\\\">Sorry, I didn t get that Say</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/aa4010_nm2_02.wav\\\">Sorry, I still didn't get that</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.aa4010_MainMenu_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            // menuOptionIsFirst\n            Global.menuOptionIsFirst = true,\n            \"<audio src=\\\"AUDIO_LOCATION/aa4010_ini_07.wav\\\">Which would you like</audio>\",\n            // menuOptionIsLast\n            Global.menuOptionIsLast = true,\n            \"<audio src=\\\"AUDIO_LOCATION/aa4010_ini_16.wav\\\">or</audio>\",\n            // menuOption == 'child_care'\n            Global.menuOption = \"child_care\",\n            \"<audio src=\\\"AUDIO_LOCATION/aa4010_ini_08.wav\\\">Child care</audio>\",\n            // menuOption == 'transaction_history'\n            Global.menuOption = \"transaction_history\",\n            \"<audio src=\\\"AUDIO_LOCATION/aa4010_ini_09.wav\\\">Transaction History</audio>\",\n            // menuOption == 'benefit_schedule'\n            Global.menuOption = \"benefit_schedule\",\n            \"<audio src=\\\"AUDIO_LOCATION/aa4010_ini_10.wav\\\">Benefit Schedule</audio>\",\n            // menuOption == 'change_pin'\n            Global.menuOption = \"change_pin\",\n            \"<audio src=\\\"AUDIO_LOCATION/aa4010_ini_11.wav\\\">Change Pin</audio>\",\n            // menuOption == 'last_deposits'\n            Global.menuOption = \"last_deposits\",\n            \"<audio src=\\\"AUDIO_LOCATION/aa4010_ini_12.wav\\\">Last Deposits</audio>\",\n            // menuOption == 'missing_card'\n            Global.menuOption = \"missing_card\",\n            \"<audio src=\\\"AUDIO_LOCATION/aa4010_ini_13.wav\\\">Missing, lost, stolen, or damaged card</audio>\",\n            // menuOption == 'freeze_card'\n            Global.menuOption = \"freeze_card\",\n            \"<audio src=\\\"AUDIO_LOCATION/aa4010_ini_01.wav\\\">Freeze card</audio>\",\n            // menuOption == 'unfreeze_card'\n            Global.menuOption = \"unfreeze_card\",\n            \"<audio src=\\\"AUDIO_LOCATION/aa4010_ini_02.wav\\\">Unfreeze card</audio>\",\n            // menuOption == 'assistance'\n            Global.menuOption = \"assistance\",\n            \"<audio src=\\\"AUDIO_LOCATION/aa4010_ini_14.wav\\\">assistance</audio>\",\n            // menuOption == 'something_else'\n            Global.menuOption = \"something_else\",\n            \"<audio src=\\\"AUDIO_LOCATION/aa4010_ini_15.wav\\\">something else</audio>\",\n            // Default\n            \"Please select an option.\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: child_care\n          displayName: child_care\n        - id: transaction_history\n          displayName: transaction_history\n        - id: benefit_schedule\n          displayName: benefit_schedule\n        - id: change_pin\n          displayName: change_pin\n        - id: last_deposits\n          displayName: last_deposits\n        - id: missing_card\n          displayName: missing_card\n        - id: freeze_card\n          displayName: freeze_card\n        - id: unfreeze_card\n          displayName: unfreeze_card\n        - id: assistance\n          displayName: assistance\n        - id: something_else\n          displayName: something_else\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/aa4010_ni1_07.wav\\\">Sorry, I didn t get anything Say</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/aa4010_ni2_02.wav\\\">Sorry, I still didn t get that</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa4010_MainMenu_DM\n  value: =Text(Global.aa4010_MainMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa4010_MainMenu_DM = \"child_care\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.currentTask\n          value: ChildCare\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.nextStep\n          value: child_care\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa4010_MainMenu_DM = \"transaction_history\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.nextStep\n          value: transaction_history\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.currentTask\n          value: TransactionHistory\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa4010_MainMenu_DM = \"benefit_schedule\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.currentTask\n          value: BenefitSchedule\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.nextStep\n          value: benefit_schedule\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa4010_MainMenu_DM = \"change_pin\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.cardInfoVariables.pinChangeOffered\n          value: true\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.currentTask\n          value: ChangePin\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.nextStep\n          value: change_pin\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa4010_MainMenu_DM = \"last_deposits\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.typeOfHistory\n          value: DE\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.currentTask\n          value: LastDeposits\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.nextStep\n          value: last_deposits\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa4010_MainMenu_DM = \"something_else\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.currentTask\n          value: OtherAssistance\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.nextStep\n          value: other_assistance\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa4104_isPersonalization_JDA\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa4010_MainMenu_DM = \"assistance\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.currentTask\n          value: OtherAssistance\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.nextStep\n          value: other_assistance\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa4104_isPersonalization_JDA\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa4010_MainMenu_DM = \"missing_card\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.cardInfoVariables.collectedLostStolenCardNumber\n          value: Global.cardInfoVariables.collectedCardNumber\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.currentTask\n          value: cardReplacement\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.nextStep\n          value: card_replacement\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.cardInfoVariables.tranType\n          value: L\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aa4010_out_01.wav\\\">Alright I can assist in cancelling your missing, lost, stolen, or damaged card short pause  To do that, I will need some additional information</audio>\"\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa4010_MainMenu_DM = \"freeze_card\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.currentTask\n          value: freezeCard\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.freezeUnfreezeOffered\n          value: true\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.nextStep\n          value: freeze_card\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aa4010_out_02.wav\\\">Alright You or anyone else will be unable to complete any transactions while your card is frozen In order to complete any transactions, you will have to unfreeze your card, which you can do at any time I'll assist you in freezing your card now</audio>\"\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa4010_MainMenu_DM = \"unfreeze_card\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.currentTask\n          value: freezeCard\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.freezeUnfreezeOffered\n          value: true\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.nextStep\n          value: freeze_card\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aa4010_out_03.wav\\\">Alright I can assist you in unfreezing your card Once your card is unfrozen, you will be able to complete any transactions</audio>\"\n```", "timestamp": 1749543366.9630246, "content_hash": "614c4c9a472796a677b68b1b7b5818c1"}