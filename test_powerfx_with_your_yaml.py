#!/usr/bin/env python3
"""
Test PowerFX Validator with Your YAML Handling
==============================================

This test script uses your exact YAML reading/writing pattern to test
the PowerFX validator and fixer functionality.
"""

import os
import tempfile
from pathlib import Path
from powerfx_validator_fixer import PowerFXValidatorFixer, setup_logging

def test_with_your_yaml_pattern():
    """Test PowerFX validator using your YAML handling pattern"""
    
    print("PowerFX Validator Test - Using Your YAML Pattern")
    print("=" * 60)
    
    # Create a test YAML file with PowerFX issues
    test_yaml_content = '''
kind: Topic
displayName: PowerFX Test with Your YAML Pattern
triggers:
  - kind: UnknownIntent

actions:
  - kind: Question
    id: question1
    interruptionPolicy:
      allowInterruption: true
    variable: Global.userChoice
    prompt:
      kind: Activity
      activity:
        speak:
          - kind: AdaptiveCardResponse
            cardPayload: |
              Switch(
                Global.npi_type = 'cellphone',
                [
                  'You can make calls immediately.',
                  'Incoming calls start within 2 hours.',
                  'Visit a store if issues persist.'
                ],
                Global.npi_type = 'landline',
                [
                  'Your landline service is ready.',
                  'Test your connection now.'
                ],
                If(Global.isActive = True, Concat('Active', 'Service'), ['Default message'])
              )
  - kind: SetVariable
    id: setVar1
    variable: Global.testVar
    value: "This has \\"escaped quotes\\" in it"
  - kind: ConditionGroup
    displayName: Test Condition Group
    conditions:
      - id: condition1
        condition: =Global.testValue = TRUE
        actions:
          - kind: SetVariable
            id: nestedSetVar
            variable: Global.nestedVar
            value: "Nested \\"quoted\\" value"
'''
    
    with tempfile.TemporaryDirectory() as temp_dir:
        test_dir = Path(temp_dir)
        test_file = test_dir / "test_powerfx_yaml.yml"
        
        # Write test file
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_yaml_content.strip())
        
        print(f"Created test file: {test_file}")
        
        # Setup logger and validator
        logger = setup_logging(verbose=True)
        validator_fixer = PowerFXValidatorFixer(logger)
        
        print(f"\n1. Reading YAML using your standard method...")
        print("-" * 50)
        
        # Test reading YAML
        data, yaml_instance = validator_fixer.read_yaml(str(test_file))
        if data is None:
            print("❌ Failed to read YAML file")
            return False
        
        print(f"✅ Successfully read YAML file")
        print(f"   Kind: {data.get('kind', 'Unknown')}")
        print(f"   Display Name: {data.get('displayName', 'Unknown')}")
        print(f"   Actions count: {len(data.get('actions', []))}")
        
        print(f"\n2. Processing PowerFX expressions...")
        print("-" * 50)
        
        # Process the file
        was_modified = validator_fixer.process_yaml_file(test_file, dry_run=False)
        
        print(f"\n3. Results:")
        print("-" * 50)
        print(f"File was modified: {was_modified}")
        print(f"Issues found: {validator_fixer.stats['issues_found']}")
        print(f"Issues fixed: {validator_fixer.stats['issues_fixed']}")
        print(f"Backup files created: {validator_fixer.stats['backup_files_created']}")
        
        # Verify backup was created
        backup_file = test_file.with_suffix(test_file.suffix + '.backup')
        if backup_file.exists():
            print(f"✅ Backup file created: {backup_file}")
        else:
            print(f"⚠️  No backup file found")
        
        print(f"\n4. Reading modified file...")
        print("-" * 50)
        
        # Read the modified file
        modified_data, modified_yaml = validator_fixer.read_yaml(str(test_file))
        if modified_data is None:
            print("❌ Failed to read modified YAML file")
            return False
        
        print(f"✅ Successfully read modified YAML file")
        
        # Show some of the modified content
        if 'actions' in modified_data and len(modified_data['actions']) > 0:
            first_action = modified_data['actions'][0]
            if 'prompt' in first_action:
                cardPayload = first_action['prompt']['activity']['speak'][0].get('cardPayload', '')
                if cardPayload:
                    print(f"\n5. Modified PowerFX content (first 200 chars):")
                    print("-" * 50)
                    print(f"{cardPayload[:200]}...")
        
        # Show SetVariable processing
        for action in modified_data.get('actions', []):
            if action.get('kind') == 'SetVariable':
                print(f"\n6. SetVariable processing:")
                print("-" * 50)
                print(f"Variable: {action.get('variable', 'Unknown')}")
                print(f"Value: {action.get('value', 'Unknown')}")
        
        # Print final statistics
        validator_fixer.print_final_report()
        
        print(f"\n7. YAML Structure Validation:")
        print("-" * 50)
        print(f"✅ YAML structure preserved")
        print(f"✅ Using your standard YAML configuration")
        print(f"✅ Width: 4096, preserve_quotes: True")
        print(f"✅ Indent: mapping=2, sequence=4, offset=2")
        
        return was_modified

def test_dry_run_mode():
    """Test dry-run mode with your YAML pattern"""
    
    print(f"\n" + "=" * 60)
    print("DRY RUN TEST - Using Your YAML Pattern")
    print("=" * 60)
    
    test_yaml_content = '''
actions:
  - kind: Question
    prompt:
      activity:
        speak:
          - cardPayload: |
              Switch(
                Global.type = 'phone',
                ['Message 1', 'Message 2'],
                If(Global.active = True, Concat('Active', 'User'), 'Inactive')
              )
  - kind: SetVariable
    variable: Global.message
    value: "Test \\"quoted\\" value"
'''
    
    with tempfile.TemporaryDirectory() as temp_dir:
        test_dir = Path(temp_dir)
        test_file = test_dir / "dry_run_test.yml"
        
        # Write test file
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_yaml_content.strip())
        
        # Setup validator
        logger = setup_logging(verbose=False)
        validator_fixer = PowerFXValidatorFixer(logger)
        
        # Test dry run
        print(f"Running dry-run validation...")
        was_modified = validator_fixer.process_yaml_file(test_file, dry_run=True)
        
        # Verify original file is unchanged
        with open(test_file, 'r', encoding='utf-8') as f:
            final_content = f.read()
        
        if final_content.strip() == test_yaml_content.strip():
            print(f"✅ Original file unchanged (dry-run mode)")
        else:
            print(f"❌ ERROR: File was modified in dry-run mode!")
            return False
        
        # Check no backup was created
        backup_file = test_file.with_suffix(test_file.suffix + '.backup')
        if not backup_file.exists():
            print(f"✅ No backup file created in dry-run mode")
        else:
            print(f"⚠️  Backup file created in dry-run mode (unexpected)")
        
        print(f"Dry-run result: File would be modified = {was_modified}")
        return True

if __name__ == "__main__":
    print("Testing PowerFX Validator with Your YAML Handling Pattern")
    print("=" * 70)
    
    try:
        # Test normal processing
        success1 = test_with_your_yaml_pattern()
        
        # Test dry-run mode
        success2 = test_dry_run_mode()
        
        if success1 and success2:
            print(f"\n🎉 ALL TESTS PASSED!")
            print(f"✅ PowerFX Validator works correctly with your YAML pattern")
            print(f"✅ YAML reading/writing follows your standard configuration")
            print(f"✅ Backup creation works as expected")
            print(f"✅ Dry-run mode works correctly")
        else:
            print(f"\n❌ SOME TESTS FAILED")
            return 1
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
