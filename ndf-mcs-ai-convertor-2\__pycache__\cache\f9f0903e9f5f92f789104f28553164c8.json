{"response": "```yaml\n- kind: Question\n  id: cp0113_ResetDefaultPIN_DM\n  displayName: cp0113_ResetDefaultPIN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/cp0113_ni1_01.wav\\\">Do you want to reset your Account PIN now? Just say Yes or No</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/cp0113_ni1_02.wav\\\">Do you want to reset your Account PIN now? Just say Yes or press 1 Or say No or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.cp0113_ResetDefaultPIN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/cp0113_ini_01.wav\\\">It looks like youre still using the default account pin you were given when you activated your phone Ill need to reset your account PIN to continue Do you want to do that now? Please say Yes or No</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/cp0113_ni1_01.wav\\\">Do you want to reset your Account PIN now? Just say Yes or No</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/cp0113_ni1_02.wav\\\">Do you want to reset your Account PIN now? Just say Yes or press 1 Or say No or press 2</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_commandgrammar\n  value: 'GlobalCommands.grxml?SWI_vars.allow=mainmenu&SWI_vars.disallow=operator'\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_dtmfcommandgrammar\n  value: 'GlobalCommands_dtmf.grxml?SWI_vars.allow=mainmenu&SWI_vars.disallow=operator'\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_cp0113_ResetDefaultPIN_DM\n  value: =Text(Global.cp0113_ResetDefaultPIN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_cp0113_ResetDefaultPIN_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/cp0113_out_02.wav\\\">Okay</audio>\"\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.callType = 611, true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.collection_commandgrammar\n                  value: 'GlobalCommands.grxml?SWI_vars.allow=mainmenu'\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.collection_dtmfcommandgrammar\n                  value: 'GlobalCommands_dtmf.grxml?SWI_vars.allow=mainmenu'\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.collection_commandgrammar\n              value: 'GlobalCommands.grxml'\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.collection_dtmfcommandgrammar\n              value: 'GlobalCommands_dtmf.grxml'\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: cp0120_AskAccountZipCode_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_cp0113_ResetDefaultPIN_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/cp0113_out_01.wav\\\">You can also reset your Account PIN online at a t t dot com slash my prepaid</audio>\"\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.attemptedDefaultPINChange\n              value: true\n\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.callType = 611, true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.collection_commandgrammar\n                      value: 'GlobalCommands.grxml?SWI_vars.allow=mainmenu'\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.collection_dtmfcommandgrammar\n                      value: 'GlobalCommands_dtmf.grxml?SWI_vars.allow=mainmenu'\n              elseActions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.collection_commandgrammar\n                  value: 'GlobalCommands.grxml'\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.collection_dtmfcommandgrammar\n                  value: 'GlobalCommands_dtmf.grxml'\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.loginStatus\n              value: account\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.intent\n              value: \"\"\n\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.C05_ResetAccountPIN_02.dvxml#cp0340_NewPINWrap_DM\n```", "timestamp": **********.3050723, "content_hash": "f9f0903e9f5f92f789104f28553164c8"}