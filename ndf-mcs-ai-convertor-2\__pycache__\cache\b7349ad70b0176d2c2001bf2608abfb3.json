{"response": "```yaml\n- kind: Question\n  id: pa0220_AskRenewCategory_DM\n  displayName: pa0220_AskRenewCategory_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/com.nuance.att.application.audio.pa0220_AskRenewCategory_DM_noinput_1\\\"> </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/com.nuance.att.application.audio.pa0220_AskRenewCategory_DM_noinput_2\\\"> </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.pa0220_AskRenewCategory_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/com.nuance.att.application.audio.pa0220_AskRenewCategory_initial\\\"> </audio>\"\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/com.nuance.att.application.audio.pa0220_AskRenewCategory_DM_noinput_1\\\"> </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/com.nuance.att.application.audio.pa0220_AskRenewCategory_DM_noinput_2\\\"> </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.pageNo\n  value: 2\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.returnCode\n  value: GetAutoRenewPackageList.returnCode\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.returnCode = 0, true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_dtmfgrammar1\n          value: =Text('pa0220_AskRenewCategory_DM_dtmf.jsp' + GetAutoRenewPackageList.autoRenewPackageString)\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_grammar1\n          value: =Text('pa0220_AskRenewCategory_DM.jsp' + GetAutoRenewPackageList.autoRenewPackageString)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.packageAutoRenewCategory\n  value: pa0220_AskRenewCategory_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.choseOtherPackages\n  value: false\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: pa0225_NumRenewOptions_JDA\n```", "timestamp": 1749471781.3316474, "content_hash": "b7349ad70b0176d2c2001bf2608abfb3"}