{"response": "```yaml\n- kind: Question\n  id: TF1311_CollectTempCode_DM\n  displayName: TF1311_CollectTempCode_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TF1311_ini_01.wav\\\">What's the 6-digit code? </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TF1311_nm2_01.wav\\\">What's the 6-digit code you received from Customer Care? Say it or enter it on your keypad Or say 'I didn't get it' or press 1 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TF1311_nm2_01.wav\\\">What's the 6-digit code you received from Customer Care? Say it or enter it on your keypad Or say 'I didn't get it' or press 1 </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.TF1311_CollectTempCode_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/TF1311_ini_01.wav\\\">What's the 6-digit code? </audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: didnt_get\n          displayName: didnt_get\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/TF1311_ini_01.wav\\\">What's the 6-digit code? </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/TF1311_nm2_01.wav\\\">What's the 6-digit code you received from Customer Care? Say it or enter it on your keypad Or say 'I didn't get it' or press 1 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/TF1311_nm2_01.wav\\\">What's the 6-digit code you received from Customer Care? Say it or enter it on your keypad Or say 'I didn't get it' or press 1 </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_TF1311_CollectTempCode_DM\n  value: =Text(Global.TF1311_CollectTempCode_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TF1311_CollectTempCode_DM = \"didnt_get\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/TF1311_out_01.wav\\\">No worries Let me get you to someone who can help</audio>\"\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.twoFactorAuthOutcome\n          value: code_timeout\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_AuthStatus\n          value: getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'tfa_failed_smstimeout')\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.playTransferMessage\n          value: false\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.playTransferPrompt\n          value: false\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TF1525_GoToTransfer_SD\n\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.tempCode\n      value: TF1311_CollectTempCode_DM.returnvalue\n\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.GlobalVars.temporaryCodeORPin\n      value: TF1311_CollectTempCode_DM.returnvalue\n\n    - kind: GotoAction\n      id: goto_REPLACE_THIS\n      actionId: TF1405_ValidateTempCode_DB_DA\n```", "timestamp": 1749529916.7564635, "content_hash": "e27ffd7ced282e643a6ffecc1c126542"}