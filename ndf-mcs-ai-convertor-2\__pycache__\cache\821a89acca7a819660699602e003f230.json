{"response": "```yaml\n- kind: Question\n  id: NL1035_BackOffMenu_DM\n  displayName: NL1035_BackOffMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/NL1035_ini_01.wav\\\">Main menu Say 'Balance and payments' or press 1 'Plans and services' or press 2 'Activations'  3 'Payment Extension' 4, 'Data options, 5 Or tell me what you need in your own words</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/NL1035_ini_01.wav\\\">Main menu Say 'Balance and payments' or press 1 'Plans and services' or press 2 'Activations'  3 'Payment Extension' 4, 'Data options, 5 Or tell me what you need in your own words</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.NL1035_BackOffMenu_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/NL1035_ini_01.wav\\\">Main menu Say 'Balance and payments' or press 1 'Plans and services' or press 2 'Activations'  3 'Payment Extension' 4, 'Data options, 5 Or tell me what you need in your own words</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: request-extension_BO\n          displayName: request-extension_BO\n        - id: bal-payments_BO\n          displayName: bal-payments_BO\n        - id: plans-services_BO\n          displayName: plans-services_BO\n        - id: activations_BO\n          displayName: activations_BO\n        - id: data-options_BO\n          displayName: data-options_BO\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/NL1035_ini_01.wav\\\">Main menu Say 'Balance and payments' or press 1 'Plans and services' or press 2 'Activations'  3 'Payment Extension' 4, 'Data options, 5 Or tell me what you need in your own words</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/NL1035_ini_01.wav\\\">Main menu Say 'Balance and payments' or press 1 'Plans and services' or press 2 'Activations'  3 'Payment Extension' 4, 'Data options, 5 Or tell me what you need in your own words</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_NL1035_BackOffMenu_DM\n  value: =Text(Global.NL1035_BackOffMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_NL1035_BackOffMenu_DM = \"request-extension_BO\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.backOffMenuResult\n          value: RequestExtension\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: NL1250_GoToMainMenu_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_NL1035_BackOffMenu_DM = \"bal-payments_BO\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.backOffMenuResult\n              value: BalAndPayments\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: NL1250_GoToMainMenu_SD\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_NL1035_BackOffMenu_DM = \"plans-services_BO\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.backOffMenuResult\n                  value: PlansAndServices\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: NL1250_GoToMainMenu_SD\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_NL1035_BackOffMenu_DM = \"activations_BO\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.backOffMenuResult\n                      value: Activations\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: NL1250_GoToMainMenu_SD\n\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.Var_NL1035_BackOffMenu_DM = \"data-options_BO\", true, false)\n                      actions:\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.GlobalVars.backOffMenuResult\n                          value: DataOptions\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: NL1250_GoToMainMenu_SD\n\n                  elseActions:\n                    - kind: ConditionGroup\n                      id: conditionGroup_REPLACE_THIS\n                      conditions:\n                        - id: conditionItem_REPLACE_THIS\n                          condition: =If(Global.Var_NL1035_BackOffMenu_DM = \"default\", true, false)\n                          actions:\n                            - kind: SetVariable\n                              id: setVariable_REPLACE_THIS\n                              variable: Global.GlobalVars.hasConfirmed\n                              value: false\n                            - kind: SetVariable\n                              id: setVariable_REPLACE_THIS\n                              variable: Global.GlobalVars.tag\n                              value: NL1035_BackOffMenu_DM.returnvalue\n                            - kind: ConditionGroup\n                              id: conditionGroup_REPLACE_THIS\n                              conditions:\n                                - id: conditionItem_REPLACE_THIS\n                                  condition: =If(Global.NL1035_BackOffMenu_DM.inputmode = \"voice\", true, false)\n                                  actions:\n                                    - kind: SetVariable\n                                      id: setVariable_REPLACE_THIS\n                                      variable: Global.GlobalVars.ssmScore\n                                      value: NL1035_BackOffMenu_DM.returnkeys.ssm_score\n                              elseActions:\n                                - kind: SetVariable\n                                  id: setVariable_REPLACE_THIS\n                                  variable: Global.GlobalVars.ssmScore\n                                  value: 100\n                            - kind: GotoAction\n                              id: goto_REPLACE_THIS\n                              actionId: NL1010_LookUpTagData_DB_DA\n```", "timestamp": 1749528809.4644413, "content_hash": "821a89acca7a819660699602e003f230"}