{"response": "```yaml\n- kind: Question\n  id: SE2145_WhatToChangeMix1_DM\n  displayName: SE2145_WhatToChangeMix1_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SE2145_nm1_01.wav\\\">You can say 'allow sim changes' or 'restrict mobile number transfers'</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SE2145_nm2_01.wav\\\">Please say 'allow sim changes' or press 1 'restrict mobile number transfers' or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SE2145_nm2_01.wav\\\">Please say 'allow sim changes' or press 1 'restrict mobile number transfers' or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.SE2145_WhatToChangeMix1_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/SE2145_ini_01.wav\\\">Your choices are 'allow sim changes' or 'restrict mobile number transfers' Which would you like?</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: allow_sim_changes\n          displayName: allow_sim_changes\n        - id: block_port_outs\n          displayName: block_port_outs\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/SE2145_nm1_01.wav\\\">You can say 'allow sim changes' or 'restrict mobile number transfers'</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SE2145_nm2_01.wav\\\">Please say 'allow sim changes' or press 1 'restrict mobile number transfers' or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SE2145_nm2_01.wav\\\">Please say 'allow sim changes' or press 1 'restrict mobile number transfers' or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.unblockingNotAllowed\n  value: \"GlobalVars.unblockingNotAllowed != undefined ? GlobalVars.unblockingNotAllowed : false\"\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_SE2145_WhatToChangeMix1_DM\n  value: =Text(Global.SE2145_WhatToChangeMix1_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_SE2145_WhatToChangeMix1_DM = \"allow_sim_changes\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.tag\n          value: unlock-sim\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.activityCode\n          value: BSS\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.blockingInd\n          value: N\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: SE2156_PlayUnblockInfo_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_SE2145_WhatToChangeMix1_DM = \"block_port_outs\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.activityCode\n              value: BPO\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.blockingInd\n              value: Y\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: SE2155_PlayUpdateTransition_PP\n```", "timestamp": 1749529491.114411, "content_hash": "93a932a1e1a59fc92faa6e2500ffecce"}