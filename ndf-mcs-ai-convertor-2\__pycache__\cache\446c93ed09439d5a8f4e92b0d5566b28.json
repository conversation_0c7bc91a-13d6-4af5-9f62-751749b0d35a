{"response": "```yaml\n- kind: Question\n  id: aa7015_BenefitScheduleWrapUp_DTMF_DM\n  displayName: aa7015_BenefitScheduleWrapUp_DTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa7015_ini_01.wav\\\">To hear that again, Press 1 To go back to the main menu, press 2 And if you re done feel free to just hang up</audio>\"\n      - |\n        {Switch(\n              true,\n              Global.dnisInfo.dtmfOnlyFlag = true,\n              \"Sorry, I still didn t get that\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.aa7015_BenefitScheduleWrapUp_DTMF_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa7015_ini_01.wav\\\">To hear that again, Press 1 To go back to the main menu, press 2 And if you re done feel free to just hang up</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: repeat\n          displayName: repeat\n        - id: main_menu\n          displayName: main_menu\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa7015_BenefitScheduleWrapUp_DTMF_DM\n  value: =Text(Global.aa7015_BenefitScheduleWrapUp_DTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa7015_BenefitScheduleWrapUp_DTMF_DM = \"repeat\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.benefitVariables.cameFrom7005 = true, true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: aa7005_BenefitSchedule_DB_DA\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.benefitVariables.benefitsScheduleCounter\n              value: 0\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa7010_BenefitSchedulePlayout_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa7015_BenefitScheduleWrapUp_DTMF_DM = \"main_menu\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.nextStep\n              value: main_menu\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa1015_NextStepHandling_JDA\n```", "timestamp": 1749543791.5632582, "content_hash": "446c93ed09439d5a8f4e92b0d5566b28"}