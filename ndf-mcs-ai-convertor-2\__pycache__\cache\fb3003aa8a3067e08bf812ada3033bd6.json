{"response": "```yaml\n- kind: Question\n  id: BR1002_ConfirmTransferYN_DM\n  displayName: BR1002_ConfirmTransferYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BR1002_nm1_01.wav\\\">To change your due date, our agents will need to charge you a fee of </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BR1002_nm1_02.wav\\\">Your payment will be due right away Should I take you to someone now?</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.BR1002_ConfirmTransferYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/BR1002_ini_01.wav\\\">To do that, I'll transfer you to an agent They'll ask you to pick a new monthly due date, and they'll explain how this will affect your amount due They'll also charge you a one-time fee of </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BR1002_ini_03.wav\\\">You'll need to make the full payment right away to avoid your service getting suspended</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BR1002_ini_02.wav\\\">Do you want to go ahead and talk to someone? </audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/BR1002_nm1_01.wav\\\">To change your due date, our agents will need to charge you a fee of </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BR1002_nm1_02.wav\\\">Your payment will be due right away Should I take you to someone now?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BR1002_nm2_01.wav\\\">Sorry, I *still* didn't get that</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BR1002_nm2_01.wav\\\">Sorry, I *still* didn't get that</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.audioMessageKey\n  value: care_bcr_charge_audio\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_BR1002_ConfirmTransferYN_DM\n  value: =Text(Global.BR1002_ConfirmTransferYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_BR1002_ConfirmTransferYN_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/BR1002_out_01.wav\\\">Great!</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: BR1035_CallTransfer_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_BR1002_ConfirmTransferYN_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/BR1002_out_02.wav\\\">No problem </audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: BR1030_RepeatCurrentDueDate_PP\n```", "timestamp": 1749527778.8776977, "content_hash": "fb3003aa8a3067e08bf812ada3033bd6"}