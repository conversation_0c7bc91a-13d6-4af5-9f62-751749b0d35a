{"response": "```yaml\n- kind: Question\n  id: VS1015_FeatureMenu_DM\n  displayName: VS1015_FeatureMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/VS1015_ini_01.wav\\\">Which feature would you like Just say its name back to me</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/VS1015_ini_08.wav\\\">You can say 'repeat that' or 'hear more features' </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/VS1015_ini_05.wav\\\">You can say 'repeat that', 'hear more features', or to hear features you currently have on your plan, say 'plan details' </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/VS1015_ini_01.wav\\\">Which feature would you like Just say its name back to me</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/VS1015_ini_08.wav\\\">You can say 'repeat that' or 'hear more features' </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/VS1015_ini_05.wav\\\">You can say 'repeat that', 'hear more features', or to hear features you currently have on your plan, say 'plan details' </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/VS1015_ini_01.wav\\\">Which feature would you like Just say its name back to me</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/VS1015_ini_08.wav\\\">You can say 'repeat that' or 'hear more features' </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/VS1015_ini_05.wav\\\">You can say 'repeat that', 'hear more features', or to hear features you currently have on your plan, say 'plan details' </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.VS1015_FeatureMenu_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/VS1015_ini_01.wav\\\">Which feature would you like Just say its name back to me</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/VS1015_ini_08.wav\\\">You can say 'repeat that' or 'hear more features' </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/VS1015_ini_05.wav\\\">You can say 'repeat that', 'hear more features', or to hear features you currently have on your plan, say 'plan details' </audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: signedup_services\n          displayName: signedup_services\n        - id: more_features\n          displayName: more_features\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/VS1015_ini_01.wav\\\">Which feature would you like Just say its name back to me</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/VS1015_ini_08.wav\\\">You can say 'repeat that' or 'hear more features' </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/VS1015_ini_05.wav\\\">You can say 'repeat that', 'hear more features', or to hear features you currently have on your plan, say 'plan details' </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/VS1015_ini_01.wav\\\">Which feature would you like Just say its name back to me</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/VS1015_ini_08.wav\\\">You can say 'repeat that' or 'hear more features' </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/VS1015_ini_05.wav\\\">You can say 'repeat that', 'hear more features', or to hear features you currently have on your plan, say 'plan details' </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/VS1015_ini_01.wav\\\">Which feature would you like Just say its name back to me</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/VS1015_ini_08.wav\\\">You can say 'repeat that' or 'hear more features' </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/VS1015_ini_05.wav\\\">You can say 'repeat that', 'hear more features', or to hear features you currently have on your plan, say 'plan details' </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.featureGrammarURL\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.featureSocs\n  value: GlobalVars.GetAvailableFeatureOffers.featureSocs.toString()\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.featureSocsThirdParty\n  value: GlobalVars.GetAvailableFeatureOffers.featureSocsThirdParty.toString()\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.allowedResponses\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.allowedResponsesDTMF\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.ifFromCurrentFeatures\n  value: GlobalVars.ifFromCurrentFeatures\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.featureGrammarURL\n  value: GlobalVars.FeatureGrammarURL\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_VS1015_FeatureMenu_DM\n  value: =Text(Global.VS1015_FeatureMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_VS1015_FeatureMenu_DM = \"signedup_services\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: VS1085_GoToCurrentFeatures_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_VS1015_FeatureMenu_DM = \"more_features\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If((GlobalVars.GetAvailableFeatureOffers.featureSocs.length + GlobalVars.GetAvailableFeatureOffers.featureSocsThirdParty.length) > 8, true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: VS1017_AdditionalFeatureMenu_DM\n\n              elseActions:\n                - kind: BeginDialog\n                  id: begin_REPLACE_THIS\n                  dialog: topic.Voicestore_Routing.dvxml#VS1315_GoToCallTransfer_SD\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_VS1015_FeatureMenu_DM = \"default\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.selectedFeature\n                  value: VS1015_FeatureMenu_DM.returnvalue.toLowerCase()\n\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/VS1015_out_01.wav\\\">Okay</audio>\"\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: VS1025_SignUp_DM\n```", "timestamp": **********.4429338, "content_hash": "e0d72d625b93ffbb56af4ea1ef0208d3"}