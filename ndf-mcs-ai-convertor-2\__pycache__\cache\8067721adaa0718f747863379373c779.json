{"response": "```yaml\n- kind: Question\n  id: aa1042_GetCardNumberLastFour_DM\n  displayName: aa1042_GetCardNumberLastFour_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.dnisInfo.cardReplacementFlag = true && Global.globalVariables.fourDigitEntryCounter = 0,\n              \"For security purposes we do need to validate all callers as card holders So please say or enter the last 4 digits of your card number If your card is missing, lost, stolen, or damaged, say, My card is missing or press 2\",\n          \n              true,\n              \"For security purposes we do need to validate all callers as card holders So please say or enter the last 4 digits of your card number If your card is missing, lost, stolen, or damaged, say My card is missing or press 2\"\n          )\n        }\n      - \"<audio src=\\\"AUDIO_LOCATION/aa1042_nm1_04.wav\\\">…Or to access your account another way say access my account or press 3</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/aa1042_nm2_01.wav\\\">Sorry, I didn t get that</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.aa1042_GetCardNumberLastFour_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            // Main prompt\n            true,\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/aa1042_ini_01.wav\\\">Please say or enter the last 4 digits of your</audio>\",\n              \"{Switch(true, Global.dnisInfo.terminologyEbtCard <> null && Global.dnisInfo.terminologyEbtCard <> \\\"\\\", Global.dnisInfo.terminologyEbtCard)}\",\n              \"<audio src=\\\"AUDIO_LOCATION/aa1042_ini_03.wav\\\">card number</audio>\",\n              \"{Switch(true, Global.dnisInfo.cardReplacementFlag = true && Global.globalVariables.fourDigitEntryCounter = 0, \\\"<audio src=\\\\\\\"AUDIO_LOCATION/aa1042_ini_04.wav\\\\\\\">If your card is missing, lost, stolen, or damaged, say,  My card is missing  or press 2</audio>\\\")}\",\n              \"{Switch(true, Global.dnisInfo.altAuthValFlag = true, \\\"<audio src=\\\\\\\"AUDIO_LOCATION/aa1042_ini_05.wav\\\\\\\">…Or to access your account another way say access my account or press 3</audio>\\\")}\"\n            ]\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: default\n          displayName: default\n        - id: missing_card\n          displayName: missing_card\n        - id: access_account\n          displayName: access_account\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.dnisInfo.cardReplacementFlag = true && Global.globalVariables.fourDigitEntryCounter = 0,\n                \"For security purposes we do need to validate all callers as card holders So please say or enter the last 4 digits of your card number If your card is missing, lost, stolen, or damaged, say, My card is missing or press 2\",\n            \n                true,\n                \"For security purposes we do need to validate all callers as card holders So please say or enter the last 4 digits of your card number If your card is missing, lost, stolen, or damaged, say, My card is missing or press 2\"\n            )\n          }\n        - \"<audio src=\\\"AUDIO_LOCATION/aa1042_ni1_04.wav\\\">…Or to access your account another way say access my account or press 3</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/aa1042_ni2_01.wav\\\">Sorry, I still didn t get anything</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa1042_GetCardNumberLastFour_DM\n  value: =Text(Global.aa1042_GetCardNumberLastFour_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa1042_GetCardNumberLastFour_DM = \"default\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.cardInfoVariables.collectedCardNumLastFour\n          value: aa1042_GetCardNumberLastFour_DM.returnvalue\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.cardInfoVariables.collectedCardNumber\n          value: aa1042_GetCardNumberLastFour_DM.returnvalue\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa1084_CheckCardsForMatch_DB_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa1042_GetCardNumberLastFour_DM = \"missing_card\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.dnisInfo.cardReplacementFlag = true, true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.globalVariables.currentTask\n                      value: cardReplacement\n\n                    - kind: ConditionGroup\n                      id: conditionGroup_REPLACE_THIS\n                      conditions:\n                        - id: conditionItem_REPLACE_THIS\n                          condition: =If(Global.dnisInfo.collectLostStolenCardNumber = true, true, false)\n                          actions:\n                            - kind: GotoAction\n                              id: goto_REPLACE_THIS\n                              actionId: aa1051_CollectLostStolenLastFour_DM\n\n                      elseActions:\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: aa1054_LostStolenNeedAdditionalInfo_PP\n\n              elseActions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: aa1055_ConfirmCardMissingDTMF_DM\n\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa1042_GetCardNumberLastFour_DM = \"access_account\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa1057_AltAuthenticationNeedAdditionalInfo_PP\n```", "timestamp": **********.709707, "content_hash": "8067721adaa0718f747863379373c779"}