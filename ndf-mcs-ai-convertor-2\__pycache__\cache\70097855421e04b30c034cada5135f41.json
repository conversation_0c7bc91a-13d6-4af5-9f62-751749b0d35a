{"response": "```yaml\n- kind: Question\n  id: aa4116_BenefitsAccessWrapUp_DM\n  displayName: aa4116_BenefitsAccessWrapUp_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.aa4116_BenefitsAccessWrapUp_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            // Main repeat option\n            true,\n            [\n              \"If you would like to hear that again, say Repeat That\",\n              {Switch(\n                true,\n                Global.dnisInfo.benefitAccessCardProblemOption = true,\n                \"If you're experiencing a problem with your card, say Card Problem\",\n                \"\"\n              )},\n              \"To go to the main menu, say Main Menu And if you're done feel free to hang up\"\n            ]\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: main_menu\n          displayName: main_menu\n        - id: repeat\n          displayName: repeat\n        - id: card_problem\n          displayName: card_problem\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa4116_BenefitsAccessWrapUp_DM\n  value: =Text(Global.aa4116_BenefitsAccessWrapUp_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa4116_BenefitsAccessWrapUp_DM = \"main_menu\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa4004_GetMainMenuOptions_DB_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa4116_BenefitsAccessWrapUp_DM = \"repeat\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/aa4116_out_01.wav\\\">Again</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa4110_BenefitsAccessPlayout_PP\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_aa4116_BenefitsAccessWrapUp_DM = \"card_problem\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.globalVariables.transferAllowed\n                  value: true\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.globalVariables.transferReason\n                  value: cardProblem\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: aa9805_ProcessTransfer_PP\n```", "timestamp": 1749543791.627936, "content_hash": "70097855421e04b30c034cada5135f41"}