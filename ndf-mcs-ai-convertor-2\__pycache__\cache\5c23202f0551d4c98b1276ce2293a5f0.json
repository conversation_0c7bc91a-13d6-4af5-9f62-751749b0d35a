{"response": "```yaml\n- kind: Question\n  id: VS1145_AddFeaturePostamble_DM\n  displayName: VS1145_AddFeaturePostamble_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/VS1145_nm1_01.wav\\\">Just say yes or no Would you like to hear that information again?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/VS1145_nm1_02.wav\\\">You can also say make a payment</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/VS1145_nm2_01.wav\\\">To hear about your new feature, say yes or press 1 If youre finished, say no, or press 2 To pay for your new feature now, say make a payment or press 3</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/VS1145_nm2_01.wav\\\">To hear about your new feature, say yes or press 1 If youre finished, say no, or press 2 To pay for your new feature now, say make a payment or press 3</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.VS1145_AddFeaturePostamble_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/VS1145_ini_01.wav\\\">To hear that again, say repeat that To pay for it now, say pay now</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: no\n          displayName: no\n        - id: yes\n          displayName: yes\n        - id: main_menu\n          displayName: main_menu\n        - id: make_payment\n          displayName: make_payment\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/VS1145_nm1_01.wav\\\">Just say yes or no Would you like to hear that information again?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/VS1145_nm1_02.wav\\\">You can also say make a payment</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/VS1145_nm2_01.wav\\\">To hear about your new feature, say yes or press 1 If youre finished, say no, or press 2 To pay for your new feature now, say make a payment or press 3</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/VS1145_nm2_01.wav\\\">To hear about your new feature, say yes or press 1 If youre finished, say no, or press 2 To pay for your new feature now, say make a payment or press 3</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_VS1145_AddFeaturePostamble_DM\n  value: =Text(Global.VS1145_AddFeaturePostamble_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_VS1145_AddFeaturePostamble_DM = \"no\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/VS1145_out_01.wav\\\">Alright</audio>\"\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.Voicestore_Main.dvxml#VS1045_AnotherFeature_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_VS1145_AddFeaturePostamble_DM = \"yes\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: VS1135_PlaySuccessSignup_PP\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_VS1145_AddFeaturePostamble_DM = \"main_menu\", true, false)\n      actions:\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.Voicestore_Main.dvxml#VS1045_AnotherFeature_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_VS1145_AddFeaturePostamble_DM = \"make_payment\", true, false)\n      actions:\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.Voicestore_Routing.dvxml#VS1330_GoToMakePayments_SD\n```", "timestamp": **********.7910674, "content_hash": "5c23202f0551d4c98b1276ce2293a5f0"}