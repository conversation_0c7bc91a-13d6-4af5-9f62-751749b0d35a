{"response": "```yaml\n- kind: Question\n  id: NL1240_PrepaidPinPreCheck_DM\n  displayName: NL1240_PrepaidPinPreCheck_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/NL1240_ini_01.wav\\\">Have you already purchased a prepaid PIN?'</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/NL1240_nm2_01.wav\\\">If you have already purchased  a prepaid pin and would like to make a payment press 1  Otherwise for general prepaid pin information press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/NL1240_nm2_01.wav\\\">If you have already purchased  a prepaid pin and would like to make a payment press 1  Otherwise for general prepaid pin information press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.NL1240_PrepaidPinPreCheck_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/NL1240_ini_01.wav\\\">Have you already purchased a prepaid PIN?'</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/NL1240_ini_01.wav\\\">Have you already purchased a prepaid PIN?'</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/NL1240_nm2_01.wav\\\">If you have already purchased  a prepaid pin and would like to make a payment press 1  Otherwise for general prepaid pin information press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/NL1240_nm2_01.wav\\\">If you have already purchased  a prepaid pin and would like to make a payment press 1  Otherwise for general prepaid pin information press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_NL1240_PrepaidPinPreCheck_DM\n  value: =Text(Global.NL1240_PrepaidPinPreCheck_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_NL1240_PrepaidPinPreCheck_DM = \"true\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.GlobalVars.GetBCSParameters.payments_enable_prepaid_methods = true || Global.GlobalVars.GetBCSParameters.payments_enable_prepaid_methods = \"true\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.tag\n                  value: make-payment\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.callType\n                  value: make_pmt\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.payingWithPrepaid\n                  value: true\n\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/NL1240_out_01.wav\\\">Ok, I'll take you to the payment flow</audio>\"\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: getReturnLink()\n\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.tag\n              value: inquire-prepaid_pin\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: NL1245_PrepaidInfo_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_NL1240_PrepaidPinPreCheck_DM = \"false\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: NL1245_PrepaidInfo_DM\n```", "timestamp": 1749528905.4523766, "content_hash": "58c30a683b8ceb710ccbcaf41d602295"}