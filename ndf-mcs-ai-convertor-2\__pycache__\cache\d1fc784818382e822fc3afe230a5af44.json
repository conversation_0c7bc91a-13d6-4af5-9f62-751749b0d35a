{"response": "```yaml\n- kind: Question\n  id: NP0035_PortInfoDetails_DM\n  displayName: NP0035_PortInfoDetails_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/NP0035_nm1_01.wav\\\">To hear those details again, say 'repeat' If you're ready to give me all the information, say 'continue' If you'd like me to wait for you to find it, say 'wait a minute' Or if you want to go on with your activation without porting your old number, say 'skip it'</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/NP0035_nm2_01.wav\\\">Please say 'repeat' or press 7 If you're ready to give me that information, say 'continue' or press 1 You can also say 'wait a minute' or press 2 Or to go on without porting your old number, say 'skip it' or press 3</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/NP0035_nm2_01.wav\\\">Please say 'repeat' or press 7 If you're ready to give me that information, say 'continue' or press 1 You can also say 'wait a minute' or press 2 Or to go on without porting your old number, say 'skip it' or press 3</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.NP0035_PortInfoDetails_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/NP0035_ini_01.wav\\\">To port your number, we need to contact your old service provider, and give them the account number, security PIN and zip code they have on file for you Now, your account number can usually be found on a recent bill, but if you don t know your PIN, you might need to contact your old service provider for it Then, you can either call us back, or stop by a Metro store or authorized dealer, and we ll get you set up If you have that info ready, say  continue  pause You can also say  wait a minute ,  repeat that  or to go on without porting your old number, say  skip it </audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: continue\n          displayName: continue\n        - id: skip\n          displayName: skip\n        - id: wait\n          displayName: wait\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/NP0035_nm1_01.wav\\\">To hear those details again, say 'repeat' If you're ready to give me all the information, say 'continue' If you'd like me to wait for you to find it, say 'wait a minute' Or if you want to go on with your activation without porting your old number, say 'skip it'</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/NP0035_nm2_01.wav\\\">Please say 'repeat' or press 7 If you're ready to give me that information, say 'continue' or press 1 You can also say 'wait a minute' or press 2 Or to go on without porting your old number, say 'skip it' or press 3</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/NP0035_nm2_01.wav\\\">Please say 'repeat' or press 7 If you're ready to give me that information, say 'continue' or press 1 You can also say 'wait a minute' or press 2 Or to go on without porting your old number, say 'skip it' or press 3</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_NP0035_PortInfoDetails_DM\n  value: =Text(Global.NP0035_PortInfoDetails_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_NP0035_PortInfoDetails_DM = \"continue\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/NP0035_out_02.wav\\\">Great</audio>\"\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.NumberPortIn_3_Dialog\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_NP0035_PortInfoDetails_DM = \"skip\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.npi_flag\n              value: false\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/NP0035_out_01.wav\\\">No problem</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: return\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_NP0035_PortInfoDetails_DM = \"wait\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: NP0040_WaitUntilReady_DM\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_NP0035_PortInfoDetails_DM = \"repeat\", true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: NP0035_PortInfoDetails_DM\n```", "timestamp": 1749528906.4500108, "content_hash": "d1fc784818382e822fc3afe230a5af44"}