{"response": "```yaml\n- kind: Question\n  id: RP0511_AskAddOrRemoveLines_DM\n  displayName: RP0511_AskAddOrRemoveLines_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/RP0511_nm1_01.wav\\\">If you'd like to add or remove lines on your account say 'yes' or press 1  If not say 'no' or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/RP0511_nm2_02.wav\\\">If you'd like to add or remove lines on your account say 'yes' or press 1  If not say 'no' or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/RP0511_nm2_02.wav\\\">If you'd like to add or remove lines on your account say 'yes' or press 1  If not say 'no' or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.RP0511_AskAddOrRemoveLines_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/RP0511_ini_01.wav\\\">Would you like to add or remove any of the lines on your account? </audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/RP0511_nm1_01.wav\\\">If you'd like to add or remove lines on your account say 'yes' or press 1  If not say 'no' or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/RP0511_nm2_02.wav\\\">If you'd like to add or remove lines on your account say 'yes' or press 1  If not say 'no' or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/RP0511_nm2_02.wav\\\">If you'd like to add or remove lines on your account say 'yes' or press 1  If not say 'no' or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_RP0511_AskAddOrRemoveLines_DM\n  value: =Text(Global.RP0511_AskAddOrRemoveLines_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_RP0511_AskAddOrRemoveLines_DM = \"true\", true, false)\n      actions:\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.RatePlan_Main.dvxml#RP1280_GoToCallTransfer_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_RP0511_AskAddOrRemoveLines_DM = \"false\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n```", "timestamp": 1749529428.1165297, "content_hash": "69315d7d8736efcfcb4830d93a1107bc"}