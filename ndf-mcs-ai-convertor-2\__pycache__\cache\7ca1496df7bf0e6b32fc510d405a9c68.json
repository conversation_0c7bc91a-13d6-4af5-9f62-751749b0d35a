{"response": "```yaml\n- kind: Question\n  id: ma2440_eSimActivationFirstStep_DM\n  displayName: ma2440_eSimActivationFirstStep_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.ma2440_eSimActivationFirstStep_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ma2440_ini_02.wav\\\">First, you will need to turn on your device and complete the device set up When youre ready say continue</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/percolation_20s.wav\\\">20 seconds of hold music</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ma2440_ini_03.wav\\\">Remember, when youve completed the device set up and are ready, say continue</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/percolation_20s.wav\\\">20 seconds of hold music</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ma2440_ini_03.wav\\\">Remember, when youve completed the device set up and are ready, say continue</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/percolation_20s.wav\\\">20 seconds of hold music</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ma2440_ini_03.wav\\\">Remember, when youve completed the device set up and are ready, say continue</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/percolation_20s.wav\\\">20 seconds of hold music</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: continue\n          displayName: continue\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ma2440_ni1_01.wav\\\">Lets go on</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\"></audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ma2440_eSimActivationFirstStep_DM\n  value: =Text(Global.ma2440_eSimActivationFirstStep_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma2440_eSimActivationFirstStep_DM = \"continue\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ma2445_eSimActivationSecondStep_DM\n```", "timestamp": 1749471525.8486524, "content_hash": "7ca1496df7bf0e6b32fc510d405a9c68"}