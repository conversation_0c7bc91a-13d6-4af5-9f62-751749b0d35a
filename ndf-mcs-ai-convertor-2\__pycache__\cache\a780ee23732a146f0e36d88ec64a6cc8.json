{"response": "```yaml\n- kind: Question\n  id: ma2450_eSimActivationCompleted_DM\n  displayName: ma2450_eSimActivationCompleted_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ma2450_nm1_01.wav\\\">Your e Sim is now downloaded and installed Can I do anything else for you? Just say Yes or No</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ma2450_nm2_01.wav\\\">Youre all set If theres anything else youd like to do with your account, like adding optional services, say Yes or press 1 If not, say No or press 2 Or feel free to just hang up</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ma2450_eSimActivationCompleted_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.skipEsimActivationSteps = true,\n            \"Great, Youre all set! Is there anything else I can help you with?\",\n            \"Youre all set! Your e SIM is now downloaded and installed Is there anything else I can help you with?\"\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/as0510_ConfirmSIM_noinput1.wav\\\"> </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/as0510_ConfirmSIM_noinput2.wav\\\"> </audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ma2450_eSimActivationCompleted_DM\n  value: =Text(Global.ma2450_eSimActivationCompleted_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma2450_eSimActivationCompleted_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.callType\n          value: 611\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_commandgrammar\n          value: GlobalCommands.grxml?SWI_vars.allow=mainmenu\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_dtmfcommandgrammar\n          value: GlobalCommands_dtmf.grxml?SWI_vars.allow=mainmenu\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.matchedANI\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: \"\"\n\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M01_MainMenu_Dialog\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ma2450_eSimActivationCompleted_DM = \"false\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: ma2435_ActiveReminder_PP\n```", "timestamp": 1749471659.8592236, "content_hash": "a780ee23732a146f0e36d88ec64a6cc8"}