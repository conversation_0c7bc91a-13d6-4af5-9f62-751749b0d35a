{"response": "```yaml\n- kind: Question\n  id: DS3011_DSGOptions_DM\n  displayName: DS3011_DSGOptions_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.DS3011_DSGOptions_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/DS3011_ini_01.wav\\\">For payment extensions, press 1 To reset an EDGE password, press 2 For Dealer Access Support, press 3 For anything else, hold the line</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: request-extension_dsg\n          displayName: request-extension_dsg\n\n        - id: edge_pw\n          displayName: edge_pw\n\n        - id: access\n          displayName: access\n\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_DS3011_DSGOptions_DM\n  value: =Text(Global.DS3011_DSGOptions_DM_reco)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.callType\n  value: GlobalVars.callType\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_DS3011_DSGOptions_DM = \"request-extension_dsg\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.callType\n          value: dsgExtension\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: DS4001_CheckContext_JDA\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_DS3011_DSGOptions_DM = \"edge_pw\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.language = \"en-US\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.TransferTag\n                  value: DSGapp_EDGEPW_English\n\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.TransferTag\n              value: DSGapp_EDGEPW_Spanish\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: DS3025_PlayDealerMessages_PP\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_DS3011_DSGOptions_DM = \"access\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.language = \"en-US\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.TransferTag\n                  value: DSGapp_AccessSupport_English\n\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.TransferTag\n              value: DSGapp_AccessSupport_Spanish\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: DS3025_PlayDealerMessages_PP\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_DS3011_DSGOptions_DM = \"default\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: DS3025_PlayDealerMessages_PP\n```", "timestamp": 1749528349.3181288, "content_hash": "6ee6904bd33c2256f5fabf952c749c66"}