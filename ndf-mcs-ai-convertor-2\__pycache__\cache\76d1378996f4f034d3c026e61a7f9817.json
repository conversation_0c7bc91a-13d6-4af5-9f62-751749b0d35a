{"response": "```yaml\n- kind: Question\n  id: aa2525_DOBDTMF_DM\n  displayName: aa2525_DOBDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa2525_nm1_01.wav\\\">Sorry</audio>\"\n      - |\n        {Switch(\n              true,\n              Global.dnisInfo.dtmfOnlyFlag = true,\n              \"<audio src=\\\"AUDIO_LOCATION/aa2525_nm2_02.wav\\\">Sorry, I still didn t get that</audio>\",\n              true,\n              \"\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.aa2525_DOBDTMF_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.validationCriteriaVariables.currentValidationType = \"LBD\" || Global.validationCriteriaVariables.currentValidationType = \"DOB\",\n              \"<audio src=\\\"AUDIO_LOCATION/aa2525_ini_01.wav\\\">Please enter the your date of birth using two   digits for the months, two digits for the day and four digits for the year   pause For example, if the primary card holder s date of birth is    August 21st 1985 you would enter 08 21 1985</audio>\",\n              true,\n              \"<audio src=\\\"AUDIO_LOCATION/aa2525_ini_02.wav\\\">Please enter the primary card holder s date of birth using two   digits for the months, two digits for the day and four digits for the year  pause For example, if the primary card holder s date of birth is   August 21st 1985 you would enter 08 21 1985</audio>\"\n          )\n        }\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.validationCriteriaVariables.currentValidationType = \"LBD\" || Global.validationCriteriaVariables.currentValidationType = \"DOB\",\n                \"<audio src=\\\"AUDIO_LOCATION/aa2525_ini_01.wav\\\">Please enter the your date of birth using two   digits for the months, two digits for the day and four digits for the year   pause For example, if the primary card holder s date of birth is    August 21st 1985 you would enter 08 21 1985</audio>\",\n                true,\n                \"<audio src=\\\"AUDIO_LOCATION/aa2525_ini_02.wav\\\">Please enter the primary card holder s date of birth using two   digits for the months, two digits for the day and four digits for the year  pause For example, if the primary card holder s date of birth is   August 21st 1985 you would enter 08 21 1985</audio>\"\n            )\n          }\n        - |\n          {Switch(\n                true,\n                Global.dnisInfo.dtmfOnlyFlag = true,\n                \"<audio src=\\\"AUDIO_LOCATION/aa2525_ni2_02.wav\\\">Sorry, I still didn t get that</audio>\",\n                true,\n                \"\"\n            )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.result\n  value: aa2525_DOBDTMF_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.birthdate\n  value: aa2525_DOBDTMF_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.validationCriteriaVariables.validationValue\n  value: validationCriteriaVariables.validationValue + ';' + result\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.validationCriteriaVariables.validationType\n  value: validationCriteriaVariables.validationType + ';' + validationCriteriaVariables.currentValidationType\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.globalVariables.currentTask = \"cardReplacement\" || Global.globalVariables.currentTask = \"altAuthVal\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa2510_ValidationDecision_JDA_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: |\n            =If(Global.result = Global.validationCriteriaVariables.currentValidationData, true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa2510_ValidationDecision_JDA_DA\n\n      elseActions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.validationCriteriaVariables.validationDataMismatch\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.validationCriteriaVariables.validationDataMismatchDob\n          value: true\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa2510_ValidationDecision_JDA_DA\n```", "timestamp": 1749556766.5344121, "content_hash": "76d1378996f4f034d3c026e61a7f9817"}