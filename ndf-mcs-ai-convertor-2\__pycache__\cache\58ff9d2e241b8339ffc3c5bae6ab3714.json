{"response": "```yaml\n- kind: Question\n  id: st0615_AskChangePlan_DM\n  displayName: st0615_AskChangePlan_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/st0615_AskChangePlan_DM_noinput1.wav\\\">com.nuance.att.application.audio.st0615_AskChangePlan_DM_noinput1</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/st0615_AskChangePlan_DM_noinput2.wav\\\">com.nuance.att.application.audio.st0615_AskChangePlan_DM_noinput2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.st0615_AskChangePlan_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/st0615_AskChangePlan_DM_initial.wav\\\">com.nuance.att.application.audio.st0615_AskChangePlan_DM_initial</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/st0615_AskChangePlan_DM_noinput1.wav\\\">com.nuance.att.application.audio.st0615_AskChangePlan_DM_noinput1</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/st0615_AskChangePlan_DM_noinput2.wav\\\">com.nuance.att.application.audio.st0615_AskChangePlan_DM_noinput2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_st0615_AskChangePlan_DM\n  value: =Text(Global.st0615_AskChangePlan_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_st0615_AskChangePlan_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: changePlan\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: st0620_DataChangePlan_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_st0615_AskChangePlan_DM = \"false\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: S02_TechSupport_01.dvxml\n\n# Command actions (not part of main success flow, but included for completeness)\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_st0615_AskChangePlan_DM = \"mainmenu\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferDestination\n          value: attPrepaid\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M01_MainMenu2.dvxml\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_st0615_AskChangePlan_DM = \"operator\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferReason\n          value: AGENT_REQUEST\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M09_MainMenu.dvxml\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_st0615_AskChangePlan_DM = \"repeat\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: st0615_AskChangePlan_DM\n```\n**Notes:**\n- All audiofile references are converted to `<audio src=\"AUDIO_LOCATION/filename.wav\">text</audio>` format.\n- The prompt and unrecognizedPrompt use the className as the text, as the XML only provides the className for custom prompts.\n- The entity is set to `BooleanPrebuiltEntity` because the success actions are for \"true\" and \"false\".\n- The command actions are included as a separate ConditionGroup for completeness, as per your instructions.\n- All ids for SetVariable, ConditionGroup, etc., are set to `REPLACE_THIS` as per your placeholder convention.\n- The structure and indentation follow your memorized YAML format.", "timestamp": 1749472089.3864965, "content_hash": "58ff9d2e241b8339ffc3c5bae6ab3714"}