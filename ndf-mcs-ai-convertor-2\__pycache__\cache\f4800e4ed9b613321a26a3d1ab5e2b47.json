{"response": "```yaml\n- kind: Question\n  id: da0115_AskSameDevice_DM\n  displayName: da0115_AskSameDevice_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I missed that</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/da0115_ni1_01.wav\\\">Is wireless number </audio>\"\n      - \"{Global.subscriberNumber}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/da0115_ni1_02.wav\\\">the one you need help with? Say Yes or No</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">Sorry</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/da0115_ni2_01.wav\\\">If you're calling about data for wireless number</audio>\"\n      - \"{Global.subscriberNumber}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/da0115_ni2_02.wav\\\">say Yes or press 1 If it's for a different account, say No or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.da0115_AskSameDevice_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/da0115_ini_01.wav\\\">You're calling about wireless number</audio> {Global.subscriberNumber} <audio src=\\\"AUDIO_LOCATION/da0115_ini_02.wav\\\">right?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/da0115_ni1_01.wav\\\">Is wireless number </audio>\"\n        - \"{Global.subscriberNumber}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/da0115_ni1_02.wav\\\">the one you need help with? Say Yes or No</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/da0115_ni2_01.wav\\\">If you're calling about data for wireless number</audio>\"\n        - \"{Global.subscriberNumber}\"\n        - \"<audio src=\\\"AUDIO_LOCATION/da0115_ni2_02.wav\\\">say Yes or press 1 If it's for a different account, say No or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_da0115_AskSameDevice_DM\n  value: =Text(Global.da0115_AskSameDevice_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_da0115_AskSameDevice_DM = \"true\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: da0135_DataDeviceType_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_da0115_AskSameDevice_DM = \"false\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.loginType\n              value: both\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: da0125_DataLogin_SD\n```\n**Notes on the conversion:**\n- All audio prompts are converted to `<audio src=\"AUDIO_LOCATION/filename.wav\">text</audio>` format, and `{Global.subscriberNumber}` is used for the dynamic number.\n- The `entity` is set to `BooleanPrebuiltEntity` because the success actions are for \"true\" and \"false\".\n- The `SetVariable` for the main variable is included as per instruction 11.\n- The `ConditionGroup` logic is built for \"true\" and \"false\" values, with the correct nesting and actions.\n- Only the `<success>` tag is processed for logic; `<command>` is ignored as per instructions.\n- All ids for SetVariable, ConditionGroup, etc., are set to `REPLACE_THIS` as per the pattern.", "timestamp": 1749470622.4291286, "content_hash": "f4800e4ed9b613321a26a3d1ab5e2b47"}