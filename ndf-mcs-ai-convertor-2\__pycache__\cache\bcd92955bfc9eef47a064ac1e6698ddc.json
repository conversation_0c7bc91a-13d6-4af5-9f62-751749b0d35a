{"response": "```yaml\n- kind: Question\n  id: UA1005_ExistingAccountYN_DM\n  displayName: UA1005_ExistingAccountYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/UA1005_nm1_01.wav\\\">Do you already have a Metro  account?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/UA1005_nm2_01.wav\\\">If you already have a Metro  account, say  yes  or press 1 Otherwise, say  no  or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/UA1005_nm2_01.wav\\\">If you already have a Metro  account, say  yes  or press 1 Otherwise, say  no  or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.UA1005_ExistingAccountYN_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n\n            UA1005_operator_counter = 1,\n            [\n              \"Im sorry, I cant transfer you right now\",\n              \"test\",\n              \"Do you already have a Metro  account?\"\n            ],\n\n            UA1005_operator_counter = 2,\n            [\n              \"Let me try to help you here one more time\",\n              \"test\",\n              \"If you already have a Metro  account, say  yes  or press 1 Otherwise, say  no  or press 2\"\n            ],\n\n            [\n              \"Do you already have an account with us?\"\n            ]\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/UA1005_nm1_01.wav\\\">Do you already have a Metro  account?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/UA1005_nm2_01.wav\\\">If you already have a Metro  account, say  yes  or press 1 Otherwise, say  no  or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/UA1005_nm2_01.wav\\\">If you already have a Metro  account, say  yes  or press 1 Otherwise, say  no  or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_UA1005_ExistingAccountYN_DM\n  value: =Text(Global.UA1005_ExistingAccountYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_UA1005_ExistingAccountYN_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/UA1005_out_01.wav\\\">Great</audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: UA1105_CollectMDN_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_UA1005_ExistingAccountYN_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/UA1005_out_02.wav\\\">Okay</audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: UA1205_NewCustomerInfoYN_DM\n```", "timestamp": **********.5408163, "content_hash": "bcd92955bfc9eef47a064ac1e6698ddc"}