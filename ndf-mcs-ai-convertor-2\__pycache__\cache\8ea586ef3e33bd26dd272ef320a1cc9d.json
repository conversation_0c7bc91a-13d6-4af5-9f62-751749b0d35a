{"response": "```yaml\n- kind: Question\n  id: aa2546_SSN_DM\n  displayName: aa2546_SSN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.aa2546_SSN_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.validationCriteriaVariables.currentValidationType = \"PSC\",\n            \"Please say the primary card holder s  social security number\",\n            true,\n            \"Please say your social security number\"\n        )}\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.result\n  value: aa2546_SSN_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.ssn\n  value: aa2546_SSN_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.validationCriteriaVariables.validationValue\n  value: validationCriteriaVariables.validationValue + ';' + result\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.validationCriteriaVariables.validationType\n  value: validationCriteriaVariables.validationType + ';' + validationCriteriaVariables.currentValidationType\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.globalVariables.currentTask = \"cardReplacement\" || Global.globalVariables.currentTask = \"altAuthVal\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa2510_ValidationDecision_JDA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: |\n            =If(Global.result = Global.validationCriteriaVariables.currentValidationData, true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa2510_ValidationDecision_JDA\n\n      elseActions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.validationCriteriaVariables.validationDataMismatch\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.validationCriteriaVariables.validationDataMismatchSsn\n          value: true\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa2510_ValidationDecision_JDA\n```", "timestamp": 1749544159.0672033, "content_hash": "8ea586ef3e33bd26dd272ef320a1cc9d"}