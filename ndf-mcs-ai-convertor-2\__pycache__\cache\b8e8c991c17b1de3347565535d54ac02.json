{"response": "```yaml\n- kind: Question\n  id: PH1215_AskChangePlanYN_DM\n  displayName: PH1215_AskChangePlanYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/PH1215_nm1_01.wav\\\">Would you like to change your monthly plan?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/PH1215_nm2_01.wav\\\">To change your monthly plan now, say  yes  or press 1 Otherwise, say  no  or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/PH1215_nm2_01.wav\\\">To change your monthly plan now, say  yes  or press 1 Otherwise, say  no  or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.PH1215_AskChangePlanYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/PH1215_ini_01.wav\\\">I don t see any add-on features on your account that we could remove to reduce your bill But we can change your rate plan! Would you like to do that?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/PH1215_nm1_01.wav\\\">Would you like to change your monthly plan?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/PH1215_nm2_01.wav\\\">To change your monthly plan now, say  yes  or press 1 Otherwise, say  no  or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/PH1215_nm2_01.wav\\\">To change your monthly plan now, say  yes  or press 1 Otherwise, say  no  or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_PH1215_AskChangePlanYN_DM\n  value: =Text(Global.PH1215_AskChangePlanYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_PH1215_AskChangePlanYN_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/PH1215_out_01.wav\\\">Okay</audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: change_plan\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: PH1220_CheckNeedPIN_JDA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_PH1215_AskChangePlanYN_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/PH1215_out_02.wav\\\">No problem</audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: PH1010_PaymentHelpMenu_DM\n```", "timestamp": 1749529092.6948793, "content_hash": "b8e8c991c17b1de3347565535d54ac02"}