{"response": "```yaml\n- kind: Question\n  id: st0108_AskWirelessAccount_DM\n  displayName: st0108_AskWirelessAccount_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/st0108_ni1_01.wav\\\">Are you calling about an A T and T Prepaid Wireless account? Please say Yes or No?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/st0108_ni2_01.wav\\\">If youre calling about an A T and T Prepaid Wireless account say yes or press 1 Otherwise, say No or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.st0108_AskWirelessAccount_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/st0108_ini_01.wav\\\">Are you calling about an A T and T Prepaid Wireless account?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/st0108_ni1_01.wav\\\">Are you calling about an A T and T Prepaid Wireless account? Please say Yes or No?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/st0108_ni2_01.wav\\\">If youre calling about an A T and T Prepaid Wireless account say yes or press 1 Otherwise, say No or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_st0108_AskWirelessAccount_DM\n  value: =Text(Global.st0108_AskWirelessAccount_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_st0108_AskWirelessAccount_DM = \"true\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: st0109_TechSupportPreMenu_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_st0108_AskWirelessAccount_DM = \"false\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: st0112_PostPaidMenu_DM\n```", "timestamp": **********.7105384, "content_hash": "4a0fade457b2f34b665a028e2639cdc6"}