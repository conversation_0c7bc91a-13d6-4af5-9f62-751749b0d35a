{"response": "```yaml\n- kind: Question\n  id: aa7016_BenefitScheduleWrapUp_DM\n  displayName: aa7016_BenefitScheduleWrapUp_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.aa7016_BenefitScheduleWrapUp_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa7016_ini_01.wav\\\">To hear that again, say  Repeat That  To go back to the main menu, say  Main Menu  And if you re done feel free to just hang up</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: main_menu\n          displayName: main_menu\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa7016_BenefitScheduleWrapUp_DM\n  value: =Text(Global.aa7016_BenefitScheduleWrapUp_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa7016_BenefitScheduleWrapUp_DM = \"main_menu\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.globalVariables.nextStep\n          value: main_menu\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa1015_NextStepHandling_JDA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa7016_BenefitScheduleWrapUp_DM = \"repeat\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.benefitVariables.cameFrom7005 = true, true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: aa7005_BenefitSchedule_DB_DA\n\n              elseActions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.benefitVariables.benefitsScheduleCounter\n                  value: 0\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: aa7010_BenefitSchedulePlayout_PP\n```", "timestamp": 1749543852.6349247, "content_hash": "4a6f8dbe1c162e2a14fec7f7da39663e"}