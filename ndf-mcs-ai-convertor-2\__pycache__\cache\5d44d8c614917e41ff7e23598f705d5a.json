{"response": "```yaml\n- kind: Question\n  id: ES1320_CollectICCID_DM\n  displayName: ES1320_CollectICCID_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ES1320_nm1_01.wav\\\">Please enter the number on the SIM card you want to use in your new phone It s 19 digits long, and ends in the letter F Enter only the numbers To hear how you can find the number again, say instructions </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ES1320_nm2_01.wav\\\">If you have the 19 digit SIM card number, please enter it now on your keypad, without the letter F To repeat the instructions to find the number, press star </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ES1320_nm2_01.wav\\\">If you have the 19 digit SIM card number, please enter it now on your keypad, without the letter F To repeat the instructions to find the number, press star </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ES1320_CollectICCID_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.enteringFrom = \"ES1325_ICCIDPassChecksum_DS\",\n            \"That doesn t look like a valid SIM card number Let s try one more time to be sure To hear the instructions to find the number, press star Otherwise, go ahead and enter it one more time, using your keypad \",\n            \"Say or enter the whole number, but without the letter F \"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: instructions\n          displayName: instructions\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ES1320_nm1_01.wav\\\">Please enter the number on the SIM card you want to use in your new phone It s 19 digits long, and ends in the letter F Enter only the numbers To hear how you can find the number again, say instructions </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ES1320_nm2_01.wav\\\">If you have the 19 digit SIM card number, please enter it now on your keypad, without the letter F To repeat the instructions to find the number, press star </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ES1320_nm2_01.wav\\\">If you have the 19 digit SIM card number, please enter it now on your keypad, without the letter F To repeat the instructions to find the number, press star </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ES1320_CollectICCID_DM\n  value: =Text(Global.ES1320_CollectICCID_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ES1320_CollectICCID_DM = \"instructions\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/ES1320_out_01.wav\\\">Alright, instructions </audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ES1310_FindICCIDInstructions_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ES1320_CollectICCID_DM = \"default\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.iccidSerialNumber\n              value: ES1320_CollectICCID_DM.returnvalue\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: ES1325_ICCIDPassChecksum_JDA\n```", "timestamp": 1749528219.0938733, "content_hash": "5d44d8c614917e41ff7e23598f705d5a"}