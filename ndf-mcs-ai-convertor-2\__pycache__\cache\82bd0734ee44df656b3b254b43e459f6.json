{"response": "```yaml\n- kind: Question\n  id: TT1025_PowerCycleTipWaitSBI_DM\n  displayName: TT1025_PowerCycleTipWaitSBI_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.TT1025_PowerCycleTipWaitSBI_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/TT1025_ini_01.wav\\\">Ok, go ahead and turn your device off and back on again using the power button on the top or side of the device When the device is back on, check if your problem's been fixed I'll wait here If you still have the same problem, say 'it's not fixed' If the problem is corrected, feel free to hang up You can also say 'skip this step'</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TT1025_ini_02.wav\\\">When you're ready, say 'it's not fixed' or press 1 or if your problem is fixed, you can just hang up You can say 'it's not fixed' or press 1 at any time or you can just hang up When you're ready, say 'it's not fixed' or press 1 or if your problem is fixed, you can just hang up You can say 'it's not fixed' or press 1 at any time or you can just hang up When you're ready, say 'it's not fixed' or press 1 or you can just hang up Let's move on</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: not_fixed\n          displayName: not_fixed\n        - id: skip\n          displayName: skip\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_TT1025_PowerCycleTipWaitSBI_DM\n  value: =Text(Global.TT1025_PowerCycleTipWaitSBI_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TT1025_PowerCycleTipWaitSBI_DM = \"not_fixed\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TT1030_WiFiTipOfferYN_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_TT1025_PowerCycleTipWaitSBI_DM = \"skip\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: TT1030_WiFiTipOfferYN_DM\n```", "timestamp": 1749530358.4960032, "content_hash": "82bd0734ee44df656b3b254b43e459f6"}