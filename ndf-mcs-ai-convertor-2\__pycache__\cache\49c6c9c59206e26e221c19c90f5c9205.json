{"response": "```yaml\n- kind: Question\n  id: aa6016_CCZeroBalance_DM\n  displayName: aa6016_CCZeroBalance_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.aa6016_CCZeroBalance_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa6016_ini_01.wav\\\">To go to Main Menu, say  Main Menu   Or, to speak with an agent say  Customer Service Representative </audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: main_menu\n          displayName: main_menu\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa6016_CCZeroBalance_DM\n  value: =Text(Global.aa6016_CCZeroBalance_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa6016_CCZeroBalance_DM = \"main_menu\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: handleMainMenu_CS\n```", "timestamp": 1749543664.7272499, "content_hash": "49c6c9c59206e26e221c19c90f5c9205"}