{"response": "```yaml\n- kind: Question\n  id: DH1020_DeviceHandlingMenu_DM\n  displayName: DH1020_DeviceHandlingMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DH1020_nm1_01.wav\\\">You can say add a line or switch my phone  You can also say change my phone number, my device is lost or damaged, or it s something else </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DH1020_nm2_02.wav\\\">You can say add a line or press 1 switch my phone or press 2 change my phone number, 3 My device is lost or damaged, 4 it s something else , 5 You can also say main menu or press 6 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DH1020_nm2_02.wav\\\">You can say add a line or press 1 switch my phone or press 2 change my phone number, 3 My device is lost or damaged, 4 it s something else , 5 You can also say main menu or press 6 </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.DH1020_DeviceHandlingMenu_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/DH1020_ini_04.wav\\\">You can say add a line or switch my phone You can also say change my phone number, my device is lost or damaged, or it s something else </audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: change-phone_number\n          displayName: change-phone_number\n        - id: switch-phone\n          displayName: switch-phone\n        - id: main_menu\n          displayName: main_menu\n        - id: make-payment\n          displayName: make-payment\n        - id: add-line\n          displayName: add-line\n        - id: report-phone_lost\n          displayName: report-phone_lost\n        - id: something-else_devicehandling\n          displayName: something-else_devicehandling\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/DH1020_nm1_01.wav\\\">You can say add a line or switch my phone  You can also say change my phone number, my device is lost or damaged, or it s something else </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DH1020_nm2_02.wav\\\">You can say add a line or press 1 switch my phone or press 2 change my phone number, 3 My device is lost or damaged, 4 it s something else , 5 You can also say main menu or press 6 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DH1020_nm2_02.wav\\\">You can say add a line or press 1 switch my phone or press 2 change my phone number, 3 My device is lost or damaged, 4 it s something else , 5 You can also say main menu or press 6 </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_DH1020_DeviceHandlingMenu_DM\n  value: =Text(Global.DH1020_DeviceHandlingMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_DH1020_DeviceHandlingMenu_DM = \"change-phone_number\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: mdn_change\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.mainMenuChoice\n          value: mdn_change\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Not(And(Global.GlobalVars.GetAccountDetails, Global.GlobalVars.GetAccountDetails.status, Global.GlobalVars.GetAccountDetails.status.toUpperCase() = \"SUCCESS\")), true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.needMDN\n                  value: true\n\n                - kind: BeginDialog\n                  id: begin_REPLACE_THIS\n                  dialog: topic.DeviceHandling_Routing.dvxml#DH1071_LoginCollectMDN_SD\n\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.securityRequired\n              value: true\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.needMDN\n              value: false\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.paymentInfoRequired\n              value: true\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.billableTask\n              value: true\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.ActivationTable.ACTIVATION_TYPE\n              value: 5\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.ActivationTable.ACTIVATION_STARTED\n              value: getGMTTime()\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.ActivationTable.ACTIVATION_STATUS\n              value: 47\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.ActivationTable.ERROR_TEXT\n              value: FAILURE OR HANG UP\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.mdnChangeVars.eventTypeGMT\n              value: getEventTime()\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.mdnChangeVars.status\n              value: incomplete\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.mdnChangeVars.eventType\n              value: mdn_change\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: DH1040_CheckPaymentInfoRequired_JDA\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_DH1020_DeviceHandlingMenu_DM = \"switch-phone\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: switch_phone\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.securityRequired\n          value: false\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.needMDN\n          value: false\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.language = \"en-US\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.TransferTag\n                  value: Activations_Support_English\n\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.TransferTag\n              value: Activations_Support_Spanish\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.mainMenuChoice\n          value: switch_phone\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_DH1020_DeviceHandlingMenu_DM = \"main_menu\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: undefined\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.securityRequired\n          value: false\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.needMDN\n          value: false\n\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.DeviceHandling_Routing.dvxml#DH1075_CheckNLUMenuConfig_DS\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_DH1020_DeviceHandlingMenu_DM = \"make-payment\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: make_pmt\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.securityRequired\n          value: false\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.needMDN\n          value: false\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.mainMenuChoice\n          value: make_pmt\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: DH1090_GoToMakePayment_SD\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_DH1020_DeviceHandlingMenu_DM = \"add-line\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: add_line\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.securityRequired\n          value: false\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.needMDN\n          value: false\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.mainMenuChoice\n          value: add_line\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_DH1020_DeviceHandlingMenu_DM = \"report-phone_lost\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: lost_phone\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.securityRequired\n          value: false\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.needMDN\n          value: false\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.mainMenuChoice\n          value: lost_phone\n\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.DeviceHandling_Routing.dvxml#DH1085_GoToLostPhone_SD\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_DH1020_DeviceHandlingMenu_DM = \"something-else_devicehandling\", true, false)\n      actions:\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.DeviceHandling_Routing.dvxml#DH1290_CallTransfer_SD\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_DH1020_DeviceHandlingMenu_DM = \"default\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: DH1040_CheckPaymentInfoRequired_JDA\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.helpMeOut\n  value: \"\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.callType\n  value: GlobalVars.callType\n```", "timestamp": **********.247165, "content_hash": "c6db589711d5c7164b8285c4a2fac487"}