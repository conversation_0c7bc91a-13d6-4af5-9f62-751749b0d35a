{"response": "```yaml\n- kind: Question\n  id: PA2005_PaySameAmountPrepaid_DM\n  displayName: PA2005_PaySameAmountPrepaid_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/PA2005_ini_01.wav\\\">How would you like to pay? Say 'bank card', ' Payment PIN' or, 'change the amount' </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/PA2005_ini_02.wav\\\">First, how would you like to pay? Say 'bank card', ' Payment PIN' Or say 'change the amount' </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/PA2005_nm2_01.wav\\\">How would you like to pay? Say 'bank card' or press 1, or ' Payment PIN' or press 2 To change the amount you wanna pay, say 'change amount or press 3 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/PA2005_nm2_01.wav\\\">How would you like to pay? Say 'bank card' or press 1, or ' Payment PIN' or press 2 To change the amount you wanna pay, say 'change amount or press 3 </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.PA2005_PaySameAmountPrepaid_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.fromAnotherPaymentMenu = true,\n            \"How would you like to pay? Say 'bank card', ' Payment PIN' or, 'change the amount' \",\n            Global.fromAnotherPaymentMenu <> true,\n            \"First, how would you like to pay? Say 'bank card', ' Payment PIN' Or say 'change the amount' \"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: card\n          displayName: card\n        - id: prepaid\n          displayName: prepaid\n        - id: change\n          displayName: change\n        - id: request-extension\n          displayName: request-extension\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/PA2005_ini_01.wav\\\">How would you like to pay? Say 'bank card', ' Payment PIN' or, 'change the amount' </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/PA2005_ini_02.wav\\\">First, how would you like to pay? Say 'bank card', ' Payment PIN' Or say 'change the amount' </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/PA2005_nm2_01.wav\\\">How would you like to pay? Say 'bank card' or press 1, or ' Payment PIN' or press 2 To change the amount you wanna pay, say 'change amount or press 3 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/PA2005_nm2_01.wav\\\">How would you like to pay? Say 'bank card' or press 1, or ' Payment PIN' or press 2 To change the amount you wanna pay, say 'change amount or press 3 </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.fromAnotherPaymentMenu\n  value: GlobalVars.fromAnotherPaymentMenu\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.extensionAllowed\n  value: \"GlobalVars.extensionAllowed != undefined ? GlobalVars.extensionAllowed : false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.dueImmediatelyAmount\n  value: GlobalVars.GetAccountDetails.dueImmediatelyAmount\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.amountDue\n  value: GlobalVars.GetAccountDetails.balance\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_grammar1\n  value: PA2005_PaySameAmountPrepaid_DM.grxml\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.extensionAllowed = false, true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_grammar1\n          value: collection_grammar1 + '?SWI_vars.disallow=request-extension'\n\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.collection_grammar1\n      value: collection_grammar1\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_PA2005_PaySameAmountPrepaid_DM\n  value: =Text(Global.PA2005_PaySameAmountPrepaid_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_PA2005_PaySameAmountPrepaid_DM = \"card\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(parseFloat(Global.dueImmediatelyAmount) > 3, true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.paymentAmount\n                  value: dueImmediatelyAmount\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.paymentAmount\n              value: amountDue\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.offerPrepaid\n          value: false\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: getReturnLink()\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_PA2005_PaySameAmountPrepaid_DM = \"prepaid\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.payingWithPrepaid\n          value: true\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: getReturnLink()\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_PA2005_PaySameAmountPrepaid_DM = \"change\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/PA2005_out_01.wav\\\">Okay </audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: PA1015_GetAmount_DM\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_PA2005_PaySameAmountPrepaid_DM = \"request-extension\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/PA2005_out_02.wav\\\">Sure!</audio>\"\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: extension\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_Intent\n          value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.extensionEntryPoint\n          value: payment_amount\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: getReturnLink()\n```", "timestamp": 1749529059.7466872, "content_hash": "4a39f05066a2267d909b264e6cf8afd2"}