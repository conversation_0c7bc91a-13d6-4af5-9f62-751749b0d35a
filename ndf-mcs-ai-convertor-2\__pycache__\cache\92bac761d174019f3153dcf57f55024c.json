{"response": "```yaml\n- kind: Question\n  id: AU1015_PlayAPInfoConfirmSetup_DM\n  displayName: AU1015_PlayAPInfoConfirmSetup_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AU1015_nm1_01.wav\\\">Just to confirm, you would like to continue with setting up autopay, right?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AU1015_nm2_01.wav\\\">To continue with autopay set up say 'yes' or press 1  Or say 'no' or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AU1015_nm2_01.wav\\\">To continue with autopay set up say 'yes' or press 1  Or say 'no' or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.AU1015_PlayAPInfoConfirmSetup_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/AU1015_ini_01.wav\\\">Before we set up autopay, please note that auto payments will be processed 2 days in advance of your due date to ensure there is no service interruptionPrior to the due date you will receive a text of with the payment amount that will be charged to the card you set up as your primary autopay cardOnce the payment goes through you will receive another text with your confirmation numberWould you like to continue with setting up autopay?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/AU1015_nm1_01.wav\\\">Just to confirm, you would like to continue with setting up autopay, right?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AU1015_nm2_01.wav\\\">To continue with autopay set up say 'yes' or press 1  Or say 'no' or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AU1015_nm2_01.wav\\\">To continue with autopay set up say 'yes' or press 1  Or say 'no' or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.payingWithPrepaid\n  value: \"GlobalVars.payingWithPrepaid != undefined ? GlobalVars.payingWithPrepaid : false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.payingWithEWallet\n  value: \"GlobalVars.payingWithEWallet != undefined ? GlobalVars.payingWithEWallet : false\"\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_AU1015_PlayAPInfoConfirmSetup_DM\n  value: =Text(Global.AU1015_PlayAPInfoConfirmSetup_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AU1015_PlayAPInfoConfirmSetup_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.tag\n          value: setup-autopay\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.GlobalVars.recentPayment = true, true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: AU1305_AskUsePaymentCard_DM\n\n          elseActions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: AU1320_GoToManageCards_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_AU1015_PlayAPInfoConfirmSetup_DM = \"false\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.callType\n              value: undefined\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n```", "timestamp": 1749527540.1430354, "content_hash": "92bac761d174019f3153dcf57f55024c"}