{"response": "```yaml\n- kind: Question\n  id: SQ1040_SpellStreetName_DM\n  displayName: SQ1040_SpellStreetName_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SQ1040_nm1_01.wav\\\">Please SPELL the name of the street out loud, like T O M</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SQ1040_nm2_01.wav\\\">Sorry, I need you to say the letters out loud Please spell the name of the street you grew up on now</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SQ1040_nm3_01.wav\\\">Sorry, I need you to say the letters out loud Please spell the name of the street you grew up on now</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.SQ1040_SpellStreetName_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/SQ1040_ini_01.wav\\\">Now, after the beep, please spell that  for me</audio>\"\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/SQ1040_ni1_01.wav\\\">Sorry, I didn t hear you Please spell the name of the street you grew up on</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SQ1040_ni2_01.wav\\\">Sorry, please spell the name of the street you grew up on</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SQ1040_ni3_01.wav\\\">Sorry, please spell the name of the street you grew up on</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.spellStreetFilename\n  value: SQ1040_SpellStreetName_DM.returnvalue\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: SQ1050_ConfirmStreetNameYN_DM\n```", "timestamp": 1749529551.112526, "content_hash": "4f2163f1fd31c1e566af292b1277fe32"}