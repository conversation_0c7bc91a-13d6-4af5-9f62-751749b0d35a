{"response": "```yaml\n- kind: Question\n  id: aa0118_ActivationTC_DM\n  displayName: aa0118_ActivationTC_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa0118_nm1_01.wav\\\">If you agree to the terms and conditions say yes or press 1 Otherwise, say no or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/aa0118_nm2_01.wav\\\">If you agree to the terms and conditions press 1 Otherwise, press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.aa0118_ActivationTC_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.numTCAttempts = 0,\n            \"OKBy activating A T and T PREPAID service, you are agreeing to the A T and T Consumer Service Agreement, available at a t t dot com slash Consumer Service Agreement, which includes dispute resolution by arbitration Account payments are nontransferable and nonrefundable Visit a t t dot com slash broadband info for more information on A T and Tees network management practices Plans, charges, rates, coverage area, and services are subject to change at any time Geographic, usage, and other restrictions apply Say yes or press 1 if you agree Say no or press 2 if you do not agree\",\n            \"In order to continue with this activation, you need to agree to the terms and conditions If you agree, say 'yes' or press 1 If not, say 'no' or press 2\"\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/aa0118_ni1_01.wav\\\">If you agree to the terms and conditions say yes or press 1 Otherwise, say no or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/aa0118_ni2_01.wav\\\">If you agree to the terms and conditions press 1 Otherwise, press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa0118_ActivationTC_DM\n  value: =Text(Global.aa0118_ActivationTC_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa0118_ActivationTC_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aa0118_out_01.wav\\\">Thanks</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa0120_ActivateAccount_DB_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.numTCAttempts = 1, true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/aa0118_out_03.wav\\\">Unfortunately, we are unable to activate your account at this time</audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\"></audio>\"\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.M09_MainMenu.dvxml#ma9110_Goodbye_PP\n\n      elseActions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.numTCAttempts\n          value: numTCAttempts + 1\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa0118_ActivationTC_DM\n```", "timestamp": **********.5747266, "content_hash": "4abdb6c6b7bac865d72be897f949b21a"}