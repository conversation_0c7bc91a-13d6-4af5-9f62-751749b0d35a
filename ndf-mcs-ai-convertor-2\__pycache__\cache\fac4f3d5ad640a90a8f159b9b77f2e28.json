{"response": "```yaml\n- kind: Question\n  id: PA1010_PaySameAmountYN_DM\n  displayName: PA1010_PaySameAmountYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"{Switch(\n            true,\n            parseFloat(Global.dueImmediatelyAmount) > 3,\n            \\\"Do you want to make your payment due now? \\\",\n            true,\n            \\\"Do you want to pay your full balance?\\\"\n        )}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"{Switch(\n            true,\n            parseFloat(Global.dueImmediatelyAmount) > 3,\n            \\\"If you want to pay your amount due now, say 'yes' or press 1 Otherwise, say 'no' or press 2\\\",\n            true,\n            \\\"If you want to pay your full balance, say 'yes' or press 1 Otherwise, say 'no' or press 2\\\"\n        )}\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"{Switch(\n            true,\n            parseFloat(Global.dueImmediatelyAmount) > 3,\n            \\\"To pay your amount due now, press 1 To pay a different amount, press 2\\\",\n            true,\n            \\\"To pay your full balance, press 1 To pay a different amount, press 2\\\"\n        )}\"\n\n  alwaysPrompt: true\n  variable: Global.PA1010_PaySameAmountYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/PA1010_ini_01.wav\\\">Is that the amount you want to pay</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: yes\n          displayName: yes\n        - id: no\n          displayName: no\n        - id: request-extension\n          displayName: request-extension\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"{Switch(\n              true,\n              parseFloat(Global.dueImmediatelyAmount) > 3,\n              \\\"Do you want to make your payment due now? \\\",\n              true,\n              \\\"Do you want to pay your full balance?\\\"\n          )}\"\n        - \"{Switch(\n              true,\n              parseFloat(Global.dueImmediatelyAmount) > 3,\n              \\\"If you want to pay your amount due now, say 'yes' or press 1 Otherwise, say 'no' or press 2\\\",\n              true,\n              \\\"If you want to pay your full balance, say 'yes' or press 1 Otherwise, say 'no' or press 2\\\"\n          )}\"\n        - \"{Switch(\n              true,\n              parseFloat(Global.dueImmediatelyAmount) > 3,\n              \\\"To pay your amount due now, press 1 To pay a different amount, press 2\\\",\n              true,\n              \\\"To pay your full balance, press 1 To pay a different amount, press 2\\\"\n          )}\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.balance\n  value: GlobalVars.GetAccountDetails.balance\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.dueImmediatelyAmount\n  value: GlobalVars.GetAccountDetails.dueImmediatelyAmount\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.extensionAllowed\n  value: \"GlobalVars.extensionAllowed != undefined ? GlobalVars.extensionAllowed : false\"\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_PA1010_PaySameAmountYN_DM\n  value: =Text(Global.PA1010_PaySameAmountYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_PA1010_PaySameAmountYN_DM = \"yes\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(parseFloat(Global.dueImmediatelyAmount) > 3, true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.paymentAmount\n                  value: Global.dueImmediatelyAmount\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.paymentAmount\n              value: Global.balance\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: getReturnLink()\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_PA1010_PaySameAmountYN_DM = \"no\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: PA1015_GetAmount_DM\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_PA1010_PaySameAmountYN_DM = \"request-extension\", true, false)\n              actions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/PA1010_out_01.wav\\\">Sure!</audio>\"\n                      - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.callType\n                  value: extension\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.cti_Intent\n                  value: getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')\n\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.extensionEntryPoint\n                  value: payment_amount\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: getReturnLink()\n```", "timestamp": 1749528969.2041388, "content_hash": "fac4f3d5ad640a90a8f159b9b77f2e28"}