{"response": "```yaml\n- kind: Question\n  id: XT1105_ConfirmPhoneReadyYN_DM\n  displayName: XT1105_ConfirmPhoneReadyYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/XT1105_rin_01.wav\\\">I'll send an authentication code to the phone that's currently on</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/XT1105_rin_02.wav\\\">Do you have that phone ready? Or, if you need time to set it up, say 'gimme a minute' </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/XT1105_nm2_01.wav\\\">If you have your current phone available and ready to get text messages, say yes or press 1 If you can't get to it now, say 'no' or press 2 If you need some time to set it up, say 'gimme a minute' or press 3</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/XT1105_nm2_01.wav\\\">If you have your current phone available and ready to get text messages, say yes or press 1 If you can't get to it now, say 'no' or press 2 If you need some time to set it up, say 'gimme a minute' or press 3</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.XT1105_ConfirmPhoneReadyYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/XT1105_ini_01.wav\\\">Do you have that phone ready</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/XT1105_rin_01.wav\\\">I'll send an authentication code to the phone that's currently on</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/XT1105_rin_02.wav\\\">Do you have that phone ready? Or, if you need time to set it up, say 'gimme a minute' </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/XT1105_nm2_01.wav\\\">If you have your current phone available and ready to get text messages, say yes or press 1 If you can't get to it now, say 'no' or press 2 If you need some time to set it up, say 'gimme a minute' or press 3</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/XT1105_nm2_01.wav\\\">If you have your current phone available and ready to get text messages, say yes or press 1 If you can't get to it now, say 'no' or press 2 If you need some time to set it up, say 'gimme a minute' or press 3</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.mdn\n  value: GlobalVars.mdn\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_XT1105_ConfirmPhoneReadyYN_DM\n  value: =Text(Global.XT1105_ConfirmPhoneReadyYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_XT1105_ConfirmPhoneReadyYN_DM = \"yes\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: XT1205_PlaySendingTempCode_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_XT1105_ConfirmPhoneReadyYN_DM = \"no\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.twoFactorAuthOutcome\n              value: no_phone\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.cti_AuthStatus\n              value: getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'tfa_failed_nophone')\n\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/XT1105_out_01.wav\\\">All Right</audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: XT1113_CheckMultiline_JDA\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_XT1105_ConfirmPhoneReadyYN_DM = \"wait\", true, false)\n              actions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/XT1105_out_02.wav\\\">Sure </audio>\"\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: XT1110_WaitPhoneReadySBI_DM\n```", "timestamp": 1749529986.1698427, "content_hash": "d1e33bba9d9827375701d25fa4238ea4"}