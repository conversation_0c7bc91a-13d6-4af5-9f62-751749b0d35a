# PowerFX Validator Integration with Your YAML Pattern

## Overview

The PowerFX Validator and Fixer has been updated to use your exact YAML reading and writing pattern, ensuring complete consistency with your existing codebase and maintaining the same YAML handling approach.

## Your YAML Pattern Integration

### ✅ **Exact YAML Configuration Used**

```python
def read_yaml(self, file_path):
    yaml = YAML()
    yaml.width = 4096
    yaml.indent(mapping=2, sequence=4, offset=2)
    yaml.preserve_quotes = True
    with open(file_path, 'r', encoding='utf-8') as file:
        data = yaml.load(file)
    return data, yaml

def write_yaml(self, data, yaml_instance, file_path):
    self.ensure_directory_exists(file_path)
    with open(file_path, 'w', encoding='utf-8') as file:
        yaml_instance.dump(data, file)
```

### ✅ **Imports Matched**

```python
from ruamel.yaml import YAML
from ruamel.yaml.scalarstring import ScalarString
from ruamel.yaml.comments import TaggedScalar
from collections import OrderedDict
```

### ✅ **Utility Functions Integrated**

- `ensure_directory_exists()` - Creates directories as needed
- `read_yaml()` - Uses your exact YAML reading configuration
- `write_yaml()` - Uses your exact YAML writing configuration

## Key Integration Features

### 1. **YAML Data Structure Processing**

Instead of processing YAML as text, the validator now works directly with YAML data structures:

```python
def process_yaml_data(self, data, file_path: str = "") -> Tuple[Any, bool, List[Dict]]:
    """Process YAML data structure directly to validate and fix PowerFX expressions"""
    
    def process_node(node, path=""):
        if isinstance(node, dict):
            for key, value in node.items():
                if isinstance(value, str) and contains_powerfx(value):
                    # Validate and fix PowerFX expression
                    fixed_expression, fixes = self.fix_powerfx_expression(value, path)
                    if fixes:
                        node[key] = fixed_expression
                        was_modified = True
```

### 2. **Backup Creation Using Your Pattern**

```python
# Create backup using your standard method
backup_path = file_path.with_suffix(file_path.suffix + '.backup')
if self.write_yaml(data, yaml_instance, str(backup_path)):
    self.logger.info(f"Backup created: {backup_path}")
```

### 3. **File Processing Workflow**

```python
def process_yaml_file(self, file_path: Path, dry_run: bool = False) -> bool:
    # 1. Read using your pattern
    data, yaml_instance = self.read_yaml(str(file_path))
    
    # 2. Process YAML data structure
    modified_data, was_modified, issues_and_fixes = self.process_yaml_data(data, str(file_path))
    
    # 3. Write using your pattern (if modified)
    if was_modified and not dry_run:
        # Create backup
        self.write_yaml(data, yaml_instance, str(backup_path))
        # Write modified data
        self.write_yaml(modified_data, yaml_instance, str(file_path))
```

## Usage Examples

### Basic Usage (Same as Before)
```bash
# Process single file
python powerfx_validator_fixer.py input.yml

# Process directory
python powerfx_validator_fixer.py input_folder/

# Dry run
python powerfx_validator_fixer.py input.yml --dry-run --verbose
```

### Integration with Your Pipeline
```bash
# Your existing workflow
python step4_generate_bot_yaml.py

# PowerFX validation using your YAML pattern
python powerfx_validator_fixer.py output/bot_yaml/ --verbose

# Continue with your pipeline
python step5_post_processing.py
```

## Validation Against Your Test File

The validator can now process your `st-mcs-tools\mcstools-st\test\output.yml` file using the exact same YAML handling pattern you use in your codebase.

### Test Command
```bash
python powerfx_validator_fixer.py st-mcs-tools\mcstools-st\test\output.yml --verbose
```

### Expected Behavior
1. ✅ Reads YAML using your exact configuration
2. ✅ Processes PowerFX expressions in the data structure
3. ✅ Maintains all YAML formatting and structure
4. ✅ Creates backup with `.backup` extension
5. ✅ Writes modified YAML using your configuration
6. ✅ Preserves comments, spacing, and quotes

## Consistency Benefits

### 1. **Same YAML Configuration**
- Width: 4096 (prevents line wrapping)
- preserve_quotes: True (maintains quote formatting)
- Indent: mapping=2, sequence=4, offset=2 (your standard indentation)

### 2. **Same File Handling**
- Directory creation with `ensure_directory_exists()`
- UTF-8 encoding for all file operations
- Error handling patterns match your code

### 3. **Same Data Structure Approach**
- Works with YAML data objects (dict/list) not text
- Preserves all YAML structure and metadata
- Maintains ruamel.yaml's comment and formatting preservation

## Testing

### Run Integration Test
```bash
python test_powerfx_with_your_yaml.py
```

This test:
- ✅ Creates YAML files with PowerFX issues
- ✅ Uses your exact YAML reading/writing pattern
- ✅ Validates PowerFX expressions are fixed
- ✅ Confirms YAML structure is preserved
- ✅ Tests backup creation
- ✅ Tests dry-run mode

### Test Your Actual File
```bash
# Dry run on your test file
python powerfx_validator_fixer.py st-mcs-tools\mcstools-st\test\output.yml --dry-run --verbose

# Process your test file (creates backup)
python powerfx_validator_fixer.py st-mcs-tools\mcstools-st\test\output.yml --verbose
```

## Microsoft PowerFX Compliance

All PowerFX fixes still follow Microsoft Learn documentation:
- ✅ Function validation against Microsoft PowerFX reference
- ✅ Syntax corrections per Microsoft standards
- ✅ Detailed logging with Microsoft documentation references
- ✅ Built-in validation against PowerFX best practices

## File Structure Preservation

The integration ensures:
- ✅ **Comments preserved** - All YAML comments maintained
- ✅ **Spacing preserved** - Original spacing and formatting kept
- ✅ **Quote style preserved** - Your quote preferences maintained
- ✅ **Structure preserved** - All YAML structure and hierarchy maintained
- ✅ **Metadata preserved** - All ruamel.yaml metadata kept intact

## Summary

The PowerFX Validator and Fixer now:

1. **Uses your exact YAML pattern** - Same configuration, same functions, same approach
2. **Maintains full compatibility** - Works seamlessly with your existing codebase
3. **Preserves all formatting** - No changes to YAML structure or formatting
4. **Follows Microsoft standards** - All PowerFX fixes based on official documentation
5. **Provides detailed logging** - Comprehensive reporting with Microsoft references

The validator is now fully integrated with your YAML handling approach while maintaining all PowerFX validation and fixing capabilities based on Microsoft Learn documentation.
