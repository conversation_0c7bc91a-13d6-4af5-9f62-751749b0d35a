{"response": "```yaml\n- kind: Question\n  id: BB1007_AskOneTimePayment_DM\n  displayName: BB1007_AskOneTimePayment_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BB1007_nm1_01.wav\\\">We were unable to process your last autopayment Would you like to make a one-time payment now?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BB1007_nm2_01.wav\\\">We were unable to process your last autopayment If you'd like to make a one-time payment now press 1  if not press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BB1007_nm2_01.wav\\\">We were unable to process your last autopayment If you'd like to make a one-time payment now press 1  if not press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.BB1007_AskOneTimePayment_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/BB1007_ini_01.wav\\\">We were unable to process your last auto payment Would you like to make a one-time payment now?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/BB1007_nm1_01.wav\\\">We were unable to process your last autopayment Would you like to make a one-time payment now?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BB1007_nm2_01.wav\\\">We were unable to process your last autopayment If you'd like to make a one-time payment now press 1  if not press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BB1007_nm2_01.wav\\\">We were unable to process your last autopayment If you'd like to make a one-time payment now press 1  if not press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.autoEligPlan\n  value: GlobalVars.GetAccountDetails.isAutopayEligPlanExists\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_BB1007_AskOneTimePayment_DM\n  value: =Text(Global.BB1007_AskOneTimePayment_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_BB1007_AskOneTimePayment_DM = \"true\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.autoEligPlan = true, true, false)\n              actions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/BB1007_out_01.wav\\\">By the way, manual payments do not qualify for the self-pay discount </audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: BB1315_MakePayment_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_BB1007_AskOneTimePayment_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/BB1007_out_02.wav\\\">no problem</audio>\"\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: BB1005_CalculatePaidFeatures_DB_DA\n```", "timestamp": 1749527591.300736, "content_hash": "4aa52da5c5792acd4b65ddb64d73f14c"}