{"response": "```yaml\n- kind: Question\n  id: UW1005_UseSavedCard_YN_DM\n  displayName: UW1005_UseSavedCard_YN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/UW1005_nm1_01.wav\\\">Would you like to pay with a card you've already saved?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/UW1005_nm2_01.wav\\\">To pay with a card you've saved, say 'yes' or press 1 Otherwise, say 'no' or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/UW1005_nm3_01.wav\\\">To pay with a card you've saved to your account, press 1 To pay with a new card, press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.UW1005_UseSavedCard_YN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/UW1005_ini_01.wav\\\">Would you like to pay with a card you've saved?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/UW1005_nm1_01.wav\\\">Would you like to pay with a card you've already saved?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/UW1005_nm2_01.wav\\\">To pay with a card you've saved, say 'yes' or press 1 Otherwise, say 'no' or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/UW1005_nm3_01.wav\\\">To pay with a card you've saved to your account, press 1 To pay with a new card, press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_UW1005_UseSavedCard_YN_DM\n  value: =Text(Global.UW1005_UseSavedCard_YN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_UW1005_UseSavedCard_YN_DM = \"true\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.GlobalVars.loggedIn = true, true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: UW1105_GetWalletItems_DB_DA\n\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.askSQEntry\n              value: EWallet\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.abandonEWalletAuth\n              value: false\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: UW1004_GetOPT_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_UW1005_UseSavedCard_YN_DM = \"false\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.payingWithEWallet\n              value: false\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n```", "timestamp": 1749529854.4576356, "content_hash": "5b2d905f70b98a4a566f7bbbafccc061"}