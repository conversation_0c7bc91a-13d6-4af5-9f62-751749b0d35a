{"response": "```yaml\n- kind: Question\n  id: bm0745_ConfirmDeficientAmount_DM\n  displayName: bm0745_ConfirmDeficientAmount_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bm0745_ni1_01.wav\\\">Are you sure you want to add an amount of money that is not enough to activate your plan? Just say Yes or No</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/bm0745_ni2_01.wav\\\">To add</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/bm0745_ni2_03.wav\\\">to your account, even though it s not enough to activate your plan, press 1 To add a different amount, press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.bm0745_ConfirmDeficientAmount_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bm0745_ini_01.wav\\\">OK, but this amount won t be enough to activate your plan Are you sure you want to continue?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/bm0745_ni1_01.wav\\\">Are you sure you want to add an amount of money that is not enough to activate your plan? Just say Yes or No</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/bm0745_ni2_01.wav\\\">To add</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/bm0745_ni2_03.wav\\\">to your account, even though it s not enough to activate your plan, press 1 To add a different amount, press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_bm0745_ConfirmDeficientAmount_DM\n  value: =Text(Global.bm0745_ConfirmDeficientAmount_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_bm0745_ConfirmDeficientAmount_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/bm0745_out_01.wav\\\">Alright</audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_100ms.wav\\\"></audio>\"\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.confirmedAmount\n          value: true\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: bm0730_IsValidAmount_JDA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_bm0745_ConfirmDeficientAmount_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/bm0745_out_01.wav\\\">Alright</audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_100ms.wav\\\"></audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: bm0725_AskPaymentAmount_DM\n```", "timestamp": 1749469988.7279253, "content_hash": "5739df3658c1bd5e6bff22d42ee63fc1"}