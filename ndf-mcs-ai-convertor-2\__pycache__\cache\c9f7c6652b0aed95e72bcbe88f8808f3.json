{"response": "```yaml\n- kind: Question\n  id: BC1105_GetExpirationDate_DM\n  displayName: BC1105_GetExpirationDate_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1105_nm1_01.wav\\\">Please give me your four-digit expiration date, like 'oh five, seventeen' Or say 'more info'</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1105_nm2_01.wav\\\">Please enter the four-digit expiration date on your card You can also say 'more info' or press star </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1105_nm3_01.wav\\\">Please enter the four digits of the expiration date, including any zeros For more information, press star</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.BC1105_GetExpirationDate_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1105_ini_01.wav\\\">And the expiration date?</audio>\"\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1105_nm1_01.wav\\\">Please give me your four-digit expiration date, like 'oh five, seventeen' Or say 'more info'</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1105_nm2_01.wav\\\">Please enter the four-digit expiration date on your card You can also say 'more info' or press star </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1105_nm3_01.wav\\\">Please enter the four digits of the expiration date, including any zeros For more information, press star</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.saidOperator\n  value: \"GlobalVars.saidOperatorBC1105?GlobalVars.saidOperatorBC1105:false\"\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.implicitConfirmReject\n  value: GlobalVars.implicitConfirmReject\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.fromBC1120\n  value: GlobalVars.fromBC1120\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.disconfirmedDetails\n  value: GlobalVars.disconfirmedDetails\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.correctAllDetails\n  value: GlobalVars.correctAllDetails\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.tryOtherCardReason\n  value: GlobalVars.tryOtherCardReason\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.fromBC1120\n  value: undefined\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.saidOperatorBC1105\n  value: false\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.implicitConfirmReject\n  value: false\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.bankCardDate\n  value: BC1105_GetExpirationDate_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.bankCardDateIsValid\n  value: BC1105_GetExpirationDate_DM.nbestresults[0].interpretation.isValid\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: BC1110_CheckDateFuture_JDA\n```", "timestamp": **********.6622112, "content_hash": "c9f7c6652b0aed95e72bcbe88f8808f3"}