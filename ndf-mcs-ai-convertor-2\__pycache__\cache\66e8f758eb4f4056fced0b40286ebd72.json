{"response": "```yaml\n- kind: Question\n  id: ca0165_UnlockInstructions_DM\n  displayName: ca0165_UnlockInstructions_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ca0165_nm1_01.wav\\\">Say Repeat or press 1 Or if you dont need any more help, just hang up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ca0165_nm2_01.wav\\\">To hear that again, press 1 Or if youre all done, feel free to hang up</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ca0165_UnlockInstructions_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.osType = \"android\",\n            \"Check your device for the device unlock app and make sure it has the most recent version Use the app to check if you meet the unlock requirements and submit your requestPlease note, you must have at least six months of paid service on the ATT network to request an unlock\",\n            \"In order to review eligibility requirements and submit an unlock request, please visit A T T dot com slash deviceunlock Please note, you must have at least six months of paid service on the ATT network to request an unlock  Again thats A T T dot com slash deviceunlock\"\n        )}\n      - \"<audio src=\\\"AUDIO_LOCATION/ca0165_ini_03.wav\\\">You can Say Repeat Or if thats all you needed, go ahead and hang up</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: mainmenu\n          displayName: mainmenu\n        - id: operator\n          displayName: operator\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ca0165_ni1_01.wav\\\">Say Repeat or press 1 Or if you dont need any more help, just hang up</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ca0165_ni2_01.wav\\\">Say Repeat or press 1 Or if you dont need any more help, just hang up</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ca0165_UnlockInstructions_DM\n  value: =Text(Global.ca0165_UnlockInstructions_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ca0165_UnlockInstructions_DM = \"mainmenu\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferDestination\n          value: attPrepaid\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M01_MainMenu2.dvxml#ma1334_InitiateMainMenu_DS\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ca0165_UnlockInstructions_DM = \"operator\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.lastTransferState\n              value: ca0165_UnlockInstructions_DM\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.transferReason\n              value: AGENT_REQUEST\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.M09_MainMenu.dvxml#ma9210_NeedTransferType_DS\n```", "timestamp": 1749470300.930799, "content_hash": "66e8f758eb4f4056fced0b40286ebd72"}