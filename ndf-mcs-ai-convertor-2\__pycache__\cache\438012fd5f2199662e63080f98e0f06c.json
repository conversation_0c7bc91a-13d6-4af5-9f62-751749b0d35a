{"response": "```yaml\n- kind: Question\n  id: bm0723_AskPayNow_DM\n  displayName: bm0723_AskPayNow_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bm0723_ni1_01.wav\\\">Would you like to make a payment NOW, or hear more options?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/bm0723_ni2_01.wav\\\">Say pay now, or press 1 Or, hear more options, or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.bm0723_AskPayNow_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bm0723_ini_01.wav\\\">Would you like to make a payment NOW, or hear more options?</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: pay_now\n          displayName: pay_now\n        - id: hear_more_options\n          displayName: hear_more_options\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/bm0723_ni1_01.wav\\\">Would you like to make a payment NOW, or hear more options?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/bm0723_ni2_01.wav\\\">Say pay now, or press 1 Or, hear more options, or press 2</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_bm0723_AskPayNow_DM\n  value: =Text(Global.bm0723_AskPayNow_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_bm0723_AskPayNow_DM = \"pay_now\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/bm0723_out_01.wav\\\">Great</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: bm0725_AskPaymentAmount_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_bm0723_AskPayNow_DM = \"hear_more_options\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.transferDestination\n              value: attPrepaid\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.M01_MainMenu2.dvxml#ma1334_InitiateMainMenu_DS\n```", "timestamp": 1749469925.5180292, "content_hash": "438012fd5f2199662e63080f98e0f06c"}