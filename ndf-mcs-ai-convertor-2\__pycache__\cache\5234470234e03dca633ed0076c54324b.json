{"response": "```yaml\n- kind: Question\n  id: pr0315_NewPlanOptions_DM\n  displayName: pr0315_NewPlanOptions_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I missed that</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/pr0315_NewPlanOptions_noinput_01.wav\\\">(custom audio: com.nuance.att.application.audio.pr0315_NewPlanOptions_noinput_01)</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">Sorry</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/pr0315_NewPlanOptions_noinput_02.wav\\\">(custom audio: com.nuance.att.application.audio.pr0315_NewPlanOptions_noinput_02)</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.pr0315_NewPlanOptions_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/pr0315_NewPlanOptions_initial.wav\\\">(custom audio: com.nuance.att.application.audio.pr0315_NewPlanOptions_initial)</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: summary\n          displayName: summary\n        - id: details\n          displayName: details\n        - id: signup\n          displayName: signup\n        - id: other\n          displayName: other\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/pr0315_NewPlanOptions_noinput_01.wav\\\">(custom audio: com.nuance.att.application.audio.pr0315_NewPlanOptions_noinput_01)</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/pr0315_NewPlanOptions_noinput_02.wav\\\">(custom audio: com.nuance.att.application.audio.pr0315_NewPlanOptions_noinput_02)</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_commandgrammar\n  value: =If(Global.callType = \"611\", \"GlobalCommands.grxml?SWI_vars.allow=mainmenu&SWI_vars.operator_cnf_mode=NEVER\", \"GlobalCommands.grxml?SWI_vars.operator_cnf_mode=NEVER\")\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.returnCode\n  value: GetNewPlanOptionsList.returnCode\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.returnCode = 0, true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_dtmfgrammar1\n          value: =\"pr0315_NewPlanOptions_DM_dtmf.jsp\" + GetNewPlanOptionsList.newPlanOptionString\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_grammar1\n          value: =\"pr0315_NewPlanOptions_DM.jsp\" + GetNewPlanOptionsList.newPlanOptionString\n\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.collection_dtmfgrammar1\n      value: pr0315_NewPlanOptions_DM_dtmf.jsp\n\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.collection_grammar1\n      value: pr0315_NewPlanOptions_DM.jsp\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_pr0315_NewPlanOptions_DM\n  value: =Text(Global.pr0315_NewPlanOptions_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.callType = \"611\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_commandgrammar\n          value: GlobalCommands.grxml?SWI_vars.allow=mainmenu\n\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.collection_commandgrammar\n      value: GlobalCommands.grxml\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pr0315_NewPlanOptions_DM = \"summary\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: pr0305_NewOrOldDescription_JDA\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pr0315_NewPlanOptions_DM = \"details\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.playNewPlanDescriptions = true, true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: pr0325_NewPlanDetailsNew_PP\n\n          elseActions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: pr0320_NewPlanDetailsOld_PP\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pr0315_NewPlanOptions_DM = \"signup\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.choseOtherPlans\n          value: false\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.callType = \"611\", true, false)\n              actions:\n                - kind: BeginDialog\n                  id: begin_REPLACE_THIS\n                  dialog: topic.P02_ChoosePlan_04_Dialog\n\n          elseActions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pr0315_NewPlanOptions_DM = \"other\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.choseOtherPlans\n          value: true\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: pr0330_OtherPlansInCategory_JDA_DA\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pr0315_NewPlanOptions_DM = \"repeat\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.justHeardDetails = true, true, false)\n              actions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.playNewPlanDescriptions = true, true, false)\n                      actions:\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: pr0325_NewPlanDetailsNew_PP\n\n                  elseActions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: pr0320_NewPlanDetailsOld_PP\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.playNewPlanDescriptions = true, true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: pr0311_NewPlanSummaryNew_PP\n\n              elseActions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: pr0310_NewPlanSummaryOld_PP\n```", "timestamp": 1749471657.9414816, "content_hash": "5234470234e03dca633ed0076c54324b"}