{"response": "```yaml\n- kind: Question\n  id: cp0220_AskIfReceivedTempPIN_DM\n  displayName: cp0220_AskIfReceivedTempPIN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/cp0220_nm1_01.wav\\\">If you ve received the text message with your temporary PIN, say Yes or press 1 Otherwise, say No, or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.cp0220_AskIfReceivedTempPIN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/cp0220_ini_01.wav\\\">By now you should ve received two text messages The PIN is located at the end of the second one Did you receive it yet?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/cp0220_ni1_01.wav\\\">Did you receive a text with your temporary PIN? Say Yes or No</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_cp0220_AskIfReceivedTempPIN_DM\n  value: =Text(Global.cp0220_AskIfReceivedTempPIN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_cp0220_AskIfReceivedTempPIN_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/cp0220_out_01.wav\\\">Okay</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: cp0225_AskTempPIN_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_cp0220_AskIfReceivedTempPIN_DM = \"false\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: cp0230_NoTempPINWrap_DM\n```", "timestamp": 1749470487.5804858, "content_hash": "e1eb72ceae1b72022637b9ece44c43f7"}