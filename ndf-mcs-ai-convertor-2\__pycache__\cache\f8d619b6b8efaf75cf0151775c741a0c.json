{"response": "```yaml\n- kind: Question\n  id: ME1040_PayCardAmount_DM\n  displayName: ME1040_PayCardAmount_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ME1040_nm1_01.wav\\\">To pay the amount indicated on your card, say continue To pay a different way, say go back If you're done here, you can simply hang up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ME1040_nm2_01.wav\\\">Your card has less money than you wanted to pay To pay the amount that's written on your card, say 'continue', or press 1 Otherwise, to pay the full amount a different way, say 'go back' or press 2 If you don't want to pay now, you can simply hang up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ME1040_nm3_01.wav\\\">To pay the amount that's on your card, press 1 To go back and pay with a debit or credit card, press 2 If you don't want to pay now, you can simply hang up</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ME1040_PayCardAmount_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            // cardBalance == 10\n            Global.cardBalance = 10,\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_01.wav\\\">You said you wanted to pay</audio>\",\n              \"{Global.paymentAmount}\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_02.wav\\\">However, this Metro PCS payment card is only for $10</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_12.wav\\\">To pay the amount on your card, say continue If you'd like to pay a different way, say go back And if you'd like to call us later, you can simply hang up</audio>\"\n            ],\n\n            // cardBalance == 20\n            Global.cardBalance = 20,\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_01.wav\\\">You said you wanted to pay</audio>\",\n              \"{Global.paymentAmount}\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_03.wav\\\">However, this Metro PCS payment card is only for $20</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_12.wav\\\">To pay the amount on your card, say continue If you'd like to pay a different way, say go back And if you'd like to call us later, you can simply hang up</audio>\"\n            ],\n\n            // cardBalance == 35\n            Global.cardBalance = 35,\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_01.wav\\\">You said you wanted to pay</audio>\",\n              \"{Global.paymentAmount}\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_04.wav\\\">However, this Metro PCS payment card is only for $35 </audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_12.wav\\\">To pay the amount on your card, say continue If you'd like to pay a different way, say go back And if you'd like to call us later, you can simply hang up</audio>\"\n            ],\n\n            // cardBalance == 40\n            Global.cardBalance = 40,\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_01.wav\\\">You said you wanted to pay</audio>\",\n              \"{Global.paymentAmount}\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_05.wav\\\">However, this Metro PCS payment card is only for $40</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_12.wav\\\">To pay the amount on your card, say continue If you'd like to pay a different way, say go back And if you'd like to call us later, you can simply hang up</audio>\"\n            ],\n\n            // cardBalance == 45\n            Global.cardBalance = 45,\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_01.wav\\\">You said you wanted to pay</audio>\",\n              \"{Global.paymentAmount}\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_06.wav\\\">However, this Metro PCS payment card is only for $45</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_12.wav\\\">To pay the amount on your card, say continue If you'd like to pay a different way, say go back And if you'd like to call us later, you can simply hang up</audio>\"\n            ],\n\n            // cardBalance == 50\n            Global.cardBalance = 50,\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_01.wav\\\">You said you wanted to pay</audio>\",\n              \"{Global.paymentAmount}\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_07.wav\\\">However, this Metro PCS payment card is only for $50</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_12.wav\\\">To pay the amount on your card, say continue If you'd like to pay a different way, say go back And if you'd like to call us later, you can simply hang up</audio>\"\n            ],\n\n            // cardBalance == 60\n            Global.cardBalance = 60,\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_01.wav\\\">You said you wanted to pay</audio>\",\n              \"{Global.paymentAmount}\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_08.wav\\\">However, this Metro PCS payment card is only for $60</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_12.wav\\\">To pay the amount on your card, say continue If you'd like to pay a different way, say go back And if you'd like to call us later, you can simply hang up</audio>\"\n            ],\n\n            // cardBalance == 70\n            Global.cardBalance = 70,\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_01.wav\\\">You said you wanted to pay</audio>\",\n              \"{Global.paymentAmount}\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_09.wav\\\">However, this Metro PCS payment card is only for $70</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_12.wav\\\">To pay the amount on your card, say continue If you'd like to pay a different way, say go back And if you'd like to call us later, you can simply hang up</audio>\"\n            ],\n\n            // cardBalance == 90\n            Global.cardBalance = 90,\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_01.wav\\\">You said you wanted to pay</audio>\",\n              \"{Global.paymentAmount}\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_10.wav\\\">However, this Metro PCS payment card is only for $90</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_12.wav\\\">To pay the amount on your card, say continue If you'd like to pay a different way, say go back And if you'd like to call us later, you can simply hang up</audio>\"\n            ],\n\n            // cardBalance not in [10,20,30,40,45,50,60,70,80,90]\n            (Global.cardBalance <> 10 && Global.cardBalance <> 20 && Global.cardBalance <> 30 && Global.cardBalance <> 40 && Global.cardBalance <> 45 && Global.cardBalance <> 50 && Global.cardBalance <> 60 && Global.cardBalance <> 70 && Global.cardBalance <> 80 && Global.cardBalance <> 90),\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_01.wav\\\">You said you wanted to pay</audio>\",\n              \"{Global.paymentAmount}\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_11.wav\\\">However, this Metro PCS payment card is only for</audio>\",\n              \"{Global.cardBalance}\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_12.wav\\\">To pay the amount on your card, say continue If you'd like to pay a different way, say go back And if you'd like to call us later, you can simply hang up</audio>\"\n            ],\n\n            // Default\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_01.wav\\\">You said you wanted to pay</audio>\",\n              \"{Global.paymentAmount}\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/ME1040_ini_12.wav\\\">To pay the amount on your card, say continue If you'd like to pay a different way, say go back And if you'd like to call us later, you can simply hang up</audio>\"\n            ]\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: continue\n          displayName: continue\n        - id: go_back\n          displayName: go_back\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ME1040_nm1_01.wav\\\">To pay the amount indicated on your card, say continue To pay a different way, say go back If you're done here, you can simply hang up</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ME1040_nm2_01.wav\\\">Your card has less money than you wanted to pay To pay the amount that's written on your card, say 'continue', or press 1 Otherwise, to pay the full amount a different way, say 'go back' or press 2 If you don't want to pay now, you can simply hang up</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ME1040_nm3_01.wav\\\">To pay the amount that's on your card, press 1 To go back and pay with a debit or credit card, press 2 If you don't want to pay now, you can simply hang up</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ME1040_PayCardAmount_DM\n  value: =Text(Global.ME1040_PayCardAmount_DM_reco)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.cardBalance\n  value: GlobalVars.InquireIncommCard.faceValue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.paymentAmount\n  value: GlobalVars.paymentAmount\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ME1040_PayCardAmount_DM = \"continue\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.tryOtherCardReason\n          value: undefined\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.paymentAmount\n          value: cardBalance\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: getReturnLink()\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ME1040_PayCardAmount_DM = \"go_back\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.preferredPaymentMethod\n              value: undefined\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.metroCardFail\n              value: false\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.firstInvalidPIN\n              value: undefined\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.tryOtherCardReason\n              value: 'fail_metrocard'\n\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/ME1040_out_01.wav\\\">Alright</audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n```", "timestamp": 1749528790.5201745, "content_hash": "f8d619b6b8efaf75cf0151775c741a0c"}