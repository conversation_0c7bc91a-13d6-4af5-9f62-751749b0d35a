{"response": "```yaml\n- kind: Question\n  id: ma0135_TeachLanguage_DM\n  displayName: ma0135_TeachLanguage_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.ma0135_TeachLanguage_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            // Default prompt\n            true,\n            \"Para espanol, marca 9\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: spanish\n          displayName: spanish\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ma0135_TeachLanguage_DM\n  value: =Text(Global.ma0135_TeachLanguage_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma0135_TeachLanguage_DM = \"spanish\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.systemLanguage\n          value: spanish\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.language\n          value: es-US\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.nluEnabled\n          value: false\n\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.Setup.dvxml#ma0130_Welcome_PP\n```", "timestamp": 1749472334.0816798, "content_hash": "810c914bb7c2f6f6e5f2362a31a72c32"}