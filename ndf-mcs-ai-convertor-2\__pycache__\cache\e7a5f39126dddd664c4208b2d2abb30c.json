{"response": "```yaml\n- kind: Question\n  id: ma1345_SwitchDeviceDisambig_DM\n  displayName: ma1345_SwitchDeviceDisambig_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.accountType = \"dataOnly\" || Global.accountType = \"whp\",\n              \"Do you want to replace your device with a new one Just say Yes or No\",\n          \n              true,\n              \"Do you want to replace your phone with a new one Just say Yes or No\"\n          )\n        }\n      - |\n        {Switch(\n              true,\n              Global.accountType = \"dataOnly\" || Global.accountType = \"whp\",\n              \"If you have a new device and want to transfer your account information to it, say Yes or press 1 If not, say No or press 2\",\n          \n              true,\n              \"If you have a new phone and want to transfer your account information to it, say Yes or press 1 If not, say No or press 2\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.ma1345_SwitchDeviceDisambig_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.accountType = \"dataOnly\" || Global.accountType = \"whp\",\n              \"Are you replacing your device with a new one\",\n          \n              true,\n              \"Are you replacing your phone with a new one\"\n          )\n        }\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.accountType = \"dataOnly\" || Global.accountType = \"whp\",\n                \"Do you want to replace your device with a new one Just say Yes or No\",\n            \n                true,\n                \"Do you want to replace your phone with a new one Just say Yes or No\"\n            )\n          }\n        - |\n          {Switch(\n                true,\n                Global.accountType = \"dataOnly\" || Global.accountType = \"whp\",\n                \"If you have a new device and want to transfer your account information to it, say Yes or press 1 If not, say No or press 2\",\n            \n                true,\n                \"If you have a new phone and want to transfer your account information to it, say Yes or press 1 If not, say No or press 2\"\n            )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ma1345_SwitchDeviceDisambig_DM\n  value: =Text(Global.ma1345_SwitchDeviceDisambig_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ma1345_SwitchDeviceDisambig_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: changeDevice\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ma1335_MainMenuRouting_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ma1345_SwitchDeviceDisambig_DM = \"false\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: ma1350_SetupServiceDisambig_DM\n```", "timestamp": **********.7117739, "content_hash": "e7a5f39126dddd664c4208b2d2abb30c"}