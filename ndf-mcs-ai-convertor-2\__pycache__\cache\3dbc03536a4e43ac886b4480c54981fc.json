{"response": "```yaml\n- kind: Question\n  id: PE1225_OfferStore_DM\n  displayName: PE1225_OfferStore_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/PE1225_ini_01.wav\\\">To see where you can pay in cash near you, say 'find a store' Otherwise, you can hang up now</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/PE1225_nm2_01.wav\\\">Please say 'find a store' or press 1 If you're done, you can simply hang up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/PE1225_nm3_01.wav\\\">Please say 'find a store' or press 1 If you're done, you can simply hang up</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.PE1225_OfferStore_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/PE1225_ini_01.wav\\\">To see where you can pay in cash near you, say 'find a store' Otherwise, you can hang up now</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: inquire-store_location\n          displayName: inquire-store_location\n        - id: request-extension_pe\n          displayName: request-extension_pe\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/PE1225_ini_01.wav\\\">To see where you can pay in cash near you, say 'find a store' Otherwise, you can hang up now</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/PE1225_ni2_01.wav\\\">Please say 'find a store' or press 1 If you're done, you can simply hang up</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/PE1225_ni3_01.wav\\\">Please say 'find a store' or press 1 If you're done, you can simply hang up</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_PE1225_OfferStore_DM\n  value: =Text(Global.PE1225_OfferStore_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_PE1225_OfferStore_DM = \"inquire-store_location\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.storeLocatorReason\n          value: payment\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: PE1230_FindStore_SD\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_PE1225_OfferStore_DM = \"request-extension_pe\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/PE1225_out_01.wav\\\">Sorry, your account's NOT eligible for an extension right now</audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: PE1225_OfferStore_DM\n```", "timestamp": **********.280169, "content_hash": "3dbc03536a4e43ac886b4480c54981fc"}