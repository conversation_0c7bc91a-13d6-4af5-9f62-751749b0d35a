{"response": "```yaml\n- kind: Question\n  id: ES2005_ApproveNewPayment_DM\n  displayName: ES2005_ApproveNewPayment_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ES2005_ini_01.wav\\\">If we switch your phone and change your plan, your payment due now will be</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ES2005_ini_02.wav\\\">If we switch your phone and update your account, your payment due now will be</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ES2005_ini_03.wav\\\">By saying  continue , you agree that you ll need to pay right away to avoid a service interruption</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ES2005_ini_04.wav\\\">Should I go ahead? Say  continue  or  cancel </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ES2005_nm2_01.wav\\\">If you re OK with your payment due now, say  continue  and I ll switch your phone Otherwise, say  cancel  and I won t make *any* changes to your account or phone</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ES2005_nm2_01.wav\\\">If you re OK with your payment due now, say  continue  and I ll switch your phone Otherwise, say  cancel  and I won t make *any* changes to your account or phone</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ES2005_ApproveNewPayment_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.esnChangedPlan = true,\n            \"If we switch your phone and change your plan, your payment due now will be\",\n            Global.esnChangedPlan = false,\n            \"If we switch your phone and update your account, your payment due now will be\",\n            \"\"\n        )}\n        {Global.dueImmediatelyAmount}\n        <audio src=\"AUDIO_LOCATION/silence_250ms.wav\">test</audio>\n        <audio src=\"AUDIO_LOCATION/ES2005_ini_03.wav\">By saying  continue , you agree that you ll need to pay right away to avoid a service interruption</audio>\n        <audio src=\"AUDIO_LOCATION/ES2005_ini_04.wav\">Should I go ahead? Say  continue  or  cancel </audio>\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: continue\n          displayName: continue\n        - id: cancel\n          displayName: cancel\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ES2005_ini_01.wav\\\">If we switch your phone and change your plan, your payment due now will be</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ES2005_ini_02.wav\\\">If we switch your phone and update your account, your payment due now will be</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ES2005_ini_03.wav\\\">By saying  continue , you agree that you ll need to pay right away to avoid a service interruption</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ES2005_ini_04.wav\\\">Should I go ahead? Say  continue  or  cancel </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ES2005_nm2_01.wav\\\">If you re OK with your payment due now, say  continue  and I ll switch your phone Otherwise, say  cancel  and I won t make *any* changes to your account or phone</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ES2005_nm2_01.wav\\\">If you re OK with your payment due now, say  continue  and I ll switch your phone Otherwise, say  cancel  and I won t make *any* changes to your account or phone</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.aniMatch\n  value: GlobalVars.aniMatch\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.switchLinesSuccess\n  value: GlobalVars.switchLinesSuccess\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.esnChangedPlan\n  value: GlobalVars.esnChangedPlan\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.dueImmediatelyAmount\n  value: GlobalVars.SubmitChangeDevice.dueImmediatlyAmount\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ES2005_ApproveNewPayment_DM\n  value: =Text(Global.ES2005_ApproveNewPayment_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ES2005_ApproveNewPayment_DM = \"continue\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/ES2005_out_01.wav\\\">Great!</audio>\"\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.aniMatch = true && Global.switchLinesSuccess = false, true, false)\n              actions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/ES2005_out_02.wav\\\">Again, this call will drop when I transfer your service to your new phone Nothing to worry about! Here we go</audio>\"\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.isOrderApproved\n          value: true\n\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.CareESNSwap_Main_Cont.dvxml#ES1510_SubmitChangeDevice_DB\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ES2005_ApproveNewPayment_DM = \"cancel\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/ES2005_out_03.wav\\\">No problem, I won t change anything</audio>\"\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.cancelESNSwap\n              value: true\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.ActivationTable.ACTIVATION_STATUS\n              value: '134'\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n```", "timestamp": 1749527970.6825333, "content_hash": "c2cec4d621530d93c9174a1dc9ab19b0"}