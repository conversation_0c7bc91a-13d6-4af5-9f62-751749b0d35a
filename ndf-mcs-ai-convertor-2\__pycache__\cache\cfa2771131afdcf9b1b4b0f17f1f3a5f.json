{"response": "```yaml\n- kind: Question\n  id: aa4171_SecondaryCardWrapUpDTMF_DM\n  displayName: aa4171_SecondaryCardWrapUpDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - aa4171_SecondaryCardWrapUpDTMF_DM_initial\n      - aa4171_SecondaryCardWrapUpDTMF_DM_initial\n\n  alwaysPrompt: true\n  variable: Global.aa4171_SecondaryCardWrapUpDTMF_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa4171_ini_01.wav\\\">To hear that again, Press 1To request a secondary cardholder application, Press 2For other options, Press 3Or if you're done here, hang up</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: repeat\n          displayName: repeat\n\n        - id: secondary_card\n          displayName: secondary_card\n\n        - id: other_options\n          displayName: other_options\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - aa4171_SecondaryCardWrapUpDTMF_DM_initial\n        - aa4171_SecondaryCardWrapUpDTMF_DM_initial\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa4171_SecondaryCardWrapUpDTMF_DM\n  value: =Text(Global.aa4171_SecondaryCardWrapUpDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa4171_SecondaryCardWrapUpDTMF_DM = \"repeat\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa4170_SecondaryCardFeesPlayout_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa4171_SecondaryCardWrapUpDTMF_DM = \"secondary_card\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.transferAllowed\n              value: true\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.transferReason\n              value: secondaryCard\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa9805_ProcessTransfer_PP\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_aa4171_SecondaryCardWrapUpDTMF_DM = \"other_options\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: aa4105_OtherOptionsMenuDTMF_DM\n```", "timestamp": 1749544040.7021058, "content_hash": "cfa2771131afdcf9b1b4b0f17f1f3a5f"}