{"response": "```yaml\n- kind: Question\n  id: pa0430_AutoRenewSuccessWrap_DM\n  displayName: pa0430_AutoRenewSuccessWrap_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/pa0430_ni1_01.wav\\\">Say Repeat or press 1, or Main Menu or press star Or if you re done, just hang up</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/pa0430_nm2_01.wav\\\">To hear that again, press 1 For anything else, press star Or feel free to hang up</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.pa0430_AutoRenewSuccessWrap_DM_reco\n  prompt:\n    speak:\n      - \"{CustomAudio: com.nuance.att.application.audio.pa0430_AutoRenewSuccessWrap_DM_initial}\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: mainmenu\n          displayName: mainmenu\n        - id: operator\n          displayName: operator\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/pa0430_ni1_01.wav\\\">Say Repeat or press 1, or Main Menu or press star Or if you re done, just hang up</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_pa0430_AutoRenewSuccessWrap_DM\n  value: =Text(Global.pa0430_AutoRenewSuccessWrap_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pa0430_AutoRenewSuccessWrap_DM = \"mainmenu\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferDestination\n          value: attPrepaid\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M01_MainMenu2_Dialog\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pa0430_AutoRenewSuccessWrap_DM = \"operator\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferReason\n          value: AGENT_REQUEST\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M09_MainMenu_Dialog\n\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pa0430_AutoRenewSuccessWrap_DM = \"repeat\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: pa0430_AutoRenewSuccessWrap_DM\n```", "timestamp": 1749471900.2591887, "content_hash": "ed1638f30901fe974aa80727fd6031e3"}