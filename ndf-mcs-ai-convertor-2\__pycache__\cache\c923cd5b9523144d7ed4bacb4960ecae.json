{"response": "```yaml\n- kind: Question\n  id: bc0435_AskLast4SSN_DM\n  displayName: bc0435_AskLast4SSN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bc0435_ni1_01.wav\\\">Please say or enter the last 4 digits of your social security number</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/bc0435_ni2_01.wav\\\">Please enter the last 4 digits of your social security number</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.bc0435_AskLast4SSN_DM_reco\n  prompt:\n    speak:\n      - \"{Audio: com.nuance.att.application.audio.bc0435_AskLast4SSN_initial}\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/bc0435_ni1_01.wav\\\">Please say or enter the last 4 digits of your social security number</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/bc0435_ni2_01.wav\\\">Please enter the last 4 digits of your social security number</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_bc0435_AskLast4SSN_DM\n  value: =Text(Global.bc0435_AskLast4SSN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_bc0435_AskLast4SSN_DM = \"dont_have\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/bc0435_out_02.wav\\\">Okay</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: bc0410_VerificationIntro_PP\n\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.last4SSN\n      value: bc0435_AskLast4SSN_DM.returnvalue\n\n    - kind: SendActivity\n      id: sendActivity_REPLACE_THIS\n      activity:\n        speak:\n          - \"<audio src=\\\"AUDIO_LOCATION/bc0435_out_01.wav\\\">Thanks</audio>\"\n\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.vestaLoginStatus = \"verified\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: bc0440_RetryProcessPayment_DB_DA\n\n      elseActions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: bc0441_AnonymousRetryProcessPayment_DB_DA\n```", "timestamp": 1749473893.4489503, "content_hash": "c923cd5b9523144d7ed4bacb4960ecae"}