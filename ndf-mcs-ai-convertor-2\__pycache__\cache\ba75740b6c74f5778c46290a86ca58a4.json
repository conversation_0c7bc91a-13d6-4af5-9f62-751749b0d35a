{"response": "```yaml\n- kind: Question\n  id: VS1025_SignUp_DM\n  displayName: VS1025_SignUp_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/VS1025_nm1_01.wav\\\">Would you like to sign up for </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/VS1025_nm1_02.wav\\\">Please say Yes or No </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/VS1025_nm2_01.wav\\\">Would you like to sign up for this? Say yes or press 1, or no or press 2 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/VS1025_nm2_01.wav\\\">Would you like to sign up for this? Say yes or press 1, or no or press 2 </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.VS1025_SignUp_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.playOneFeature = true && Global.isThirdPartyFeature <> true,\n            \"<audio src=\\\"AUDIO_LOCATION/VS1025_ini_01.wav\\\">Theres one feature you can sign up for</audio>\",\n            Global.playOneFeature = true && Global.isThirdPartyFeature = true,\n            \"<audio src=\\\"AUDIO_LOCATION/VS1025_ini_02.wav\\\">Theres one third party feature you can sign up for</audio>\",\n            Global.playOneFeature = true,\n            [\n              \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\",\n              \"{selectedFeature}\", \n              \"<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\"\n            ],\n            true,\n            [\n              \"{selectedFeatureDetails}\",\n              \"<audio src=\\\"AUDIO_LOCATION/silence_1000ms.wav\\\">test</audio>\",\n              (Global.isThirdPartyFeature = true && Global.heardThirdPartyInfo <> true) ? \"<audio src=\\\"AUDIO_LOCATION/VS1025_ini_04.wav\\\">Third party charges will appear separately on your bill To block those purchases, check your account settings on metrobyt-mobilecom, or say operator now to find out more</audio>\" : \"\",\n              (Global.isThirdPartyFeature = true && (Global.fromIOM = true || Global.heardThirdPartyInfo <> true)) ? \"<audio src=\\\"AUDIO_LOCATION/silence_1000ms.wav\\\">test</audio>\" : \"\",\n              \"<audio src=\\\"AUDIO_LOCATION/VS1025_ini_11.wav\\\">Would you like to hear that again, sign up, or cancel?</audio>\",\n              \"<audio src=\\\"AUDIO_LOCATION/VS1025_ini_12.wav\\\">You can also say 'repeat that'</audio>\"\n            ]\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/VS1025_nm1_01.wav\\\">Would you like to sign up for </audio>\"\n        - \"{selectedFeatureDetailsMedialPromptURL}\"\n        - |\n          {Switch(\n            true,\n            Global.fromIOM = true,\n            \"<audio src=\\\"AUDIO_LOCATION/VS1025_nm1_03.wav\\\">say 'repeat' Otherwise, please say 'sign Up' or 'skip it'</audio>\",\n            true,\n            \"<audio src=\\\"AUDIO_LOCATION/VS1025_nm1_02.wav\\\">Please say Yes or No </audio>\"\n          )}\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"{selectedFeatureDetails}\"\n        - |\n          {Switch(\n            true,\n            Global.fromIOM = true,\n            \"<audio src=\\\"AUDIO_LOCATION/VS1025_nm2_02.wav\\\">Please say 'sign up' or press 1, or say 'skip it' or press 2</audio>\",\n            true,\n            \"<audio src=\\\"AUDIO_LOCATION/VS1025_nm2_01.wav\\\">Would you like to sign up for this? Say yes or press 1, or no or press 2 </audio>\"\n          )}\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"{selectedFeatureDetails}\"\n        - |\n          {Switch(\n            true,\n            Global.fromIOM = true,\n            \"<audio src=\\\"AUDIO_LOCATION/VS1025_nm2_02.wav\\\">Please say 'sign up' or press 1, or say 'skip it' or press 2</audio>\",\n            true,\n            \"<audio src=\\\"AUDIO_LOCATION/VS1025_nm2_01.wav\\\">Would you like to sign up for this? Say yes or press 1, or no or press 2 </audio>\"\n          )}\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.selectedFeature\n  value: GlobalVars.selectedFeature\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.isThirdPartyFeature\n  value: isSelectedFeatureAThirdPartyFeature(GlobalVars.GetAvailableFeatureOffers.featureSocsThirdParty, GlobalVars.selectedFeature)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.playOneFeature\n  value: GlobalVars.playOneFeature\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.fromIOM\n  value: GlobalVars.fromIOM\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.heardThirdPartyInfo\n  value: GlobalVars.heardThirdPartyInfo\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.addFeatureAction\n  value: \"\"\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_VS1025_SignUp_DM\n  value: =Text(Global.VS1025_SignUp_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.isThirdPartyFeature = true, true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.heardThirdPartyInfo\n          value: true\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: |\n            =If(Global.Var_VS1025_SignUp_DM = \"yes\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: |\n                    =If(Global.addFeatureAction = \"Custom after selected\", true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: VS1205_CheckServiceIndicatorAddMessage_JDA\n\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: |\n                        =If(Global.addFeatureAction = \"Transfer after selected\", true, false)\n                      actions:\n                        - kind: BeginDialog\n                          id: begin_REPLACE_THIS\n                          dialog: topic.Voicestore_Routing.dvxml#VS1315_GoToCallTransfer_SD\n\n                  elseActions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.securityRequired\n                      value: true\n\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.addFeatures\n                      value: GlobalVars.selectedFeature\n\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: VS1035_CheckNeedPIN_JDA\n\n        - id: conditionItem_REPLACE_THIS\n          condition: |\n            =If(Global.Var_VS1025_SignUp_DM = \"no\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: VS1045_AnotherFeature_DM\n\n        - id: conditionItem_REPLACE_THIS\n          condition: |\n            =If(Global.Var_VS1025_SignUp_DM = \"repeat\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: VS1025_SignUp_DM\n```", "timestamp": 1749530181.855647, "content_hash": "ba75740b6c74f5778c46290a86ca58a4"}