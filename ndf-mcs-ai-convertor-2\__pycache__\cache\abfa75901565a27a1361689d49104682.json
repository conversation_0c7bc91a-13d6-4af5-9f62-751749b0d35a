{"response": "```yaml\n- kind: Question\n  id: aa6027_CCBalanceWrapUp_DM\n  displayName: aa6027_CCBalanceWrapUp_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.aa6027_CCBalanceWrapUp_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa6027_ini_01.wav\\\">To hear that again, say  Repeat That   to make a payment, say  Make a Payment  for more details about your child care account, say  More Details  or to return to the main menu, say  Main Menu </audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: repeat\n          displayName: repeat\n\n        - id: make_payment\n          displayName: make_payment\n\n        - id: more_details\n          displayName: more_details\n\n        - id: main_menu\n          displayName: main_menu\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa6027_CCBalanceWrapUp_DM\n  value: =Text(Global.aa6027_CCBalanceWrapUp_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa6027_CCBalanceWrapUp_DM = \"repeat\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/aa6027_out_01.wav\\\">Again</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa6025_CCBalance_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa6027_CCBalanceWrapUp_DM = \"make_payment\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.globalVariables.currentTask\n              value: CCPayment\n\n            # EBT113 event log script is not represented in YAML as per instructions\n\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.ccProviderInfo.ccPinSelect = \"N\", true, false)\n                  actions:\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: aa6020_CCMustHavePinSelected_PP\n\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.dnisInfo.ccTieredState = true, true, false)\n                      actions:\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: aa6045_CCConfirmProviderIdDTMF_DM\n\n                  elseActions:\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/aa6027_out_02.wav\\\">Sure [audio icon] You ll be able to use your voice while making your payment</audio>\"\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: aa6030_CCProviderID_DM\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_aa6027_CCBalanceWrapUp_DM = \"more_details\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.globalVariables.transferAllowed\n                  value: true\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: transferHandler_CS\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.Var_aa6027_CCBalanceWrapUp_DM = \"main_menu\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.globalVariables.nextStep\n                      value: main_menu\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: aa1015_NextStepHandling_JDA\n```", "timestamp": 1749543732.6200259, "content_hash": "abfa75901565a27a1361689d49104682"}