{"response": "```yaml\n- kind: Question\n  id: AC2320_CollectICCID_DM\n  displayName: AC2320_CollectICCID_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AC2320_nm1_01.wav\\\">Please enter the number on the SIM card you want to use in your new phone It s 19 digits long, and ends in the letter F Enter only the *numbers* To hear how you can find the number again, say  instructions </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AC2320_nm2_01.wav\\\">If you have the 19-digit SIM card number, please enter it now on your keypad, without the letter F To repeat the instructions to *find* the number, press star</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AC2320_nm2_01.wav\\\">If you have the 19-digit SIM card number, please enter it now on your keypad, without the letter F To repeat the instructions to *find* the number, press star</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.AC2320_CollectICCID_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.iccidFailChecksum = true,\n            \"That doesn t look like a valid SIM card number Let s try one more time to be sure To hear the instructions to *find* the number, press star Otherwise, go ahead and enter it one more time, using your keypad\",\n            true,\n            \"Say or enter the whole number, but without the letter F\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: instructions\n          displayName: instructions\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/AC2320_nm1_01.wav\\\">Please enter the number on the SIM card you want to use in your new phone It s 19 digits long, and ends in the letter F Enter only the *numbers* To hear how you can find the number again, say  instructions </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AC2320_nm2_01.wav\\\">If you have the 19-digit SIM card number, please enter it now on your keypad, without the letter F To repeat the instructions to *find* the number, press star</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AC2320_nm2_01.wav\\\">If you have the 19-digit SIM card number, please enter it now on your keypad, without the letter F To repeat the instructions to *find* the number, press star</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.iccidFailChecksum\n  value: \"GlobalVars.iccidFailChecksum != undefined ? GlobalVars.iccidFailChecksum : false  \"\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_AC2320_CollectICCID_DM\n  value: =Text(Global.AC2320_CollectICCID_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AC2320_CollectICCID_DM = \"instructions\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.iccidFailChecksum\n          value: false\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/AC2320_out_01.wav\\\">Alright, instructions</audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: AC2310_FindICCIDInstructions_PP\n\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.GlobalVars.iccidSerialNumber\n      value: AC2320_CollectICCID_DM.returnvalue\n\n    - kind: GotoAction\n      id: goto_REPLACE_THIS\n      actionId: AC2325_ICCIDPassChecksum_JDA_DA\n```\n", "timestamp": 1749558768.9395568, "content_hash": "423236bd72ed7274282608ba4c01f081"}