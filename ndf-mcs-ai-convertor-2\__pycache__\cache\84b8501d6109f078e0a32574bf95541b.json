{"response": "```yaml\n- kind: Question\n  id: SP1317_AskPrimary_DM\n  displayName: SP1317_AskPrimary_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/SP1317_nm1_01.wav\\\">Sorry, I didn't get that If you'd like to make this your primary payment card say 'yes' or press 1  If not say 'no' or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/SP1317_nm2_01.wav\\\">I still didnt get that  To set this card as your primary payment card press 1  if not press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.SP1317_AskPrimary_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/SP1317_ini_01.wav\\\">And would you like to make this your primary payment card?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/SP1317_ini_01.wav\\\">And would you like to make this your primary payment card?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/SP1317_nm2_01.wav\\\">I still didnt get that  To set this card as your primary payment card press 1  if not press 2</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_SP1317_AskPrimary_DM\n  value: =Text(Global.SP1317_AskPrimary_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_SP1317_AskPrimary_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.defaultPaymentMethod\n          value: true\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: SP1330_ManageCards_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_SP1317_AskPrimary_DM = \"false\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.callType\n              value: undefined\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: SP1330_ManageCards_SD\n```", "timestamp": 1749529613.421675, "content_hash": "84b8501d6109f078e0a32574bf95541b"}