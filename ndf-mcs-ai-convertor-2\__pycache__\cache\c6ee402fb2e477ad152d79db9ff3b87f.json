{"response": "```yaml\n- kind: Question\n  id: TT1030_WiFiTipOfferYN_DM\n  displayName: TT1030_WiFiTipOfferYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TT1030_nm1_01.wav\\\">Do you want to try turning off WiFi now?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TT1030_nm2_01.wav\\\">If you want to turn off WiFi now, say 'yes' or press 1 Otherwise, say 'no' or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.TT1030_WiFiTipOfferYN_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.fromTT1020 = true,\n            [\n              \"Alright\",\n              \"test\"\n            ],\n            Global.transferDestination = \"agent\",\n            \"I have one last tip for you  and if this one doesn t work, I ll get you to an agent Here s the tip You can use your phone on the Metro network or over Wi-Fi To make sure that Wi-Fi is not the cause of your problem, you should try turning it off but you must be in a Metro coverage area Do you want to try that now\",\n            Global.transferDestination <> \"agent\",\n            \"I have one last tip for you Here it is You can use your phone on the Metro network or over Wi-Fi To make sure that Wi-Fi is not the cause of your problem, you should try turning it off but you must be in a Metro coverage area Do you want to try that now?\"\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/TT1030_ni1_01.wav\\\">Do you want to try turning off WiFi now?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/TT1030_ni2_01.wav\\\">If you want to turn off WiFi now, say 'yes' or press 1 Otherwise, say 'no' or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.fromTT1020\n  value: GlobalVars.fromTT1020\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_TT1030_WiFiTipOfferYN_DM\n  value: =Text(Global.TT1030_WiFiTipOfferYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TT1030_WiFiTipOfferYN_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.fromTT1020\n          value: false\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TT1035_WiFiTipWaitSBI_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_TT1030_WiFiTipOfferYN_DM = \"false\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.fromTT1020\n              value: false\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.transferDestination = \"agent\", true, false)\n                  actions:\n                    - kind: ConditionGroup\n                      id: conditionGroup_REPLACE_THIS\n                      conditions:\n                        - id: conditionItem_REPLACE_THIS\n                          condition: =If(Global.usingBrokenDevice = \"yes\", true, false)\n                          actions:\n                            - kind: GotoAction\n                              id: goto_REPLACE_THIS\n                              actionId: TT1040_TransferOffer_DM\n                      elseActions:\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.GlobalVars.maxRetryTroubleshooting\n                          value: false\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: TT1045_Transfer_PP\n              elseActions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: TT1038_StoreOffer_DM\n```", "timestamp": 1749530381.8056927, "content_hash": "c6ee402fb2e477ad152d79db9ff3b87f"}