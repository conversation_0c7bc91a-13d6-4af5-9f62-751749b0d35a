{"response": "```yaml\n- kind: Question\n  id: st1035_AskFixedProblem_DM\n  displayName: st1035_AskFixedProblem_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.intent = \"techSupportCall\",\n              \"Are you able to make and receive calls now?\",\n              Global.intent = \"techSupportCallText\",\n              \"Are you able to make and receive calls and texts now?\",\n              Global.intent = \"techSupportData\",\n              \"Is your data working now?\",\n              \"Say Yes or No, or say I am Not Sure\"\n          )\n        }\n      - |\n        {Switch(\n              true,\n              Global.intent = \"techSupportCall\",\n              \"If that solved your problem and youre now able to make and receive calls, say Yes or press 1 If you still arent able to call, say No or press 2\",\n              Global.intent = \"techSupportCallText\",\n              \"If that solved your problem and youre now able to make and receive calls and texts, say Yes or press 1 If you still arent able to call or text, say No or press 2\",\n              Global.intent = \"techSupportData\",\n              \"If that solved your problem and youre now able to use data, say Yes or press 1 If you still arent able to use data, say No or press 2\",\n              \"You can also say I am Not Sure or press 3\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.st1035_AskFixedProblem_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.intent = \"techSupportCall\",\n              \"Were you able to make your call?\",\n              Global.intent = \"techSupportCallText\",\n              \"Were you able to make your call or send your text?\",\n              Global.intent <> \"techSupportCallText\",\n              \"Did the website load?\",\n              \"Did the website load?\"\n          )\n        }\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.intent = \"techSupportCall\",\n                \"Are you able to make and receive calls now?\",\n                Global.intent = \"techSupportCallText\",\n                \"Are you able to make and receive calls and texts now?\",\n                Global.intent = \"techSupportData\",\n                \"Is your data working now?\",\n                \"Say Yes or No, or say I am Not Sure\"\n            )\n          }\n        - |\n          {Switch(\n                true,\n                Global.intent = \"techSupportCall\",\n                \"If that solved your problem and youre now able to make and receive calls, say Yes or press 1 If you still arent able to call, say No or press 2\",\n                Global.intent = \"techSupportCallText\",\n                \"If that solved your problem and youre now able to make and receive calls and texts, say Yes or press 1 If you still arent able to call or text, say No or press 2\",\n                Global.intent = \"techSupportData\",\n                \"If that solved your problem and youre now able to use data, say Yes or press 1 If you still arent able to use data, say No or press 2\",\n                \"You can also say I am Not Sure or press 3\"\n            )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_st1035_AskFixedProblem_DM\n  value: =Text(Global.st1035_AskFixedProblem_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_st1035_AskFixedProblem_DM = \"yes\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.intent = \"techSupportData\" && (Global.deviceType = \"featurephone\" || Global.deviceType = \"smartphone\" || Global.deviceType = \"tablet\"), true, false)\n              actions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/st1035_out_05.wav\\\">Great, you should be all set! You can go ahead and turn Wi-Fi back on</audio>\"\n          elseActions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/st1035_out_01.wav\\\">Great!</audio>\"\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\"></audio>\"\n\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.S02_TechSupport_01_Dialog\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_st1035_AskFixedProblem_DM = \"no\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/st1035_out_04.wav\\\">Alright I'll need to connect you to someone for more assistance</audio>\"\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.transferReason\n              value: BUSINESS_RULE\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.transferPrompt\n              value: short\n\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.M09_MainMenu_Dialog\n```", "timestamp": 1749472463.615882, "content_hash": "f34d4a2ea11d240154a12f8ec7c6ff08"}