{"response": "```yaml\n- kind: Question\n  id: bm1245_RefillCompleteWrapMenu_DM\n  displayName: bm1245_RefillCompleteWrapMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bm1245_ni1_01.wav\\\">If you re done, just hang up Or if you need help with something else, say Main Menu or press star</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/bm1245_nm2_01.wav\\\">If you don t need help with anything else, you can hang up Otherwise, to return to the main menu, press star</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.bm1245_RefillCompleteWrapMenu_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bm1245_ini_01.wav\\\">If you re done, go ahead and hang up For anything else, say Main Menu</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: mainmenu\n          displayName: mainmenu\n        - id: operator\n          displayName: operator\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/bm1245_ni1_01.wav\\\">If you re done, just hang up Or if you need help with something else, say Main Menu or press star</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_bm1245_RefillCompleteWrapMenu_DM\n  value: =Text(Global.bm1245_RefillCompleteWrapMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_bm1245_RefillCompleteWrapMenu_DM = \"mainmenu\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferDestination\n          value: attPrepaid\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M01_MainMenu2.dvxml#ma1334_InitiateMainMenu_DS\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_bm1245_RefillCompleteWrapMenu_DM = \"operator\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.lastTransferState\n              value: bm1245_RefillCompleteWrapMenu_DM\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.transferReason\n              value: AGENT_REQUEST\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.M09_MainMenu.dvxml#ma9210_NeedTransferType_DS\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_bm1245_RefillCompleteWrapMenu_DM = \"repeat\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: bm1245_RefillCompleteWrapMenu_DM\n```", "timestamp": 1749469935.1601849, "content_hash": "a07c802d15e5abddf2a51b7811663335"}