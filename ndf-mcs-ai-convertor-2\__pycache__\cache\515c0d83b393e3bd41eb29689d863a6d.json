{"response": "```yaml\n- kind: Question\n  id: sl0110_AskLostStolenFound_DM\n  displayName: sl0110_AskLostStolenFound_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n              \"Please say Report A Lost Phone or press 1, Report A Stolen Phone or press 2, or Found My Phone or press 3\",\n          \n              true,\n              \"Please say Report A Lost Device or press 1, Report A Stolen Device or press 2, or Found My Device or press 3\"\n          )\n        }\n      - |\n        {Switch(\n              true,\n              Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n              \"If your phone is lost, press 1 If it was stolen, press 2 Or if Youve recovered a phone that was already reported as lost or stolen, press 3\",\n          \n              true,\n              \"If your device is lost, press 1 If it was stolen, press 2 Or if Youve recovered a device that was already reported as lost or stolen, press 3\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.sl0110_AskLostStolenFound_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n              true,\n              Global.accountType = \"dataOnly\" || Global.accountType = \"whp\",\n              \"Say Report a Lost Device or Report a Stolen Device Or if Youve recovered your device, say Found My Device\",\n          \n              true,\n              \"Say Report a Lost Phone or Report a Stolen Phone Or if Youve recovered your phone, say Found My Phone\"\n          )\n        }\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: lost\n          displayName: lost\n        - id: stolen\n          displayName: stolen\n        - id: recovered\n          displayName: recovered\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n                true,\n                Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n                \"Please say Report A Lost Phone or press 1, Report A Stolen Phone or press 2, or Found My Phone or press 3\",\n            \n                true,\n                \"Please say Report A Lost Device or press 1, Report A Stolen Device or press 2, or Found My Device or press 3\"\n            )\n          }\n        - |\n          {Switch(\n                true,\n                Global.accountType = \"gophone\" || Global.accountType = \"spareOne\",\n                \"If your phone is lost, press 1 If it was stolen, press 2 Or if Youve recovered a phone that was already reported as lost or stolen, press 3\",\n            \n                true,\n                \"If your device is lost, press 1 If it was stolen, press 2 Or if Youve recovered a device that was already reported as lost or stolen, press 3\"\n            )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_sl0110_AskLostStolenFound_DM\n  value: =Text(Global.sl0110_AskLostStolenFound_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_sl0110_AskLostStolenFound_DM = \"lost\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.reasonCode\n          value: lost\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: lostDevice\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: sl0115_NeedsLogin_JDA_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_sl0110_AskLostStolenFound_DM = \"stolen\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.reasonCode\n              value: stolen\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.intent\n              value: stolenDevice\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: sl0115_NeedsLogin_JDA_DA\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_sl0110_AskLostStolenFound_DM = \"recovered\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.reasonCode\n                  value: st\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.intent\n                  value: recoveredDevice\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: sl0115_NeedsLogin_JDA_DA\n```", "timestamp": 1749471910.2361774, "content_hash": "515c0d83b393e3bd41eb29689d863a6d"}