{"response": "```yaml\n- kind: Question\n  id: pf0125_RefillOrChangePlan_DM\n  displayName: pf0125_RefillOrChangePlan_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"com.nuance.att.application.audio.pf0125_RefillOrChangePlan_ni1_01\"\n      - \"com.nuance.att.application.audio.pf0125_RefillOrChangePlan_ni2_01\"\n\n  alwaysPrompt: true\n  variable: Global.pf0125_RefillOrChangePlan_DM_reco\n  prompt:\n    speak:\n      - \"com.nuance.att.application.audio.pf0125_RefillOrChangePlan_initial\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: refill\n          displayName: refill\n        - id: change_plan\n          displayName: change_plan\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"com.nuance.att.application.audio.pf0125_RefillOrChangePlan_ni1_01\"\n        - \"com.nuance.att.application.audio.pf0125_RefillOrChangePlan_ni2_01\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_pf0125_RefillOrChangePlan_DM\n  value: =Text(Global.pf0125_RefillOrChangePlan_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_pf0125_RefillOrChangePlan_DM = \"refill\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.intent\n          value: refillAccount\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: pf0130_InactivePackageRefill_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_pf0125_RefillOrChangePlan_DM = \"change_plan\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.intent\n              value: changePlan\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: pf0135_InactivePackageChangePlan_SD\n```", "timestamp": **********.519904, "content_hash": "717a91f4dc26d3db4312aae97a81d6eb"}