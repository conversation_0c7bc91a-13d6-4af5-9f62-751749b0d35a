{"response": "```yaml\n- kind: Question\n  id: aa2540_SSN4DTMF_DM\n  displayName: aa2540_SSN4DTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"\"\n      - |\n        {Switch(\n              true,\n              Global.dtmfOnlyFlag = true,\n              [\n                \"<audio src=\\\"AUDIO_LOCATION/aa2540_nm2_02.wav\\\">Sorry, I still didn t get that</audio>\",\n                \"\"\n              ]\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.aa2540_SSN4DTMF_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n\n            // Case 1: validationCriteriaVariables.currentValidationType = \"PSS\" || validationCriteriaVariables.currentValidationType = \"PSN\"\n            Global.validationCriteriaVariables.currentValidationType = \"PSS\" || Global.validationCriteriaVariables.currentValidationType = \"PSN\",\n            {Switch(\n                true,\n                Global.dnisInfo.ssn4PrimaryPromptDtmfOn = true,\n                dnisInfo.ssn4PrimaryPromptDtmf,\n                \"Please enter the last four digits of the primary card holder s social security number\"\n            )},\n\n            // Case 2: else\n            true,\n            {Switch(\n                true,\n                Global.dnisInfo.ssn4PromptDtmfOn = true,\n                dnisInfo.ssn4PromptDtmf,\n                \"Please enter the last four digits of your social security number\"\n            )}\n        )}\n\n  entity: StringPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"\"\n        - |\n          {Switch(\n                true,\n                Global.dtmfOnlyFlag = true,\n                [\n                  \"<audio src=\\\"AUDIO_LOCATION/aa2540_ni2_02.wav\\\">Sorry, I still didn t get that</audio>\",\n                  \"\"\n                ]\n            )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.result\n  value: aa2540_SSN4DTMF_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.ssn4\n  value: aa2540_SSN4DTMF_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.validationCriteriaVariables.validationValue\n  value: validationCriteriaVariables.validationValue + ';' + result\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.validationCriteriaVariables.validationType\n  value: validationCriteriaVariables.validationType + ';' + validationCriteriaVariables.currentValidationType\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.globalVariables.currentTask = \"cardReplacement\" || Global.globalVariables.currentTask = \"altAuthVal\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa2510_ValidationDecision_JDA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: |\n            =If(Global.result = Global.validationCriteriaVariables.currentValidationData, true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: aa2510_ValidationDecision_JDA\n\n      elseActions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.validationCriteriaVariables.validationDataMismatch\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.validationCriteriaVariables.validationDataMismatchSsn4\n          value: true\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa2510_ValidationDecision_JDA\n```\n**Notes on the conversion:**\n- **unrecognizedPrompt** and **inputTimeoutResponse**: The first nomatch/noinput prompt is empty (as per the XML), so the first entry is an empty string. The second prompt uses a Switch for the dtmfOnlyFlag condition, as per your instructions.\n- **prompt**: Nested Switches are used to represent the if/else logic for the initial prompt, as per your instructions.\n- **SetVariable**: All session-mapping tags are converted to SetVariable actions.\n- **ConditionGroup**: The if/elseif/else logic under success is converted to nested ConditionGroups, with the correct conditions and actions.\n- **GotoAction**: All next actions are converted to GotoAction with the correct actionId.\n- **entity**: Set to StringPrebuiltEntity as there is no closed list or boolean entity in the success actions.\n- **variable**: Set to Global.aa2540_SSN4DTMF_DM_reco as per your instructions.\n- **YAML indentation and structure**: Follows your memorized format and examples.", "timestamp": 1749544141.5303967, "content_hash": "d3bbb12765dad63b21226b5394638e04"}