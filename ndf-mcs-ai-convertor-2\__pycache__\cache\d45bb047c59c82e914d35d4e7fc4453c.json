{"response": "```yaml\n- kind: Question\n  id: XT1515_RetryTempCode_DM\n  displayName: XT1515_RetryTempCode_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/XT1515_nm1_01.wav\\\">What s the 6-digit code in the text message I just sent you? Or say I didn t get it</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/XT1515_nm2_01.wav\\\">Please enter the 6-digit code in the text message using your keypad If you didn t get it, press 1</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/XT1515_nm2_01.wav\\\">Please enter the 6-digit code in the text message using your keypad If you didn t get it, press 1</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.XT1515_RetryTempCode_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/XT1515_ini_02.wav\\\">Try again, or say 'I didn't get it'</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: didnt_get\n          displayName: didnt_get\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/XT1515_ni1_01.wav\\\">What s the 6-digit code in the text message I just sent you? Or say  I didn t get it</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/XT1515_ni2_01.wav\\\">Please enter the 6-digit code in the text message using your keypad If you didn t get it, press 1</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/XT1515_nm2_01.wav\\\">Please enter the 6-digit code in the text message using your keypad If you didn t get it, press 1</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.twoFactorAuthNumberRetries\n  value: GlobalVars.twoFactorAuthNumberRetries\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_XT1515_RetryTempCode_DM\n  value: =Text(Global.XT1515_RetryTempCode_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_XT1515_RetryTempCode_DM = \"didnt_get\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/XT1515_out_01.wav\\\">No worries Let me get you to someone who can help</audio>\"\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.twoFactorAuthOutcome\n          value: code_timeout\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.playTransferMessage\n          value: false\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.playTransferPrompt\n          value: false\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.cti_AuthStatus\n          value: getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'tfa_failed_smstimeout')\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: XT1525_GoToTransfer_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_XT1515_RetryTempCode_DM = \"default\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.tempCode\n              value: XT1515_RetryTempCode_DM.returnvalue\n\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.visitedTFRetry_counter\n              value: GlobalVars.visitedTFRetry_counter+1\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: XT1405_ValidateTempCode_DB_DA\n```", "timestamp": 1749530223.3588736, "content_hash": "d45bb047c59c82e914d35d4e7fc4453c"}