{"response": "```yaml\n- kind: Question\n  id: AC2615_CollecteSIMorpSIM_DM\n  displayName: AC2615_CollecteSIMorpSIM_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AC2615_nm1_01.wav\\\">Which would you like to activate today, say 'eSIM' or 'physical SIM' You can also say 'more information'</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AC2615_nm2_01.wav\\\">Say 'eSIM' or press 1, or 'physical SIM' or press 2 You can also say 'more information' or press 3 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/AC2615_nm3_01.wav\\\">If you'd like to activate the eSIM, press 1 To activate the physical SIM, press 2 For more information, press 3 </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.AC2615_CollecteSIMorpSIM_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/AC2615_ini_01.wav\\\">Your phone has an eSIm AND a physical SIM Which one would you like to activate today, say 'eSIM', 'physical SIM' or more information </audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: eSim\n          displayName: eSim\n        - id: pSim\n          displayName: pSim\n        - id: more_info\n          displayName: more_info\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/AC2615_nm1_01.wav\\\">Which would you like to activate today, say 'eSIM' or 'physical SIM' You can also say 'more information'</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AC2615_nm2_01.wav\\\">Say 'eSIM' or press 1, or 'physical SIM' or press 2 You can also say 'more information' or press 3 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/AC2615_nm3_01.wav\\\">If you'd like to activate the eSIM, press 1 To activate the physical SIM, press 2 For more information, press 3 </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_AC2615_CollecteSIMorpSIM_DM\n  value: =Text(Global.AC2615_CollecteSIMorpSIM_DM_reco)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.GlobalVars.AC2615ReRecognition\n  value: false\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_AC2615_CollecteSIMorpSIM_DM = \"eSim\", true, false)\n      actions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |-\n                =If(Or(Global.GlobalVars.GetDeviceStatus.eid = undefined, Global.GlobalVars.GetDeviceStatus.eid = \"\", Global.GlobalVars.GetDeviceStatus.eid = null, Global.GlobalVars.GetDeviceStatus.eid = \"null\"), true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.activationResult\n                  value: transfer\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: getReturnLink()\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.activationType\n              value: eSIM\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: AC2620_AskWiFiConnected_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_AC2615_CollecteSIMorpSIM_DM = \"pSim\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.activationType\n              value: pSIM\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: AC2305_ICCIDTransitionSkipSBI_DM\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_AC2615_CollecteSIMorpSIM_DM = \"more_info\", true, false)\n              actions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: AC2615_CollecteSIMorpSIM_DM\n```", "timestamp": 1749527532.4349144, "content_hash": "cec0c410f323b95bdfd6ee16a9d225a4"}