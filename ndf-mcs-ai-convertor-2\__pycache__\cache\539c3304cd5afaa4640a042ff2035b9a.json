{"response": "```yaml\n- kind: Question\n  id: aa5005_PinSelect1DTMF_DM\n  displayName: aa5005_PinSelect1DTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa5005_ini_01.wav\\\">Please enter your new 4 digit PIN</audio>\"\n      - |\n        {Switch(\n              true,\n              Global.dnisInfo.dtmfOnlyFlag = true,\n              \"<audio src=\\\"AUDIO_LOCATION/aa5005_nm2_01.wav\\\">Sorry, I still didn t get that</audio>\"\n          )\n        }\n\n  alwaysPrompt: true\n  variable: Global.aa5005_PinSelect1DTMF_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/aa5005_ini_01.wav\\\">Please enter your new 4 digit PIN</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items: []\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/aa5005_ini_01.wav\\\">Please enter your new 4 digit PIN</audio>\"\n        - |\n          {Switch(\n                true,\n                Global.dnisInfo.dtmfOnlyFlag = true,\n                \"<audio src=\\\"AUDIO_LOCATION/aa5005_ni2_01.wav\\\">Sorry, I still didnt get that</audio>\"\n            )\n          }\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.pinVariables.pin1\n  value: aa5005_PinSelect1DTMF_DM.returnvalue\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.pinVariables.pinChangeCounter\n  value: pinVariables.pinChangeCounter + 1\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: aa5010_PinSelect2DTMF_DM\n```", "timestamp": 1749543487.4306405, "content_hash": "539c3304cd5afaa4640a042ff2035b9a"}