{"response": "```yaml\n- kind: Question\n  id: GC1010_AskLoginWait_DM\n  displayName: GC1010_AskLoginWait_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.GC1010_AskLoginWait_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.accountPinToggleOn = true,\n            \"Once you're ready with your 6-to-15-digit account PIN, just say 'Continue' Or say 'I can't find it' When you're ready, say 'Continue' or press 1, or say 'I can't find it' or press 2 At any time, you can say 'Continue' or press 1, or 'I can't find it' or press 2 If you're ready with your 6-to-15-digit account PIN, say 'Continue' or press 1, or say 'I can't find it' or press 2\",\n            Global.accountPinToggleOn = false,\n            \"Once you're ready with your 8-digit account PIN, just say 'Continue'Or say 'I can't find it' When you're ready, say 'Continue' or press 1, or say 'I can't find it' or press 2 At any time, you can say 'Continue' or press 1, or 'I can't find it' or press 2 If you're ready with your 8-digit account PIN, say 'Continue' or press 1, or say 'I can't find it' or press 2\",\n            \"You can say 'Continue' or press 1, or 'I can't find it' or press 2 Hmmm, I seem to be having some trouble Let's move on\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: ready\n          displayName: ready\n        - id: dont_know\n          displayName: dont_know\n        - id: default\n          displayName: default\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n              true,\n              Global.accountPinToggleOn = true,\n              \"Once you're ready with your 6-to-15-digit account PIN, just say 'Continue' Or say 'I can't find it' When you're ready, say 'Continue' or press 1, or say 'I can't find it' or press 2 At any time, you can say 'Continue' or press 1, or 'I can't find it' or press 2 If you're ready with your 6-to-15-digit account PIN, say 'Continue' or press 1, or say 'I can't find it' or press 2\",\n              Global.accountPinToggleOn = false,\n              \"Once you're ready with your 8-digit account PIN, just say 'Continue'Or say 'I can't find it' When you're ready, say 'Continue' or press 1, or say 'I can't find it' or press 2 At any time, you can say 'Continue' or press 1, or 'I can't find it' or press 2 If you're ready with your 8-digit account PIN, say 'Continue' or press 1, or say 'I can't find it' or press 2\",\n              \"You can say 'Continue' or press 1, or 'I can't find it' or press 2 Hmmm, I seem to be having some trouble Let's move on\"\n          )}\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.callType\n  value: GlobalVars.callType\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.accountPinToggleOn\n  value: GlobalVars.GetBCSParameters.accountPinToggleOn\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_GC1010_AskLoginWait_DM\n  value: =Text(Global.GC1010_AskLoginWait_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_GC1010_AskLoginWait_DM = \"ready\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.FromGC1010\n          value: true\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: GC1000_GetSecurityCode_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_GC1010_AskLoginWait_DM = \"dont_know\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/GC1010_out_01.wav\\\">Allright</audio>\"\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.GlobalVars.lastPinTry = true, true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.dontKnowPIN\n                      value: true\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: GC1090_GoToTransfer_SD\n\n              elseActions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.transferReason\n                  value: getPIN.dontKnow\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: GC1085_GoToAccountPinReset_SD\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_GC1010_AskLoginWait_DM = \"default\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GetSecurityCodeVars.securityCode\n                  value: GC1010_AskLoginWait_DM.returnvalue\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.verificationType\n                  value: pin\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.verificationValue\n                  value: GC1010_AskLoginWait_DM.returnvalue\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: GC1029_Authenticate_DB_DA\n```", "timestamp": 1749528411.8428564, "content_hash": "fcc3549c38188824cd25f0ff00e829c7"}