{"response": "```yaml\n- kind: Question\n  id: bm0310_AddMoneyMenu_DM\n  displayName: bm0310_AddMoneyMenu_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.bm0310_AddMoneyMenu_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.fromBm0235 <> \"true\",\n            \"[AUDIO: com.nuance.att.application.audio.bm0310_AddMoneyMenu_initial]\",\n            \" \"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: credit_card\n          displayName: credit_card\n        - id: debit_card\n          displayName: debit_card\n        - id: refill_pin\n          displayName: refill_pin\n        - id: auto_refill\n          displayName: auto_refill\n        - id: change_plans\n          displayName: change_plans\n        - id: balance\n          displayName: balance\n        - id: skip_it\n          displayName: skip_it\n        - id: check\n          displayName: check\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"[AUDIO: com.nuance.att.application.audio.bm0310_AddMoneyMenu_DM_noinput_1]\"\n        - \"[AUDIO: com.nuance.att.application.audio.bm0310_AddMoneyMenu_DM_noinput_2]\"\n    defaultValueMissingAction: Escalate\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.returnCode = 0, true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_dtmfgrammar1\n          value: =Text(\"bm0310_AddMoneyMenu_DM_dtmf.jsp\" + Global.GetAddMoneyMenuOptionList.addMoneyMenusDtmf)\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_grammar1\n          value: =Text(\"bm0310_AddMoneyMenu_DM.jsp\" + Global.GetAddMoneyMenuOptionList.addMoneyMenusSpeech)\n  elseActions:\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.collection_dtmfgrammar1\n      value: =Text(\"bm0310_AddMoneyMenu_DM_dtmf.jsp\")\n    - kind: SetVariable\n      id: setVariable_REPLACE_THIS\n      variable: Global.collection_grammar1\n      value: =Text(\"bm0310_AddMoneyMenu_DM.jsp\")\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.returnCode\n  value: GetAddMoneyMenuOptionList.returnCode\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.fromBm0235\n  value: false\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.simplifiedAddMoneyMenu\n  value: false\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.Var_bm0310_AddMoneyMenu_DM\n  value: =Text(Global.bm0310_AddMoneyMenu_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |-\n        =If(Global.Var_bm0310_AddMoneyMenu_DM = \"credit_card\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.refillMethod\n          value: credit\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.transferDestination\n          value: vesta\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.B02_AddMoney_05_Dialog\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: |-\n            =If(Global.Var_bm0310_AddMoneyMenu_DM = \"debit_card\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.refillMethod\n              value: debit\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.transferDestination\n              value: vesta\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.B02_AddMoney_05_Dialog\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: |-\n                =If(Global.Var_bm0310_AddMoneyMenu_DM = \"refill_pin\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.firstTimeInBm0410\n                  value: true\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.refillMethod\n                  value: refillPIN\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.askRefillPINFromVar\n                  value: bm0310_AddMoneyMenu_DM\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: bm0311_RefillPinDiscontinued_PP\n\n          elseActions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: |-\n                    =If(Global.Var_bm0310_AddMoneyMenu_DM = \"auto_refill\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.transferDestination\n                      value: vesta\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.intent\n                      value: autoRefill\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: bm0315_AddMoneyRouting_SD\n\n              elseActions:\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: |-\n                        =If(Global.Var_bm0310_AddMoneyMenu_DM = \"change_plans\", true, false)\n                      actions:\n                        - kind: SetVariable\n                          id: setVariable_REPLACE_THIS\n                          variable: Global.intent\n                          value: changePlan\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: bm0315_AddMoneyRouting_SD\n\n                  elseActions:\n                    - kind: ConditionGroup\n                      id: conditionGroup_REPLACE_THIS\n                      conditions:\n                        - id: conditionItem_REPLACE_THIS\n                          condition: |-\n                            =If(Global.Var_bm0310_AddMoneyMenu_DM = \"balance\", true, false)\n                          actions:\n                            - kind: SetVariable\n                              id: setVariable_REPLACE_THIS\n                              variable: Global.askedForBalance\n                              value: yes\n                            - kind: BeginDialog\n                              id: begin_REPLACE_THIS\n                              dialog: topic.B02_AddMoney_01_Dialog\n\n                      elseActions:\n                        - kind: ConditionGroup\n                          id: conditionGroup_REPLACE_THIS\n                          conditions:\n                            - id: conditionItem_REPLACE_THIS\n                              condition: |-\n                                =If(Global.Var_bm0310_AddMoneyMenu_DM = \"skip_it\", true, false)\n                              actions:\n                                - kind: SendActivity\n                                  id: sendActivity_REPLACE_THIS\n                                  activity:\n                                    speak:\n                                      - \"<audio src=\\\"AUDIO_LOCATION/bm0310_out_01.wav\\\">Okay</audio>\"\n                                - kind: EndDialog\n                                  id: endDialog_REPLACE_THIS\n\n                          elseActions:\n                            - kind: ConditionGroup\n                              id: conditionGroup_REPLACE_THIS\n                              conditions:\n                                - id: conditionItem_REPLACE_THIS\n                                  condition: |-\n                                    =If(Global.Var_bm0310_AddMoneyMenu_DM = \"check\", true, false)\n                                  actions:\n                                    - kind: SendActivity\n                                      id: sendActivity_REPLACE_THIS\n                                      activity:\n                                        speak:\n                                          - \"<audio src=\\\"AUDIO_LOCATION/bm0310_out_02.wav\\\">I ll need to connect you to someone who can assist with your payment Please make sure to tell the agent that you want to make an e-check payment</audio>\"\n                                    - kind: SetVariable\n                                      id: setVariable_REPLACE_THIS\n                                      variable: Global.transferDestination\n                                      value: vesta\n                                    - kind: SetVariable\n                                      id: setVariable_REPLACE_THIS\n                                      variable: Global.intent\n                                      value: refillCheck\n                                    - kind: SetVariable\n                                      id: setVariable_REPLACE_THIS\n                                      variable: Global.transferPrompt\n                                      value: short\n                                    - kind: SetVariable\n                                      id: setVariable_REPLACE_THIS\n                                      variable: Global.transferReason\n                                      value: BUSINESS_RULE\n                                    - kind: BeginDialog\n                                      id: begin_REPLACE_THIS\n                                      dialog: topic.M09_MainMenu_Dialog\n```", "timestamp": 1749469683.3759897, "content_hash": "aa481843ffbbae868972fefc01b4b27c"}