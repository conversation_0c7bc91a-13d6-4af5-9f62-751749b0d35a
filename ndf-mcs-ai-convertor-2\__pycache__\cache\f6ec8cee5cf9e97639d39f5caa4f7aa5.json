{"response": "```yaml\n- kind: Question\n  id: ES1005_KeepingSameSIMCard_DM\n  displayName: ES1005_KeepingSameSIMCard_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ES1005_nm1_01.wav\\\">If you re keeping the SIM card from your old phone, say same one Otherwise, say new one </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ES1005_nm2_01.wav\\\">If you re going to use the same SIM card you have now, say same one or press 1 If you got a new one for your new phone, say new one or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ES1005_nm2_01.wav\\\">If you re going to use the same SIM card you have now, say same one or press 1 If you got a new one for your new phone, say new one or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ES1005_KeepingSameSIMCard_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ES1005_ini_01.wav\\\">Are you keeping the same SIM card, or did you get a new one? Say same one or new one</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: same\n          displayName: same\n        - id: new\n          displayName: new\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ES1005_nm1_01.wav\\\">If you re keeping the SIM card from your old phone, say same one Otherwise, say new one </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ES1005_nm2_01.wav\\\">If you re going to use the same SIM card you have now, say same one or press 1 If you got a new one for your new phone, say new one or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ES1005_nm2_01.wav\\\">If you re going to use the same SIM card you have now, say same one or press 1 If you got a new one for your new phone, say new one or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ES1005_KeepingSameSIMCard_DM\n  value: =Text(Global.ES1005_KeepingSameSIMCard_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ES1005_KeepingSameSIMCard_DM = \"same\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/ES1005_out_04.wav\\\">Great </audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.usingOldSIMForSwap\n          value: true\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.iccidSerialNumber\n          value: GlobalVars.GetAccountDetails.iccidSerialNumber\n\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.CareESNSwap_Main_Cont.dvxml#ES1405_ValidateDeviceTransition_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ES1005_KeepingSameSIMCard_DM = \"new\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.GlobalVars.activityCode = \"BSS\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.GlobalVars.fromESNSwapDialog\n                      value: true\n\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: ES1007_SimSwappedBlocked_DM\n\n              elseActions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/ES1005_out_02.wav\\\">Great</audio>\"\n                      - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: ES1305_ICCIDTransitionSkipSBI_DM\n```", "timestamp": 1749527905.5122135, "content_hash": "f6ec8cee5cf9e97639d39f5caa4f7aa5"}