{"response": "```yaml\n- kind: Question\n  id: BC1505_UseDifferentCardYN_DM\n  displayName: BC1505_UseDifferentCardYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1505_nm1_01.wav\\\">Would you like to try with a different card</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1505_nm2_01.wav\\\">I couldn't validate the card details you gave me If you have a different card you can use, say 'yes' or press 1 Otherwise, say 'no' or press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1505_nm3_02.wav\\\">I can't complete your activation without a payment If you have a different card you can use, press 1 Otherwise, press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/BC1505_nm3_01.wav\\\">I can't complete your payment without a valid card If you have a different card you can use, press 1 Otherwise, press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.BC1505_UseDifferentCardYN_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.callType = \"activate\",\n            [\n              \"I can't match the information you gave me information to a bank account,  and I wont be able to complete your activation without a payment\",\n              \"test\"\n            ],\n            Global.cardStatus <> \"valid\" && Global.payingWithEWallet = true,\n            \"Actually, your card isn't going through You can verify the information you've entered for it in the myMetro app on your phone, or online at metrobyt-mobilecom For now\",\n            !(Global.cardStatus <> \"valid\" && Global.payingWithEWallet = true),\n            [\n              \"I can't match the information you gave me to a bank account, and I wont be able to complete your payment\",\n              \"test\"\n            ],\n            \"Would you like to use a different card?\"\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1505_nm1_01.wav\\\">Would you like to try with a different card</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1505_nm2_01.wav\\\">I couldn't validate the card details you gave me If you have a different card you can use, say 'yes' or press 1 Otherwise, say 'no' or press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1505_nm3_02.wav\\\">I can't complete your activation without a payment If you have a different card you can use, press 1 Otherwise, press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/BC1505_nm3_01.wav\\\">I can't complete your payment without a valid card If you have a different card you can use, press 1 Otherwise, press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.callType\n  value: GlobalVars.callType\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.cardStatus\n  value: GlobalVars.ValidateCardOptions.cardStatus\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.payingWithEWallet\n  value: GlobalVars.payingWithEWallet\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_BC1505_UseDifferentCardYN_DM\n  value: =Text(Global.BC1505_UseDifferentCardYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_BC1505_UseDifferentCardYN_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.tryOtherCardReason\n          value: fail_validation\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.preferredPaymentMethod\n          value: undefined\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.PaymentTable.CARD_TYPE\n          value: not_set\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.disconfirmedDetails\n          value: false\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/BC1505_out_01.wav\\\">alright</audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: getReturnLink()\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_BC1505_UseDifferentCardYN_DM = \"false\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: BC1510_GoTo_ErrorHandling_SD\n```", "timestamp": 1749528220.2203481, "content_hash": "dacd1074ef09040b86e2f853c815b900"}