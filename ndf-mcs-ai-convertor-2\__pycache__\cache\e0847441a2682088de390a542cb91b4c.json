{"response": "```yaml\n- kind: Question\n  id: DH1295_AddLineWrap_DM\n  displayName: DH1295_AddLineWrap_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DH1295_nm1_01.wav\\\">You can say 'repeat that' Or ff you need something else say 'main menu'</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DH1295_nm1_02.wav\\\">If you need something else say 'main menu' or press 1</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/DH1295_nm1_02.wav\\\">If you need something else say 'main menu' or press 1</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.DH1295_AddLineWrap_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/DH1295_ini_01.wav\\\">If your account is suspended you will need to make a payment before you can add a line Otherwise adding a line is easy Just visit metro by tmobilecom and go to the 'my account' option If you are not suspended  there will be an option in the middle of the screen to add a line It will guide you through the process If that's all you needed you can simply hang up If you need help with something else say 'main menu'</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: main_menu\n          displayName: main_menu\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/DH1295_nm1_01.wav\\\">You can say 'repeat that' Or ff you need something else say 'main menu'</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DH1295_nm1_02.wav\\\">If you need something else say 'main menu' or press 1</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/DH1295_nm1_02.wav\\\">If you need something else say 'main menu' or press 1</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_DH1295_AddLineWrap_DM\n  value: =Text(Global.DH1295_AddLineWrap_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_DH1295_AddLineWrap_DM = \"main_menu\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.callType\n          value: undefined\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.tag\n          value: undefined\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: DH1075_CheckNLUMenuConfig_JDA\n```", "timestamp": 1749528354.7323916, "content_hash": "e0847441a2682088de390a542cb91b4c"}