{"response": "```yaml\n- kind: Question\n  id: aa1510_ConfirmAddressDTMF_DM\n  displayName: aa1510_ConfirmAddressDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.dnisInfo.callType = \"TX\" && Global.validateLostStolenCriteriaInfo.poBox = false,\n            \"Our records indicate the Street Number of the mailing address is {Global.validateLostStolenCriteriaInfo.cardholderMailingAddress} If this is your correct Street Number of the mailing address, press 1 If this is not your correct Street Number of the mailing address, press 2\",\n            Global.dnisInfo.callType = \"TX\" && Global.validateLostStolenCriteriaInfo.poBox = true,\n            \"Our records indicate that your mailing address starts with PO Box {Global.validateLostStolenCriteriaInfo.cardholderMailingAddress} If this is the correct PO Box number for your mailing address, press 1  If this is not the correct PO Box for your mailing address, press 2\",\n            Global.dnisInfo.callType <> \"TX\" && Global.validateLostStolenCriteriaInfo.poBox = false,\n            \"Our records indicate that your mailing address starts with {Global.validateLostStolenCriteriaInfo.cardholderMailingAddress} If this is correct, press 1 Otherwise, press 2\",\n            Global.dnisInfo.callType <> \"TX\" && Global.validateLostStolenCriteriaInfo.poBox = true,\n            \"Our records indicate that your mailing address starts with PO Box {Global.validateLostStolenCriteriaInfo.cardholderMailingAddress} If this is correct, press 1 Otherwise, press 2\"\n        )}\n\n      - |\n        {Switch(\n            true,\n            Global.dnisInfo.dtmfOnlyFlag = true,\n            \"Sorry, I still didn t get that\"\n        )}\n\n  alwaysPrompt: true\n  variable: Global.aa1510_ConfirmAddressDTMF_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.dnisInfo.callType = \"TX\" && Global.validateLostStolenCriteriaInfo.poBox = false,\n            \"Our records indicate the Street Number of the mailing address is {Global.validateLostStolenCriteriaInfo.cardholderMailingAddress} If this is your correct Street Number of the mailing address, press 1 If this is not your correct Street Number of the mailing address, press 2\",\n            Global.dnisInfo.callType = \"TX\" && Global.validateLostStolenCriteriaInfo.poBox = true,\n            \"Our records indicate that your mailing address starts with PO Box {Global.validateLostStolenCriteriaInfo.cardholderMailingAddress} If this is the correct PO Box number for your mailing address, press 1  If this is not the correct PO Box for your mailing address, press 2\",\n            Global.dnisInfo.callType <> \"TX\" && Global.validateLostStolenCriteriaInfo.poBox = false,\n            \"Our records indicate that your mailing address starts with {Global.validateLostStolenCriteriaInfo.cardholderMailingAddress} If this is correct, press 1 Otherwise, press 2\",\n            Global.dnisInfo.callType <> \"TX\" && Global.validateLostStolenCriteriaInfo.poBox = true,\n            \"Our records indicate that your mailing address starts with PO Box {Global.validateLostStolenCriteriaInfo.cardholderMailingAddress} If this is correct, press 1 Otherwise, press 2\"\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - |\n          {Switch(\n              true,\n              Global.dnisInfo.callType = \"TX\" && Global.validateLostStolenCriteriaInfo.poBox = false,\n              \"Our records indicate the Street Number of the mailing address is {Global.validateLostStolenCriteriaInfo.cardholderMailingAddress} If this is your correct Street Number of the mailing address, press 1 If this is not your correct Street Number of the mailing address, press 2\",\n              Global.dnisInfo.callType = \"TX\" && Global.validateLostStolenCriteriaInfo.poBox = true,\n              \"Our records indicate that your mailing address starts with PO Box {Global.validateLostStolenCriteriaInfo.cardholderMailingAddress} If this is the correct PO Box number for your mailing address, press 1  If this is not the correct PO Box for your mailing address, press 2\",\n              Global.dnisInfo.callType <> \"TX\" && Global.validateLostStolenCriteriaInfo.poBox = false,\n              \"Our records indicate that your mailing address starts with {Global.validateLostStolenCriteriaInfo.cardholderMailingAddress} If this is correct, press 1 Otherwise, press 2\",\n              Global.dnisInfo.callType <> \"TX\" && Global.validateLostStolenCriteriaInfo.poBox = true,\n              \"Our records indicate that your mailing address starts with PO Box {Global.validateLostStolenCriteriaInfo.cardholderMailingAddress} If this is correct, press 1 Otherwise, press 2\"\n          )}\n\n        - |\n          {Switch(\n              true,\n              Global.dnisInfo.dtmfOnlyFlag = true,\n              \"Sorry, I still didn t get anything\"\n          )}\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa1510_ConfirmAddressDTMF_DM\n  value: =Text(Global.aa1510_ConfirmAddressDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa1510_ConfirmAddressDTMF_DM = \"yes\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.cardReplacementVariables.callerConfirmedAddress\n          value: true\n\n        # AddEventLogValue script for EBT218 (no YAML action needed)\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa1520_CardWillBeDeactivatedMsg_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa1510_ConfirmAddressDTMF_DM = \"no\", true, false)\n          actions:\n            # AddEventLogValue script for EBT219 (no YAML action needed)\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.dnisInfo.invalidAddressTransferAllowed = true, true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.globalVariables.transferAllowed\n                      value: true\n\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/aa1510_out_01.wav\\\">To update your records, I ll transfer you to customer service</audio>\"\n\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: transferHandler_CS\n\n              elseActions:\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: aa1510_InvalidMessage_PP\n```\n**Notes:**\n- All prompts with conditions are converted to PowerFx-style Switch statements.\n- All session-mapping tags are converted to SetVariable actions.\n- All audiofile text is included in the Switch cases or as audio markup where appropriate.\n- The AddEventLogValue script is noted in comments, as it does not map to a YAML action.\n- The structure and indentation follow the memorized YAML format and your instructions.", "timestamp": 1749543486.2148247, "content_hash": "6c78638157d07799e97637be5b5552e3"}