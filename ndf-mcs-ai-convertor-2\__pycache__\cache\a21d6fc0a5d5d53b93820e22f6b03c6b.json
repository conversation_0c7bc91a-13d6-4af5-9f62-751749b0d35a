{"response": "```yaml\n- kind: Question\n  id: cp0142_AskDeviceAvailable_DM\n  displayName: cp0142_AskDeviceAvailable_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/cp0142_nm1_01.wav\\\">To reset your account pin youll need the phone associated with this number and it must be able to receive text messages Do you have that now? Please say Yes or No</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/cp0142_nm2_01.wav\\\">If you have the phone associated with this number and youre able to receive text messages, press 1 Otherwise, press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.cp0142_AskDeviceAvailable_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.collectPinFromTransfer = true || Global.collectPinFromTransfer = \"true\",\n            \"To do that, youll need the phone associated with this number and it must be able to receive text messages Are you able to do that now?\",\n            true,\n            \"For your security, youll need to set an account PIN now To do that, youll need the phone associated with this number and it must be able to receive text messages Are you able to do that now?\"\n        )}\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/cp0142_ni1_01.wav\\\">To reset your account pin youll need the phone associated with this number and it must be able to receive text messages Do you have that now? Please say Yes or No</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/cp0142_ni2_01.wav\\\">If you have the phone associated with this number and youre able to receive text messages, press 1 Otherwise, press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_commandgrammar\n  value: 'GlobalCommands.grxml?SWI_vars.allow=mainmenu&SWI_vars.disallow=operator'\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.collection_dtmfcommandgrammar\n  value: 'GlobalCommands_dtmf.grxml?SWI_vars.allow=mainmenu&SWI_vars.disallow=operator'\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_cp0142_AskDeviceAvailable_DM\n  value: =Text(Global.cp0142_AskDeviceAvailable_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_cp0142_AskDeviceAvailable_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/cp0142_out_01.wav\\\">Alright</audio>\"\n\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.callType = \"611\", true, false)\n              actions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.collection_commandgrammar\n                  value: 'GlobalCommands.grxml?SWI_vars.allow=mainmenu'\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.collection_dtmfcommandgrammar\n                  value: 'GlobalCommands_dtmf.grxml?SWI_vars.allow=mainmenu'\n        # elseActions for callType != 611\n          elseActions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.collection_commandgrammar\n              value: 'GlobalCommands.grxml'\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.collection_dtmfcommandgrammar\n              value: 'GlobalCommands_dtmf.grxml'\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: cp0120_AskAccountZipCode_DM\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_cp0142_AskDeviceAvailable_DM = \"false\", true, false)\n          actions:\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.callType = \"611\", true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.collection_commandgrammar\n                      value: 'GlobalCommands.grxml?SWI_vars.allow=mainmenu'\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.collection_dtmfcommandgrammar\n                      value: 'GlobalCommands_dtmf.grxml?SWI_vars.allow=mainmenu'\n              elseActions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.collection_commandgrammar\n                  value: 'GlobalCommands.grxml'\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.collection_dtmfcommandgrammar\n                  value: 'GlobalCommands_dtmf.grxml'\n\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.collectPinFromTransfer = true && Global.matchedANI = true, true, false)\n                  actions:\n                    - kind: BeginDialog\n                      id: begin_REPLACE_THIS\n                      dialog: topic.M09_MainMenu.dvxml#ma9210_NeedTransferType_DS\n              elseActions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/cp0142_out_03.wav\\\">We apologize but we cannot assist you further until you setup a PIN Our customer service representatives cannot assist you either To set your pin please get your phone and then call us back or go to A T T dot com slash myPrepaid  If unable to find your phone, visit an A T and T store for further assistance</audio>\"\n                - kind: BeginDialog\n                  id: begin_REPLACE_THIS\n                  dialog: topic.M09_MainMenu.dvxml#ma9110_Goodbye_PP\n```", "timestamp": 1749470616.234601, "content_hash": "a21d6fc0a5d5d53b93820e22f6b03c6b"}