{"response": "```yaml\n- kind: Question\n  id: bc0335_AuthorizationRetry_DM\n  displayName: bc0335_AuthorizationRetry_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bc0335_ni1_01.wav\\\">Your payment wasnt authorized by your bank Do you want to try another card? Please say Yes or No</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/bc0335_ni2_01.wav\\\">That payment wasnt authorized by your bank and therefore did not go through If you want to try a different card, say Yes or press 1 If you don't, say No or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.bc0335_AuthorizationRetry_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/bc0335_ini_01.wav\\\">The payment wasnt authorized by your bank Would you like to try with a different card?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/bc0335_ni1_01.wav\\\">Your payment wasnt authorized by your bank Do you want to try another card? Please say Yes or No</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/bc0335_ni2_01.wav\\\">That payment wasnt authorized by your bank and therefore did not go through If you want to try a different card, say Yes or press 1 If you don't, say No or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_bc0335_AuthorizationRetry_DM\n  value: =Text(Global.bc0335_AuthorizationRetry_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_bc0335_AuthorizationRetry_DM = \"true\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/bc0335_out_01.wav\\\">Okay</audio>\"\n\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.bankAuthRetryCard\n          value: true\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: getReturnLink()\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_bc0335_AuthorizationRetry_DM = \"false\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/bc0335_out_02.wav\\\">Alright</audio>\"\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n```", "timestamp": **********.4028993, "content_hash": "c770377e717867419b359ce2b2e947f2"}