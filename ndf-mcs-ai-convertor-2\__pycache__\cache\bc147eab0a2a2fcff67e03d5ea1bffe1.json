{"response": "```yaml\n- kind: Question\n  id: TS1330_OfferTopupYN_DM\n  displayName: TS1330_OfferTopupYN_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TS1330_nm1_01.wav\\\">Would you like to add a top-up now, to get your hotspot connection back? You can also say more tips </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TS1330_nm2_01.wav\\\">To add a top-up, say yes or press 1 Otherwise, say no or press 2, or more tips - 3 If you are done for now, you can just hang up </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/TS1330_nm2_01.wav\\\">To add a top-up, say yes or press 1 Otherwise, say no or press 2, or more tips - 3 If you are done for now, you can just hang up </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.TS1330_OfferTopupYN_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/TS1330_ini_01.wav\\\">Would you like to add one now? Or say more tips </audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: yes\n          displayName: yes\n        - id: no\n          displayName: no\n        - id: tips\n          displayName: tips\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/TS1330_nm1_01.wav\\\">Would you like to add a top-up now, to get your hotspot connection back? You can also say more tips </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/TS1330_nm2_01.wav\\\">To add a top-up, say yes or press 1 Otherwise, say no or press 2, or more tips - 3 If you are done for now, you can just hang up </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/TS1330_nm2_01.wav\\\">To add a top-up, say yes or press 1 Otherwise, say no or press 2, or more tips - 3 If you are done for now, you can just hang up </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_TS1330_OfferTopupYN_DM\n  value: =Text(Global.TS1330_OfferTopupYN_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_TS1330_OfferTopupYN_DM = \"yes\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/TS1330_out_01.wav\\\">You got it! </audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.tag\n          value: buy-data_topup\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: TS1405_GetPIN_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_TS1330_OfferTopupYN_DM = \"no\", true, false)\n          actions:\n            - kind: SendActivity\n              id: sendActivity_REPLACE_THIS\n              activity:\n                speak:\n                  - \"<audio src=\\\"AUDIO_LOCATION/TS1330_out_02.wav\\\">No problem</audio>\"\n                  - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.tag\n              value: undefined\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.GlobalVars.callType\n              value: undefined\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: getReturnLink()\n\n      elseActions:\n        - kind: ConditionGroup\n          id: conditionGroup_REPLACE_THIS\n          conditions:\n            - id: conditionItem_REPLACE_THIS\n              condition: =If(Global.Var_TS1330_OfferTopupYN_DM = \"tips\", true, false)\n              actions:\n                - kind: SendActivity\n                  id: sendActivity_REPLACE_THIS\n                  activity:\n                    speak:\n                      - \"<audio src=\\\"AUDIO_LOCATION/TS1330_out_03.wav\\\">Sure tips</audio>\"\n                      - \"<audio src=\\\"AUDIO_LOCATION/silence_250ms.wav\\\">test</audio>\"\n                - kind: GotoAction\n                  id: goto_REPLACE_THIS\n                  actionId: TS1310_PlayHotspotTips_PP\n```", "timestamp": 1749529917.6354766, "content_hash": "bc147eab0a2a2fcff67e03d5ea1bffe1"}