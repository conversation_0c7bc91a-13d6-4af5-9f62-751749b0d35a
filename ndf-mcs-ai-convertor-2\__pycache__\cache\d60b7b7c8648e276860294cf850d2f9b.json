{"response": "```yaml\n- kind: Question\n  id: LP1005_LostPhoneHandling_DM\n  displayName: LP1005_LostPhoneHandling_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm1_01.wav\\\">I didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/LP1005_nm1_01.wav\\\">If you would like to temporarily suspend your service, say suspend line If you have phone insurance and would like to find out how you can get a replacement phone, file a claim and more, say phone insurance </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm2_01.wav\\\">I *still* didn't get that </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/LP1005_nm2_01.wav\\\">If you need to temporarily suspend your service, please say suspend line or press 1 If you signed up for insurance and need to know where to get started with it, please say phone insurance or press 2 </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/gl_nm3_01.wav\\\">Let's try one more time </audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/LP1005_nm2_01.wav\\\">If you need to temporarily suspend your service, please say suspend line or press 1 If you signed up for insurance and need to know where to get started with it, please say phone insurance or press 2 </audio>\"\n\n  alwaysPrompt: true\n  variable: Global.LP1005_LostPhoneHandling_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/LP1005_ini_01.wav\\\">If you need to find out about temporarily suspending service while you look for your phone, say suspend line If you signed up for phone insurance, and would like to find out about a replacement phone, filing a claim, deductible, and more, say phone insurance</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: suspend\n          displayName: suspend\n        - id: insurance\n          displayName: insurance\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/LP1005_nm1_01.wav\\\">If you would like to temporarily suspend your service, say suspend line If you have phone insurance and would like to find out how you can get a replacement phone, file a claim and more, say phone insurance </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni2_01.wav\\\">I didn't hear you </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/LP1005_nm2_01.wav\\\">If you need to temporarily suspend your service, please say suspend line or press 1 If you signed up for insurance and need to know where to get started with it, please say phone insurance or press 2 </audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/gl_ni3_01.wav\\\">I still didn't hear</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/silence_500ms.wav\\\">test</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/LP1005_nm2_01.wav\\\">If you need to temporarily suspend your service, please say suspend line or press 1 If you signed up for insurance and need to know where to get started with it, please say phone insurance or press 2 </audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_LP1005_LostPhoneHandling_DM\n  value: =Text(Global.LP1005_LostPhoneHandling_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_LP1005_LostPhoneHandling_DM = \"suspend\", true, false)\n      actions:\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/LP1005_out_01.wav\\\">Just so you know, to suspend your account, we'll need the phone number for the device and the account PIN for the account</audio>\"\n              - \"<audio src=\\\"AUDIO_LOCATION/silence_750ms.wav\\\">test</audio>\"\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: LP1010_GoToCallTransfer_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_LP1005_LostPhoneHandling_DM = \"insurance\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: LP1020_InsuranceInfoWait_DM\n```", "timestamp": **********.4000802, "content_hash": "d60b7b7c8648e276860294cf850d2f9b"}