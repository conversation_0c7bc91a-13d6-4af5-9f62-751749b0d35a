{"response": "```yaml\n- kind: Question\n  id: st0315_AskSameDevice_DM\n  displayName: st0315_AskSameDevice_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/st0315_ni1_01.wav\\\">Is the phone that youre calling from the one that you need help with? Say Yes or No</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/st0315_ni2_01.wav\\\">If youre having problems with the phone that youre calling from, say Yes or press 1 If youre having problems with a different phone, say No or press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.st0315_AskSameDevice_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/st0315_ini_01.wav\\\">Are you calling from the phone youre having problems with?</audio>\"\n\n  entity: BooleanPrebuiltEntity\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/st0315_ni1_01.wav\\\">Is the phone that youre calling from the one that you need help with? Say Yes or No</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/st0315_ni2_01.wav\\\">If youre having problems with the phone that youre calling from, say Yes or press 1 If youre having problems with a different phone, say No or press 2</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_st0315_AskSameDevice_DM\n  value: =Text(Global.st0315_AskSameDevice_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_st0315_AskSameDevice_DM = \"true\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.callingFromProblemDevice\n          value: true\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: st0330_WhichSupportOption_JDA_DA\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_st0315_AskSameDevice_DM = \"false\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.loginType\n              value: both\n\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: st0325_TechSupportLogin_SD\n```", "timestamp": 1749472024.53117, "content_hash": "d0bb186b4a21f2e7a26ff16820a7b643"}