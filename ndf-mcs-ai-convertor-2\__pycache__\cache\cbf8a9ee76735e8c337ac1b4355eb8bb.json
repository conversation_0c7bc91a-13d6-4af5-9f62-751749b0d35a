{"response": "```yaml\n- kind: Question\n  id: aa1028_EmergencyMessageMenuDTMF_DM\n  displayName: aa1028_EmergencyMessageMenuDTMF_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - aa1028_EmergencyMessageMenuDTMF_DM_initial\n      - aa1028_EmergencyMessageMenuDTMF_DM_initial\n\n  alwaysPrompt: true\n  variable: Global.aa1028_EmergencyMessageMenuDTMF_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n\n            // Option 1 Prompt\n            Global.dnisInfo.emergencyMsgMenuOption1PromptOn = true && (Global.globalVariables.language = \"es-US\" || Global.globalVariables.language = \"es-TX\"),\n            Global.dnisInfo.emergencyMsgMenuOption1TtsSpanish,\n\n            Global.dnisInfo.emergencyMsgMenuOption1PromptOn = true && Global.globalVariables.language = \"fr-CA\",\n            Global.dnisInfo.dnisInfo.emergencyMsgMenuOption1TtsCreole,\n\n            Global.dnisInfo.emergencyMsgMenuOption1PromptOn = true,\n            Global.dnisInfo.emergencyMsgMenuOption1TtsEnglish,\n\n            // Option 2 Prompt\n            Global.dnisInfo.emergencyMsgMenuOption2PromptOn = true && (Global.globalVariables.language = \"es-US\" || Global.globalVariables.language = \"es-TX\"),\n            Global.dnisInfo.emergencyMsgMenuOption2TtsSpanish,\n\n            Global.dnisInfo.emergencyMsgMenuOption2PromptOn = true && Global.globalVariables.language = \"fr-CA\",\n            Global.dnisInfo.emergencyMsgMenuOption2TtsCreole,\n\n            Global.dnisInfo.emergencyMsgMenuOption2PromptOn = true,\n            Global.dnisInfo.emergencyMsgMenuOption2TtsEnglish,\n\n            // Default\n            \" \"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: play_emergency_msg\n          displayName: play_emergency_msg\n\n        - id: skip_emergency_msg\n          displayName: skip_emergency_msg\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - aa1028_EmergencyMessageMenuDTMF_DM_initial\n        - aa1028_EmergencyMessageMenuDTMF_DM_initial\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_aa1028_EmergencyMessageMenuDTMF_DM\n  value: =Text(Global.aa1028_EmergencyMessageMenuDTMF_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_aa1028_EmergencyMessageMenuDTMF_DM = \"play_emergency_msg\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: aa1029_EmergencyMessage_PP\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_aa1028_EmergencyMessageMenuDTMF_DM = \"skip_emergency_msg\", true, false)\n          actions:\n            - kind: GotoAction\n              id: goto_REPLACE_THIS\n              actionId: return\n```", "timestamp": 1749458395.5411656, "content_hash": "cbf8a9ee76735e8c337ac1b4355eb8bb"}