{"response": "```yaml\n- kind: Question\n  id: ca0171_NeedAccountRep_DM\n  displayName: ca0171_NeedAccountRep_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ca0171_nm1_01.wav\\\">Would you like to speak to a representative, or return to the main menu?</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ca0171_nm2_01.wav\\\">To speak to a representative, press 0 To return to the main menu, press 2 or you can just hang up</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ca0171_NeedAccountRep_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n            Global.matchedANI = \"true\" || Global.matchedANI = true,\n            \"In order to make changes to your account, you will need to have your account number You can obtain your account number by visiting a t t dot com slash my prepaid You can say repeat that, main menu, or you can just hang up now\",\n            true,\n            \"In order to receive a text message with your account number, you need the phone associated with your account If you don't have your phone, you can speak to a representative, by saying representative You can also visit an A T and T store Say representative, main menu or you can hang up now\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: repeat\n          displayName: repeat\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ca0171_ni1_01.wav\\\">Would you like to speak to a representative, or return to the main menu?</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ca0171_ni2_01.wav\\\">To speak to a representative, press 0 To return to the main menu, press 2 or you can just hang up</audio>\"\n\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ca0171_NeedAccountRep_DM\n  value: =Text(Global.ca0171_NeedAccountRep_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ca0171_NeedAccountRep_DM = \"repeat\", true, false)\n      actions:\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: ca0171_NeedAccountRep_DM\n```", "timestamp": **********.1558833, "content_hash": "c893d6567011486c3f706d2a31e9a359"}