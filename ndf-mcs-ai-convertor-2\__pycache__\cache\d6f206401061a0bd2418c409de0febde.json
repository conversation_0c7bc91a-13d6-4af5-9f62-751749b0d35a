{"response": "```yaml\n- kind: Question\n  id: ca0170_LineOnAccount_DM\n  displayName: ca0170_LineOnAccount_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ca0170_nm1_01.wav\\\">To add a line, press 1 Or To remove a line press 2</audio>\"\n      - \"<audio src=\\\"AUDIO_LOCATION/ca0170_nm2_01.wav\\\">To add a line, press 1 Or To remove a line press 2</audio>\"\n\n  alwaysPrompt: true\n  variable: Global.ca0170_LineOnAccount_DM_reco\n  prompt:\n    speak:\n      - \"<audio src=\\\"AUDIO_LOCATION/ca0170_ini_01.wav\\\">Would you like to add a line or remove a line</audio>\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: add_line\n          displayName: add_line\n        - id: remove_line\n          displayName: remove_line\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"<audio src=\\\"AUDIO_LOCATION/ca0170_ni1_01.wav\\\">To add a line, press 1 Or To remove a line press 2</audio>\"\n        - \"<audio src=\\\"AUDIO_LOCATION/ca0170_ni2_01.wav\\\">To add a line, press 1 Or To remove a line press 2</audio>\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_ca0170_LineOnAccount_DM\n  value: =Text(Global.ca0170_LineOnAccount_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_ca0170_LineOnAccount_DM = \"add_line\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.nextCallerIntent\n          value: add_line\n        - kind: BeginDialog\n          id: begin_REPLACE_THIS\n          dialog: topic.M01_MainMenu2.dvxml#ma1334_InitiateMainMenu_DS\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_ca0170_LineOnAccount_DM = \"remove_line\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.nextCallerIntent\n              value: remove_line\n            - kind: BeginDialog\n              id: begin_REPLACE_THIS\n              dialog: topic.M01_MainMenu2.dvxml#ma1334_InitiateMainMenu_DS\n```", "timestamp": **********.2448084, "content_hash": "d6f206401061a0bd2418c409de0febde"}