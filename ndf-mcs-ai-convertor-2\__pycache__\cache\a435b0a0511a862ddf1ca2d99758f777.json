{"response": "```yaml\n- kind: Question\n  id: pa0320_AskCancelCategory_DM\n  displayName: pa0320_AskCancelCategory_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  unrecognizedPrompt:\n    speak:\n      - \"com.nuance.att.application.audio.pa0320_AskCancelCategory_DM_noinput_1\"\n      - \"com.nuance.att.application.audio.pa0320_AskCancelCategory_DM_noinput_2\"\n\n  alwaysPrompt: true\n  variable: Global.pa0320_AskCancelCategory_DM_reco\n  prompt:\n    speak:\n      - \"com.nuance.att.application.audio.pa0320_AskCancelCategory_initial\"\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items: []\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak:\n        - \"com.nuance.att.application.audio.pa0320_AskCancelCategory_DM_noinput_1\"\n        - \"com.nuance.att.application.audio.pa0320_AskCancelCategory_DM_noinput_2\"\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.pageNo\n  value: 3\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.returnCode\n  value: GetAutoRenewPackageList.returnCode\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: |\n        =If(Global.returnCode = 0, true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_dtmfgrammar1\n          value: =Text('pa0320_AskCancelCategory_DM_dtmf.jsp' + GetAutoRenewPackageList.autoRenewPackageString)\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.collection_grammar1\n          value: =Text('pa0320_AskCancelCategory_DM.jsp' + GetAutoRenewPackageList.autoRenewPackageString)\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.choseOtherPackages\n  value: false\n\n- kind: SetVariable\n  id: setVariable_REPLACE_THIS\n  variable: Global.packageAutoRenewCategory\n  value: pa0320_AskCancelCategory_DM.returnvalue\n\n- kind: GotoAction\n  id: goto_REPLACE_THIS\n  actionId: pa0325_NumEnrolledOptions_JDA\n```", "timestamp": 1749471838.0174267, "content_hash": "a435b0a0511a862ddf1ca2d99758f777"}