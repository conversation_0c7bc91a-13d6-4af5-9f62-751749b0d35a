{"response": "```yaml\n- kind: Question\n  id: DC0030_AskIntent_DM\n  displayName: DC0030_AskIntent_DM\n  interruptionPolicy:\n    allowInterruption: true\n\n  alwaysPrompt: true\n  variable: Global.DC0030_AskIntent_DM_reco\n  prompt:\n    speak:\n      - |\n        {Switch(\n            true,\n\n            // Case 1: Random 1, IntentMSG = \"Default\"\n            Global.IfRandom = 1 && Global.IntentMSG = \"Default\",\n            \"What ll it be today? Tell me in a few words\",\n\n            // Case 2: Random 1, IntentMSG = \"operator_request\"\n            Global.IfRandom = 1 && Global.IntentMSG = \"operator_request\",\n            \"To get you to the right representative, please tell me the reason for your call\",\n\n            // Case 3: Random 2, IntentMSG = \"Default\"\n            Global.IfRandom = 2 && Global.IntentMSG = \"Default\",\n            \"What can I do for you? Tell me in a couple words\",\n\n            // Case 4: Random 2, IntentMSG = \"operator_request\"\n            Global.IfRandom = 2 && Global.IntentMSG = \"operator_request\",\n            \"I can help you right here, so please, tell me the reason for your call\",\n\n            // Case 5: Random 3, IntentMSG = \"Default\"\n            Global.IfRandom = 3 && Global.IntentMSG = \"Default\",\n            \"Let s get started! In a few words, what can I help you with?\",\n\n            // Case 6: Random 3, IntentMSG = \"operator_request\"\n            Global.IfRandom = 3 && Global.IntentMSG = \"operator_request\",\n            \"In order to help you better, please tell me the reason for your call\",\n\n            // Case 7: Else, IntentMSG = \"Default\"\n            Global.IntentMSG = \"Default\",\n            \"How can I make your day? Tell me in your own words\",\n\n            // Case 8: Else, IntentMSG = \"operator_request\"\n            Global.IntentMSG = \"operator_request\",\n            \"I understand you'd like to speak with someone, but first please tell me the reason for your call\",\n\n            // Default\n            \"How can I help you today?\"\n        )}\n\n  entity:\n    kind: EmbeddedEntity\n    definition:\n      kind: ClosedListEntity\n      items:\n        - id: dtmf_entry\n          displayName: dtmf_entry\n        - id: operator\n          displayName: operator\n\n  voiceInputSettings:\n    silenceDetectionTimeoutInMilliseconds: 7000\n    repeatCountOnSilence: 2\n    inputTimeoutResponse:\n      speak: []\n    defaultValueMissingAction: Escalate\n\n- kind: SetVariable\n  id: set_REPLACE_THIS\n  variable: Global.Var_DC0030_AskIntent_DM\n  value: =Text(Global.DC0030_AskIntent_DM_reco)\n\n- kind: ConditionGroup\n  id: conditionGroup_REPLACE_THIS\n  conditions:\n    - id: conditionItem_REPLACE_THIS\n      condition: =If(Global.Var_DC0030_AskIntent_DM = \"dtmf_entry\", true, false)\n      actions:\n        - kind: SetVariable\n          id: setVariable_REPLACE_THIS\n          variable: Global.GlobalVars.playTransferMessage\n          value: false\n\n        - kind: SendActivity\n          id: sendActivity_REPLACE_THIS\n          activity:\n            speak:\n              - \"<audio src=\\\"AUDIO_LOCATION/DC0030_nm1_01.wav\\\">One moment while I transfer you</audio>\"\n\n        - kind: GotoAction\n          id: goto_REPLACE_THIS\n          actionId: DC0050_CallTransfer_SD\n\n  elseActions:\n    - kind: ConditionGroup\n      id: conditionGroup_REPLACE_THIS\n      conditions:\n        - id: conditionItem_REPLACE_THIS\n          condition: =If(Global.Var_DC0030_AskIntent_DM = \"operator\", true, false)\n          actions:\n            - kind: SetVariable\n              id: setVariable_REPLACE_THIS\n              variable: Global.agentCounter\n              value: agentCounter + 1\n\n            - kind: ConditionGroup\n              id: conditionGroup_REPLACE_THIS\n              conditions:\n                - id: conditionItem_REPLACE_THIS\n                  condition: =If(Global.agentCounter = 1, true, false)\n                  actions:\n                    - kind: SetVariable\n                      id: setVariable_REPLACE_THIS\n                      variable: Global.IntentMSG\n                      value: operator_request\n\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: DC0030_AskIntent_DM\n\n              elseActions:\n                - kind: SetVariable\n                  id: setVariable_REPLACE_THIS\n                  variable: Global.GlobalVars.playTransferMessage\n                  value: false\n\n                - kind: ConditionGroup\n                  id: conditionGroup_REPLACE_THIS\n                  conditions:\n                    - id: conditionItem_REPLACE_THIS\n                      condition: =If(Global.agentCounter < 1, true, false)\n                      actions:\n                        - kind: SendActivity\n                          id: sendActivity_REPLACE_THIS\n                          activity:\n                            speak:\n                              - \"<audio src=\\\"AUDIO_LOCATION/DC0030_out_01.wav\\\">Thanks, we'll use your response to improve our services for the future</audio>\"\n\n                        - kind: GotoAction\n                          id: goto_REPLACE_THIS\n                          actionId: DC0050_CallTransfer_SD\n\n                  elseActions:\n                    - kind: SendActivity\n                      id: sendActivity_REPLACE_THIS\n                      activity:\n                        speak:\n                          - \"<audio src=\\\"AUDIO_LOCATION/DC0030_out_02.wav\\\">I didn't quite get that One moment while I transfer you</audio>\"\n\n                    - kind: GotoAction\n                      id: goto_REPLACE_THIS\n                      actionId: DC0050_CallTransfer_SD\n```", "timestamp": 1749528032.0617435, "content_hash": "e5fafb113022504b0b95de2af2f4b23e"}